// components/InputPatternMonitor.js
// This component monitors typing patterns and clipboard usage to detect suspicious behavior

import { useEffect, useRef } from 'react';

/**
 * InputPatternMonitor - Monitors keyboard input patterns to detect suspicious behavior
 * 
 * @param {Function} onViolation - Callback to report violations (type, message)
 * @param {boolean} isActive - Whether monitoring is active
 * @returns {null} - This component has no UI
 */
const InputPatternMonitor = ({ onViolation, isActive = true }) => {
  // Store typing timestamps for analysis
  const typingTimestampsRef = useRef([]);
  // Store typed content for content analysis
  const typingBufferRef = useRef('');
  // Track when analysis was last performed
  const lastAnalysisRef = useRef(Date.now());
  // Track consecutive suspicious patterns
  const suspiciousPatternCountRef = useRef(0);
  // Track paste events
  const pasteCountRef = useRef(0);
  
  useEffect(() => {
    if (!isActive) return;
    
    console.log('Starting input pattern monitoring');
    
    // Track typing patterns
    const handleKeyDown = (e) => {
      // Only track character keys, not control keys
      if (e.key.length === 1) {
        typingTimestampsRef.current.push(Date.now());
        typingBufferRef.current += e.key;
        
        // Don't analyze on every keystroke - too performance heavy
        if (Date.now() - lastAnalysisRef.current > 5000 && 
            typingTimestampsRef.current.length > 30) {
          analyzeTypingPatterns();
          lastAnalysisRef.current = Date.now();
        }
      }
    };
    
    // Analyze for suspicious typing patterns
    const analyzeTypingPatterns = () => {
      const timestamps = typingTimestampsRef.current;
      if (timestamps.length < 30) return;
      
      // Calculate intervals between keystrokes
      const intervals = [];
      for (let i = 1; i < timestamps.length; i++) {
        intervals.push(timestamps[i] - timestamps[i-1]);
      }
      
      // Calculate statistics
      const avgInterval = intervals.reduce((sum, val) => sum + val, 0) / intervals.length;
      const variance = intervals.reduce((sum, val) => sum + Math.pow(val - avgInterval, 2), 0) / 
                       intervals.length;
      const stdDev = Math.sqrt(variance);
      
      console.log(`Typing analysis - avg: ${avgInterval.toFixed(2)}ms, stdDev: ${stdDev.toFixed(2)}ms, count: ${intervals.length}`);
      
      // 1. Detect suspiciously consistent typing (bot-like or copy-paste)
      // Normal human typing has variations (high stdDev)
      if (stdDev < 25 && avgInterval < 100 && intervals.length > 30) {
        console.log('🚨 Detected unusually consistent typing pattern');
        suspiciousPatternCountRef.current++;
        
        if (suspiciousPatternCountRef.current >= 2) {
          onViolation('suspiciousTyping', 
            'Unusually consistent typing pattern detected');
          
          // Reset counter after reporting
          suspiciousPatternCountRef.current = 0;
        }
        
        // Reset after detection
        typingTimestampsRef.current = [];
        typingBufferRef.current = '';
        return;
      }
      
      // 2. Detect extreme typing speed (copy-paste or pre-written)
      // Normal human typing is around 200ms between keystrokes (5 chars per second)
      // Extremely fast typing with very low intervals is suspicious
      if (avgInterval < 50 && intervals.length > 25) {
        console.log('🚨 Detected unusually fast typing');
        suspiciousPatternCountRef.current++;
        
        if (suspiciousPatternCountRef.current >= 2) {
          onViolation('suspiciousTyping', 
            'Unusually fast typing detected');
          
          // Reset counter after reporting
          suspiciousPatternCountRef.current = 0;
        }
        
        // Reset after detection
        typingTimestampsRef.current = [];
        typingBufferRef.current = '';
        return;
      }
      
      // Normal pattern detected - decrease suspicious count
      if (suspiciousPatternCountRef.current > 0) {
        suspiciousPatternCountRef.current--;
      }
      
      // Keep buffer to reasonable size by trimming oldest entries
      if (timestamps.length > 200) {
        typingTimestampsRef.current = timestamps.slice(-100);
        typingBufferRef.current = typingBufferRef.current.slice(-500);
      }
    };
    
    // Track paste events
    const handlePaste = (e) => {
      const pastedText = e.clipboardData.getData('text');
      
      // If substantial content pasted
      if (pastedText && pastedText.length > 20) {
        pasteCountRef.current++;
        console.log(`📋 Content pasted (${pastedText.length} characters)`);
        
        // Report paste event (most paste events are suspicious during an interview)
        onViolation('contentPasted', 
          `Content pasted (${pastedText.length} characters)`);
        
        // Check for code in pasted content
        const hasCodeIndicators = /function|class|import|export|const|var|let|return|if\s*\(|for\s*\(/.test(pastedText);
        
        if (hasCodeIndicators) {
          console.log('🚨 Code detected in pasted content');
          onViolation('codePasted', 'Code snippet pasted into answer');
        }
        
        // Analysis for potentially pre-written or AI-generated content
        analyzeContent(pastedText);
      }
    };
    
    // Analyze text content for patterns indicating pre-written or AI-generated content
    const analyzeContent = (text) => {
      // Strip whitespace
      const cleanText = text.trim();
      
      // Skip short texts
      if (cleanText.length < 100) return;
      
      // Check for highly structured content (common in pre-written answers)
      const hasStructuredFormat = /^\s*(?:[1-5]\.|\*|\-)\s+/m.test(cleanText) && 
                                 /\b(?:first|second|third|finally|in conclusion)\b/i.test(cleanText);
      
      // Check for academic language patterns (common in AI generation)
      const hasAcademicPatterns = /\b(?:paradigm|framework|methodology|implementation|conceptual|theoretical)\b/gi.test(cleanText) &&
                                 cleanText.length > 300;
      
      // Detect highly refined language unlikely in spontaneous answers
      const refinedLanguageScore = countRefinedLanguageMarkers(cleanText);
      
      // Report if highly suspicious
      if ((hasStructuredFormat && hasAcademicPatterns) || refinedLanguageScore > 5) {
        console.log('🚨 Pre-written or AI-generated content detected');
        onViolation('suspiciousContent', 'Content appears pre-written or AI-generated');
      }
    };
    
    // Count markers of highly refined language
    const countRefinedLanguageMarkers = (text) => {
      let score = 0;
      
      // Check for perfectly balanced paragraphs
      const paragraphs = text.split(/\n\s*\n/);
      if (paragraphs.length >= 3) {
        const lengths = paragraphs.map(p => p.length);
        const avgLength = lengths.reduce((sum, len) => sum + len, 0) / lengths.length;
        const allSimilarLength = lengths.every(len => Math.abs(len - avgLength) < avgLength * 0.3);
        
        if (allSimilarLength) score += 2;
      }
      
      // Check for complex transitions (common in refined writing)
      const transitions = [
        'furthermore', 'consequently', 'nevertheless', 'in addition', 
        'moreover', 'subsequently', 'in contrast'
      ];
      
      const transitionCount = transitions.reduce((count, term) => {
        return count + (text.toLowerCase().match(new RegExp('\\b' + term + '\\b', 'g')) || []).length;
      }, 0);
      
      score += Math.min(transitionCount, 3);
      
      // Check for perfect citation patterns
      if (/\(\w+,\s+\d{4}\)/.test(text)) {
        score += 1;
      }
      
      return score;
    };
    
    // Add event listeners with capture to catch all events
    document.addEventListener('keydown', handleKeyDown, { capture: true });
    document.addEventListener('paste', handlePaste, { capture: true });
    
    // Cleanup function
    return () => {
      document.removeEventListener('keydown', handleKeyDown, { capture: true });
      document.removeEventListener('paste', handlePaste, { capture: true });
      console.log('Input pattern monitoring stopped');
    };
  }, [isActive, onViolation]);
  
  // Component has no UI
  return null;
};

export default InputPatternMonitor;
