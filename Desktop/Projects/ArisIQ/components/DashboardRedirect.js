// File: components/DashboardRedirect.js
// Helper component to handle proper dashboard routing

import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';

export default function DashboardRedirect() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'loading') return; // Still loading

    if (!session) {
      // Not authenticated, redirect to home
      router.push('/');
      return;
    }

    // Determine correct dashboard based on user type
    const userType = session.user.userType;
    const currentPath = router.pathname;

    console.log('Dashboard redirect check:', {
      userType,
      currentPath,
      email: session.user.email
    });

    // If user is on wrong dashboard, redirect to correct one
    if (userType === 'recruiter' && currentPath === '/candidate-dashboard') {
      console.log('Redirecting recruiter to recruiter dashboard');
      router.replace('/recruiter/dashboard');
    } else if (userType === 'candidate' && currentPath === '/recruiter/dashboard') {
      console.log('Redirecting candidate to candidate dashboard');
      router.replace('/candidate-dashboard');
    } else if (!userType) {
      // If userType is not set, default to candidate
      console.log('No userType found, defaulting to candidate dashboard');
      router.replace('/candidate-dashboard');
    }
  }, [session, status, router]);

  return null; // This component doesn't render anything
}

// Alternative: Create a higher-order component for dashboard pages
export function withDashboardRedirect(WrappedComponent, requiredUserType) {
  return function DashboardComponent(props) {
    const { data: session, status } = useSession();
    const router = useRouter();

    useEffect(() => {
      if (status === 'loading') return;

      if (!session) {
        router.push('/');
        return;
      }

      const userType = session.user.userType || 'candidate';
      
      if (requiredUserType && userType !== requiredUserType) {
        const correctDashboard = userType === 'recruiter' 
          ? '/recruiter/dashboard' 
          : '/candidate-dashboard';
        router.replace(correctDashboard);
        return;
      }
    }, [session, status, router]);

    if (status === 'loading') {
      return (
        <div className="min-h-screen bg-gradient-to-br from-slate-200 via-blue-100 to-purple-200 flex items-center justify-center">
          <div className="text-slate-800 text-xl flex items-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
            Loading...
          </div>
        </div>
      );
    }

    if (!session) {
      return null; // Will redirect
    }

    const userType = session.user.userType || 'candidate';
    if (requiredUserType && userType !== requiredUserType) {
      return null; // Will redirect
    }

    return <WrappedComponent {...props} />;
  };
}
