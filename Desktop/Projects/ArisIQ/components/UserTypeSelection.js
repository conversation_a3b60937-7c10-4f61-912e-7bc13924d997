// File: components/UserTypeSelection.js
// Simplified component for Google users to select their type

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';

const UserTypeSelection = ({ onComplete }) => {
  const [selectedType, setSelectedType] = useState('candidate');
  const [companyName, setCompanyName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { data: session } = useSession();
  const router = useRouter();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    if (selectedType === 'recruiter' && !companyName.trim()) {
      setError('Company name is required for recruiters');
      setLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/auth/update-user-type', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: session?.user?.email,
          userType: selectedType,
          ...(selectedType === 'recruiter' && { company: companyName.trim() })
        }),
      });

      if (response.ok) {
        console.log('User type updated successfully');
        
        // Force a page reload to refresh the session
        if (selectedType === 'recruiter') {
          window.location.href = '/recruiter/dashboard';
        } else {
          window.location.href = '/candidate-dashboard';
        }
        
        if (onComplete) onComplete();
      } else {
        const data = await response.json();
        setError(data.error || 'Failed to update user type');
      }
    } catch (error) {
      console.error('Update user type error:', error);
      setError('Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full p-6">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Welcome to ArisIQ!
          </h2>
          <p className="text-gray-600">
            Please tell us how you'll be using our platform:
          </p>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* User Type Selection */}
          <div className="space-y-3">
            <div
              className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                selectedType === 'candidate'
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setSelectedType('candidate')}
            >
              <div className="flex items-center">
                <input
                  type="radio"
                  name="userType"
                  value="candidate"
                  checked={selectedType === 'candidate'}
                  onChange={() => setSelectedType('candidate')}
                  className="mr-3"
                />
                <div>
                  <h3 className="font-semibold">👤 I'm a Job Seeker</h3>
                  <p className="text-sm text-gray-600">
                    Looking for job opportunities and want to showcase my skills
                  </p>
                </div>
              </div>
            </div>

            <div
              className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                selectedType === 'recruiter'
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setSelectedType('recruiter')}
            >
              <div className="flex items-center">
                <input
                  type="radio"
                  name="userType"
                  value="recruiter"
                  checked={selectedType === 'recruiter'}
                  onChange={() => setSelectedType('recruiter')}
                  className="mr-3"
                />
                <div>
                  <h3 className="font-semibold">🏢 I'm a Recruiter</h3>
                  <p className="text-sm text-gray-600">
                    Looking to hire talent and post job opportunities
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Company Name for Recruiters */}
          {selectedType === 'recruiter' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Company Name
              </label>
              <input
                type="text"
                value={companyName}
                onChange={(e) => setCompanyName(e.target.value)}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter your company name"
              />
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
          >
            {loading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Setting up your account...
              </div>
            ) : (
              'Continue'
            )}
          </button>
        </form>
      </div>
    </div>
  );
};

export default UserTypeSelection;
