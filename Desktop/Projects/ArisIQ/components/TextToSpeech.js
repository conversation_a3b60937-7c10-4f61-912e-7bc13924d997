// File: components/TextToSpeech.js

import React, { useState, useEffect, useRef } from 'react';
import { FaVolumeUp, FaVolumeMute, FaPause, FaPlay } from 'react-icons/fa';

const TextToSpeech = ({ text, rate = 1, autoPlay = false }) => {
  const [isPaused, setIsPaused] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [availableVoices, setAvailableVoices] = useState([]);
  const [selectedVoice, setSelectedVoice] = useState(null);
  const [speechRate, setSpeechRate] = useState(rate);
  const [showControls, setShowControls] = useState(false);
  
  const utteranceRef = useRef(null);
  
  // Load available voices
  useEffect(() => {
    const loadVoices = () => {
      const voices = window.speechSynthesis.getVoices();
      if (voices.length > 0) {
        setAvailableVoices(voices);
        
        // Select an English voice by default
        const englishVoice = voices.find(voice => 
          voice.lang.includes('en-') && !voice.name.includes('Google')
        );
        
        setSelectedVoice(englishVoice || voices[0]);
      }
    };
    
    if (window.speechSynthesis) {
      loadVoices();
      
      // Chrome loads voices asynchronously
      if (window.speechSynthesis.onvoiceschanged !== undefined) {
        window.speechSynthesis.onvoiceschanged = loadVoices;
      }
    }
    
    return () => {
      // Cleanup
      stopSpeaking();
      if (window.speechSynthesis) {
        window.speechSynthesis.onvoiceschanged = null;
      }
    };
  }, []);
  
  // Auto-play when requested
  useEffect(() => {
    if (autoPlay && text && selectedVoice) {
      speak();
    }
  }, [text, selectedVoice, autoPlay]);
  
  // Create and configure the utterance
  const createUtterance = () => {
    const utterance = new SpeechSynthesisUtterance(text);
    
    if (selectedVoice) {
      utterance.voice = selectedVoice;
    }
    
    utterance.rate = speechRate;
    
    utterance.onstart = () => {
      setIsSpeaking(true);
      setIsPaused(false);
    };
    
    utterance.onend = () => {
      setIsSpeaking(false);
      setIsPaused(false);
    };
    
    utterance.onerror = (event) => {
      console.error('Speech synthesis error:', event);
      setIsSpeaking(false);
      setIsPaused(false);
    };
    
    utteranceRef.current = utterance;
    return utterance;
  };
  
  // Start speaking
  const speak = () => {
    if (!window.speechSynthesis || !text) return;
    
    // Cancel any ongoing speech
    window.speechSynthesis.cancel();
    
    const utterance = createUtterance();
    window.speechSynthesis.speak(utterance);
  };
  
  // Pause speaking
  const pause = () => {
    if (!window.speechSynthesis || !isSpeaking) return;
    
    window.speechSynthesis.pause();
    setIsPaused(true);
  };
  
  // Resume speaking
  const resume = () => {
    if (!window.speechSynthesis || !isPaused) return;
    
    window.speechSynthesis.resume();
    setIsPaused(false);
  };
  
  // Stop speaking
  const stopSpeaking = () => {
    if (!window.speechSynthesis) return;
    
    window.speechSynthesis.cancel();
    setIsSpeaking(false);
    setIsPaused(false);
  };
  
  // Handle rate change
  const handleRateChange = (e) => {
    const newRate = parseFloat(e.target.value);
    setSpeechRate(newRate);
    
    // Update current utterance if speaking
    if (isSpeaking && utteranceRef.current) {
      const currentTime = utteranceRef.current.currentTime;
      stopSpeaking();
      
      // Create new utterance with updated rate
      const utterance = createUtterance();
      utterance.rate = newRate;
      window.speechSynthesis.speak(utterance);
    }
  };
  
  // Handle voice change
  const handleVoiceChange = (e) => {
    const selectedVoiceName = e.target.value;
    const newVoice = availableVoices.find(voice => voice.name === selectedVoiceName);
    
    if (newVoice) {
      setSelectedVoice(newVoice);
      
      // Update current utterance if speaking
      if (isSpeaking) {
        const wasPlaying = !isPaused;
        stopSpeaking();
        
        // If it was playing, restart with new voice
        if (wasPlaying) {
          setTimeout(() => {
            speak();
          }, 100);
        }
      }
    }
  };
  
  // If speech synthesis is not available
  if (!window.speechSynthesis) {
    return (
      <div className="text-red-600 text-sm">
        Text-to-speech is not supported in this browser.
      </div>
    );
  }
  
  return (
    <div className="flex flex-col">
      <div className="flex items-center space-x-2">
        {!isSpeaking ? (
          <button
            onClick={speak}
            className="p-2 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200"
            title="Read text aloud"
          >
            <FaVolumeUp />
          </button>
        ) : isPaused ? (
          <button
            onClick={resume}
            className="p-2 rounded-full bg-green-100 text-green-600 hover:bg-green-200"
            title="Resume"
          >
            <FaPlay />
          </button>
        ) : (
          <button
            onClick={pause}
            className="p-2 rounded-full bg-yellow-100 text-yellow-600 hover:bg-yellow-200"
            title="Pause"
          >
            <FaPause />
          </button>
        )}
        
        {isSpeaking && (
          <button
            onClick={stopSpeaking}
            className="p-2 rounded-full bg-red-100 text-red-600 hover:bg-red-200"
            title="Stop"
          >
            <FaVolumeMute />
          </button>
        )}
        
        <button
          onClick={() => setShowControls(!showControls)}
          className="text-xs text-blue-600 hover:underline"
        >
          {showControls ? 'Hide options' : 'Show options'}
        </button>
      </div>
      
      {showControls && (
        <div className="mt-2 p-2 bg-gray-50 rounded">
          <div className="mb-2">
            <label className="block text-xs text-gray-600 mb-1">Speech Rate</label>
            <input
              type="range"
              min="0.5"
              max="2"
              step="0.1"
              value={speechRate}
              onChange={handleRateChange}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>Slow</span>
              <span>Normal</span>
              <span>Fast</span>
            </div>
          </div>
          
          <div>
            <label className="block text-xs text-gray-600 mb-1">Voice</label>
            <select
              value={selectedVoice?.name || ''}
              onChange={handleVoiceChange}
              className="w-full text-sm p-1 border rounded"
            >
              {availableVoices.map((voice, index) => (
                <option key={index} value={voice.name}>
                  {voice.name} ({voice.lang})
                </option>
              ))}
            </select>
          </div>
        </div>
      )}
    </div>
  );
};

export default TextToSpeech;
