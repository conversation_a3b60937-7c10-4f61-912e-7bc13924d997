// File: components/WebcamFaceDetection.js - COMPLETE FIXED VERSION WITH ENHANCED DEBUGGING

'use client';
import React, { useEffect, useRef, useState, useImperativeHandle, forwardRef, useCallback } from 'react';

// Face-api.js loader with single instance prevention
const ensureFaceApiLoaded = () => {
  return new Promise((resolve, reject) => {
    if (window.faceApiLoaded && window.faceapi && window.faceapi.nets) {
      console.log('Face-api.js already loaded');
      resolve(true);
      return;
    }
    
    if (window.faceApiLoading) {
      console.log('Face-api.js already loading, waiting...');
      const checkInterval = setInterval(() => {
        if (window.faceApiLoaded) {
          clearInterval(checkInterval);
          resolve(true);
        }
      }, 100);
      return;
    }
    
    window.faceApiLoading = true;
    console.log('Loading face-api.js...');
    
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/@vladmandic/face-api/dist/face-api.min.js';
    script.onload = () => {
      console.log('✅ Face-api.js loaded successfully');
      window.faceApiLoaded = true;
      window.faceApiLoading = false;
      setTimeout(() => resolve(true), 500);
    };
    script.onerror = (error) => {
      console.error('❌ Failed to load face-api.js:', error);
      window.faceApiLoading = false;
      reject(error);
    };
    document.head.appendChild(script);
  });
};

// Model loader with error handling and retry logic
const loadFaceModels = async () => {
  try {
    console.log('🔄 Starting face model loading...');
    
    await ensureFaceApiLoaded();
    
    if (!window.faceapi || !window.faceapi.nets) {
      throw new Error('face-api.js not properly initialized');
    }
    
    console.log('📥 Loading face detection models...');
    
    const loadModel = async (modelLoader, modelName, maxRetries = 3) => {
      for (let i = 0; i < maxRetries; i++) {
        try {
          await modelLoader;
          console.log(`✅ ${modelName} loaded`);
          return true;
        } catch (error) {
          console.warn(`⚠️ ${modelName} failed (attempt ${i + 1}/${maxRetries}):`, error);
          if (i === maxRetries - 1) {
            throw error;
          }
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    };
    
    // Load models with retry logic
    await loadModel(
      window.faceapi.nets.ssdMobilenetv1.loadFromUri('/models'),
      'SSD MobileNet v1'
    );
    
    await loadModel(
      window.faceapi.nets.faceLandmark68TinyNet.loadFromUri('/models'),
      'Face Landmark 68 Tiny'
    );
    
    // Try to load expressions (optional)
    try {
      await loadModel(
        window.faceapi.nets.faceExpressionNet.loadFromUri('/models'),
        'Face Expression Net'
      );
    } catch (error) {
      console.warn('Face Expression Net not available (optional)');
    }
    
    console.log('✅ Face detection models loaded successfully');
    
    // Verify models
    console.log('Model verification:');
    console.log('- SSD:', window.faceapi.nets.ssdMobilenetv1.isLoaded);
    console.log('- Landmarks Tiny:', window.faceapi.nets.faceLandmark68TinyNet.isLoaded);
    console.log('- Expressions:', window.faceapi.nets.faceExpressionNet?.isLoaded || 'Not loaded');
    
    return true;
  } catch (error) {
    console.error('❌ Error loading face models:', error);
    return false;
  }
};

const WebcamFaceDetection = forwardRef((props, ref) => {
  const { onStatusUpdate } = props;
  
  // Refs
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const streamRef = useRef(null);
  const detectionIntervalRef = useRef(null);
  const isComponentMountedRef = useRef(false);
  const modelsLoadedRef = useRef(false);
  const detectionRunningRef = useRef(false);
  const initializingRef = useRef(false);
  const statusUpdateTimeoutRef = useRef(null);
  
  // State
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState(null);
  const [facesDetected, setFacesDetected] = useState(0);
  const [lookingAway, setLookingAway] = useState(false);
  const [faceQuality, setFaceQuality] = useState('unknown');
  const [faceConfidence, setFaceConfidence] = useState(0);
  const [modelLoadingStatus, setModelLoadingStatus] = useState('loading');
  
  // Detection state - using useRef for persistence
  const detectionStateRef = useRef({
    consecutiveFaceNotVisible: 0,
    consecutiveLookingAway: 0,
    consecutiveMultipleFaces: 0,
    lastFaceDetectionTime: Date.now()
  });
  
  // CRITICAL FIX: Enhanced status update function with better debugging
  const updateStatus = useCallback((status) => {
    if (!onStatusUpdate || !isComponentMountedRef.current) {
      console.error('❌ [WEBCAM-FACEDETECTION] Cannot send status - callback missing or unmounted');
      console.error('- onStatusUpdate:', !!onStatusUpdate);
      console.error('- Component mounted:', isComponentMountedRef.current);
      return;
    }
    
    try {
      // Clear any pending timeout
      if (statusUpdateTimeoutRef.current) {
        clearTimeout(statusUpdateTimeoutRef.current);
      }
      
      // Add timestamp and ensure all fields exist
      const statusWithDefaults = {
        isDetecting: Boolean(status.isDetecting),
        facesDetected: Number(status.facesDetected || 0),
        lookingAway: Boolean(status.lookingAway),
        faceConfidence: Number(status.faceConfidence || 0),
        faceQuality: String(status.faceQuality || 'unknown'),
        consecutiveFaceNotVisible: Number(status.consecutiveFaceNotVisible || 0),
        consecutiveLookingAway: Number(status.consecutiveLookingAway || 0),
        consecutiveMultipleFaces: Number(status.consecutiveMultipleFaces || 0),
        timestamp: Date.now(),
        ...status
      };
      
      console.log('📤 [WEBCAM-FACEDETECTION] Sending status update:', statusWithDefaults);
      console.log('🔗 [WEBCAM-FACEDETECTION] Callback function:', onStatusUpdate);
      
      // Send immediately
      onStatusUpdate(statusWithDefaults);
      
      // Verify the callback was executed by adding a marker
      console.log('✅ [WEBCAM-FACEDETECTION] Status update callback executed');
      
      // Also send with a slight delay to ensure parent receives it
      statusUpdateTimeoutRef.current = setTimeout(() => {
        if (isComponentMountedRef.current && onStatusUpdate) {
          console.log('🔄 [WEBCAM-FACEDETECTION] Sending delayed status update...');
          onStatusUpdate(statusWithDefaults);
        }
      }, 50);
      
    } catch (error) {
      console.error('❌ [WEBCAM-FACEDETECTION] Error sending status update:', error);
    }
  }, [onStatusUpdate]);
  
  // Initialize camera with better error handling
  const initializeCamera = useCallback(async () => {
    if (initializingRef.current) {
      console.log('Camera already initializing, skipping...');
      return false;
    }
    
    try {
      initializingRef.current = true;
      console.log('🎥 Initializing camera...');
      
      updateStatus({
        isDetecting: false,
        facesDetected: 0,
        lookingAway: false,
        faceConfidence: 0,
        faceQuality: 'initializing'
      });
      
      // Stop existing stream if any
      if (streamRef.current && streamRef.current !== window.savedInterviewStream) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
      
      let stream = null;
      
      // First try to use existing stream
      if (window.savedInterviewStream && window.savedInterviewStream.active) {
        console.log('✅ Using saved interview stream');
        stream = window.savedInterviewStream;
      } else {
        console.log('⚠️ Creating new camera stream');
        try {
          stream = await navigator.mediaDevices.getUserMedia({
            video: {
              width: { ideal: 640, max: 1280 },
              height: { ideal: 480, max: 720 },
              frameRate: { ideal: 15, max: 30 },
              facingMode: 'user'
            },
            audio: false
          });
        } catch (mediaError) {
          console.error('❌ Media access error:', mediaError);
          setError(`Camera access denied: ${mediaError.message}`);
          updateStatus({
            isDetecting: false,
            facesDetected: 0,
            lookingAway: false,
            faceConfidence: 0,
            faceQuality: 'camera_error',
            error: mediaError.message
          });
          return false;
        }
      }
      
      if (!isComponentMountedRef.current) {
        if (stream && stream !== window.savedInterviewStream) {
          stream.getTracks().forEach(track => track.stop());
        }
        return false;
      }
      
      streamRef.current = stream;
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        
        const setupVideo = () => {
          return new Promise((resolve, reject) => {
            const video = videoRef.current;
            
            const onLoadedMetadata = () => {
              console.log('✅ Video metadata loaded');
              console.log('Video dimensions:', video.videoWidth, 'x', video.videoHeight);
              
              if (canvasRef.current) {
                canvasRef.current.width = video.videoWidth;
                canvasRef.current.height = video.videoHeight;
                console.log('Canvas size set to:', canvasRef.current.width, 'x', canvasRef.current.height);
              }
              
              video.removeEventListener('loadedmetadata', onLoadedMetadata);
              video.removeEventListener('error', onError);
              resolve();
            };
            
            const onError = (error) => {
              console.error('Video error:', error);
              video.removeEventListener('loadedmetadata', onLoadedMetadata);
              video.removeEventListener('error', onError);
              reject(error);
            };
            
            video.addEventListener('loadedmetadata', onLoadedMetadata);
            video.addEventListener('error', onError);
          });
        };
        
        try {
          await setupVideo();
          await videoRef.current.play();
          console.log('✅ Video playing');
          
          setIsInitialized(true);
          setError(null);
          
          // Send initial status update that camera is now active
          updateStatus({
            isDetecting: true,
            facesDetected: 0,
            lookingAway: false,
            faceConfidence: 0,
            faceQuality: 'camera_ready'
          });
          
          if (modelsLoadedRef.current) {
            console.log('🚀 Starting detection (everything ready)');
            startFaceDetection();
          }
        } catch (playError) {
          console.error('Error setting up video:', playError);
          // Retry video play after a delay
          setTimeout(async () => {
            if (videoRef.current && isComponentMountedRef.current) {
              try {
                await videoRef.current.play();
                console.log('✅ Video playing (retry successful)');
              } catch (retryError) {
                console.error('Video retry failed:', retryError);
              }
            }
          }, 1000);
        }
      }
      
      return true;
    } catch (error) {
      console.error('❌ Camera initialization error:', error);
      setError(`Camera error: ${error.message}`);
      
      updateStatus({
        isDetecting: false,
        facesDetected: 0,
        lookingAway: false,
        faceConfidence: 0,
        faceQuality: 'camera_error',
        error: error.message
      });
      
      return false;
    } finally {
      initializingRef.current = false;
    }
  }, [updateStatus]);
  
  // Face detection function with proper scoping and error handling
  const startFaceDetection = useCallback(() => {
    if (detectionRunningRef.current) {
      console.log('Detection already running, skipping...');
      return;
    }
    
    console.log('🎯 Starting face detection...');
    console.log('- Models loaded:', modelsLoadedRef.current);
    console.log('- Face API available:', !!window.faceapi);
    console.log('- SSD loaded:', window.faceapi?.nets?.ssdMobilenetv1?.isLoaded);
    console.log('- Landmarks Tiny loaded:', window.faceapi?.nets?.faceLandmark68TinyNet?.isLoaded);
    console.log('- Video ready:', videoRef.current?.readyState);
    
    if (!modelsLoadedRef.current || !window.faceapi?.nets?.ssdMobilenetv1?.isLoaded) {
      console.log('⚠️ Models not ready, retrying in 1 second...');
      setTimeout(() => {
        if (isComponentMountedRef.current && !detectionRunningRef.current) {
          startFaceDetection();
        }
      }, 1000);
      return;
    }
    
    if (detectionIntervalRef.current) {
      clearInterval(detectionIntervalRef.current);
      console.log('Cleared existing detection interval');
    }
    
    console.log('🚀 Starting face detection NOW!');
    detectionRunningRef.current = true;
    
    const runDetection = async () => {
      if (!videoRef.current || !canvasRef.current || !isComponentMountedRef.current) {
        console.log('Detection prerequisites missing');
        return;
      }
      
      try {
        const video = videoRef.current;
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');
        
        if (video.readyState !== 4 || video.videoWidth === 0) {
          console.log('Video not ready, readyState:', video.readyState);
          return;
        }
        
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        console.log('Running face detection...');
        let detections = [];
        
        try {
          // First detect faces
          detections = await window.faceapi
            .detectAllFaces(video, new window.faceapi.SsdMobilenetv1Options({ 
              scoreThreshold: 0.5 
            }));
          
          console.log(`Initial SSD detection: ${detections.length} faces`);
          
          // Add landmarks only if available
          if (detections.length > 0 && window.faceapi.nets.faceLandmark68TinyNet.isLoaded) {
            console.log('Adding tiny landmarks...');
            detections = await window.faceapi
              .detectAllFaces(video, new window.faceapi.SsdMobilenetv1Options({ 
                scoreThreshold: 0.5 
              }))
              .withFaceLandmarks('TinyNet');
          }
          
          console.log(`Final detection with landmarks: ${detections.length} faces`);
          
        } catch (detectionError) {
          console.error('Face detection error:', detectionError);
          return;
        }
        
        const state = detectionStateRef.current;
        
        // Initialize local scope variables for this detection cycle
        let currentIsLookingAway = false;
        let currentFaceQuality = 'not_visible';
        let currentFaceConfidence = 0;
        
        // Process detection results
        if (detections.length === 0) {
          state.consecutiveFaceNotVisible++;
          setFacesDetected(0);
          setLookingAway(false);
          setFaceConfidence(0);
          setFaceQuality('not_visible');
          
          // Draw red border
          ctx.strokeStyle = 'red';
          ctx.lineWidth = 3;
          ctx.strokeRect(0, 0, canvas.width, canvas.height);
          
          ctx.fillStyle = 'red';
          ctx.font = '20px Arial';
          ctx.fillText('No Face Detected', 10, 30);
          
          console.log('No face detected');
          
        } else if (detections.length === 1) {
          state.consecutiveFaceNotVisible = 0;
          state.consecutiveMultipleFaces = 0;
          
          const detection = detections[0];
          const confidence = detection.detection.score;
          currentFaceConfidence = confidence;
          
          console.log(`✅ Face detected! Confidence: ${(confidence * 100).toFixed(1)}%`);
          
          // Check for looking away with proper scoping
          if (detection.landmarks) {
            try {
              const landmarks = detection.landmarks;
              if (landmarks.getLeftEye && landmarks.getRightEye && landmarks.getNose) {
                const leftEye = landmarks.getLeftEye();
                const rightEye = landmarks.getRightEye();
                const nose = landmarks.getNose();
                
                if (leftEye.length > 0 && rightEye.length > 0 && nose.length > 0) {
                  const eyeDistance = Math.abs(leftEye[0].x - rightEye[3].x);
                  const midEyeX = (leftEye[0].x + rightEye[3].x) / 2;
                  const noseX = nose[0].x;
                  const deviation = Math.abs(noseX - midEyeX);
                  
                  currentIsLookingAway = deviation > eyeDistance * 0.13;
                  console.log(`Looking away check: ${currentIsLookingAway}, deviation: ${deviation.toFixed(2)}, threshold: ${(eyeDistance * 0.13).toFixed(2)}`);
                }
              }
            } catch (landmarkError) {
              console.warn('Landmark processing error:', landmarkError);
            }
          } else {
            console.log('No landmarks available');
          }
          
          setFacesDetected(1);
          setLookingAway(currentIsLookingAway);
          setFaceConfidence(confidence);
          
          // Set quality
          currentFaceQuality = 'poor';
          if (confidence > 0.8) currentFaceQuality = 'excellent';
          else if (confidence > 0.6) currentFaceQuality = 'good';
          else if (confidence > 0.4) currentFaceQuality = 'fair';
          setFaceQuality(currentFaceQuality);
          
          // Draw face box
          const box = detection.detection.box;
          ctx.strokeStyle = currentIsLookingAway ? 'orange' : 'green';
          ctx.lineWidth = 3;
          ctx.strokeRect(box.x, box.y, box.width, box.height);
          
          ctx.fillStyle = currentIsLookingAway ? 'orange' : 'green';
          ctx.font = '16px Arial';
          ctx.fillText(currentIsLookingAway ? 'Looking Away' : 'Face OK', 10, 30);
          
        } else {
          state.consecutiveFaceNotVisible = 0;
          state.consecutiveLookingAway = 0;
          state.consecutiveMultipleFaces++;
          
          setFacesDetected(detections.length);
          setLookingAway(false);
          setFaceConfidence(0);
          currentFaceQuality = 'multiple_faces';
          setFaceQuality(currentFaceQuality);
          
          console.log(`Multiple faces detected: ${detections.length}`);
          
          // Draw boxes for all faces
          ctx.strokeStyle = 'red';
          ctx.lineWidth = 3;
          detections.forEach(detection => {
            const box = detection.detection.box;
            ctx.strokeRect(box.x, box.y, box.width, box.height);
          });
          
          ctx.fillStyle = 'red';
          ctx.font = '16px Arial';
          ctx.fillText(`${detections.length} Faces`, 10, 30);
        }
        
        // CRITICAL: Update parent component with properly scoped variables
        const statusUpdate = {
          isDetecting: true,
          facesDetected: detections.length,
          lookingAway: detections.length === 1 ? currentIsLookingAway : false,
          faceConfidence: detections.length === 1 ? currentFaceConfidence : 0,
          faceQuality: currentFaceQuality,
          consecutiveFaceNotVisible: state.consecutiveFaceNotVisible,
          consecutiveLookingAway: state.consecutiveLookingAway,
          consecutiveMultipleFaces: state.consecutiveMultipleFaces
        };
        
        // Log the status being sent
        console.log('📊 [WEBCAM-FACEDETECTION] Status update being sent:', statusUpdate);
        
        // Send the status update
        updateStatus(statusUpdate);
        
      } catch (error) {
        console.error('Detection loop error:', error);
        setError(`Detection error: ${error.message}`);
      }
    };
    
    // Run first detection immediately
    console.log('Running first detection...');
    runDetection();
    
    // Set up interval
    detectionIntervalRef.current = setInterval(() => {
      console.log('Running periodic detection...');
      runDetection();
    }, 1000);
    
    console.log('✅ Detection interval set up');
  }, [updateStatus]);
  
  // Stop camera with proper cleanup
  const stopCamera = useCallback(() => {
    console.log('🛑 Stopping camera...');
    
    detectionRunningRef.current = false;
    
    if (detectionIntervalRef.current) {
      clearInterval(detectionIntervalRef.current);
      detectionIntervalRef.current = null;
    }
    
    if (statusUpdateTimeoutRef.current) {
      clearTimeout(statusUpdateTimeoutRef.current);
      statusUpdateTimeoutRef.current = null;
    }
    
    if (streamRef.current && streamRef.current !== window.savedInterviewStream) {
      streamRef.current.getTracks().forEach(track => track.stop());
    }
    streamRef.current = null;
    
    if (videoRef.current) {
      videoRef.current.srcObject = null;
      videoRef.current.pause();
    }
    
    if (canvasRef.current) {
      const ctx = canvasRef.current.getContext('2d');
      ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);
    }
    
    setIsInitialized(false);
    setFacesDetected(0);
    setLookingAway(false);
    setFaceConfidence(0);
    setFaceQuality('camera_stopped');
    
    updateStatus({
      isDetecting: false,
      facesDetected: 0,
      lookingAway: false,
      faceConfidence: 0,
      faceQuality: 'camera_stopped'
    });
  }, [updateStatus]);
  
  // Expose methods to parent
  useImperativeHandle(ref, () => ({
    stopCamera,
    startCamera: initializeCamera,
    video: videoRef.current,
    canvas: canvasRef.current,
    getStatus: () => ({
      isDetecting: isInitialized && detectionRunningRef.current,
      facesDetected,
      lookingAway,
      faceConfidence,
      faceQuality
    })
  }), [stopCamera, initializeCamera, isInitialized, facesDetected, lookingAway, faceConfidence, faceQuality]);
  
  // Initialize on mount
  useEffect(() => {
    isComponentMountedRef.current = true;
    console.log('🚀 WebcamFaceDetection component mounted');
    console.log('🔗 onStatusUpdate callback provided:', !!onStatusUpdate);
    
    const initializeComponent = async () => {
      try {
        setModelLoadingStatus('loading');
        console.log('📥 Starting model loading...');
        
        const modelsLoaded = await loadFaceModels();
        modelsLoadedRef.current = modelsLoaded;
        
        if (modelsLoaded) {
          setModelLoadingStatus('loaded');
          console.log('✅ Models loaded successfully');
          
          // Start camera after a short delay
          setTimeout(() => {
            if (isComponentMountedRef.current) {
              initializeCamera();
            }
          }, 500);
        } else {
          setModelLoadingStatus('error');
          setError('Failed to load face detection models');
          console.error('❌ Failed to load models');
        }
      } catch (error) {
        console.error('Initialization error:', error);
        setModelLoadingStatus('error');
        setError(`Initialization error: ${error.message}`);
      }
    };
    
    initializeComponent();
    
    return () => {
      console.log('🧹 WebcamFaceDetection cleanup');
      isComponentMountedRef.current = false;
      stopCamera();
    };
  }, []);
  
  // Monitor readiness and start detection when everything is ready
  useEffect(() => {
    const checkAndStartDetection = () => {
      console.log('🔍 Checking if we should start detection...');
      console.log('- Models loaded:', modelsLoadedRef.current);
      console.log('- Camera initialized:', isInitialized);
      console.log('- Detection running:', detectionRunningRef.current);
      console.log('- Video ready:', videoRef.current?.readyState === 4);
      
      if (modelsLoadedRef.current && isInitialized && !detectionRunningRef.current) {
        console.log('🚀 All conditions met, starting detection!');
        setTimeout(() => {
          if (isComponentMountedRef.current && !detectionRunningRef.current) {
            startFaceDetection();
          }
        }, 500);
      }
    };
    
    checkAndStartDetection();
  }, [modelsLoadedRef.current, isInitialized, startFaceDetection]);
  
  // Periodic check to ensure detection is running
  useEffect(() => {
    const forceStartInterval = setInterval(() => {
      if (modelsLoadedRef.current && isInitialized && !detectionRunningRef.current) {
        console.log('⚡ Forcing detection start (periodic check)');
        startFaceDetection();
      }
    }, 3000);
    
    return () => clearInterval(forceStartInterval);
  }, [modelsLoadedRef.current, isInitialized, startFaceDetection]);
  
  // Ensure status updates are sent periodically
  useEffect(() => {
    const statusInterval = setInterval(() => {
      if (detectionRunningRef.current && isComponentMountedRef.current) {
        const currentStatus = {
          isDetecting: detectionRunningRef.current,
          facesDetected,
          lookingAway,
          faceConfidence,
          faceQuality,
          consecutiveFaceNotVisible: detectionStateRef.current.consecutiveFaceNotVisible,
          consecutiveLookingAway: detectionStateRef.current.consecutiveLookingAway,
          consecutiveMultipleFaces: detectionStateRef.current.consecutiveMultipleFaces
        };
        
        console.log('🔄 [WEBCAM-FACEDETECTION] Periodic status update:', currentStatus);
        updateStatus(currentStatus);
      }
    }, 2000); // Send status every 2 seconds
    
    return () => clearInterval(statusInterval);
  }, [facesDetected, lookingAway, faceConfidence, faceQuality, updateStatus]);
  
  return (
    <div className="webcam-container relative overflow-hidden rounded-lg bg-black">
      <video 
        ref={videoRef} 
        autoPlay 
        playsInline
        muted 
        className="w-full h-full object-cover"
        style={{ transform: 'scaleX(-1)' }}
      />
      
      <canvas
        ref={canvasRef}
        className="absolute top-0 left-0 w-full h-full pointer-events-none"
        style={{ transform: 'scaleX(-1)' }}
      />
      
      {error && (
        <div className="absolute top-2 left-2 bg-red-600 bg-opacity-90 text-white p-2 rounded text-sm max-w-[90%]">
          <div>⚠️ {error}</div>
        </div>
      )}
      
      <div className="absolute top-2 right-2 space-y-1">
        <div className={`px-2 py-1 rounded text-xs font-semibold ${
          detectionRunningRef.current ? 'bg-green-600 text-white' : 'bg-yellow-600 text-white'
        }`}>
          {detectionRunningRef.current ? 'Active' : 'Initializing...'}
        </div>
        
        {isInitialized && (
          <div className={`px-2 py-1 rounded text-xs font-semibold ${
            facesDetected === 1 ? 'bg-green-600 text-white' :
            facesDetected > 1 ? 'bg-red-600 text-white' :
            'bg-red-600 text-white'
          }`}>
            {facesDetected === 0 ? 'No Face' :
             facesDetected === 1 ? 'Face OK' :
             `${facesDetected} Faces`}
          </div>
        )}
        
        {isInitialized && facesDetected === 1 && (
          <>
            <div className={`px-2 py-1 rounded text-xs font-semibold ${
              lookingAway ? 'bg-orange-600 text-white' : 'bg-green-600 text-white'
            }`}>
              {lookingAway ? 'Looking Away' : 'Looking Good'}
            </div>
            
            <div className={`px-2 py-1 rounded text-xs font-semibold ${
              faceQuality === 'excellent' ? 'bg-green-600 text-white' :
              faceQuality === 'good' ? 'bg-blue-600 text-white' :
              faceQuality === 'fair' ? 'bg-yellow-600 text-white' :
              'bg-red-600 text-white'
            }`}>
              Quality: {faceQuality}
            </div>
          </>
        )}
      </div>
      
      {/* Enhanced debug info */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white p-2 rounded text-xs font-mono">
          <div>Models: {modelLoadingStatus}</div>
          <div>Camera: {isInitialized ? '✅' : '❌'}</div>
          <div>Detection: {detectionRunningRef.current ? '✅' : '❌'}</div>
          <div>Face API: {window.faceapi ? '✅' : '❌'}</div>
          <div>Video Ready: {videoRef.current?.readyState === 4 ? '✅' : '❌'}</div>
          <div>Models Loaded: {modelsLoadedRef.current ? '✅' : '❌'}</div>
          <div>SSD: {window.faceapi?.nets?.ssdMobilenetv1?.isLoaded ? '✅' : '❌'}</div>
          <div>Landmarks Tiny: {window.faceapi?.nets?.faceLandmark68TinyNet?.isLoaded ? '✅' : '❌'}</div>
          <div>Faces Found: {facesDetected}</div>
          <div>Status Callback: {onStatusUpdate ? '✅' : '❌'}</div>
          <div>Component Mounted: {isComponentMountedRef.current ? '✅' : '❌'}</div>
          <div className="border-t pt-1 mt-1">
            <div>Callback Function: {onStatusUpdate?.name || 'anonymous'}</div>
            <div>Last Update: {new Date().toLocaleTimeString()}</div>
          </div>
        </div>
      )}
    </div>
  );
});

WebcamFaceDetection.displayName = 'WebcamFaceDetection';

export default WebcamFaceDetection;
