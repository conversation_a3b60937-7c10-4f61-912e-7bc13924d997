// File: components/InterviewPrepHub.js

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";

export default function InterviewPrepHub() {
  const { data: session } = useSession();
  const [activeSection, setActiveSection] = useState("overview");
  const [practiceResults, setPracticeResults] = useState([]);
  const [currentQuestion, setCurrentQuestion] = useState(null);
  const [userAnswer, setUserAnswer] = useState("");
  const [isRecording, setIsRecording] = useState(false);
  const [selectedJobType, setSelectedJobType] = useState("general");
  const [difficultyLevel, setDifficultyLevel] = useState("intermediate");
  const [showFeedback, setShowFeedback] = useState(false);
  const [hoveredCard, setHoveredCard] = useState(null);

  // Sample interview questions by category
  const questionBank = {
    general: {
      easy: [
        "Tell me about yourself.",
        "Why are you interested in this position?",
        "What are your greatest strengths?",
        "Where do you see yourself in 5 years?",
        "Why are you leaving your current job?"
      ],
      intermediate: [
        "Describe a challenging project you worked on and how you overcame obstacles.",
        "How do you handle conflict in the workplace?",
        "Give an example of a time you had to learn a new skill quickly.",
        "How do you prioritize your work when you have multiple deadlines?",
        "Describe a time when you had to work with a difficult team member."
      ],
      advanced: [
        "How would you approach leading a team through a major organizational change?",
        "Describe a time when you had to make a decision with incomplete information.",
        "How do you measure success in your role, and how do you ensure continuous improvement?",
        "Tell me about a time when you had to influence stakeholders without direct authority.",
        "How would you handle a situation where your team consistently misses deadlines?"
      ]
    },
    technical: {
      easy: [
        "What programming languages are you most comfortable with?",
        "Explain the difference between a class and an object.",
        "What is version control and why is it important?",
        "Describe the software development lifecycle.",
        "What is debugging and what tools do you use?"
      ],
      intermediate: [
        "How would you optimize a slow-performing database query?",
        "Explain the concept of RESTful APIs and when you would use them.",
        "Describe the difference between synchronous and asynchronous programming.",
        "How do you ensure code quality in your projects?",
        "Walk me through how you would design a simple web application."
      ],
      advanced: [
        "Design a scalable system that can handle millions of users.",
        "How would you implement a real-time chat application?",
        "Explain microservices architecture and its trade-offs.",
        "How do you handle data consistency in distributed systems?",
        "Describe your approach to implementing CI/CD pipelines."
      ]
    },
    behavioral: {
      easy: [
        "Tell me about a time you helped a coworker.",
        "Describe a goal you set and how you achieved it.",
        "How do you handle stress at work?",
        "What motivates you in your career?",
        "Describe your ideal work environment."
      ],
      intermediate: [
        "Tell me about a time you failed and what you learned from it.",
        "Describe a situation where you had to adapt to significant changes.",
        "How do you handle receiving constructive criticism?",
        "Tell me about a time you had to meet a tight deadline.",
        "Describe a situation where you disagreed with your manager."
      ],
      advanced: [
        "Tell me about a time you had to make an unpopular decision.",
        "Describe how you've handled a crisis or emergency situation.",
        "How do you balance competing priorities from different stakeholders?",
        "Tell me about a time you identified and solved a process improvement.",
        "Describe a situation where you had to lead without formal authority."
      ]
    }
  };

  // Interview tips by category
  const interviewTips = {
    preparation: [
      "Research the company thoroughly - mission, values, recent news, and competitors",
      "Review the job description and align your experiences with required skills", 
      "Prepare STAR (Situation, Task, Action, Result) examples for behavioral questions",
      "Practice your elevator pitch and key talking points out loud",
      "Prepare thoughtful questions to ask the interviewer about the role and company"
    ],
    technical: [
      "Practice coding problems on platforms like LeetCode or HackerRank",
      "Review fundamental concepts in your tech stack",
      "Be prepared to explain your thought process while solving problems",
      "Practice whiteboarding or coding in a shared document",
      "Understand time and space complexity of common algorithms"
    ],
    communication: [
      "Use the STAR method for behavioral questions",
      "Be specific with examples and quantify your achievements when possible",
      "Listen carefully to questions and ask for clarification if needed",
      "Maintain good eye contact and positive body language",
      "Show enthusiasm and genuine interest in the role"
    ],
    followup: [
      "Send a thank-you email within 24 hours",
      "Reference specific topics discussed during the interview",
      "Reiterate your interest and qualifications for the role",
      "Include any additional information you forgot to mention",
      "Follow up appropriately if you don't hear back within their specified timeframe"
    ]
  };

  const generateQuestion = () => {
    const questions = questionBank[selectedJobType]?.[difficultyLevel] || questionBank.general.intermediate;
    const randomQuestion = questions[Math.floor(Math.random() * questions.length)];
    setCurrentQuestion(randomQuestion);
    setUserAnswer("");
    setShowFeedback(false);
  };

  const submitAnswer = () => {
    if (!userAnswer.trim()) return;
    
    const result = {
      question: currentQuestion,
      answer: userAnswer,
      timestamp: new Date().toLocaleString(),
      jobType: selectedJobType,
      difficulty: difficultyLevel
    };
    
    setPracticeResults([result, ...practiceResults]);
    setShowFeedback(true);
  };

  const startNewSession = () => {
    setCurrentQuestion(null);
    setUserAnswer("");
    setShowFeedback(false);
    generateQuestion();
  };

  // Component for metric cards
  const MetricCard = ({ title, value, subtitle, icon, gradient, delay = 0, onClick }) => (
    <div 
      className={`relative overflow-hidden rounded-2xl bg-white/50 backdrop-blur-xl border border-white/60 p-6 transform transition-all duration-500 hover:scale-105 hover:shadow-2xl cursor-pointer animate-fade-in-up`}
      style={{ 
        animationDelay: `${delay}ms`,
        boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)'
      }}
      onMouseEnter={() => setHoveredCard(title)}
      onMouseLeave={() => setHoveredCard(null)}
      onClick={onClick}
    >
      <div 
        className="absolute top-0 left-0 right-0 h-2 rounded-t-xl"
        style={{ background: gradient }}
      ></div>
      
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-400/30 to-purple-400/30 transform rotate-12 translate-x-full hover:translate-x-0 transition-transform duration-1000"></div>
      </div>
      
      <div className="relative z-10">
        <div className="flex items-center justify-between mb-4">
          <div className="text-slate-700 text-sm font-medium">{title}</div>
          <div className="text-2xl">{icon}</div>
        </div>
        <div className="text-3xl font-bold text-slate-900 mb-2">{value}</div>
        {subtitle && <div className="text-slate-600 text-sm">{subtitle}</div>}
      </div>
      
      {hoveredCard === title && (
        <div className="absolute inset-0 bg-gradient-to-r from-blue-100/40 to-purple-100/40 rounded-2xl"></div>
      )}
    </div>
  );

  const renderOverview = () => (
    <div className="space-y-8">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Practice Sessions"
          value={practiceResults.length}
          subtitle="Questions answered"
          icon="🎯"
          gradient="linear-gradient(135deg, #3B82F6, #1D4ED8)"
          delay={0}
          onClick={() => setActiveSection("practice")}
        />
        <MetricCard
          title="Study Materials"
          value="50+"
          subtitle="Tips & resources"
          icon="📚"
          gradient="linear-gradient(135deg, #10B981, #059669)"
          delay={100}
          onClick={() => setActiveSection("tips")}
        />
        <MetricCard
          title="Mock Interviews"
          value="0"
          subtitle="Scheduled sessions"
          icon="🎥"
          gradient="linear-gradient(135deg, #8B5CF6, #7C3AED)"
          delay={200}
          onClick={() => setActiveSection("mock")}
        />
        <MetricCard
          title="Progress Score"
          value="75%"
          subtitle="Interview readiness"
          icon="📈"
          gradient="linear-gradient(135deg, #F59E0B, #D97706)"
          delay={300}
        />
      </div>

      {/* Quick Actions */}
      <div className="bg-white/50 backdrop-blur-xl border border-white/60 rounded-2xl p-8 shadow-xl animate-fade-in-up" style={{animationDelay: '400ms'}}>
        <h3 className="text-2xl font-bold text-slate-800 mb-6">🚀 Quick Start</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <button
            onClick={() => {
              setActiveSection("practice");
              startNewSession();
            }}
            className="p-6 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-lg group"
          >
            <div className="text-3xl mb-2 group-hover:scale-110 transition-transform duration-200">🎯</div>
            <h4 className="font-semibold mb-2">Start Practice</h4>
            <p className="text-sm opacity-90">Answer interview questions and get feedback</p>
          </button>
          
          <button
            onClick={() => setActiveSection("tips")}
            className="p-6 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-lg group"
          >
            <div className="text-3xl mb-2 group-hover:scale-110 transition-transform duration-200">💡</div>
            <h4 className="font-semibold mb-2">Study Tips</h4>
            <p className="text-sm opacity-90">Learn best practices and strategies</p>
          </button>
          
          <button
            onClick={() => setActiveSection("mock")}
            className="p-6 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-200 shadow-lg group"
          >
            <div className="text-3xl mb-2 group-hover:scale-110 transition-transform duration-200">🎥</div>
            <h4 className="font-semibold mb-2">Mock Interview</h4>
            <p className="text-sm opacity-90">Practice with realistic interview scenarios</p>
          </button>
        </div>
      </div>

      {/* Recent Practice */}
      {practiceResults.length > 0 && (
        <div className="bg-white/50 backdrop-blur-xl border border-white/60 rounded-2xl p-8 shadow-xl animate-fade-in-up" style={{animationDelay: '500ms'}}>
          <h3 className="text-2xl font-bold text-slate-800 mb-6">📈 Recent Practice</h3>
          <div className="space-y-4">
            {practiceResults.slice(0, 3).map((result, index) => (
              <div key={index} className="bg-slate-50/60 rounded-xl p-4">
                <h4 className="font-medium text-slate-800 mb-2">{result.question}</h4>
                <p className="text-sm text-slate-600 mb-2">{result.answer.substring(0, 100)}...</p>
                <div className="flex justify-between items-center text-xs text-slate-500">
                  <span>{result.jobType} • {result.difficulty}</span>
                  <span>{result.timestamp}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );

  const renderPractice = () => (
    <div className="space-y-8">
      {/* Settings */}
      <div className="bg-white/50 backdrop-blur-xl border border-white/60 rounded-2xl p-8 shadow-xl">
        <h3 className="text-2xl font-bold text-slate-800 mb-6">🎯 Practice Settings</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">Interview Type</label>
            <select
              value={selectedJobType}
              onChange={(e) => setSelectedJobType(e.target.value)}
              className="w-full bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-2 text-slate-900 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg"
            >
              <option value="general">General Questions</option>
              <option value="technical">Technical Questions</option>
              <option value="behavioral">Behavioral Questions</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">Difficulty Level</label>
            <select
              value={difficultyLevel}
              onChange={(e) => setDifficultyLevel(e.target.value)}
              className="w-full bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-2 text-slate-900 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg"
            >
              <option value="easy">Easy</option>
              <option value="intermediate">Intermediate</option>
              <option value="advanced">Advanced</option>
            </select>
          </div>
          
          <div className="flex items-end">
            <button
              onClick={startNewSession}
              className="w-full px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium shadow-lg"
            >
              🎲 Generate Question
            </button>
          </div>
        </div>
      </div>

      {/* Current Question */}
      {currentQuestion && (
        <div className="bg-white/50 backdrop-blur-xl border border-white/60 rounded-2xl p-8 shadow-xl">
          <h3 className="text-2xl font-bold text-slate-800 mb-6">❓ Current Question</h3>
          <div className="bg-blue-50/60 rounded-xl p-6 mb-6">
            <h4 className="text-lg font-medium text-blue-800 mb-4">{currentQuestion}</h4>
            <p className="text-sm text-blue-600">
              Take your time to think through your answer. Use the STAR method for behavioral questions.
            </p>
          </div>
          
          <div className="mb-6">
            <label className="block text-sm font-medium text-slate-700 mb-2">Your Answer</label>
            <textarea
              value={userAnswer}
              onChange={(e) => setUserAnswer(e.target.value)}
              placeholder="Type your answer here... Think about specific examples and quantifiable results."
              className="w-full h-32 bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-3 text-slate-900 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg resize-none"
            />
          </div>
          
          <div className="flex space-x-4">
            <button
              onClick={submitAnswer}
              disabled={!userAnswer.trim()}
              className="px-6 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 font-medium shadow-lg disabled:from-gray-400 disabled:to-gray-500"
            >
              ✅ Submit Answer
            </button>
            <button
              onClick={generateQuestion}
              className="px-6 py-2 bg-gradient-to-r from-yellow-500 to-yellow-600 text-white rounded-xl hover:from-yellow-600 hover:to-yellow-700 transition-all duration-200 font-medium shadow-lg"
            >
              ⏭️ Skip Question
            </button>
          </div>
        </div>
      )}

      {/* Feedback */}
      {showFeedback && (
        <div className="bg-green-50/60 backdrop-blur-md border border-green-200/60 rounded-2xl p-8 shadow-xl">
          <h3 className="text-xl font-bold text-green-800 mb-4">🎉 Great Job!</h3>
          <p className="text-green-700 mb-4">Your answer has been recorded. Here are some tips to improve:</p>
          <ul className="text-green-600 space-y-2">
            <li>• Be more specific with examples and include quantifiable results</li>
            <li>• Use the STAR method: Situation, Task, Action, Result</li>
            <li>• Practice speaking your answers out loud for better delivery</li>
            <li>• Connect your experience to the role you're applying for</li>
          </ul>
        </div>
      )}

      {/* Practice History */}
      {practiceResults.length > 0 && (
        <div className="bg-white/50 backdrop-blur-xl border border-white/60 rounded-2xl p-8 shadow-xl">
          <h3 className="text-2xl font-bold text-slate-800 mb-6">📊 Practice History</h3>
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {practiceResults.map((result, index) => (
              <div key={index} className="bg-slate-50/60 rounded-xl p-4">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium text-slate-800">{result.question}</h4>
                  <span className="text-xs text-slate-500 bg-slate-200/60 px-2 py-1 rounded-full">
                    {result.jobType} • {result.difficulty}
                  </span>
                </div>
                <p className="text-sm text-slate-600 mb-2">{result.answer}</p>
                <div className="text-xs text-slate-500">{result.timestamp}</div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );

  const renderTips = () => (
    <div className="space-y-8">
      {Object.entries(interviewTips).map(([category, tips], categoryIndex) => (
        <div key={category} className="bg-white/50 backdrop-blur-xl border border-white/60 rounded-2xl p-8 shadow-xl animate-fade-in-up" style={{animationDelay: `${categoryIndex * 100}ms`}}>
          <h3 className="text-2xl font-bold text-slate-800 mb-6 capitalize">
            {category === 'preparation' && '🎯'} 
            {category === 'technical' && '💻'} 
            {category === 'communication' && '💬'} 
            {category === 'followup' && '📧'} 
            {category.replace(/([A-Z])/g, ' $1').trim()} Tips
          </h3>
          <div className="grid gap-4">
            {tips.map((tip, index) => (
              <div key={index} className="bg-slate-50/60 rounded-xl p-4 hover:bg-slate-100/60 transition-all duration-200">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xs font-bold mt-0.5">
                    {index + 1}
                  </div>
                  <p className="text-slate-700 flex-1">{tip}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );

  const renderMock = () => (
    <div className="space-y-8">
      <div className="bg-white/50 backdrop-blur-xl border border-white/60 rounded-2xl p-8 shadow-xl text-center">
        <div className="text-6xl mb-4">🎥</div>
        <h3 className="text-2xl font-bold text-slate-800 mb-4">Mock Interview Sessions</h3>
        <p className="text-slate-600 mb-6">
          Practice with realistic interview scenarios including video recording and AI feedback.
        </p>
        <div className="space-y-4">
          <button className="w-full max-w-md px-6 py-3 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-200 font-medium shadow-lg">
            🎯 Start Mock Interview
          </button>
          <button className="w-full max-w-md px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium shadow-lg">
            📅 Schedule Session
          </button>
        </div>
        
        <div className="mt-8 p-4 bg-yellow-50/60 rounded-xl">
          <p className="text-yellow-700 text-sm">
            💡 <strong>Coming Soon:</strong> AI-powered mock interviews with real-time feedback and performance analytics.
          </p>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white/50 backdrop-blur-xl border border-white/60 rounded-2xl p-6 shadow-xl">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-700 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
          🎯 Interview Preparation Hub
        </h2>
        <p className="text-slate-700">Master your interview skills with AI-powered practice and personalized feedback</p>
      </div>

      {/* Navigation */}
      <div className="flex items-center space-x-1 bg-white/50 backdrop-blur-xl rounded-2xl p-2 w-fit shadow-xl">
        {[
          { id: 'overview', label: '🏠 Overview' },
          { id: 'practice', label: '🎯 Practice' },
          { id: 'tips', label: '💡 Tips' },
          { id: 'mock', label: '🎥 Mock Interview' }
        ].map((tab) => (
          <button 
            key={tab.id}
            onClick={() => setActiveSection(tab.id)} 
            className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
              activeSection === tab.id 
                ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg' 
                : 'text-slate-600 hover:bg-white/70'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Content */}
      {activeSection === 'overview' && renderOverview()}
      {activeSection === 'practice' && renderPractice()}
      {activeSection === 'tips' && renderTips()}
      {activeSection === 'mock' && renderMock()}

      <style jsx>{`
        @keyframes fade-in-up {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-fade-in-up {
          animation: fade-in-up 0.6s ease-out forwards;
          opacity: 0;
        }
      `}</style>
    </div>
  );
}
