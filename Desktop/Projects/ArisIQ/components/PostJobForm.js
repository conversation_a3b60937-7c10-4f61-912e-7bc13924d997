// File: components/PostJobForm.js

import { useState } from "react";
import axios from "axios";
import { useSession } from "next-auth/react";
import { useRouter } from "next/router";

export default function PostJobForm() {
  const { data: session } = useSession();
  const router = useRouter();

  const [form, setForm] = useState({
    title: "",
    company: "",
    industry: "",
    experience: "",
    salary: "",
    country: "",
    city: "",
    requiredSkills: "",
    preferredSkills: "",
    website: "",
    employmentType: "Full-time",
    workMode: "", // ✅ NEW
    description: "",
    applicationDeadline: "",
    screeningDeadline: "",
    questionSource: "100% AI Generated",
  });

  const [countrySuggestions, setCountrySuggestions] = useState([]);
  const [citySuggestions, setCitySuggestions] = useState([]);
  const [loadingSuggestions, setLoadingSuggestions] = useState(false);
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const industries = [
    "Information Technology", "Healthcare", "Finance", "Education", "Retail", "Transportation",
    "Manufacturing", "Marketing", "Construction", "Legal", "Energy", "Insurance",
    "Government", "Non-profit", "Real Estate", "Hospitality", "Telecommunications",
    "Aerospace", "Entertainment", "Biotechnology", "Other"
  ];

  const handleChange = (e) => {
    const { name, value } = e.target;
    setForm({ ...form, [name]: value });

    // Clear any existing errors for this field
    if (errors[name]) {
      setErrors({ ...errors, [name]: "" });
    }

    if (name === "country" && value.length > 2) {
      getSuggestions("country", value);
    } else if (name === "country") {
      setCountrySuggestions([]);
    }

    if (name === "city" && value.length > 2 && form.country) {
      getSuggestions("city", value, form.country);
    } else if (name === "city") {
      setCitySuggestions([]);
    }
  };

  const getSuggestions = async (type, query, country = null) => {
    try {
      setLoadingSuggestions(true);
      const response = await axios.post("/api/openai-location", {
        query,
        type,
        country: country || null,
      });
      if (type === "country") {
        setCountrySuggestions(response.data.suggestions || []);
      } else if (type === "city") {
        setCitySuggestions(response.data.suggestions || []);
      }
    } catch (err) {
      console.error(`${type} suggestion error:`, err);
    } finally {
      setLoadingSuggestions(false);
    }
  };

  const handleSuggestionClick = (name, value) => {
    setForm({ ...form, [name]: value });
    if (name === "country") setCountrySuggestions([]);
    if (name === "city") setCitySuggestions([]);
    // Clear any existing errors for this field
    if (errors[name]) {
      setErrors({ ...errors, [name]: "" });
    }
  };

  // Client-side validation function
  const validateForm = () => {
    const newErrors = {};

    // Required fields validation
    if (!form.title.trim()) {
      newErrors.title = "Job title is required";
    }
    if (!form.company.trim()) {
      newErrors.company = "Company name is required";
    }
    if (!form.industry) {
      newErrors.industry = "Industry is required";
    }

    // Date validations
    if (form.applicationDeadline) {
      const appDeadline = new Date(form.applicationDeadline);
      const currentDate = new Date();
      currentDate.setHours(0, 0, 0, 0); // Reset time to start of day for fair comparison

      if (isNaN(appDeadline.getTime())) {
        newErrors.applicationDeadline = "Application deadline is not a valid date";
      } else if (appDeadline < currentDate) {
        newErrors.applicationDeadline = "Application deadline cannot be in the past";
      }
    }

    if (form.screeningDeadline) {
      const scrDeadline = new Date(form.screeningDeadline);
      const appDeadline = form.applicationDeadline ? new Date(form.applicationDeadline) : null;

      if (isNaN(scrDeadline.getTime())) {
        newErrors.screeningDeadline = "Screening deadline is not a valid date";
      } else if (appDeadline && scrDeadline < appDeadline) {
        newErrors.screeningDeadline = "Screening deadline cannot be before application deadline";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!session?.user?.email) {
      alert("Please sign in to post a job.");
      return;
    }

    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setErrors({}); // Clear any previous errors

    const payload = {
      ...form,
      location: `${form.city}, ${form.country}`,
      requiredSkills: form.requiredSkills,
      preferredSkills: form.preferredSkills,
      postedBy: session.user.email,
      workMode: form.workMode, // ✅ NEW
    };

    try {
      const res = await axios.post("/api/jobs/post", payload);
      if (res.data.success) {
        alert("Job posted successfully!");
        router.push("/recruiter/dashboard");
      } else {
        alert(res.data.message || "Failed to post job.");
      }
    } catch (error) {
      console.error("❌ Error posting job:", error);

      // Handle specific validation errors from the API
      if (error.response?.data?.errors && Array.isArray(error.response.data.errors)) {
        alert("Validation failed:\n" + error.response.data.errors.join("\n"));
      } else {
        alert(error.response?.data?.message || "An error occurred while posting the job.");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white shadow-md rounded-xl p-8 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold text-blue-700 mb-6">Post a New Job</h2>
      <form onSubmit={handleSubmit} className="space-y-5 relative">
        {/* Job Title - Required */}
        <div>
          <input
            name="title"
            value={form.title}
            onChange={handleChange}
            placeholder="Job Title *"
            className={`w-full border p-3 rounded ${errors.title ? 'border-red-500' : 'border-gray-300'}`}
          />
          {errors.title && <p className="text-red-500 text-sm mt-1">{errors.title}</p>}
        </div>

        {/* Company Name - Required */}
        <div>
          <input
            name="company"
            value={form.company}
            onChange={handleChange}
            placeholder="Company Name *"
            className={`w-full border p-3 rounded ${errors.company ? 'border-red-500' : 'border-gray-300'}`}
          />
          {errors.company && <p className="text-red-500 text-sm mt-1">{errors.company}</p>}
        </div>

        {/* Industry - Required */}
        <div>
          <select
            name="industry"
            value={form.industry}
            onChange={handleChange}
            className={`w-full border p-3 rounded ${errors.industry ? 'border-red-500' : 'border-gray-300'}`}
          >
            <option value="">Select Industry *</option>
            {industries.map((ind) => <option key={ind} value={ind}>{ind}</option>)}
          </select>
          {errors.industry && <p className="text-red-500 text-sm mt-1">{errors.industry}</p>}
        </div>

        <input name="experience" value={form.experience} onChange={handleChange} placeholder="Experience Level" className="w-full border border-gray-300 p-3 rounded" />
        <input name="salary" value={form.salary} onChange={handleChange} placeholder="Salary Range" className="w-full border border-gray-300 p-3 rounded" />

        <div className="relative">
          <input
            name="country"
            value={form.country}
            onChange={handleChange}
            placeholder="Country (AI-powered)"
            className="w-full border border-gray-300 p-3 rounded"
          />
          {loadingSuggestions && <p className="text-sm text-gray-400 mt-1">Loading...</p>}
          {countrySuggestions.length > 0 && (
            <ul className="absolute z-10 bg-white border w-full mt-1 rounded shadow">
              {countrySuggestions.map((suggestion) => (
                <li
                  key={suggestion}
                  onClick={() => handleSuggestionClick("country", suggestion)}
                  className="p-2 hover:bg-gray-100 cursor-pointer"
                >
                  {suggestion}
                </li>
              ))}
            </ul>
          )}
        </div>

        <div className="relative">
          <input
            name="city"
            value={form.city}
            onChange={handleChange}
            placeholder="City (AI-powered)"
            className="w-full border border-gray-300 p-3 rounded"
          />
          {loadingSuggestions && <p className="text-sm text-gray-400 mt-1">Loading...</p>}
          {citySuggestions.length > 0 && (
            <ul className="absolute z-10 bg-white border w-full mt-1 rounded shadow">
              {citySuggestions.map((suggestion) => (
                <li
                  key={suggestion}
                  onClick={() => handleSuggestionClick("city", suggestion)}
                  className="p-2 hover:bg-gray-100 cursor-pointer"
                >
                  {suggestion}
                </li>
              ))}
            </ul>
          )}
        </div>

        <input name="requiredSkills" value={form.requiredSkills} onChange={handleChange} placeholder="Required Skills (comma-separated)" className="w-full border border-gray-300 p-3 rounded" />
        <input name="preferredSkills" value={form.preferredSkills} onChange={handleChange} placeholder="Preferred Skills (comma-separated)" className="w-full border border-gray-300 p-3 rounded" />
        <input name="website" value={form.website} onChange={handleChange} placeholder="Company Website" className="w-full border border-gray-300 p-3 rounded" />

        {/* Application Deadline */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Application Deadline</label>
          <input
            type="date"
            name="applicationDeadline"
            value={form.applicationDeadline}
            onChange={handleChange}
            className={`w-full border p-3 rounded ${errors.applicationDeadline ? 'border-red-500' : 'border-gray-300'}`}
          />
          {errors.applicationDeadline && <p className="text-red-500 text-sm mt-1">{errors.applicationDeadline}</p>}
        </div>

        {/* Screening Deadline */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Screening Deadline</label>
          <input
            type="date"
            name="screeningDeadline"
            value={form.screeningDeadline}
            onChange={handleChange}
            className={`w-full border p-3 rounded ${errors.screeningDeadline ? 'border-red-500' : 'border-gray-300'}`}
          />
          {errors.screeningDeadline && <p className="text-red-500 text-sm mt-1">{errors.screeningDeadline}</p>}
        </div>

        <select name="employmentType" value={form.employmentType} onChange={handleChange} className="w-full border border-gray-300 p-3 rounded">
          <option>Full-time</option>
          <option>Part-time</option>
          <option>Contract</option>
          <option>Internship</option>
        </select>

        <select name="workMode" value={form.workMode} onChange={handleChange} className="w-full border border-gray-300 p-3 rounded">
          <option value="">Select Work Mode</option>
          <option value="On-site">On-site</option>
          <option value="Remote">Remote</option>
          <option value="Hybrid">Hybrid</option>
        </select>

        <textarea name="description" value={form.description} onChange={handleChange} placeholder="Job Description" className="w-full border border-gray-300 p-3 rounded h-40"></textarea>

        <div>
          <label className="block font-medium mb-1">Question Source</label>
          <select name="questionSource" value={form.questionSource} onChange={handleChange} className="w-full border border-gray-300 p-3 rounded">
            <option value="100% AI Generated">100% AI Generated</option>
            <option value="50% AI + 50% Recruiter">50% AI + 50% Recruiter</option>
            <option value="100% Recruiter">100% Recruiter</option>
          </select>
        </div>

        {/* Required fields notice */}
        <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded">
          <p>* Required fields</p>
        </div>

        <button
          type="submit"
          disabled={isSubmitting}
          className={`w-full px-6 py-3 rounded-lg font-semibold transition-colors ${
            isSubmitting
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-green-600 hover:bg-green-700'
          } text-white`}
        >
          {isSubmitting ? 'Posting Job...' : 'Post Job'}
        </button>
      </form>
    </div>
  );
}

