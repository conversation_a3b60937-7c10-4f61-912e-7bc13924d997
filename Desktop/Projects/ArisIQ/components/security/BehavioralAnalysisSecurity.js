// File: components/security/BehavioralAnalysisSecurity.js
// Handles behavioral analysis: typing patterns, response times, eye tracking, code analysis

export class BehavioralAnalysisSecurity {
  constructor(options = {}) {
    this.exitingRef = options.exitingRef;
    this.securityManagerRef = options.securityManagerRef;
    this.trackPreSecurityViolation = options.trackPreSecurityViolation;
    this.currentQuestion = options.currentQuestion;
    this.showRequiredAcknowledgmentWarning = options.showRequiredAcknowledgmentWarning;
    
    // Typing pattern analysis
    this.typingAnalysis = {
      keystrokes: [],
      typingSpeed: [],
      pausePatterns: [],
      unusualPatterns: 0,
      baselineEstablished: false,
      baselineTypingSpeed: 0,
      baselinePausePattern: 0
    };
    
    // Response time analysis
    this.responseTimeAnalysis = {
      questionStartTimes: new Map(),
      responseTimes: [],
      suspiciouslyFastResponses: 0,
      averageResponseTime: 0
    };
    
    // Code analysis
    this.codeAnalysis = {
      codeSubmissions: [],
      suspiciouslyPerfectCode: 0,
      rapidCodeAppearance: 0,
      codeComplexityScores: []
    };
    
    // Eye tracking simulation (placeholder for real implementation)
    this.eyeTracking = {
      gazePoints: [],
      offScreenGazes: 0,
      focusPoints: [],
      attentionScore: 100
    };
    
    // Audio analysis
    this.audioAnalysis = {
      voicePatterns: [],
      multipleVoiceDetections: 0,
      silencePeriods: [],
      suspiciousAudioEvents: 0
    };
    
    this.setupBehavioralAnalysis = this.setupBehavioralAnalysis.bind(this);
    this.analyzeTypingPattern = this.analyzeTypingPattern.bind(this);
    this.analyzeResponseTime = this.analyzeResponseTime.bind(this);
    this.analyzeCodeSubmission = this.analyzeCodeSubmission.bind(this);
    this.reportBehavioralAnomaly = this.reportBehavioralAnomaly.bind(this);
  }

  // Check if security is active
  isSecurityActive() {
    return !this.exitingRef?.current && 
           (this.securityManagerRef?.current?.isInterviewActive || 
            this.trackPreSecurityViolation);
  }

  // Report behavioral anomaly (analytics only - doesn't trigger disqualification)
  reportBehavioralAnomaly(type, message, data) {
    console.log(`📊 Behavioral anomaly detected: ${type} - ${message}`, data);
    
    if (this.securityManagerRef?.current && this.securityManagerRef.current.isInterviewActive) {
      try {
        if (typeof this.securityManagerRef.current.handleAnalyticsViolation === 'function') {
          this.securityManagerRef.current.handleAnalyticsViolation(type, message);
        }
      } catch (error) {
        console.error('Error reporting behavioral anomaly:', error);
      }
    }
    
    // Dispatch custom event for analytics tracking
    const event = new CustomEvent('behavioral-anomaly-detected', {
      detail: { 
        type,
        message,
        data,
        timestamp: new Date().toISOString(),
        questionIndex: this.currentQuestion
      }
    });
    window.dispatchEvent(event);
  }

  // TYPING PATTERN ANALYSIS
  setupTypingPatternAnalysis() {
    console.log('⌨️ Setting up typing pattern analysis...');
    
    const cleanupFunctions = [];
    
    try {
      let lastKeyTime = 0;
      let keystrokeSequence = [];
      
      const analyzeKeystroke = (e) => {
        if (!this.isSecurityActive()) return;
        
        const currentTime = Date.now();
        const timeBetweenKeys = currentTime - lastKeyTime;
        
        // Record keystroke data
        const keystrokeData = {
          key: e.key,
          timestamp: currentTime,
          timeBetweenKeys: timeBetweenKeys,
          keyCode: e.keyCode,
          target: e.target.tagName,
          isInCodeEditor: this.isInCodeEditor(e.target),
          isInTextArea: e.target.tagName === 'TEXTAREA'
        };
        
        this.typingAnalysis.keystrokes.push(keystrokeData);
        keystrokeSequence.push(keystrokeData);
        
        // Keep only last 100 keystrokes for analysis
        if (this.typingAnalysis.keystrokes.length > 100) {
          this.typingAnalysis.keystrokes = this.typingAnalysis.keystrokes.slice(-100);
        }
        
        // Analyze typing patterns every 20 keystrokes
        if (keystrokeSequence.length >= 20) {
          this.analyzeTypingPattern(keystrokeSequence);
          keystrokeSequence = keystrokeSequence.slice(-10); // Keep overlap
        }
        
        lastKeyTime = currentTime;
      };
      
      document.addEventListener('keydown', analyzeKeystroke);
      cleanupFunctions.push(() => {
        document.removeEventListener('keydown', analyzeKeystroke);
      });
      
      console.log('✅ Typing pattern analysis initialized');
      
    } catch (error) {
      console.error('Error setting up typing pattern analysis:', error);
    }
    
    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }

  // Analyze typing patterns for anomalies
  analyzeTypingPattern(keystrokes) {
    if (keystrokes.length < 10) return;
    
    try {
      // Calculate typing speed (WPM)
      const timeSpan = keystrokes[keystrokes.length - 1].timestamp - keystrokes[0].timestamp;
      const wordsTyped = keystrokes.filter(k => k.key === ' ').length + 1;
      const wpm = (wordsTyped / (timeSpan / 1000)) * 60;
      
      this.typingAnalysis.typingSpeed.push(wpm);
      
      // Calculate inter-keystroke intervals
      const intervals = [];
      for (let i = 1; i < keystrokes.length; i++) {
        if (keystrokes[i].timeBetweenKeys > 0 && keystrokes[i].timeBetweenKeys < 2000) {
          intervals.push(keystrokes[i].timeBetweenKeys);
        }
      }
      
      if (intervals.length > 0) {
        const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
        const intervalVariance = this.calculateVariance(intervals);
        
        // Establish baseline if not done
        if (!this.typingAnalysis.baselineEstablished && this.typingAnalysis.typingSpeed.length >= 5) {
          this.typingAnalysis.baselineTypingSpeed = 
            this.typingAnalysis.typingSpeed.reduce((a, b) => a + b, 0) / this.typingAnalysis.typingSpeed.length;
          this.typingAnalysis.baselinePausePattern = avgInterval;
          this.typingAnalysis.baselineEstablished = true;
          
          console.log('📊 Typing baseline established:', {
            speed: this.typingAnalysis.baselineTypingSpeed.toFixed(2) + ' WPM',
            avgInterval: this.typingAnalysis.baselinePausePattern.toFixed(2) + 'ms'
          });
        }
        
        // Detect anomalies after baseline is established
        if (this.typingAnalysis.baselineEstablished) {
          // Extremely fast typing (>120 WPM is suspicious for coding)
          if (wpm > 120 && keystrokes.some(k => k.isInCodeEditor || k.isInTextArea)) {
            this.typingAnalysis.unusualPatterns++;
            this.reportBehavioralAnomaly('suspiciousTypingSpeed', 
              `Extremely fast typing detected: ${wpm.toFixed(2)} WPM`, { wpm, baseline: this.typingAnalysis.baselineTypingSpeed });
          }
          
          // Robotic typing pattern (very low variance in intervals)
          if (intervalVariance < 100 && intervals.length > 10) {
            this.typingAnalysis.unusualPatterns++;
            this.reportBehavioralAnomaly('roboticTypingPattern', 
              `Robotic typing pattern detected: variance ${intervalVariance.toFixed(2)}`, { variance: intervalVariance, intervals });
          }
          
          // Sudden dramatic change in typing speed
          const recentSpeeds = this.typingAnalysis.typingSpeed.slice(-3);
          const avgRecentSpeed = recentSpeeds.reduce((a, b) => a + b, 0) / recentSpeeds.length;
          
          if (Math.abs(avgRecentSpeed - this.typingAnalysis.baselineTypingSpeed) > 50) {
            this.typingAnalysis.unusualPatterns++;
            this.reportBehavioralAnomaly('typingSpeedChange', 
              `Dramatic typing speed change: ${avgRecentSpeed.toFixed(2)} vs baseline ${this.typingAnalysis.baselineTypingSpeed.toFixed(2)}`, 
              { current: avgRecentSpeed, baseline: this.typingAnalysis.baselineTypingSpeed });
          }
        }
      }
      
    } catch (error) {
      console.error('Error analyzing typing pattern:', error);
    }
  }

  // RESPONSE TIME ANALYSIS
  setupResponseTimeAnalysis() {
    console.log('⏱️ Setting up response time analysis...');
    
    // Track when questions are displayed
    this.startQuestionTimer = (questionIndex) => {
      this.responseTimeAnalysis.questionStartTimes.set(questionIndex, Date.now());
      console.log(`⏱️ Started timer for question ${questionIndex}`);
    };
    
    // Track when answers are submitted
    this.endQuestionTimer = (questionIndex, questionType) => {
      const startTime = this.responseTimeAnalysis.questionStartTimes.get(questionIndex);
      if (!startTime) return;
      
      const responseTime = (Date.now() - startTime) / 1000; // in seconds
      this.analyzeResponseTime(questionIndex, questionType, responseTime);
    };
    
    console.log('✅ Response time analysis initialized');
  }

  // Analyze response time for anomalies
  analyzeResponseTime(questionIndex, questionType, responseTime) {
    try {
      const responseData = {
        questionIndex,
        questionType,
        responseTime,
        timestamp: new Date().toISOString()
      };
      
      this.responseTimeAnalysis.responseTimes.push(responseData);
      
      // Define suspicious thresholds by question type
      const suspiciousThresholds = {
        'mcq': 3, // Less than 3 seconds for MCQ
        'technical': 10, // Less than 10 seconds for technical
        'behavioral': 8, // Less than 8 seconds for behavioral
        'coding': 30 // Less than 30 seconds for coding
      };
      
      const threshold = suspiciousThresholds[questionType] || 5;
      
      if (responseTime < threshold) {
        this.responseTimeAnalysis.suspiciouslyFastResponses++;
        
        this.reportBehavioralAnomaly('suspiciousResponseTime', 
          `Suspiciously fast response: ${responseTime.toFixed(2)}s for ${questionType} question (threshold: ${threshold}s)`,
          { responseTime, threshold, questionType, questionIndex });
      }
      
      // Calculate average response time
      this.responseTimeAnalysis.averageResponseTime = 
        this.responseTimeAnalysis.responseTimes.reduce((sum, r) => sum + r.responseTime, 0) / 
        this.responseTimeAnalysis.responseTimes.length;
      
      console.log(`📊 Response time recorded: ${responseTime.toFixed(2)}s for ${questionType} question`);
      
    } catch (error) {
      console.error('Error analyzing response time:', error);
    }
  }

  // CODE ANALYSIS
  setupCodeAnalysis() {
    console.log('💻 Setting up code analysis...');
    
    this.analyzeCode = (code, submissionTime, questionIndex) => {
      try {
        const codeData = {
          code,
          submissionTime,
          questionIndex,
          timestamp: new Date().toISOString(),
          complexity: this.calculateCodeComplexity(code),
          length: code.length,
          linesOfCode: code.split('\n').filter(line => line.trim().length > 0).length
        };
        
        this.codeAnalysis.codeSubmissions.push(codeData);
        
        this.analyzeCodeSubmission(codeData);
        
      } catch (error) {
        console.error('Error in code analysis:', error);
      }
    };
    
    console.log('✅ Code analysis initialized');
  }

  // Analyze code submission for anomalies
  analyzeCodeSubmission(codeData) {
    try {
      const { code, submissionTime, complexity, linesOfCode } = codeData;
      
      // Check for suspiciously fast code appearance
      const codePerSecond = linesOfCode / (submissionTime / 1000);
      if (codePerSecond > 3) { // More than 3 lines per second
        this.codeAnalysis.rapidCodeAppearance++;
        
        this.reportBehavioralAnomaly('rapidCodeAppearance', 
          `Code appeared too quickly: ${codePerSecond.toFixed(2)} lines/second`,
          { codePerSecond, linesOfCode, submissionTime });
      }
      
      // Check for suspiciously perfect code
      const perfectnessScore = this.calculateCodePerfectnessScore(code);
      if (perfectnessScore > 0.9) {
        this.codeAnalysis.suspiciouslyPerfectCode++;
        
        this.reportBehavioralAnomaly('suspiciouslyPerfectCode', 
          `Code appears suspiciously perfect: score ${perfectnessScore.toFixed(2)}`,
          { perfectnessScore, complexity, linesOfCode });
      }
      
      // Track complexity scores for pattern analysis
      this.codeAnalysis.codeComplexityScores.push(complexity);
      
      console.log(`💻 Code analysis completed: ${linesOfCode} lines, complexity: ${complexity.toFixed(2)}, perfectness: ${perfectnessScore.toFixed(2)}`);
      
    } catch (error) {
      console.error('Error analyzing code submission:', error);
    }
  }

  // Calculate code complexity (simplified metric)
  calculateCodeComplexity(code) {
    try {
      let complexity = 1; // Base complexity
      
      // Count control structures
      const controlStructures = ['if', 'else', 'for', 'while', 'switch', 'case', 'try', 'catch'];
      controlStructures.forEach(structure => {
        const regex = new RegExp(`\\b${structure}\\b`, 'g');
        const matches = code.match(regex);
        if (matches) complexity += matches.length;
      });
      
      // Count functions
      const functionMatches = code.match(/function\s+\w+|=>\s*{|\w+\s*=\s*function/g);
      if (functionMatches) complexity += functionMatches.length;
      
      // Count operators
      const operators = ['+', '-', '*', '/', '&&', '||', '==', '!=', '<', '>', '<=', '>='];
      operators.forEach(op => {
        const matches = code.split(op).length - 1;
        complexity += matches * 0.1;
      });
      
      return complexity;
      
    } catch (error) {
      console.error('Error calculating code complexity:', error);
      return 1;
    }
  }

  // Calculate code perfectness score (0-1)
  calculateCodePerfectnessScore(code) {
    try {
      let score = 0;
      
      // Check for proper indentation
      const lines = code.split('\n');
      const properlyIndentedLines = lines.filter(line => {
        if (line.trim().length === 0) return true;
        return line.match(/^(\s{2}|\s{4}|\t)/) || !line.startsWith(' ');
      });
      score += (properlyIndentedLines.length / lines.length) * 0.3;
      
      // Check for comments
      const commentRatio = (code.match(/\/\/|\/\*|\*\//g) || []).length / lines.length;
      score += Math.min(commentRatio * 2, 0.2);
      
      // Check for consistent naming
      const variableNames = code.match(/\b[a-zA-Z_$][a-zA-Z0-9_$]*\b/g) || [];
      const camelCaseNames = variableNames.filter(name => /^[a-z][a-zA-Z0-9]*$/.test(name));
      score += (camelCaseNames.length / variableNames.length) * 0.2;
      
      // Check for error handling
      if (code.includes('try') && code.includes('catch')) score += 0.1;
      if (code.includes('throw') || code.includes('return')) score += 0.1;
      
      // Check for modern JavaScript features (suspicious if too advanced)
      const modernFeatures = ['const ', 'let ', '=>', 'async', 'await', 'Promise', '...'];
      const modernFeatureCount = modernFeatures.filter(feature => code.includes(feature)).length;
      if (modernFeatureCount > 3) score += 0.1;
      
      return Math.min(score, 1);
      
    } catch (error) {
      console.error('Error calculating code perfectness score:', error);
      return 0;
    }
  }

  // EYE TRACKING SIMULATION (Placeholder for real implementation)
  setupEyeTrackingSimulation() {
    console.log('👁️ Setting up eye tracking simulation...');
    
    // Simulate eye tracking by monitoring mouse movement patterns
    const trackGaze = (e) => {
      if (!this.isSecurityActive()) return;
      
      const gazePoint = {
        x: e.clientX,
        y: e.clientY,
        timestamp: Date.now(),
        isOnScreen: true
      };
      
      this.eyeTracking.gazePoints.push(gazePoint);
      
      // Keep only last 100 gaze points
      if (this.eyeTracking.gazePoints.length > 100) {
        this.eyeTracking.gazePoints = this.eyeTracking.gazePoints.slice(-100);
      }
      
      // Analyze gaze patterns
      this.analyzeGazePatterns();
    };
    
    document.addEventListener('mousemove', trackGaze);
    
    // Simulate off-screen gaze detection
    document.addEventListener('mouseleave', () => {
      if (!this.isSecurityActive()) return;
      
      this.eyeTracking.offScreenGazes++;
      
      if (this.eyeTracking.offScreenGazes > 10) {
        this.reportBehavioralAnomaly('excessiveOffScreenGaze', 
          `Excessive off-screen gaze detected: ${this.eyeTracking.offScreenGazes} times`,
          { offScreenGazes: this.eyeTracking.offScreenGazes });
        
        this.eyeTracking.offScreenGazes = 0; // Reset after reporting
      }
    });
    
    console.log('✅ Eye tracking simulation initialized');
  }

  // Analyze gaze patterns (simplified simulation)
  analyzeGazePatterns() {
    try {
      if (this.eyeTracking.gazePoints.length < 10) return;
      
      const recentGazes = this.eyeTracking.gazePoints.slice(-10);
      
      // Calculate attention score based on gaze stability
      const gazeStability = this.calculateGazeStability(recentGazes);
      this.eyeTracking.attentionScore = Math.max(0, Math.min(100, gazeStability * 100));
      
      // Report low attention
      if (this.eyeTracking.attentionScore < 30) {
        this.reportBehavioralAnomaly('lowAttentionScore', 
          `Low attention score detected: ${this.eyeTracking.attentionScore.toFixed(2)}%`,
          { attentionScore: this.eyeTracking.attentionScore });
      }
      
    } catch (error) {
      console.error('Error analyzing gaze patterns:', error);
    }
  }

  // Calculate gaze stability (0-1)
  calculateGazeStability(gazePoints) {
    if (gazePoints.length < 2) return 1;
    
    try {
      let totalMovement = 0;
      for (let i = 1; i < gazePoints.length; i++) {
        const dx = gazePoints[i].x - gazePoints[i-1].x;
        const dy = gazePoints[i].y - gazePoints[i-1].y;
        totalMovement += Math.sqrt(dx * dx + dy * dy);
      }
      
      const avgMovement = totalMovement / (gazePoints.length - 1);
      return Math.max(0, 1 - (avgMovement / 100)); // Normalize to 0-1
      
    } catch (error) {
      console.error('Error calculating gaze stability:', error);
      return 0.5;
    }
  }

  // AUDIO ANALYSIS (Multiple Voice Detection)
  setupAudioAnalysis() {
    console.log('🎤 Setting up audio analysis...');
    
    // This would integrate with Web Audio API for real implementation
    // For now, it's a placeholder that could be enhanced with actual audio processing
    
    this.analyzeAudioStream = (audioStream) => {
      try {
        if (!audioStream) return;
        
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const analyser = audioContext.createAnalyser();
        const microphone = audioContext.createMediaStreamSource(audioStream);
        
        microphone.connect(analyser);
        analyser.fftSize = 256;
        
        const bufferLength = analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);
        
        const checkAudio = () => {
          if (!this.isSecurityActive()) return;
          
          analyser.getByteFrequencyData(dataArray);
          
          // Simple multiple voice detection (placeholder)
          const audioLevel = dataArray.reduce((sum, value) => sum + value, 0) / bufferLength;
          const frequencyVariance = this.calculateVariance(Array.from(dataArray));
          
          // High variance might indicate multiple voices
          if (frequencyVariance > 1000 && audioLevel > 30) {
            this.audioAnalysis.multipleVoiceDetections++;
            
            if (this.audioAnalysis.multipleVoiceDetections > 3) {
              this.reportBehavioralAnomaly('multipleVoiceDetection', 
                `Multiple voices detected: variance ${frequencyVariance.toFixed(2)}`,
                { frequencyVariance, audioLevel, detectionCount: this.audioAnalysis.multipleVoiceDetections });
              
              this.audioAnalysis.multipleVoiceDetections = 0; // Reset after reporting
            }
          }
          
          requestAnimationFrame(checkAudio);
        };
        
        checkAudio();
        
      } catch (error) {
        console.error('Error setting up audio analysis:', error);
      }
    };
    
    console.log('✅ Audio analysis setup complete');
  }

  // Utility function to check if element is in code editor
  isInCodeEditor(element) {
    try {
      return element && element.closest && element.closest('.monaco-editor') !== null;
    } catch (error) {
      return false;
    }
  }

  // Calculate variance for an array of numbers
  calculateVariance(numbers) {
    if (numbers.length === 0) return 0;
    
    const avg = numbers.reduce((a, b) => a + b, 0) / numbers.length;
    const squaredDiffs = numbers.map(n => Math.pow(n - avg, 2));
    return squaredDiffs.reduce((a, b) => a + b, 0) / numbers.length;
  }

  // Main setup function
  setupBehavioralAnalysis() {
    console.log('📊 Setting up behavioral analysis security...');
    
    const cleanupFunctions = [];
    
    try {
      // Initialize all behavioral analysis features
      cleanupFunctions.push(this.setupTypingPatternAnalysis());
      this.setupResponseTimeAnalysis();
      this.setupCodeAnalysis();
      this.setupEyeTrackingSimulation();
      this.setupAudioAnalysis();
      
      console.log('✅ Behavioral analysis security initialized');
      
    } catch (error) {
      console.error('Error setting up behavioral analysis:', error);
    }
    
    // Return cleanup function
    return () => {
      console.log('🧹 Cleaning up behavioral analysis...');
      cleanupFunctions.forEach(cleanup => {
        try {
          cleanup();
        } catch (error) {
          console.error('Error in behavioral analysis cleanup:', error);
        }
      });
    };
  }

  // Get comprehensive analytics report
  getAnalyticsReport() {
    return {
      typing: {
        baselineEstablished: this.typingAnalysis.baselineEstablished,
        baselineSpeed: this.typingAnalysis.baselineTypingSpeed,
        unusualPatterns: this.typingAnalysis.unusualPatterns,
        totalKeystrokes: this.typingAnalysis.keystrokes.length
      },
      responseTime: {
        averageResponseTime: this.responseTimeAnalysis.averageResponseTime,
        suspiciouslyFastResponses: this.responseTimeAnalysis.suspiciouslyFastResponses,
        totalResponses: this.responseTimeAnalysis.responseTimes.length
      },
      code: {
        totalSubmissions: this.codeAnalysis.codeSubmissions.length,
        rapidCodeAppearance: this.codeAnalysis.rapidCodeAppearance,
        suspiciouslyPerfectCode: this.codeAnalysis.suspiciouslyPerfectCode,
        avgComplexity: this.codeAnalysis.codeComplexityScores.length > 0 ? 
          this.codeAnalysis.codeComplexityScores.reduce((a, b) => a + b, 0) / this.codeAnalysis.codeComplexityScores.length : 0
      },
      eyeTracking: {
        attentionScore: this.eyeTracking.attentionScore,
        offScreenGazes: this.eyeTracking.offScreenGazes,
        totalGazePoints: this.eyeTracking.gazePoints.length
      },
      audio: {
        multipleVoiceDetections: this.audioAnalysis.multipleVoiceDetections,
        suspiciousAudioEvents: this.audioAnalysis.suspiciousAudioEvents
      }
    };
  }

  // Update options (useful for React state updates)
  updateOptions(newOptions) {
    Object.assign(this, newOptions);
  }

  // Cleanup method
  cleanup() {
    // Reset all analysis data
    this.typingAnalysis = {
      keystrokes: [],
      typingSpeed: [],
      pausePatterns: [],
      unusualPatterns: 0,
      baselineEstablished: false,
      baselineTypingSpeed: 0,
      baselinePausePattern: 0
    };
    
    this.responseTimeAnalysis = {
      questionStartTimes: new Map(),
      responseTimes: [],
      suspiciouslyFastResponses: 0,
      averageResponseTime: 0
    };
    
    this.codeAnalysis = {
      codeSubmissions: [],
      suspiciouslyPerfectCode: 0,
      rapidCodeAppearance: 0,
      codeComplexityScores: []
    };
    
    this.eyeTracking = {
      gazePoints: [],
      offScreenGazes: 0,
      focusPoints: [],
      attentionScore: 100
    };
    
    this.audioAnalysis = {
      voicePatterns: [],
      multipleVoiceDetections: 0,
      silencePeriods: [],
      suspiciousAudioEvents: 0
    };
  }
}

export default BehavioralAnalysisSecurity;
