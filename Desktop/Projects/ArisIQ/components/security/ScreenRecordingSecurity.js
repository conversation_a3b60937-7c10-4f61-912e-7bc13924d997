// File: components/security/ScreenRecordingSecurity.js
// Following same pattern as FullscreenSecurity and CopyPasteSecurity
// This class handles screen recording/sharing DETECTION, SecurityManager handles VIOLATION REPORTING

export class ScreenRecordingSecurity {
  constructor(options = {}) {
    this.exitingRef = options.exitingRef;
    this.securityManagerRef = options.securityManagerRef;
    this.trackPreSecurityViolation = options.trackPreSecurityViolation;
    this.currentQuestion = options.currentQuestion;
    
    // Detection state
    this.isSetupComplete = false;
    this.detectionEnabled = false;
    this.detectionStartTime = Date.now();
    this.activationDelay = 3000; // 3 seconds before detection starts
    
    // Store original methods for cleanup
    this.originalGetDisplayMedia = null;
    this.originalGetUserMedia = null;
    
    // Deduplication
    this.lastDetectionTime = 0;
    this.detectionCooldown = 5000; // 5 seconds between detections
    
    // Visual deterrents
    this.watermarkElement = null;
    this.watermarkInterval = null;
    
    console.log('🔒 ScreenRecordingSecurity initialized - following same pattern as other security classes');
    
    // Bind methods
    this.reportToSecurityManager = this.reportToSecurityManager.bind(this);
    this.setupScreenRecordingDetection = this.setupScreenRecordingDetection.bind(this);
    this.cleanup = this.cleanup.bind(this);
  }

  // Check if we should be active
  isSecurityActive() {
    // Don't run if exiting
    if (this.exitingRef && this.exitingRef.current) {
      return false;
    }
    
    // Don't run if setup not complete
    if (!this.isSetupComplete) {
      return false;
    }
    
    // Don't run if detection not enabled
    if (!this.detectionEnabled) {
      return false;
    }
    
    // Don't run if not enough time has passed
    if ((Date.now() - this.detectionStartTime) < this.activationDelay) {
      return false;
    }
    
    return true;
  }

  // Basic deduplication check
  shouldSkipDetection() {
    var timeSinceLastDetection = Date.now() - this.lastDetectionTime;
    return timeSinceLastDetection < this.detectionCooldown;
  }

  // Override getDisplayMedia to detect and block screen sharing
  overrideGetDisplayMedia() {
    if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
      // Store original method
      this.originalGetDisplayMedia = navigator.mediaDevices.getDisplayMedia.bind(navigator.mediaDevices);
      
      // Override with detection
      var self = this;
      navigator.mediaDevices.getDisplayMedia = function(constraints) {
        console.log('🚨 DETECTION: Screen sharing attempt detected via getDisplayMedia');
        
        // Report to SecurityManager (same pattern as other security classes)
        if (self.isSecurityActive() && !self.shouldSkipDetection()) {
          self.lastDetectionTime = Date.now();
          self.reportToSecurityManager('screenSharing', 'Screen sharing attempt detected (getDisplayMedia API)');
        }
        
        // Block the request completely
        return Promise.reject(new DOMException('Screen sharing is not allowed during this interview', 'NotAllowedError'));
      };
      
      console.log('✅ getDisplayMedia override installed');
    }
  }

  // Override getUserMedia to detect screen recording attempts
  overrideGetUserMedia() {
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      // Store original method
      this.originalGetUserMedia = navigator.mediaDevices.getUserMedia.bind(navigator.mediaDevices);
      
      // Override with detection for screen recording
      var self = this;
      navigator.mediaDevices.getUserMedia = function(constraints) {
        // Check if constraints include screen recording
        if (constraints && 
            ((constraints.video && (constraints.video.mediaSource === 'screen' || 
                                   constraints.video.chromeMediaSource === 'screen' ||
                                   constraints.video.chromeMediaSource === 'desktop')) ||
             (constraints.audio && (constraints.audio.mediaSource === 'screen' ||
                                   constraints.audio.chromeMediaSource === 'desktop')))) {
          
          console.log('🚨 DETECTION: Screen recording attempt detected via getUserMedia');
          
          // Report to SecurityManager
          if (self.isSecurityActive() && !self.shouldSkipDetection()) {
            self.lastDetectionTime = Date.now();
            self.reportToSecurityManager('screenRecording', 'Screen recording attempt detected (getUserMedia API)');
          }
          
          // Block the request
          return Promise.reject(new DOMException('Screen recording is not allowed during this interview', 'NotAllowedError'));
        }
        
        // For normal camera/microphone requests, use original method
        return self.originalGetUserMedia.call(this, constraints);
      };
      
      console.log('✅ getUserMedia override installed');
    }
  }

  // Monitor permissions for screen capture
  monitorPermissions() {
    var self = this;
    
    // Check permissions periodically
    var permissionCheckInterval = setInterval(function() {
      if (!self.isSecurityActive()) {
        return;
      }
      
      // Check for display-capture permission (if supported)
      if (navigator.permissions && navigator.permissions.query) {
        navigator.permissions.query({name: 'display-capture'}).then(function(permission) {
          if (permission.state === 'granted') {
            console.log('🚨 DETECTION: Screen capture permission granted');
            
            if (!self.shouldSkipDetection()) {
              self.lastDetectionTime = Date.now();
              self.reportToSecurityManager('screenSharing', 'Screen capture permission detected');
            }
          }
        }).catch(function(error) {
          // Permission query not supported or failed
          console.log('Display-capture permission query not supported');
        });
      }
    }, 3000); // Check every 3 seconds
    
    // Store interval for cleanup
    this.permissionCheckInterval = permissionCheckInterval;
  }

  // Create moving watermarks to deter recording
  createVisualDeterrents() {
    try {
      // Create watermark element
      this.watermarkElement = document.createElement('div');
      this.watermarkElement.id = 'interview-watermark';
      this.watermarkElement.style.cssText = `
        position: fixed !important;
        top: 20% !important;
        left: 20% !important;
        width: 200px !important;
        height: 50px !important;
        background: rgba(255, 0, 0, 0.1) !important;
        color: rgba(255, 0, 0, 0.3) !important;
        font-size: 12px !important;
        font-weight: bold !important;
        text-align: center !important;
        line-height: 50px !important;
        z-index: 9999 !important;
        pointer-events: none !important;
        user-select: none !important;
        transform: rotate(-15deg) !important;
        font-family: Arial, sans-serif !important;
        border: 1px solid rgba(255, 0, 0, 0.2) !important;
        border-radius: 5px !important;
        transition: all 0.5s ease !important;
      `;
      
      this.watermarkElement.textContent = 'PROCTORED INTERVIEW';
      document.body.appendChild(this.watermarkElement);
      
      // Move watermark around every few seconds
      var positions = [
        {top: '20%', left: '20%'},
        {top: '20%', right: '20%', left: 'auto'},
        {bottom: '20%', right: '20%', top: 'auto', left: 'auto'},
        {bottom: '20%', left: '20%', top: 'auto', right: 'auto'},
        {top: '50%', left: '50%', right: 'auto', bottom: 'auto'}
      ];
      
      var currentPosition = 0;
      var self = this;
      
      this.watermarkInterval = setInterval(function() {
        if (!self.watermarkElement) return;
        
        var pos = positions[currentPosition];
        
        // Reset all position properties
        self.watermarkElement.style.top = 'auto';
        self.watermarkElement.style.bottom = 'auto';
        self.watermarkElement.style.left = 'auto';
        self.watermarkElement.style.right = 'auto';
        
        // Apply new position
        Object.keys(pos).forEach(function(key) {
          self.watermarkElement.style[key] = pos[key];
        });
        
        currentPosition = (currentPosition + 1) % positions.length;
      }, 8000); // Move every 8 seconds
      
      console.log('✅ Visual deterrents (watermarks) created');
    } catch (error) {
      console.error('❌ Error creating visual deterrents:', error);
    }
  }

  // Check for existing screen sharing
  checkExistingScreenSharing() {
    var self = this;
    
    // Try to detect if screen sharing is already active
    if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
      navigator.mediaDevices.enumerateDevices().then(function(devices) {
        devices.forEach(function(device) {
          // Look for screen capture devices
          if (device.kind === 'videoinput' && 
              (device.label.toLowerCase().includes('screen') || 
               device.label.toLowerCase().includes('display'))) {
            
            console.log('🚨 DETECTION: Screen capture device detected');
            
            if (self.isSecurityActive() && !self.shouldSkipDetection()) {
              self.lastDetectionTime = Date.now();
              self.reportToSecurityManager('screenSharing', 'Screen capture device detected');
            }
          }
        });
      }).catch(function(error) {
        console.log('Could not enumerate devices:', error);
      });
    }
  }

  // Report to SecurityManager (following same pattern as other security classes)
  reportToSecurityManager(violationType, message) {
    console.log('🚨 REPORTING TO SECURITY MANAGER: ' + violationType + ' - ' + message);
    
    try {
      if (this.securityManagerRef && this.securityManagerRef.current && this.securityManagerRef.current.isInterviewActive) {
        // Same pattern as other security classes - try different violation handler methods
        if (typeof this.securityManagerRef.current.handleWarningViolation === 'function') {
          this.securityManagerRef.current.handleWarningViolation(violationType, message);
          console.log('✅ Violation reported to SecurityManager via handleWarningViolation');
        } else if (typeof this.securityManagerRef.current.handleViolation === 'function') {
          this.securityManagerRef.current.handleViolation(violationType, message);
          console.log('✅ Violation reported to SecurityManager via handleViolation');
        } else {
          console.log('❌ No violation handler found in SecurityManager');
        }
      } else if (this.trackPreSecurityViolation) {
        // Same pattern as other security classes - track violation even if SecurityManager isn't active yet
        this.trackPreSecurityViolation(violationType, message + ' (Question ' + ((this.currentQuestion || 0) + 1) + ')');
        console.log('✅ Violation tracked via trackPreSecurityViolation');
      } else {
        console.log('❌ No SecurityManager or tracking function available');
      }
    } catch (error) {
      console.error('❌ Error reporting violation to SecurityManager:', error);
    }
  }

  // Setup screen recording detection (following same pattern as other security classes)
  setupScreenRecordingDetection() {
    console.log('🔒 Setting up Screen Recording Detection - following same pattern as other security classes...');
    
    // Mark setup as starting
    this.isSetupComplete = false;
    
    try {
      // 1. Override API methods to block and detect
      this.overrideGetDisplayMedia();
      this.overrideGetUserMedia();
      
      // 2. Monitor permissions
      this.monitorPermissions();
      
      // 3. Create visual deterrents
      this.createVisualDeterrents();
      
      // 4. Check for existing screen sharing
      this.checkExistingScreenSharing();
      
      // Mark setup as complete
      this.isSetupComplete = true;
      console.log('✅ Screen recording detection setup complete');
      
      // Enable detection after delay (same pattern as other security classes)
      var self = this;
      setTimeout(function() {
        self.detectionEnabled = true;
        console.log('✅ Screen Recording Detection ENABLED - will report to SecurityManager');
      }, this.activationDelay);
      
      console.log('✅ Screen Recording Detection setup complete - following same pattern as other security classes');
      
      // Return cleanup function (same pattern as other security classes)
      return function() {
        self.cleanup();
      };
    } catch (error) {
      console.error('❌ Error setting up screen recording detection:', error);
      return function() {}; // Return empty cleanup function
    }
  }

  // Update options (same pattern as other security classes)
  updateOptions(newOptions) {
    if (newOptions) {
      for (var key in newOptions) {
        if (newOptions.hasOwnProperty(key)) {
          this[key] = newOptions[key];
        }
      }
    }
    console.log('🔒 Screen recording options updated:', newOptions);
  }

  // Get current status (same pattern as other security classes)
  getStatus() {
    return {
      isActive: this.isSecurityActive(),
      isSetupComplete: this.isSetupComplete,
      detectionEnabled: this.detectionEnabled,
      timeSinceSetup: Date.now() - this.detectionStartTime,
      activationDelay: this.activationDelay,
      lastDetectionTime: this.lastDetectionTime,
      hasWatermark: !!this.watermarkElement
    };
  }

  // Cleanup method (same pattern as other security classes)
  cleanup() {
    console.log('🧹 Screen recording security cleanup initiated');
    
    try {
      // Disable detection
      this.detectionEnabled = false;
      this.isSetupComplete = false;
      
      // Restore original API methods
      if (this.originalGetDisplayMedia && navigator.mediaDevices) {
        navigator.mediaDevices.getDisplayMedia = this.originalGetDisplayMedia;
        this.originalGetDisplayMedia = null;
        console.log('✅ getDisplayMedia restored');
      }
      
      if (this.originalGetUserMedia && navigator.mediaDevices) {
        navigator.mediaDevices.getUserMedia = this.originalGetUserMedia;
        this.originalGetUserMedia = null;
        console.log('✅ getUserMedia restored');
      }
      
      // Clear permission monitoring
      if (this.permissionCheckInterval) {
        clearInterval(this.permissionCheckInterval);
        this.permissionCheckInterval = null;
      }
      
      // Remove visual deterrents
      if (this.watermarkInterval) {
        clearInterval(this.watermarkInterval);
        this.watermarkInterval = null;
      }
      
      if (this.watermarkElement && this.watermarkElement.parentNode) {
        this.watermarkElement.parentNode.removeChild(this.watermarkElement);
        this.watermarkElement = null;
      }
      
      // Reset detection state
      this.lastDetectionTime = 0;
      
      console.log('✅ Screen recording security cleanup completed');
    } catch (error) {
      console.error('❌ Error during screen recording cleanup:', error);
    }
  }
}

export default ScreenRecordingSecurity;
