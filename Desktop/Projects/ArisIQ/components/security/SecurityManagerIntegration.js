// File: components/security/SecurityManagerIntegration.js
// Main security integration that combines all security modules

import FullscreenSecurity from './FullscreenSecurity';
import TabSwitchSecurity from './TabSwitchSecurity';
import CopyPasteSecurity from './CopyPasteSecurity';
import ScreenshotSecurity from './ScreenshotSecurity';
import DeviceDetectionSecurity from './DeviceDetectionSecurity';
import KeyboardSecurity from './KeyboardSecurity';
import AdvancedMonitoringSecurity from './AdvancedMonitoringSecurity';
import BehavioralAnalysisSecurity from './BehavioralAnalysisSecurity';
import DeepfakeDetectionSecurity from './DeepfakeDetectionSecurity';
import EnhancedSecurityFeatures from './EnhancedSecurityFeatures';

export class SecurityManagerIntegration {
  constructor(options = {}) {
    // Core options
    this.exitingRef = options.exitingRef;
    this.securityManagerRef = options.securityManagerRef;
    this.trackPreSecurityViolation = options.trackPreSecurityViolation;
    this.currentQuestion = options.currentQuestion;
    this.showRequiredAcknowledgmentWarning = options.showRequiredAcknowledgmentWarning;
    this.showCopyPasteWarning = options.showCopyPasteWarning;
    this.setIsFullScreen = options.setIsFullScreen;
    this.questionsLength = options.questionsLength || 0;
    
    // FIXED: Track cleanup functions properly to prevent infinite loops
    this.cleanupFunctions = [];
    this.isCleaningUp = false; // Prevent recursive cleanup
    this.isInitialized = false; // Prevent multiple initializations
    
    // Initialize all security modules with safe error handling
    this.initializeSecurityModules();
    
    // Bind methods
    this.initializeAllSecurity = this.initializeAllSecurity.bind(this);
    this.updateAllOptions = this.updateAllOptions.bind(this);
    this.handleFaceDetection = this.handleFaceDetection.bind(this);
    this.cleanup = this.cleanup.bind(this);
  }

  // FIXED: Safe initialization of security modules
  initializeSecurityModules() {
    try {
      this.fullscreenSecurity = new FullscreenSecurity({
        exitingRef: this.exitingRef,
        securityManagerRef: this.securityManagerRef,
        trackPreSecurityViolation: this.trackPreSecurityViolation,
        currentQuestion: this.currentQuestion,
        showRequiredAcknowledgmentWarning: this.showRequiredAcknowledgmentWarning,
        setIsFullScreen: this.setIsFullScreen,
        questionsLength: this.questionsLength
      });
      
      this.tabSwitchSecurity = new TabSwitchSecurity({
        exitingRef: this.exitingRef,
        securityManagerRef: this.securityManagerRef,
        trackPreSecurityViolation: this.trackPreSecurityViolation,
        currentQuestion: this.currentQuestion,
        showRequiredAcknowledgmentWarning: this.showRequiredAcknowledgmentWarning
      });
      
      this.copyPasteSecurity = new CopyPasteSecurity({
        exitingRef: this.exitingRef,
        securityManagerRef: this.securityManagerRef,
        trackPreSecurityViolation: this.trackPreSecurityViolation,
        currentQuestion: this.currentQuestion,
        showCopyPasteWarning: this.showCopyPasteWarning
      });
      
      this.screenshotSecurity = new ScreenshotSecurity({
        exitingRef: this.exitingRef,
        securityManagerRef: this.securityManagerRef,
        trackPreSecurityViolation: this.trackPreSecurityViolation,
        currentQuestion: this.currentQuestion,
        showRequiredAcknowledgmentWarning: this.showRequiredAcknowledgmentWarning
      });
      
      this.deviceDetectionSecurity = new DeviceDetectionSecurity({
        exitingRef: this.exitingRef,
        securityManagerRef: this.securityManagerRef,
        trackPreSecurityViolation: this.trackPreSecurityViolation,
        currentQuestion: this.currentQuestion,
        showRequiredAcknowledgmentWarning: this.showRequiredAcknowledgmentWarning
      });
      
      this.keyboardSecurity = new KeyboardSecurity({
        exitingRef: this.exitingRef,
        securityManagerRef: this.securityManagerRef,
        trackPreSecurityViolation: this.trackPreSecurityViolation,
        currentQuestion: this.currentQuestion,
        showRequiredAcknowledgmentWarning: this.showRequiredAcknowledgmentWarning
      });
      
      this.advancedMonitoringSecurity = new AdvancedMonitoringSecurity({
        exitingRef: this.exitingRef,
        securityManagerRef: this.securityManagerRef,
        trackPreSecurityViolation: this.trackPreSecurityViolation,
        currentQuestion: this.currentQuestion,
        showRequiredAcknowledgmentWarning: this.showRequiredAcknowledgmentWarning
      });
      
      this.behavioralAnalysisSecurity = new BehavioralAnalysisSecurity({
        exitingRef: this.exitingRef,
        securityManagerRef: this.securityManagerRef,
        trackPreSecurityViolation: this.trackPreSecurityViolation,
        currentQuestion: this.currentQuestion,
        showRequiredAcknowledgmentWarning: this.showRequiredAcknowledgmentWarning
      });
      
      this.deepfakeDetectionSecurity = new DeepfakeDetectionSecurity({
        exitingRef: this.exitingRef,
        securityManagerRef: this.securityManagerRef,
        trackPreSecurityViolation: this.trackPreSecurityViolation,
        currentQuestion: this.currentQuestion,
        showRequiredAcknowledgmentWarning: this.showRequiredAcknowledgmentWarning
      });
      
      // NEW: Enhanced security features from security-manager.js
      this.enhancedSecurityFeatures = new EnhancedSecurityFeatures({
        exitingRef: this.exitingRef,
        securityManagerRef: this.securityManagerRef,
        trackPreSecurityViolation: this.trackPreSecurityViolation,
        currentQuestion: this.currentQuestion,
        showRequiredAcknowledgmentWarning: this.showRequiredAcknowledgmentWarning
      });
      
      console.log('✅ Security modules created successfully');
    } catch (error) {
      console.error('❌ Error creating security modules:', error);
      // Create dummy modules to prevent errors
      this.createDummyModules();
    }
  }

  // FIXED: Create dummy modules if real ones fail to prevent errors
  createDummyModules() {
    console.log('🔧 Creating dummy security modules as fallback...');
    
    const dummyModule = {
      setupFullscreenDetection: () => () => {},
      setupTabSwitchDetection: () => () => {},
      setupCopyPasteMonitoring: () => () => {},
      setupScreenshotDetection: () => () => {},
      setupDeviceDetection: () => () => {},
      setupKeyboardSecurity: () => () => {},
      setupAdvancedMonitoring: () => () => {},
      setupBehavioralAnalysis: () => () => {},
      setupDeepfakeDetection: () => () => {},
      updateOptions: () => {},
      cleanup: () => {},
      isDocumentFullScreen: () => false,
      requestFullscreen: () => Promise.resolve(false),
      exitFullscreen: () => Promise.resolve(false),
      enforceFullscreenModeSimple: () => {},
      reportScreenshotViolation: () => {},
      startPotentialScreenshotTracking: () => {},
      clearPotentialScreenshotAttempt: () => {},
      checkClipboardForImages: () => Promise.resolve(false),
      getDetectionStats: () => ({}),
      resetDetectionState: () => {},
      getCurrentlyPressedKeys: () => [],
      clearPressedKeys: () => {},
      getMonitoringStats: () => ({}),
      getAnalyticsReport: () => ({}),
      getDeepfakeReport: () => ({}),
      triggerRandomVerification: () => {},
      handleFaceDetection: () => {},
      reportedScreenshots: 0
    };
    
    this.fullscreenSecurity = { ...dummyModule };
    this.tabSwitchSecurity = { ...dummyModule };
    this.copyPasteSecurity = { ...dummyModule };
    this.screenshotSecurity = { ...dummyModule };
    this.deviceDetectionSecurity = { ...dummyModule };
    this.keyboardSecurity = { ...dummyModule };
    this.advancedMonitoringSecurity = { ...dummyModule };
    this.behavioralAnalysisSecurity = { ...dummyModule };
    this.deepfakeDetectionSecurity = { ...dummyModule };
  }

  // FIXED: Initialize all security modules with proper error handling
  initializeAllSecurity() {
    if (this.isInitialized) {
      console.log('⚠️ Security already initialized, skipping...');
      return () => {};
    }

    console.log('🛡️ Initializing comprehensive security system...');
    
    try {
      this.isInitialized = true;
      
      // Initialize each module with error handling
      const initializeModule = (moduleName, module, setupMethod) => {
        try {
          console.log(`🔧 Initializing ${moduleName}...`);
          if (module && typeof module[setupMethod] === 'function') {
            const cleanup = module[setupMethod]();
            if (typeof cleanup === 'function') {
              this.cleanupFunctions.push({
                name: moduleName,
                cleanup: cleanup
              });
            }
          } else {
            console.warn(`⚠️ ${moduleName} module or ${setupMethod} method not available`);
          }
        } catch (error) {
          console.error(`❌ Error initializing ${moduleName}:`, error);
        }
      };
      
      // Initialize all modules
      initializeModule('fullscreen', this.fullscreenSecurity, 'setupFullscreenDetection');
      initializeModule('tabSwitch', this.tabSwitchSecurity, 'setupTabSwitchDetection');
      initializeModule('copyPaste', this.copyPasteSecurity, 'setupCopyPasteMonitoring');
      initializeModule('screenshot', this.screenshotSecurity, 'setupScreenshotDetection');
      initializeModule('deviceDetection', this.deviceDetectionSecurity, 'setupDeviceDetection');
      initializeModule('keyboard', this.keyboardSecurity, 'setupKeyboardSecurity');
      initializeModule('advancedMonitoring', this.advancedMonitoringSecurity, 'setupAdvancedMonitoring');
      initializeModule('behavioralAnalysis', this.behavioralAnalysisSecurity, 'setupBehavioralAnalysis');
      initializeModule('deepfakeDetection', this.deepfakeDetectionSecurity, 'setupDeepfakeDetection');
      
      console.log(`✅ Security initialized with ${this.cleanupFunctions.length} cleanup functions`);
      
    } catch (error) {
      console.error('❌ Error initializing security modules:', error);
    }
    
    // Return master cleanup function
    return () => {
      this.cleanup();
    };
  }

  // FIXED: Safe update options for all security modules
  updateAllOptions(newOptions) {
    console.log('🔄 Updating options for all security modules...');
    
    try {
      // Update core options
      Object.assign(this, newOptions);
      
      // Helper function to safely update module options
      const updateModuleOptions = (moduleName, module, options) => {
        try {
          if (module && typeof module.updateOptions === 'function') {
            module.updateOptions(options);
          }
        } catch (error) {
          console.error(`❌ Error updating ${moduleName} options:`, error);
        }
      };
      
      const commonOptions = {
        exitingRef: this.exitingRef,
        securityManagerRef: this.securityManagerRef,
        trackPreSecurityViolation: this.trackPreSecurityViolation,
        currentQuestion: this.currentQuestion,
        showRequiredAcknowledgmentWarning: this.showRequiredAcknowledgmentWarning
      };
      
      // Update each security module with common options plus specific ones
      updateModuleOptions('fullscreen', this.fullscreenSecurity, {
        ...commonOptions,
        setIsFullScreen: this.setIsFullScreen,
        questionsLength: this.questionsLength
      });
      
      updateModuleOptions('tabSwitch', this.tabSwitchSecurity, commonOptions);
      
      updateModuleOptions('copyPaste', this.copyPasteSecurity, {
        ...commonOptions,
        showCopyPasteWarning: this.showCopyPasteWarning
      });
      
      updateModuleOptions('screenshot', this.screenshotSecurity, commonOptions);
      updateModuleOptions('deviceDetection', this.deviceDetectionSecurity, commonOptions);
      updateModuleOptions('keyboard', this.keyboardSecurity, commonOptions);
      updateModuleOptions('advancedMonitoring', this.advancedMonitoringSecurity, commonOptions);
      updateModuleOptions('behavioralAnalysis', this.behavioralAnalysisSecurity, commonOptions);
      updateModuleOptions('deepfakeDetection', this.deepfakeDetectionSecurity, commonOptions);
      
      console.log('✅ All security modules updated successfully');
      
    } catch (error) {
      console.error('❌ Error updating security modules:', error);
    }
  }

  // Handle face detection data (delegate to device detection and deepfake detection)
  handleFaceDetection(faceData) {
    try {
      // Pass face detection data to device detection security
      if (this.deviceDetectionSecurity && typeof this.deviceDetectionSecurity.handleFaceDetection === 'function') {
        this.deviceDetectionSecurity.handleFaceDetection(faceData);
      }
      
      // Pass face detection data to deepfake detection security
      if (this.deepfakeDetectionSecurity && 
          typeof this.deepfakeDetectionSecurity.analyzeFrameForDeepfake === 'function' && 
          faceData.videoElement) {
        this.deepfakeDetectionSecurity.analyzeFrameForDeepfake(faceData.videoElement, faceData);
      }
      
      // NEW: Pass face detection data to enhanced security features
      if (this.enhancedSecurityFeatures && 
          typeof this.enhancedSecurityFeatures.handleFaceDetectionWithDevices === 'function') {
        this.enhancedSecurityFeatures.handleFaceDetectionWithDevices(faceData);
      }
    } catch (error) {
      console.error('Error handling face detection in security integration:', error);
    }
  }

  // Handle behavioral analysis events
  handleBehavioralAnalysis(data) {
    try {
      if (!this.behavioralAnalysisSecurity) return;
      
      const { type, ...analysisData } = data;
      
      switch (type) {
        case 'responseTime':
          if (typeof this.behavioralAnalysisSecurity.analyzeResponseTime === 'function') {
            this.behavioralAnalysisSecurity.analyzeResponseTime(
              analysisData.questionIndex, 
              analysisData.questionType, 
              analysisData.responseTime
            );
          }
          break;
          
        case 'codeSubmission':
          if (typeof this.behavioralAnalysisSecurity.analyzeCode === 'function') {
            this.behavioralAnalysisSecurity.analyzeCode(
              analysisData.code,
              analysisData.submissionTime,
              analysisData.questionIndex
            );
          }
          break;
          
        case 'questionStart':
          if (typeof this.behavioralAnalysisSecurity.startQuestionTimer === 'function') {
            this.behavioralAnalysisSecurity.startQuestionTimer(analysisData.questionIndex);
          }
          break;
          
        case 'questionEnd':
          if (typeof this.behavioralAnalysisSecurity.endQuestionTimer === 'function') {
            this.behavioralAnalysisSecurity.endQuestionTimer(
              analysisData.questionIndex,
              analysisData.questionType
            );
          }
          break;
          
        case 'audioStream':
          if (typeof this.behavioralAnalysisSecurity.analyzeAudioStream === 'function') {
            this.behavioralAnalysisSecurity.analyzeAudioStream(analysisData.audioStream);
          }
          break;
          
        default:
          console.log('Unknown behavioral analysis type:', type);
      }
    } catch (error) {
      console.error('Error handling behavioral analysis:', error);
    }
  }

  // FIXED: Safe utility methods with error handling
  
  // Fullscreen utilities
  isDocumentFullScreen() {
    try {
      return this.fullscreenSecurity?.isDocumentFullScreen?.() || false;
    } catch (error) {
      console.error('Error checking fullscreen status:', error);
      return false;
    }
  }
  
  async requestFullscreen() {
    try {
      return await this.fullscreenSecurity?.requestFullscreen?.() || false;
    } catch (error) {
      console.error('Error requesting fullscreen:', error);
      return false;
    }
  }
  
  async exitFullscreen() {
    try {
      return await this.fullscreenSecurity?.exitFullscreen?.() || false;
    } catch (error) {
      console.error('Error exiting fullscreen:', error);
      return false;
    }
  }
  
  // Advanced fullscreen enforcement (from enhanced features)
  enforceFullscreenModeAdvanced() {
    try {
      return this.enhancedSecurityFeatures?.enforceFullscreenModeAdvanced?.();
    } catch (error) {
      console.error('Error enforcing advanced fullscreen:', error);
    }
  }
  
  // Enhanced face detection with devices
  handleFaceDetectionWithDevices(faceData) {
    try {
      return this.enhancedSecurityFeatures?.handleFaceDetectionWithDevices?.(faceData);
    } catch (error) {
      console.error('Error handling enhanced face detection:', error);
    }
  }
  
  // Enhanced clipboard checking
  async checkClipboardForImagesAdvanced() {
    try {
      return await this.enhancedSecurityFeatures?.checkClipboardForImages?.() || false;
    } catch (error) {
      console.error('Error checking clipboard (enhanced):', error);
      return false;
    }
  }
  
  // Screenshot utilities
  reportScreenshotViolation(method, details) {
    try {
      return this.screenshotSecurity?.reportScreenshotViolation?.(method, details);
    } catch (error) {
      console.error('Error reporting screenshot violation:', error);
    }
  }
  
  startPotentialScreenshotTracking(method) {
    try {
      return this.screenshotSecurity?.startPotentialScreenshotTracking?.(method);
    } catch (error) {
      console.error('Error starting screenshot tracking:', error);
    }
  }
  
  clearPotentialScreenshotAttempt() {
    try {
      return this.screenshotSecurity?.clearPotentialScreenshotAttempt?.();
    } catch (error) {
      console.error('Error clearing screenshot attempt:', error);
    }
  }
  
  async checkClipboardForImages() {
    try {
      return await this.screenshotSecurity?.checkClipboardForImages?.() || false;
    } catch (error) {
      console.error('Error checking clipboard:', error);
      return false;
    }
  }
  
  // Device detection utilities
  getDeviceDetectionStats() {
    try {
      return this.deviceDetectionSecurity?.getDetectionStats?.() || {};
    } catch (error) {
      console.error('Error getting device stats:', error);
      return {};
    }
  }
  
  resetDeviceDetectionState() {
    try {
      return this.deviceDetectionSecurity?.resetDetectionState?.();
    } catch (error) {
      console.error('Error resetting device state:', error);
    }
  }
  
  // Keyboard utilities
  getCurrentlyPressedKeys() {
    try {
      return this.keyboardSecurity?.getCurrentlyPressedKeys?.() || [];
    } catch (error) {
      console.error('Error getting pressed keys:', error);
      return [];
    }
  }
  
  clearPressedKeys() {
    try {
      return this.keyboardSecurity?.clearPressedKeys?.();
    } catch (error) {
      console.error('Error clearing pressed keys:', error);
    }
  }

  // Advanced monitoring utilities
  getMonitoringStats() {
    try {
      return this.advancedMonitoringSecurity?.getMonitoringStats?.() || {};
    } catch (error) {
      console.error('Error getting monitoring stats:', error);
      return {};
    }
  }
  
  // Behavioral analysis utilities
  getAnalyticsReport() {
    try {
      return this.behavioralAnalysisSecurity?.getAnalyticsReport?.() || {};
    } catch (error) {
      console.error('Error getting analytics report:', error);
      return {};
    }
  }
  
  // Deepfake detection utilities
  getDeepfakeReport() {
    try {
      return this.deepfakeDetectionSecurity?.getDeepfakeReport?.() || {};
    } catch (error) {
      console.error('Error getting deepfake report:', error);
      return {};
    }
  }
  
  triggerRandomVerification() {
    try {
      return this.deepfakeDetectionSecurity?.triggerRandomVerification?.();
    } catch (error) {
      console.error('Error triggering verification:', error);
    }
  }

  // Get comprehensive security status
  getSecurityStatus() {
    try {
      return {
        fullscreen: {
          isFullscreen: this.isDocumentFullScreen(),
          module: 'active'
        },
        tabSwitch: {
          module: 'active'
        },
        copyPaste: {
          module: 'active'
        },
        screenshot: {
          reportedScreenshots: this.screenshotSecurity?.reportedScreenshots || 0,
          module: 'active'
        },
        deviceDetection: {
          stats: this.getDeviceDetectionStats(),
          module: 'active'
        },
        keyboard: {
          pressedKeys: this.getCurrentlyPressedKeys(),
          module: 'active'
        },
        advancedMonitoring: {
          stats: this.getMonitoringStats(),
          module: 'active'
        },
        behavioralAnalysis: {
          report: this.getAnalyticsReport(),
          module: 'active'
        },
        deepfakeDetection: {
          report: this.getDeepfakeReport(),
          module: 'active'
        },
        overall: 'fully_active'
      };
    } catch (error) {
      console.error('Error getting security status:', error);
      return { overall: 'error' };
    }
  }

  // Get all security modules (for advanced usage)
  getSecurityModules() {
    return {
      fullscreen: this.fullscreenSecurity,
      tabSwitch: this.tabSwitchSecurity,
      copyPaste: this.copyPasteSecurity,
      screenshot: this.screenshotSecurity,
      deviceDetection: this.deviceDetectionSecurity,
      keyboard: this.keyboardSecurity,
      advancedMonitoring: this.advancedMonitoringSecurity,
      behavioralAnalysis: this.behavioralAnalysisSecurity,
      deepfakeDetection: this.deepfakeDetectionSecurity
    };
  }

  // Setup event handlers for cross-module communication
  setupCrossModuleEvents() {
    console.log('🔗 Setting up cross-module event communication...');
    
    try {
      // Handle screenshot detection from any source
      const handleScreenshotEvent = (event) => {
        console.log('📸 Screenshot event received by integration:', event.detail);
      };
      
      window.addEventListener('security-screenshot-attempt', handleScreenshotEvent, { capture: true });
      this.cleanupFunctions.push({
        name: 'screenshot-event-listener',
        cleanup: () => {
          window.removeEventListener('security-screenshot-attempt', handleScreenshotEvent, { capture: true });
        }
      });
      
      // Handle device detection events
      const handleDeviceEvent = (event) => {
        console.log('📱 Device event received by integration:', event.detail);
      };
      
      window.addEventListener('security-device-violation', handleDeviceEvent, { capture: true });
      this.cleanupFunctions.push({
        name: 'device-event-listener',
        cleanup: () => {
          window.removeEventListener('security-device-violation', handleDeviceEvent, { capture: true });
        }
      });
      
      // Handle tab switch events
      const handleTabSwitchEvent = (event) => {
        console.log('🔄 Tab switch event received by integration:', event.detail);
      };
      
      window.addEventListener('security-tab-switch', handleTabSwitchEvent, { capture: true });
      this.cleanupFunctions.push({
        name: 'tab-switch-event-listener',
        cleanup: () => {
          window.removeEventListener('security-tab-switch', handleTabSwitchEvent, { capture: true });
        }
      });
      
      console.log('✅ Cross-module events setup complete');
      
    } catch (error) {
      console.error('❌ Error setting up cross-module events:', error);
    }
  }

  // Initialize everything
  initialize() {
    console.log('🚀 Initializing Security Manager Integration...');
    
    try {
      // Setup cross-module communication first
      this.setupCrossModuleEvents();
      
      // Initialize all security modules
      const masterCleanup = this.initializeAllSecurity();
      
      // Add master cleanup to the list
      if (typeof masterCleanup === 'function') {
        this.cleanupFunctions.push({
          name: 'master-cleanup',
          cleanup: masterCleanup
        });
      }
      
      console.log('✅ Security Manager Integration fully initialized');
      
      // Return the master cleanup function
      return () => {
        this.cleanup();
      };
      
    } catch (error) {
      console.error('❌ Error initializing Security Manager Integration:', error);
      return () => {};
    }
  }

  // FIXED: Cleanup all security modules with stack overflow prevention
  cleanup() {
    // Prevent recursive cleanup calls
    if (this.isCleaningUp) {
      console.log('🔄 Cleanup already in progress, skipping...');
      return;
    }
    
    this.isCleaningUp = true;
    console.log('🧹 Cleaning up all security modules...');
    
    try {
      // Run all cleanup functions
      const cleanupCount = this.cleanupFunctions.length;
      console.log(`🧹 Running ${cleanupCount} cleanup functions...`);
      
      // FIXED: Create a copy of cleanup functions and clear original to prevent infinite loops
      const cleanupsCopy = [...this.cleanupFunctions];
      this.cleanupFunctions = []; // Clear immediately to prevent re-entry
      
      cleanupsCopy.forEach((cleanupObj, index) => {
        try {
          const name = cleanupObj.name || `function-${index + 1}`;
          console.log(`🧹 Running cleanup: ${name} (${index + 1}/${cleanupCount})`);
          
          if (typeof cleanupObj.cleanup === 'function') {
            cleanupObj.cleanup();
          } else if (typeof cleanupObj === 'function') {
            cleanupObj(); // Handle direct function references
          }
        } catch (error) {
          console.error(`❌ Error in cleanup ${cleanupObj.name || index + 1}:`, error);
        }
      });
      
      // Cleanup individual modules safely
      const modules = [
        { name: 'fullscreen', module: this.fullscreenSecurity },
        { name: 'tabSwitch', module: this.tabSwitchSecurity },
        { name: 'copyPaste', module: this.copyPasteSecurity },
        { name: 'screenshot', module: this.screenshotSecurity },
        { name: 'deviceDetection', module: this.deviceDetectionSecurity },
        { name: 'keyboard', module: this.keyboardSecurity },
        { name: 'advancedMonitoring', module: this.advancedMonitoringSecurity },
        { name: 'behavioralAnalysis', module: this.behavioralAnalysisSecurity },
        { name: 'deepfakeDetection', module: this.deepfakeDetectionSecurity }
      ];
      
      modules.forEach(({ name, module }) => {
        try {
          if (module && typeof module.cleanup === 'function') {
            console.log(`🧹 Cleaning up ${name} module...`);
            module.cleanup();
          }
        } catch (error) {
          console.error(`❌ Error cleaning up ${name} module:`, error);
        }
      });
      
      // Reset state
      this.isInitialized = false;
      
      console.log('✅ All security modules cleaned up successfully');
      
    } catch (error) {
      console.error('❌ Error during security cleanup:', error);
    } finally {
      this.isCleaningUp = false;
    }
  }
}

export default SecurityManagerIntegration;
