// File: components/security/CopyPasteSecurity.js
// COMPLETE FIXED VERSION: Space bar completely unrestricted in all areas

export class CopyPasteSecurity {
  constructor(options = {}) {
    this.exitingRef = options.exitingRef;
    this.securityManagerRef = options.securityManagerRef;
    this.trackPreSecurityViolation = options.trackPreSecurityViolation;
    this.currentQuestion = options.currentQuestion;
    this.showCopyPasteWarning = options.showCopyPasteWarning;
    
    // Store for tracking internal clipboard operations within code editor
    this.internalClipboardContent = '';
    this.lastInternalCopyTime = 0;
    this.isInternalOperation = false;
    
    // ADDED: Cleanup references
    this.clipboardInterval = null;
    this.styleElement = null;
    
    // Bind all methods
    this.handleCopyOperation = this.handleCopyOperation.bind(this);
    this.handlePasteOperation = this.handlePasteOperation.bind(this);
    this.handleCutOperation = this.handleCutOperation.bind(this);
    this.handleKeyboardShortcuts = this.handleKeyboardShortcuts.bind(this);
    this.handleContextMenu = this.handleContextMenu.bind(this);
    this.handleSelectStart = this.handleSelectStart.bind(this);
    this.monitorClipboard = this.monitorClipboard.bind(this);
    this.setupCopyPasteMonitoring = this.setupCopyPasteMonitoring.bind(this);
    this.updateOptions = this.updateOptions.bind(this);
    this.cleanup = this.cleanup.bind(this);
  }

  // Check if security is active
  isSecurityActive() {
    return !this.exitingRef?.current;
  }

  // Report violation to SecurityManager if available
  reportViolation(type, message) {
    try {
      if (this.securityManagerRef?.current?.isInterviewActive) {
        // Try different violation handler methods
        if (typeof this.securityManagerRef.current.handleWarningViolation === 'function') {
          this.securityManagerRef.current.handleWarningViolation(type, message);
        } else if (typeof this.securityManagerRef.current.handleViolation === 'function') {
          this.securityManagerRef.current.handleViolation(type, message);
        }
      } else if (this.trackPreSecurityViolation) {
        // Track violation even if SecurityManager isn't active yet
        this.trackPreSecurityViolation(type, message);
      }
      
      console.log(`🚨 Copy/Paste violation: ${type} - ${message}`);
    } catch (error) {
      console.error('Error reporting copy/paste violation:', error);
    }
  }

  // Helper function to safely check if element is in code editor
  isInCodeEditor(target) {
    try {
      if (!target || typeof target.closest !== 'function') {
        return false;
      }
      return target.closest('.monaco-editor') !== null;
    } catch (error) {
      console.error('Error checking if in code editor:', error);
      return false;
    }
  }

  // Helper function to check if element is in other allowed areas (with external paste blocking)
  isInOtherAllowedArea(target) {
    try {
      if (!target || typeof target.closest !== 'function') {
        return false;
      }
      
      const otherAllowedSelectors = [
        'input[data-allow-clipboard]',
        '[data-allow-clipboard]'
        // REMOVED: 'textarea[data-allow-clipboard]' - we'll handle textareas specially
      ];
      
      return otherAllowedSelectors.some(selector => {
        try {
          return target.closest(selector) !== null;
        } catch (error) {
          return false;
        }
      });
    } catch (error) {
      console.error('Error checking other allowed areas:', error);
      return false;
    }
  }

  // Helper function to check if element is a technical/behavioral answer textarea
  isAnswerTextarea(target) {
    try {
      if (!target || typeof target.closest !== 'function') {
        return false;
      }
      
      // Check if this is a textarea that should allow typing but block external paste
      return target.tagName === 'TEXTAREA' && 
             !target.hasAttribute('data-allow-clipboard') &&
             !target.closest('.monaco-editor');
    } catch (error) {
      console.error('Error checking if answer textarea:', error);
      return false;
    }
  }

  // SMART COPY HANDLER - Tracks internal vs external operations
  async handleCopyOperation(e) {
    try {
      if (!this.isSecurityActive()) return;
      
      const isCodeEditor = this.isInCodeEditor(e.target);
      const isOtherAllowed = this.isInOtherAllowedArea(e.target);
      const isAnswerArea = this.isAnswerTextarea(e.target);
      
      console.log(`🔍 Copy operation detected`, {
        isCodeEditor,
        isOtherAllowed,
        isAnswerArea,
        target: e.target.className
      });
      
      if (isCodeEditor) {
        // Allow copy within code editor, but track the content
        console.log('✅ Copy allowed within code editor');
        this.isInternalOperation = true;
        this.lastInternalCopyTime = Date.now();
        
        // Try to capture what was copied for internal tracking
        setTimeout(async () => {
          try {
            if (navigator.clipboard && navigator.clipboard.readText) {
              const text = await navigator.clipboard.readText();
              this.internalClipboardContent = text;
              console.log('📋 Internal clipboard content tracked (length):', text.length);
            }
          } catch (error) {
            // Expected - clipboard access limitations
            console.log('Could not track internal clipboard content');
          }
        }, 100);
        
        return; // Allow the operation
      } else if (isAnswerArea) {
        // Block copy from answer textareas to prevent sharing answers
        console.log('🚫 Copy blocked from answer textarea');
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
        
        // Report violation and show warning
        this.reportViolation('copyPaste', 'Attempted to copy from answer textarea');
        if (this.showCopyPasteWarning) {
          this.showCopyPasteWarning('copy');
        }
        return false;
      } else if (isOtherAllowed) {
        // Allow copy in other designated areas
        console.log('✅ Copy allowed in designated area');
        return;
      } else {
        // Block copy from non-allowed areas
        console.log('🚫 Copy blocked from non-allowed area');
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
        
        // Report violation and show warning
        this.reportViolation('copyPaste', 'Attempted to copy from restricted area');
        if (this.showCopyPasteWarning) {
          this.showCopyPasteWarning('copy');
        }
        return false;
      }
    } catch (error) {
      console.error('Error handling copy operation:', error);
      e.preventDefault();
      if (this.showCopyPasteWarning) {
        this.showCopyPasteWarning('copy');
      }
      return false;
    }
  }

  // SMART PASTE HANDLER - Blocks external content, allows internal in code editor only
  async handlePasteOperation(e) {
    try {
      if (!this.isSecurityActive()) return;
      
      const isCodeEditor = this.isInCodeEditor(e.target);
      const isOtherAllowed = this.isInOtherAllowedArea(e.target);
      const isAnswerArea = this.isAnswerTextarea(e.target);
      
      console.log(`🔍 Paste operation detected`, {
        isCodeEditor,
        isOtherAllowed,
        isAnswerArea,
        target: e.target.className
      });
      
      if (isAnswerArea) {
        // ALWAYS block paste in answer textareas (technical/behavioral questions)
        console.log('🚫 Paste blocked in answer textarea (technical/behavioral questions)');
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
        
        // Report violation and show warning
        this.reportViolation('copyPaste', 'Attempted to paste into answer textarea');
        if (this.showCopyPasteWarning) {
          this.showCopyPasteWarning('paste', true); // true = external
        }
        return false;
      } else if (!isCodeEditor && !isOtherAllowed) {
        // Block paste in non-allowed areas
        console.log('🚫 Paste blocked in non-allowed area');
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
        
        // Report violation and show warning
        this.reportViolation('copyPaste', 'Attempted to paste in restricted area');
        if (this.showCopyPasteWarning) {
          this.showCopyPasteWarning('paste');
        }
        return false;
      }
      
      if (isCodeEditor) {
        // For code editor, check if this is an internal or external paste
        console.log('🔍 Analyzing paste content in code editor...');
        
        let clipboardText = '';
        
        // Try to get clipboard content from the event first
        if (e.clipboardData && e.clipboardData.getData) {
          try {
            clipboardText = e.clipboardData.getData('text/plain');
            console.log('📋 Got clipboard text from event (length):', clipboardText.length);
          } catch (error) {
            console.log('Could not get clipboard text from event');
          }
        }
        
        // Fallback: try to get from navigator.clipboard
        if (!clipboardText) {
          try {
            if (navigator.clipboard && navigator.clipboard.readText) {
              clipboardText = await navigator.clipboard.readText();
              console.log('📋 Got clipboard text from navigator (length):', clipboardText.length);
            }
          } catch (error) {
            console.log('Could not access clipboard via navigator');
          }
        }
        
        // Check if this is an internal operation
        const isRecentInternalCopy = (Date.now() - this.lastInternalCopyTime) < 10000; // 10 seconds window
        const isInternalContent = this.internalClipboardContent && 
                                 clipboardText && 
                                 (clipboardText === this.internalClipboardContent || 
                                  this.internalClipboardContent.includes(clipboardText) ||
                                  clipboardText.includes(this.internalClipboardContent));
        
        console.log('🔍 Paste analysis:', {
          isRecentInternalCopy,
          isInternalContent,
          internalContentLength: this.internalClipboardContent.length,
          clipboardTextLength: clipboardText.length,
          timeSinceLastCopy: Date.now() - this.lastInternalCopyTime
        });
        
        if (isRecentInternalCopy && (isInternalContent || this.isInternalOperation)) {
          // Allow internal paste operation
          console.log('✅ Internal paste operation allowed in code editor');
          this.isInternalOperation = false; // Reset flag
          return; // Allow the operation
        } else {
          // This appears to be external content - block it
          console.log('🚫 External paste blocked in code editor');
          e.preventDefault();
          e.stopPropagation();
          e.stopImmediatePropagation();
          
          // Report violation and show warning
          this.reportViolation('copyPaste', 'Attempted to paste external content into code editor');
          if (this.showCopyPasteWarning) {
            this.showCopyPasteWarning('paste', true); // true = external
          }
          return false;
        }
      } else if (isOtherAllowed) {
        // Allow paste in other designated areas
        console.log('✅ Paste allowed in designated area');
        return;
      }
    } catch (error) {
      console.error('Error handling paste operation:', error);
      e.preventDefault();
      if (this.showCopyPasteWarning) {
        this.showCopyPasteWarning('paste', true);
      }
      return false;
    }
  }

  // SMART CUT HANDLER - Similar to copy but removes content
  handleCutOperation(e) {
    try {
      if (!this.isSecurityActive()) return;
      
      const isCodeEditor = this.isInCodeEditor(e.target);
      const isOtherAllowed = this.isInOtherAllowedArea(e.target);
      const isAnswerArea = this.isAnswerTextarea(e.target);
      
      console.log(`🔍 Cut operation detected`, {
        isCodeEditor,
        isOtherAllowed,
        isAnswerArea,
        target: e.target.className
      });
      
      if (isCodeEditor) {
        // Allow cut within code editor
        console.log('✅ Cut allowed within code editor');
        this.isInternalOperation = true;
        this.lastInternalCopyTime = Date.now();
        return;
      } else if (isAnswerArea) {
        // Block cut from answer textareas
        console.log('🚫 Cut blocked from answer textarea');
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
        
        // Report violation and show warning
        this.reportViolation('copyPaste', 'Attempted to cut from answer textarea');
        if (this.showCopyPasteWarning) {
          this.showCopyPasteWarning('cut');
        }
        return false;
      } else if (isOtherAllowed) {
        // Allow cut in other designated areas
        console.log('✅ Cut allowed in designated area');
        return;
      } else {
        // Block cut from non-allowed areas
        console.log('🚫 Cut blocked from non-allowed area');
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
        
        // Report violation and show warning
        this.reportViolation('copyPaste', 'Attempted to cut from restricted area');
        if (this.showCopyPasteWarning) {
          this.showCopyPasteWarning('cut');
        }
        return false;
      }
    } catch (error) {
      console.error('Error handling cut operation:', error);
      e.preventDefault();
      if (this.showCopyPasteWarning) {
        this.showCopyPasteWarning('cut');
      }
      return false;
    }
  }

  // COMPLETELY FIXED: SMART KEYBOARD SHORTCUTS HANDLER - SPACE BAR COMPLETELY UNRESTRICTED
  handleKeyboardShortcuts(e) {
    try {
      if (!this.isSecurityActive()) return;
      
      // ✅ CRITICAL FIX: IMMEDIATELY ALLOW SPACE BAR AND ALL NORMAL TYPING
      if (e.key === ' ' || e.key === 'Spacebar' || e.code === 'Space') {
        return; // ALWAYS allow space bar - NO FURTHER PROCESSING
      }
      
      // ✅ ALLOW ALL NORMAL TYPING KEYS
      const normalTypingKeys = /^[a-zA-Z0-9`~!@#$%^&*()_+\-=\[\]\\{}|;':",./<>?]$/;
      if (normalTypingKeys.test(e.key) && !e.ctrlKey && !e.metaKey && !e.altKey) {
        return; // Allow normal typing
      }
      
      // ✅ ALLOW COMMON EDITING KEYS
      const editingKeys = [
        'Backspace', 'Delete', 'Enter', 'Tab', 'ArrowUp', 'ArrowDown', 
        'ArrowLeft', 'ArrowRight', 'Home', 'End', 'PageUp', 'PageDown',
        'Insert', 'CapsLock', 'NumLock', 'ScrollLock', 'Pause',
        'ContextMenu', 'Meta', 'Control', 'Alt', 'Shift'
      ];
      
      if (editingKeys.includes(e.key)) {
        return; // Allow editing keys
      }
      
      // NOW handle ONLY specific copy/paste keyboard shortcuts
      if ((e.ctrlKey || e.metaKey) && ['c', 'v', 'x', 'a'].includes(e.key.toLowerCase())) {
        const isCodeEditor = this.isInCodeEditor(e.target);
        const isOtherAllowed = this.isInOtherAllowedArea(e.target);
        const isAnswerArea = this.isAnswerTextarea(e.target);
        
        console.log(`🔍 Keyboard shortcut detected: ${e.ctrlKey ? 'Ctrl' : 'Cmd'}+${e.key.toUpperCase()}`, {
          isCodeEditor,
          isOtherAllowed,
          isAnswerArea
        });
        
        const actionMap = {
          'c': 'copy',
          'v': 'paste', 
          'x': 'cut',
          'a': 'select all'
        };
        
        const action = actionMap[e.key.toLowerCase()];
        
        if (isAnswerArea) {
          // Special handling for answer textareas
          if (action === 'paste') {
            // Always block paste in answer areas
            console.log('🚫 Paste shortcut blocked in answer textarea');
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();
            
            // Report violation and show warning
            this.reportViolation('copyPaste', 'Attempted to paste using keyboard shortcut in answer textarea');
            if (this.showCopyPasteWarning) {
              this.showCopyPasteWarning('paste', true); // external paste blocked
            }
            return false;
          } else if (action === 'copy' || action === 'cut') {
            // Block copy/cut from answer areas to prevent sharing
            console.log(`🚫 ${action} shortcut blocked in answer textarea`);
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();
            
            // Report violation and show warning
            this.reportViolation('copyPaste', `Attempted to ${action} using keyboard shortcut from answer textarea`);
            if (this.showCopyPasteWarning) {
              this.showCopyPasteWarning(action);
            }
            return false;
          } else if (action === 'select all') {
            // Allow select all in answer areas
            console.log('✅ Select all allowed in answer textarea');
            return;
          }
        } else if (isCodeEditor) {
          // For code editor, handle copy/cut/select-all normally, but special handling for paste
          if (action === 'paste') {
            // Let the paste event handler deal with this
            console.log('✅ Paste shortcut in code editor - will be handled by paste event');
            return;
          } else {
            // Allow copy, cut, select-all in code editor
            console.log(`✅ ${action} shortcut allowed in code editor`);
            if (action === 'copy' || action === 'cut') {
              this.isInternalOperation = true;
              this.lastInternalCopyTime = Date.now();
            }
            return;
          }
        } else if (isOtherAllowed) {
          // Allow in other designated areas
          console.log(`✅ ${action} shortcut allowed in designated area`);
          return;
        } else {
          // Block in non-allowed areas
          console.log(`🚫 ${action} keyboard shortcut blocked`);
          e.preventDefault();
          e.stopPropagation();
          e.stopImmediatePropagation();
          
          // Report violation and show warning
          this.reportViolation('copyPaste', `Attempted ${action} using keyboard shortcut in restricted area`);
          if (this.showCopyPasteWarning) {
            this.showCopyPasteWarning(action);
          }
          return false;
        }
      }
      
      // ✅ CRITICAL FIX: ALL OTHER KEYS ARE ALLOWED (including space bar)
      return;
    } catch (error) {
      console.error('Error handling keyboard shortcut:', error);
      // CRITICAL FIX: Only prevent copy/paste shortcuts on error, not normal typing
      if ((e.ctrlKey || e.metaKey) && ['c', 'v', 'x', 'a'].includes(e.key.toLowerCase())) {
        e.preventDefault();
        e.stopPropagation();
        const actionMap = { 'c': 'copy', 'v': 'paste', 'x': 'cut', 'a': 'select all' };
        
        // Report violation and show warning
        this.reportViolation('copyPaste', `Keyboard shortcut error: ${actionMap[e.key.toLowerCase()]}`);
        if (this.showCopyPasteWarning) {
          this.showCopyPasteWarning(actionMap[e.key.toLowerCase()]);
        }
      }
      // Don't prevent other keys (including space bar)
      return false;
    }
  }

  // COMPREHENSIVE: Handle right-click context menu
  handleContextMenu(e) {
    try {
      if (!this.isSecurityActive()) return;
      
      const isCodeEditor = this.isInCodeEditor(e.target);
      const isOtherAllowed = this.isInOtherAllowedArea(e.target);
      
      if (!isCodeEditor && !isOtherAllowed) {
        console.log('🚫 Context menu (right-click) blocked');
        e.preventDefault();
        e.stopPropagation();
        
        // Report violation
        this.reportViolation('rightClick', 'Attempted to open context menu in restricted area');
        return false;
      } else {
        console.log('✅ Context menu allowed in designated area');
      }
    } catch (error) {
      console.error('Error handling context menu:', error);
      e.preventDefault();
      return false;
    }
  }

  // COMPREHENSIVE: Handle text selection
  handleSelectStart(e) {
    try {
      if (!this.isSecurityActive()) return;
      
      const isCodeEditor = this.isInCodeEditor(e.target);
      const isOtherAllowed = this.isInOtherAllowedArea(e.target);
      
      if (!isCodeEditor && !isOtherAllowed) {
        console.log('🚫 Text selection blocked');
        e.preventDefault();
        return false;
      }
    } catch (error) {
      console.error('Error handling text selection:', error);
      e.preventDefault();
      return false;
    }
  }

  // MONITOR: Track clipboard changes for external content detection
  async monitorClipboard() {
    try {
      if (navigator.clipboard && navigator.clipboard.readText && 
          !this.isInternalOperation && (Date.now() - this.lastInternalCopyTime) > 2000) {
        // Only monitor when not doing internal operations
        const text = await navigator.clipboard.readText();
        if (text && text.length > 0 && text !== this.internalClipboardContent) {
          console.log('📋 External clipboard content detected during monitoring');
          // Just log for now - the paste handler will block external content
        }
      }
    } catch (error) {
      // Expected - clipboard access requires user interaction
    }
  }

  // Setup copy/paste monitoring with intelligent blocking
  setupCopyPasteMonitoring() {
    console.log('📋 Setting up intelligent copy/paste monitoring - SPACE BAR UNRESTRICTED...');
    
    try {
      // Primary: Copy/Cut/Paste events
      document.addEventListener('copy', this.handleCopyOperation, { capture: true, passive: false });
      document.addEventListener('cut', this.handleCutOperation, { capture: true, passive: false });
      document.addEventListener('paste', this.handlePasteOperation, { capture: true, passive: false });
      
      window.addEventListener('copy', this.handleCopyOperation, { capture: true, passive: false });
      window.addEventListener('cut', this.handleCutOperation, { capture: true, passive: false });
      window.addEventListener('paste', this.handlePasteOperation, { capture: true, passive: false });
      
      // Secondary: Keyboard shortcuts
      document.addEventListener('keydown', this.handleKeyboardShortcuts, { capture: true, passive: false });
      window.addEventListener('keydown', this.handleKeyboardShortcuts, { capture: true, passive: false });
      
      // Tertiary: Context menu and text selection
      document.addEventListener('contextmenu', this.handleContextMenu, { capture: true, passive: false });
      window.addEventListener('contextmenu', this.handleContextMenu, { capture: true, passive: false });
      document.addEventListener('selectstart', this.handleSelectStart, { capture: true, passive: false });
      
      // Monitor clipboard periodically
      this.clipboardInterval = setInterval(this.monitorClipboard, 3000);
      
      // UPDATED CSS for selective text selection - SPACE BAR FRIENDLY
      this.styleElement = document.createElement('style');
      this.styleElement.textContent = `
        /* Block text selection globally */
        * {
          -webkit-user-select: none !important;
          -moz-user-select: none !important;
          -ms-user-select: none !important;
          user-select: none !important;
        }
        
        /* ✅ ALLOW selection and typing in ALL input areas - SPACE BAR WORKS */
        .monaco-editor,
        .monaco-editor *,
        [data-allow-clipboard],
        [data-allow-select],
        textarea,
        textarea:focus,
        input[type="text"],
        input[type="text"]:focus,
        input[type="password"],
        input[type="password"]:focus,
        input[type="email"],
        input[type="email"]:focus,
        input[type="search"],
        input[type="search"]:focus,
        input[data-allow-clipboard],
        .answer-textarea,
        .question-input,
        div[contenteditable="true"] {
          -webkit-user-select: text !important;
          -moz-user-select: text !important;
          -ms-user-select: text !important;
          user-select: text !important;
          pointer-events: auto !important;
          cursor: text !important;
        }
        
        /* ✅ Ensure textarea and input elements can receive focus and allow ALL typing */
        textarea:focus,
        input:focus,
        .monaco-editor:focus,
        .monaco-editor *:focus {
          outline: none;
          -webkit-user-select: text !important;
          -moz-user-select: text !important;
          -ms-user-select: text !important;
          user-select: text !important;
          pointer-events: auto !important;
          cursor: text !important;
        }
        
        /* ✅ Override any conflicting styles that might block typing */
        textarea *, 
        input[type="text"] *, 
        .monaco-editor * {
          pointer-events: auto !important;
          user-select: text !important;
        }
      `;
      document.head.appendChild(this.styleElement);
      
      console.log('✅ Intelligent copy/paste system initialized - SPACE BAR WORKS EVERYWHERE');
      
      // Return cleanup function
      return () => {
        this.cleanup();
      };
    } catch (error) {
      console.error('❌ Error setting up copy/paste monitoring:', error);
      return () => {}; // Return empty cleanup function
    }
  }

  // Update options (useful for React state updates)
  updateOptions(newOptions) {
    Object.assign(this, newOptions);
    console.log('📋 Copy/paste options updated:', newOptions);
  }

  // Get current status
  getStatus() {
    return {
      isActive: this.isSecurityActive(),
      hasInternalContent: this.internalClipboardContent.length > 0,
      lastInternalCopyTime: this.lastInternalCopyTime,
      isInternalOperation: this.isInternalOperation
    };
  }

  // Cleanup method
  cleanup() {
    console.log('🧹 Copy/paste security cleanup initiated');
    
    try {
      // Clear clipboard monitoring
      if (this.clipboardInterval) {
        clearInterval(this.clipboardInterval);
        this.clipboardInterval = null;
      }
      
      // Remove CSS styles
      if (this.styleElement && this.styleElement.parentNode) {
        this.styleElement.parentNode.removeChild(this.styleElement);
        this.styleElement = null;
      }
      
      // Remove event listeners
      document.removeEventListener('copy', this.handleCopyOperation, { capture: true });
      document.removeEventListener('cut', this.handleCutOperation, { capture: true });
      document.removeEventListener('paste', this.handlePasteOperation, { capture: true });
      
      window.removeEventListener('copy', this.handleCopyOperation, { capture: true });
      window.removeEventListener('cut', this.handleCutOperation, { capture: true });
      window.removeEventListener('paste', this.handlePasteOperation, { capture: true });
      
      document.removeEventListener('keydown', this.handleKeyboardShortcuts, { capture: true });
      window.removeEventListener('keydown', this.handleKeyboardShortcuts, { capture: true });
      
      document.removeEventListener('contextmenu', this.handleContextMenu, { capture: true });
      window.removeEventListener('contextmenu', this.handleContextMenu, { capture: true });
      document.removeEventListener('selectstart', this.handleSelectStart, { capture: true });
      
      // Reset internal state
      this.internalClipboardContent = '';
      this.lastInternalCopyTime = 0;
      this.isInternalOperation = false;
      
      console.log('✅ Copy/paste security cleanup completed');
    } catch (error) {
      console.error('❌ Error during copy/paste cleanup:', error);
    }
  }
}

export default CopyPasteSecurity;
