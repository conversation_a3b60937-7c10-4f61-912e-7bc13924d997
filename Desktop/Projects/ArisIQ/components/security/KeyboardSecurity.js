// File: components/security/KeyboardSecurity.js
// Handles keyboard shortcuts and escape key violations

export class KeyboardSecurity {
  constructor(options = {}) {
    this.exitingRef = options.exitingRef;
    this.securityManagerRef = options.securityManagerRef;
    this.trackPreSecurityViolation = options.trackPreSecurityViolation;
    this.currentQuestion = options.currentQuestion;
    this.showRequiredAcknowledgmentWarning = options.showRequiredAcknowledgmentWarning;
    
    this.keysPressed = new Set();
    
    this.globalKeyboardBlocker = this.globalKeyboardBlocker.bind(this);
    this.handleKeyDown = this.handleKeyDown.bind(this);
    this.handleKeyUp = this.handleKeyUp.bind(this);
    this.setupKeyboardBlocking = this.setupKeyboardBlocking.bind(this);
    this.setupEscapeKeyBlocking = this.setupEscapeKeyBlocking.bind(this);
    this.setupDeveloperToolsBlocking = this.setupDeveloperToolsBlocking.bind(this);
  }

  // Check if security is active
  isSecurityActive() {
    return !this.exitingRef?.current && 
           (this.securityManagerRef?.current?.isInterviewActive || 
            this.trackPreSecurityViolation);
  }

  // Report keyboard violation
  reportKeyboardViolation(type, message) {
    console.log(`🚨 Keyboard violation: ${type} - ${message}`);
    
    if (this.securityManagerRef?.current && this.securityManagerRef.current.isInterviewActive) {
      try {
        if (typeof this.securityManagerRef.current.handleWarningViolation === 'function') {
          this.securityManagerRef.current.handleWarningViolation(type, message);
        } else if (typeof this.securityManagerRef.current.handleViolation === 'function') {
          this.securityManagerRef.current.handleViolation(type, message);
        } else if (typeof this.securityManagerRef.current.reportViolation === 'function') {
          this.securityManagerRef.current.reportViolation(type, message);
        }
      } catch (error) {
        console.error('Error reporting keyboard violation:', error);
      }
    } else if (this.trackPreSecurityViolation) {
      // Track violation even if SecurityManager isn't active yet
      this.trackPreSecurityViolation(type, 
        `${message} (question ${(this.currentQuestion || 0) + 1})`);
    }
  }

  // Enhanced keyboard blocker - specifically targets screenshot keys and other violations
  globalKeyboardBlocker(e) {
    if (!this.isSecurityActive()) return;
    
    // Block Escape key COMPLETELY - prevent all default behavior
    if (e.key === 'Escape' || e.keyCode === 27) {
      console.log('🚫 Escape key blocked - preventing fullscreen exit');
      e.preventDefault();
      e.stopPropagation();
      e.stopImmediatePropagation();
      
      this.reportKeyboardViolation('escapeKey', 'Attempted to press Escape key');
      return false;
    }
    
    // SCREENSHOT KEY BLOCKING - Special priority
    
    // Block PrintScreen key directly
    if (e.key === 'PrintScreen' || e.code === 'PrintScreen' || e.key === 'PrtScn' || e.key === 'PrtSc') {
      console.log('🚫 PrintScreen key blocked');
      e.preventDefault();
      e.stopPropagation();
      e.stopImmediatePropagation();
      
      this.reportKeyboardViolation('keyboardShortcut', 'PrintScreen key blocked');
      return false;
    }
    
    // Block Mac screenshot combinations (Command+Shift+3/4/5)
    if (e.metaKey && e.shiftKey && ['3', '4', '5'].includes(e.key)) {
      console.log(`🚫 Mac screenshot key blocked: Command+Shift+${e.key}`);
      e.preventDefault();
      e.stopPropagation();
      e.stopImmediatePropagation();
      
      this.reportKeyboardViolation('keyboardShortcut', `Mac screenshot shortcut blocked: Command+Shift+${e.key}`);
      return false;
    }
    
    // Block Windows snipping tool (Win+Shift+S)
    if ((e.metaKey || e.key === 'Meta' || e.key === 'OS') && e.shiftKey && (e.key === 's' || e.key === 'S')) {
      console.log('🚫 Windows+Shift+S key blocked');
      e.preventDefault();
      e.stopPropagation();
      e.stopImmediatePropagation();
      
      this.reportKeyboardViolation('keyboardShortcut', 'Windows Snipping Tool shortcut blocked');
      return false;
    }
    
    // Block copy, paste, and cut shortcuts (handled by CopyPasteSecurity, but add blocking here too)
    if (e.ctrlKey || e.metaKey) {
      if (e.key === 'c' || e.key === 'C') {
        // Allow in specific areas - check if target is in allowed area
        if (!this.isInAllowedArea(e.target)) {
          console.log('🚫 Copy blocked');
          e.preventDefault();
          e.stopPropagation();
          this.reportKeyboardViolation('keyboardShortcut', 'Copy shortcut blocked');
          return false;
        }
      }
      if (e.key === 'v' || e.key === 'V') {
        if (!this.isInAllowedArea(e.target)) {
          console.log('🚫 Paste blocked');
          e.preventDefault();
          e.stopPropagation();
          this.reportKeyboardViolation('keyboardShortcut', 'Paste shortcut blocked');
          return false;
        }
      }
      if (e.key === 'x' || e.key === 'X') {
        if (!this.isInAllowedArea(e.target)) {
          console.log('🚫 Cut blocked');
          e.preventDefault();
          e.stopPropagation();
          this.reportKeyboardViolation('keyboardShortcut', 'Cut shortcut blocked');
          return false;
        }
      }
      // Block select all in non-allowed areas
      if (e.key === 'a' || e.key === 'A') {
        if (!this.isInAllowedArea(e.target)) {
          console.log('🚫 Select All blocked');
          e.preventDefault();
          e.stopPropagation();
          this.reportKeyboardViolation('keyboardShortcut', 'Select All shortcut blocked');
          return false;
        }
      }
      // Block developer tools
      if (e.key === 'F12' || (e.shiftKey && (e.key === 'I' || e.key === 'i'))) {
        console.log('🚫 Developer tools blocked');
        e.preventDefault();
        e.stopPropagation();
        this.reportKeyboardViolation('devTools', 'Developer tools shortcut blocked');
        return false;
      }
      // Block refresh
      if (e.key === 'r' || e.key === 'R') {
        console.log('🚫 Refresh blocked');
        e.preventDefault();
        e.stopPropagation();
        this.reportKeyboardViolation('keyboardShortcut', 'Refresh shortcut blocked');
        return false;
      }
      // Block new tab/window
      if (e.key === 't' || e.key === 'T' || e.key === 'n' || e.key === 'N') {
        console.log('🚫 New tab/window blocked');
        e.preventDefault();
        e.stopPropagation();
        this.reportKeyboardViolation('keyboardShortcut', 'New tab/window shortcut blocked');
        return false;
      }
    }
    
    // Block F12 (developer tools)
    if (e.key === 'F12') {
      console.log('🚫 F12 blocked');
      e.preventDefault();
      e.stopPropagation();
      this.reportKeyboardViolation('devTools', 'F12 developer tools key blocked');
      return false;
    }
    
    // Block Alt+Tab (task switching)
    if (e.altKey && e.key === 'Tab') {
      console.log('🚫 Alt+Tab blocked');
      e.preventDefault();
      e.stopPropagation();
      this.reportKeyboardViolation('keyboardShortcut', 'Alt+Tab task switching blocked');
      return false;
    }
    
    // Block Windows key on PC
    if (e.key === 'Meta' || e.key === 'Super') {
      console.log('🚫 Windows/Super key blocked');
      e.preventDefault();
      e.stopPropagation();
      this.reportKeyboardViolation('keyboardShortcut', 'Windows/Super key blocked');
      return false;
    }
    
    // Block F11 (fullscreen toggle)
    if (e.key === 'F11') {
      console.log('🚫 F11 blocked');
      e.preventDefault();
      e.stopPropagation();
      this.reportKeyboardViolation('keyboardShortcut', 'F11 fullscreen toggle blocked');
      return false;
    }
    
    // Block F5 (refresh)
    if (e.key === 'F5') {
      console.log('🚫 F5 refresh blocked');
      e.preventDefault();
      e.stopPropagation();
      this.reportKeyboardViolation('keyboardShortcut', 'F5 refresh blocked');
      return false;
    }
  }

  // Check if target is in an allowed area for copy/paste/select
  isInAllowedArea(target) {
    try {
      if (!target || typeof target.closest !== 'function') {
        return false;
      }
      
      const allowedSelectors = [
        '.monaco-editor',
        'textarea[data-allow-clipboard]',
        'input[data-allow-clipboard]',
        '[data-allow-clipboard]',
        '[data-allow-select]'
      ];
      
      return allowedSelectors.some(selector => {
        try {
          return target.closest(selector) !== null;
        } catch (error) {
          return false;
        }
      });
    } catch (error) {
      console.error('Error checking if in allowed area:', error);
      return false;
    }
  }

  // Handle key down events
  handleKeyDown(e) {
    if (!this.isSecurityActive()) return;
    
    // Track pressed keys
    this.keysPressed.add(e.key);
    if (e.code) this.keysPressed.add(e.code);
    
    // Add modifier keys
    if (e.metaKey) {
      this.keysPressed.add('Meta');
      this.keysPressed.add('Command');
    }
    if (e.shiftKey) this.keysPressed.add('Shift');
    if (e.altKey) this.keysPressed.add('Alt');
    if (e.ctrlKey) this.keysPressed.add('Control');
    
    // Check for blocked key combinations
    const blockedCombinations = [
      // Developer tools
      { key: 'F12' },
      { key: 'I', ctrl: true, shift: true },
      { key: 'i', ctrl: true, shift: true },
      { key: 'C', ctrl: true, shift: true },
      { key: 'c', ctrl: true, shift: true },
      { key: 'J', ctrl: true, shift: true },
      { key: 'j', ctrl: true, shift: true },
      // Navigation
      { key: 'Tab', alt: true },
      { key: 'Tab', ctrl: true },
      { key: 'w', ctrl: true },
      { key: 'W', ctrl: true },
      { key: 'n', ctrl: true },
      { key: 'N', ctrl: true },
      { key: 't', ctrl: true },
      { key: 'T', ctrl: true },
      // Refresh
      { key: 'r', ctrl: true },
      { key: 'R', ctrl: true },
      { key: 'F5' },
      // Other
      { key: 'F4', alt: true },
      { key: 'F11' },
      // Screenshot keys
      { key: 'PrintScreen' },
      { key: 'PrtScn' },
      { key: 'PrtSc' }
    ];
    
    const isBlocked = blockedCombinations.some(combo => {
      if (combo.key === e.key) {
        if (combo.ctrl && combo.shift) {
          return e.ctrlKey && e.shiftKey;
        } else if (combo.ctrl) {
          return e.ctrlKey;
        } else if (combo.alt) {
          return e.altKey;
        } else if (combo.shift) {
          return e.shiftKey;
        }
        return true;
      }
      return false;
    });
    
    if (isBlocked) {
      e.preventDefault();
      e.stopPropagation();
      e.stopImmediatePropagation();
      this.reportKeyboardViolation('keyboardShortcut', `Blocked shortcut: ${e.key}`);
      return false;
    }
  }

  // Handle key up events
  handleKeyUp(e) {
    // Remove the released key
    this.keysPressed.delete(e.key);
    if (e.code) this.keysPressed.delete(e.code);
    
    // Remove modifier keys if released
    if (e.key === 'Meta' || e.key === 'Command') {
      this.keysPressed.delete('Meta');
      this.keysPressed.delete('Command');
    }
    if (e.key === 'Shift') this.keysPressed.delete('Shift');
    if (e.key === 'Alt') this.keysPressed.delete('Alt');
    if (e.key === 'Control') this.keysPressed.delete('Control');
  }

  // Setup escape key blocking specifically
  setupEscapeKeyBlocking() {
    console.log('🚫 Setting up escape key blocking...');
    
    const cleanupFunctions = [];
    
    // Block ESC key at document level before other handlers
    const blockEscape = (e) => {
      if (e.key === 'Escape') {
        console.log('🚫 Escape key blocked by KeyboardSecurity');
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
        
        // Only flag if security is active
        if (this.isSecurityActive()) {
          this.reportKeyboardViolation('escapeKey', 'Attempted to press Escape key');
        }
        return false;
      }
    };
    
    // Add listener with capture=true to catch escape before any other handlers
    document.addEventListener('keydown', blockEscape, { 
      capture: true, 
      passive: false 
    });
    
    cleanupFunctions.push(() => {
      document.removeEventListener('keydown', blockEscape, { capture: true });
    });
    
    // Also add at window level as fallback
    window.addEventListener('keydown', blockEscape, { 
      capture: true, 
      passive: false 
    });
    
    cleanupFunctions.push(() => {
      window.removeEventListener('keydown', blockEscape, { capture: true });
    });
    
    console.log('✅ Escape key blocking initialized');
    
    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }

  // Setup developer tools detection and blocking
  setupDeveloperToolsBlocking() {
    console.log('🛠️ Setting up developer tools blocking...');
    
    const cleanupFunctions = [];
    let devToolsOpen = false;
    
    // Method 1: Window size inspection
    const checkWindowSize = () => {
      if (!this.isSecurityActive()) return;
      
      const threshold = 200;
      if (window.outerHeight - window.innerHeight > threshold || 
          window.outerWidth - window.innerWidth > threshold) {
        if (!devToolsOpen) {
          devToolsOpen = true;
          this.reportKeyboardViolation('devTools', 'Developer tools opened (window size detection)');
        }
      } else {
        devToolsOpen = false;
      }
    };
    
    // Check every 3 seconds
    const sizeCheckInterval = setInterval(checkWindowSize, 3000);
    cleanupFunctions.push(() => {
      clearInterval(sizeCheckInterval);
    });
    
    // Method 2: Console detection
    const detectConsole = () => {
      if (!this.isSecurityActive()) return;
      
      const start = new Date();
      debugger;
      const duration = new Date() - start;
      if (duration > 100) {
        console.log('🚨 Developer tools detected via debugger timing!');
        this.reportKeyboardViolation('devTools', 'Developer tools detected (console detection)');
      }
    };
    
    // Run console detection every 2 seconds
    const consoleCheckInterval = setInterval(detectConsole, 2000);
    cleanupFunctions.push(() => {
      clearInterval(consoleCheckInterval);
    });
    
    // Method 3: Function toString detection
    const setupFunctionDetection = () => {
      const originalToString = Function.prototype.toString;
      Function.prototype.toString = function() {
        if (this === detectConsole) {
          this.reportKeyboardViolation('devTools', 'Developer tools detected (function inspection)');
        }
        return originalToString.apply(this, arguments);
      };
      
      cleanupFunctions.push(() => {
        Function.prototype.toString = originalToString;
      });
    };
    
    setupFunctionDetection();
    
    console.log('✅ Developer tools blocking initialized');
    
    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }

  // Setup comprehensive keyboard blocking
  setupKeyboardBlocking() {
    console.log('⌨️ Setting up comprehensive keyboard blocking...');
    
    const cleanupFunctions = [];
    
    // Add MULTIPLE listeners with HIGHEST priority to ensure blocking
    document.addEventListener('keydown', this.globalKeyboardBlocker, { 
      capture: true, 
      passive: false 
    });
    
    cleanupFunctions.push(() => {
      document.removeEventListener('keydown', this.globalKeyboardBlocker, { capture: true });
    });
    
    window.addEventListener('keydown', this.globalKeyboardBlocker, { 
      capture: true, 
      passive: false 
    });
    
    cleanupFunctions.push(() => {
      window.removeEventListener('keydown', this.globalKeyboardBlocker, { capture: true });
    });
    
    // Add event listener to body (only if body exists)
    if (document.body) {
      document.body.addEventListener('keydown', this.globalKeyboardBlocker, { 
        capture: true, 
        passive: false 
      });
      
      cleanupFunctions.push(() => {
        if (document.body) {
          document.body.removeEventListener('keydown', this.globalKeyboardBlocker, { capture: true });
        }
      });
    }
    
    // Enhanced key tracking
    document.addEventListener('keydown', this.handleKeyDown, { 
      capture: true, 
      passive: false 
    });
    
    cleanupFunctions.push(() => {
      document.removeEventListener('keydown', this.handleKeyDown, { capture: true });
    });
    
    document.addEventListener('keyup', this.handleKeyUp, { 
      capture: true, 
      passive: true 
    });
    
    cleanupFunctions.push(() => {
      document.removeEventListener('keyup', this.handleKeyUp, { capture: true });
    });
    
    // Block drag and drop
    const blockDragStart = (e) => {
      if (this.isSecurityActive()) {
        console.log('🚫 Drag blocked');
        e.preventDefault();
        this.reportKeyboardViolation('keyboardShortcut', 'Drag and drop blocked');
      }
    };
    
    document.addEventListener('dragstart', blockDragStart, { capture: true, passive: false });
    cleanupFunctions.push(() => {
      document.removeEventListener('dragstart', blockDragStart, { capture: true });
    });
    
    // FIXED: Use MutationObserver to ensure handlers stay attached (corrected document API usage)
    const observer = new MutationObserver(() => {
      // Re-attach handlers if they get removed (basic check)
      // FIXED: Check if documentElement has the attribute, not document
      if (!document.documentElement.hasAttribute('data-keyboard-security')) {
        document.documentElement.setAttribute('data-keyboard-security', 'active');
        console.log('🔄 Re-attaching keyboard security handlers');
      }
    });
    
    observer.observe(document, { 
      childList: true, 
      subtree: true 
    });
    
    cleanupFunctions.push(() => {
      observer.disconnect();
    });
    
    // FIXED: Set initial marker on documentElement, not document
    document.documentElement.setAttribute('data-keyboard-security', 'active');
    
    console.log('✅ Comprehensive keyboard blocking initialized');
    
    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
      // FIXED: Remove attribute from documentElement, not document
      if (document.documentElement.hasAttribute('data-keyboard-security')) {
        document.documentElement.removeAttribute('data-keyboard-security');
      }
    };
  }

  // Main setup function
  setupKeyboardSecurity() {
    console.log('⌨️ Setting up keyboard security...');
    
    const cleanupFunctions = [];
    
    try {
      // Setup all keyboard security measures
      cleanupFunctions.push(this.setupEscapeKeyBlocking());
      cleanupFunctions.push(this.setupDeveloperToolsBlocking());
      cleanupFunctions.push(this.setupKeyboardBlocking());
      
      // Display security message
      console.log('%c🛡️ Keyboard Security Active: Various keys disabled during interview', 'color: red; font-size: 16px; font-weight: bold;');
      
      console.log('✅ Keyboard security fully initialized');
    } catch (error) {
      console.error('Error setting up keyboard security:', error);
    }
    
    // Return cleanup function
    return () => {
      console.log('🧹 Cleaning up keyboard security...');
      cleanupFunctions.forEach(cleanup => {
        try {
          cleanup();
        } catch (error) {
          console.error('Error in keyboard security cleanup:', error);
        }
      });
      this.keysPressed.clear();
    };
  }

  // Update options (useful for React state updates)
  updateOptions(newOptions) {
    Object.assign(this, newOptions);
  }

  // Get currently pressed keys (for debugging)
  getCurrentlyPressedKeys() {
    return Array.from(this.keysPressed);
  }

  // Clear pressed keys (useful for cleanup)
  clearPressedKeys() {
    this.keysPressed.clear();
  }

  // Cleanup method
  cleanup() {
    this.clearPressedKeys();
  }
}

export default KeyboardSecurity;
