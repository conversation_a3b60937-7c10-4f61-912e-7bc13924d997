// File: components/security/FullscreenSecurity.js
// MINIMAL VERSION - ONLY HANDLES RED OVERLAY, LETS SECURITY MANAGER HANDLE VIOLATIONS

export const setupFullscreenSecurity = (options) => {
  console.log('🔴 MINIMAL: Setting up red overlay only - SecurityManager handles violations');
  
  const { exitingRef, setIsFullScreen } = options;

  let redOverlay = null;
  let isActive = false;
  let hasBeenInFullscreen = false;

  // Check if in fullscreen
  const isFullscreen = () => {
    try {
      const fullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      );
      return fullscreen;
    } catch (error) {
      return false;
    }
  };

  // Create red overlay (ONLY VISUAL - no violation reporting)
  const showRedOverlay = () => {
    try {
      console.log('🔴 MINIMAL: Creating red overlay (SecurityManager will handle violation)');
      
      // Remove any existing overlay first
      const existingOverlays = document.querySelectorAll('[id*="overlay"], [id*="red"], [id*="fullscreen-blocker"]');
      existingOverlays.forEach(el => {
        try {
          el.remove();
        } catch (e) {}
      });
      
      // Create new overlay
      redOverlay = document.createElement('div');
      redOverlay.id = 'minimal-red-overlay';
      
      // Use exact same styles that worked in your test
      redOverlay.style.cssText = `
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background-color: red !important;
        color: white !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        z-index: 2147483647 !important;
        font-size: 24px !important;
        text-align: center !important;
        font-family: Arial, sans-serif !important;
        pointer-events: all !important;
      `;
      
      redOverlay.innerHTML = `
        <div style="font-size: 48px; margin-bottom: 30px; font-weight: bold;">⚠️ FULLSCREEN REQUIRED ⚠️</div>
        <div style="font-size: 24px; margin-bottom: 20px;">You have exited fullscreen mode!</div>
        <div style="font-size: 20px; margin-bottom: 20px;">This is a security violation and has been recorded.</div>
        <button onclick="
          try {
            document.documentElement.requestFullscreen().catch(console.error);
          } catch(e) {
            console.error('Fullscreen failed:', e);
          }
        " style="
          font-size: 20px;
          padding: 15px 30px;
          background: white;
          color: red;
          border: none;
          border-radius: 10px;
          cursor: pointer;
          font-weight: bold;
        ">Click Here to Return to Fullscreen</button>
        <div style="font-size: 16px; margin-top: 20px;">The interview is paused until you return to fullscreen</div>
      `;
      
      document.body.appendChild(redOverlay);
      console.log('🔴 MINIMAL: Red overlay created - SecurityManager will handle the violation reporting');
      
      return true;
    } catch (error) {
      console.error('🔴 MINIMAL: Error creating overlay:', error);
      return false;
    }
  };

  // Remove red overlay
  const hideRedOverlay = () => {
    try {
      if (redOverlay && redOverlay.parentNode) {
        console.log('🔴 MINIMAL: Removing red overlay');
        redOverlay.remove();
        redOverlay = null;
      }
    } catch (error) {
      console.error('🔴 MINIMAL: Error removing overlay:', error);
    }
  };

  // SIMPLE fullscreen change handler - ONLY shows/hides overlay
  const handleChange = () => {
    try {
      console.log('🔴 MINIMAL: Fullscreen change detected');
      
      const inFullscreen = isFullscreen();
      console.log('🔴 MINIMAL: In fullscreen:', inFullscreen);
      console.log('🔴 MINIMAL: Has been in fullscreen:', hasBeenInFullscreen);
      console.log('🔴 MINIMAL: Is active:', isActive);
      console.log('🔴 MINIMAL: Exiting:', exitingRef?.current);
      
      // Update React state
      if (setIsFullScreen && typeof setIsFullScreen === 'function') {
        setIsFullScreen(inFullscreen);
      }
      
      // Activate when user first enters fullscreen
      if (inFullscreen && !hasBeenInFullscreen) {
        console.log('🔴 MINIMAL: ===== ACTIVATING OVERLAY SYSTEM - USER ENTERED FULLSCREEN =====');
        hasBeenInFullscreen = true;
        isActive = true;
        hideRedOverlay(); // Remove any existing overlay
        return;
      }
      
      // MAIN LOGIC: Show/hide overlay (SecurityManager handles violation reporting)
      if (hasBeenInFullscreen && isActive && !exitingRef?.current) {
        if (!inFullscreen) {
          console.log('🔴 MINIMAL: ===== NOT IN FULLSCREEN - SHOWING RED OVERLAY =====');
          console.log('🔴 MINIMAL: (SecurityManager will automatically detect and report the violation)');
          showRedOverlay();
        } else if (inFullscreen && redOverlay) {
          console.log('🔴 MINIMAL: Back in fullscreen - hiding overlay');
          hideRedOverlay();
        }
      }
      
    } catch (error) {
      console.error('🔴 MINIMAL: Error in change handler:', error);
    }
  };

  // ESC key handler - ONLY shows overlay
  const handleEscKey = (e) => {
    if (e.key === 'Escape' || e.keyCode === 27) {
      console.log('🔴 MINIMAL: ESC key detected');
      
      if (hasBeenInFullscreen && isActive && !exitingRef?.current) {
        // Small delay to let fullscreen exit complete
        setTimeout(() => {
          const stillFullscreen = isFullscreen();
          console.log('🔴 MINIMAL: After ESC - still fullscreen:', stillFullscreen);
          if (!stillFullscreen) {
            console.log('🔴 MINIMAL: ===== ESC CAUSED EXIT - SHOWING RED OVERLAY =====');
            console.log('🔴 MINIMAL: (SecurityManager will automatically detect and report the violation)');
            showRedOverlay();
          }
        }, 100);
      }
    }
  };

  // Setup event listeners
  try {
    const events = ['fullscreenchange', 'webkitfullscreenchange', 'mozfullscreenchange', 'MSFullscreenChange'];
    
    events.forEach(event => {
      document.addEventListener(event, handleChange, { passive: false });
    });

    document.addEventListener('keydown', handleEscKey, { passive: false });

    // Test functions (same as working version)
    if (typeof window !== 'undefined') {
      window.testRedOverlay = () => {
        console.log('🔴 MINIMAL: Manual test - showing red overlay');
        return showRedOverlay();
      };

      window.testExitFullscreen = () => {
        console.log('🔴 MINIMAL: Manual test - exiting fullscreen');
        try {
          if (document.exitFullscreen) {
            return document.exitFullscreen();
          } else if (document.mozCancelFullScreen) {
            return document.mozCancelFullScreen();
          } else if (document.webkitExitFullscreen) {
            return document.webkitExitFullscreen();
          } else if (document.msExitFullscreen) {
            return document.msExitFullscreen();
          }
        } catch (error) {
          console.error('🔴 MINIMAL: Error exiting fullscreen:', error);
        }
      };

      window.getMinimalOverlayStatus = () => {
        return {
          isActive,
          hasBeenInFullscreen,
          inFullscreen: isFullscreen(),
          overlayVisible: !!redOverlay
        };
      };
    }

    console.log('🔴 MINIMAL: Security system ready - waiting for user to enter fullscreen');

    // Cleanup function
    const cleanup = () => {
      try {
        console.log('🔴 MINIMAL: Cleaning up');
        isActive = false;
        hideRedOverlay();
        
        events.forEach(event => {
          document.removeEventListener(event, handleChange);
        });
        
        document.removeEventListener('keydown', handleEscKey);
        
        if (typeof window !== 'undefined') {
          delete window.testRedOverlay;
          delete window.testExitFullscreen;
          delete window.getMinimalOverlayStatus;
        }
      } catch (error) {
        console.error('🔴 MINIMAL: Error in cleanup:', error);
      }
    };

    cleanup.markEnding = () => {
      try {
        console.log('🔴 MINIMAL: Marking as ending');
        isActive = false;
        hideRedOverlay();
      } catch (error) {
        console.error('🔴 MINIMAL: Error marking ending:', error);
      }
    };

    console.log('🔴 MINIMAL: ===== SETUP COMPLETE - RED OVERLAY ONLY SYSTEM =====');
    return cleanup;

  } catch (error) {
    console.error('🔴 MINIMAL: Error setting up listeners:', error);
    return () => {};
  }
};

export const showInitialFullscreenPrompt = () => {
  console.log('🔴 MINIMAL: Showing initial fullscreen prompt');
  
  try {
    // Remove any existing prompts/overlays first
    const existingElements = document.querySelectorAll('[id*="fullscreen"], [id*="prompt"], [id*="blocker"]');
    existingElements.forEach(el => {
      try {
        el.remove();
      } catch (e) {}
    });
    
    const prompt = document.createElement('div');
    prompt.id = 'minimal-fullscreen-request-prompt';
    
    Object.assign(prompt.style, {
      position: 'fixed',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      background: 'rgba(0,0,255,0.95)',
      color: 'white',
      padding: '30px 40px',
      borderRadius: '10px',
      fontSize: '18px',
      textAlign: 'center',
      zIndex: '100000',
      boxShadow: '0 4px 20px rgba(0,0,0,0.5)'
    });
    
    prompt.innerHTML = `
      <div style="font-size: 24px; margin-bottom: 10px;">🎯 Interview Ready!</div>
      <div style="margin: 20px 0;">
        Click below to enter fullscreen mode and begin your interview
      </div>
      <button id="minimal-enter-fullscreen-btn" style="
        font-size: 16px;
        padding: 12px 24px;
        background: white;
        color: blue;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-weight: bold;
      ">Enter Fullscreen & Start Interview</button>
      <div style="font-size: 14px; margin-top: 15px; color: #E0E0E0;">
        This is required for interview security
      </div>
    `;
    
    document.body.appendChild(prompt);
    
    const button = document.getElementById('minimal-enter-fullscreen-btn');
    if (button) {
      button.addEventListener('click', async () => {
        try {
          const elem = document.documentElement;
          if (elem.requestFullscreen) {
            await elem.requestFullscreen();
          } else if (elem.mozRequestFullScreen) {
            await elem.mozRequestFullScreen();
          } else if (elem.webkitRequestFullscreen) {
            await elem.webkitRequestFullscreen();
          } else if (elem.msRequestFullscreen) {
            await elem.msRequestFullscreen();
          }
          prompt.remove();
          console.log('🔴 MINIMAL: User clicked to enter fullscreen - overlay system will activate');
        } catch (error) {
          console.error('Fullscreen request failed:', error);
          alert('Please allow fullscreen access to continue the interview.');
        }
      });
    }
  } catch (error) {
    console.error('Error showing fullscreen prompt:', error);
  }
};

export default setupFullscreenSecurity;
