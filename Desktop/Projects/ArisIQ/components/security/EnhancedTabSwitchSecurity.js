// File: components/security/EnhancedTabSwitchSecurity.js
// FOLLOWING SAME PATTERN AS FullscreenSecurity and CopyPasteSecurity
// This class handles tab switch DETECTION, SecurityManager handles VIOLATION REPORTING

export class EnhancedTabSwitchSecurity {
  constructor(options = {}) {
    this.exitingRef = options.exitingRef;
    this.securityManagerRef = options.securityManagerRef;
    this.trackPreSecurityViolation = options.trackPreSecurityViolation;
    this.currentQuestion = options.currentQuestion;
    this.isLoadingRef = options.isLoadingRef;
    
    // Detection state
    this.isSetupComplete = false;
    this.detectionEnabled = false;
    this.detectionStartTime = Date.now();
    this.activationDelay = 5000; // 5 seconds before detection starts
    
    // Smart deduplication - track what type of action is happening
    this.lastDetectionTime = 0;
    this.lastDetectionType = '';
    this.detectionCooldown = 2000; // 2 seconds minimum between ANY detections
    this.lastVisibilityState = document.visibilityState;
    
    // SMART BLUR DETECTION: Track tab switch vs window switch
    this.tabSwitchInProgress = false;
    this.tabSwitchTimeout = null;
    
    console.log('🔄 EnhancedTabSwitchSecurity initialized - following same pattern as other security classes');
    
    // Bind methods
    this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
    this.handleWindowBlur = this.handleWindowBlur.bind(this);
    this.handleKeyDown = this.handleKeyDown.bind(this);
    this.reportToSecurityManager = this.reportToSecurityManager.bind(this);
    this.setupTabSwitchDetection = this.setupTabSwitchDetection.bind(this);
    this.cleanup = this.cleanup.bind(this);
  }

  // Check if we should be active
  isSecurityActive() {
    // Don't run if exiting
    if (this.exitingRef && this.exitingRef.current) {
      return false;
    }
    
    // Don't run if still loading
    if (this.isLoadingRef && this.isLoadingRef.current) {
      return false;
    }
    
    // Don't run if setup not complete
    if (!this.isSetupComplete) {
      return false;
    }
    
    // Don't run if detection not enabled
    if (!this.detectionEnabled) {
      return false;
    }
    
    // Don't run if not enough time has passed
    if ((Date.now() - this.detectionStartTime) < this.activationDelay) {
      return false;
    }
    
    return true;
  }

  // Enhanced deduplication check - prevent multiple violations for same action
  shouldSkipDetection(detectionType) {
    var timeSinceLastDetection = Date.now() - this.lastDetectionTime;
    
    // If it's the same type of detection within cooldown period, skip
    if (this.lastDetectionType === detectionType && timeSinceLastDetection < this.detectionCooldown) {
      return true;
    }
    
    // If ANY detection happened recently (within shorter window), skip to prevent duplicates
    if (timeSinceLastDetection < 1000) { // 1 second window for any detection
      return true;
    }
    
    return false;
  }

  // Handle visibility change (main tab switch detection)
  handleVisibilityChange() {
    if (!this.isSecurityActive()) {
      return;
    }
    
    // Only process genuine visibility changes
    if (document.visibilityState !== this.lastVisibilityState) {
      this.lastVisibilityState = document.visibilityState;
      
      if (document.hidden) {
        console.log('👁️ DETECTION: Document hidden - tab switch detected');
        
        // SMART: Mark that a tab switch is in progress to ignore window blur
        this.tabSwitchInProgress = true;
        
        // Clear any existing timeout
        if (this.tabSwitchTimeout) {
          clearTimeout(this.tabSwitchTimeout);
        }
        
        // Enhanced deduplication check
        if (this.shouldSkipDetection('tabSwitch')) {
          console.log('🛡️ Tab switch detection skipped - recent detection already processed');
          return;
        }
        
        // Update detection tracking
        this.lastDetectionTime = Date.now();
        this.lastDetectionType = 'tabSwitch';
        
        // Report to SecurityManager
        this.reportToSecurityManager('tabSwitch', 'Browser tab switch detected (visibility change)');
        
        // Reset tab switch flag after a delay (enough time for window blur to be ignored)
        var self = this;
        this.tabSwitchTimeout = setTimeout(function() {
          self.tabSwitchInProgress = false;
          console.log('🔄 Tab switch period ended - window blur detection re-enabled');
        }, 1000); // 1 second should be enough
        
      } else {
        // Document became visible again - clear tab switch flag
        this.tabSwitchInProgress = false;
        if (this.tabSwitchTimeout) {
          clearTimeout(this.tabSwitchTimeout);
          this.tabSwitchTimeout = null;
        }
        console.log('🔄 Document visible again - tab switch ended');
      }
    }
  }

  // Handle window blur - COMPLETELY DISABLED due to false positives
  handleWindowBlur(e) {
    // DISABLED: Window blur causes too many false positives with tab switching
    // Even with smart detection, the timing is unreliable
    console.log('👁️ Window blur detected but COMPLETELY DISABLED (too many false positives)');
    console.log('📝 Note: New browser detection relies on keyboard shortcuts (Ctrl+N, Cmd+N)');
    return;
  }

  // Handle keyboard shortcuts
  handleKeyDown(e) {
    if (!this.isSecurityActive()) {
      return;
    }
    
    var blocked = false;
    var message = '';
    
    // Detect tab switching shortcuts
    if ((e.ctrlKey || e.metaKey) && (e.key === 'Tab' || e.keyCode === 9)) {
      blocked = true;
      message = 'Tab switch keyboard shortcut detected: ' + (e.metaKey ? 'Cmd+Tab' : 'Ctrl+Tab');
    }
    else if (e.altKey && (e.key === 'Tab' || e.keyCode === 9)) {
      blocked = true;
      message = 'Application switch detected: Alt+Tab';
    }
    else if ((e.ctrlKey || e.metaKey) && (e.key === 't' || e.key === 'T')) {
      blocked = true;
      message = 'New tab attempt detected: ' + (e.metaKey ? 'Cmd+T' : 'Ctrl+T');
    }
    else if ((e.ctrlKey || e.metaKey) && (e.key === 'n' || e.key === 'N')) {
      blocked = true;
      message = 'New browser window attempt detected: ' + (e.metaKey ? 'Cmd+N' : 'Ctrl+N');
    }
    // NEW: Detect Ctrl+Shift+N (incognito window)
    else if (e.ctrlKey && e.shiftKey && (e.key === 'n' || e.key === 'N')) {
      blocked = true;
      message = 'Incognito browser window attempt detected: Ctrl+Shift+N';
    }
    // NEW: Detect Command+Shift+N (Mac incognito window)
    else if (e.metaKey && e.shiftKey && (e.key === 'n' || e.key === 'N')) {
      blocked = true;
      message = 'Incognito browser window attempt detected: Command+Shift+N';
    }
    // NEW: Detect Ctrl+Shift+T (reopen closed tab)
    else if (e.ctrlKey && e.shiftKey && (e.key === 't' || e.key === 'T')) {
      blocked = true;
      message = 'Reopen closed tab attempt detected: Ctrl+Shift+T';
    }
    // NEW: Detect Command+Shift+T (Mac reopen closed tab)
    else if (e.metaKey && e.shiftKey && (e.key === 't' || e.key === 'T')) {
      blocked = true;
      message = 'Reopen closed tab attempt detected: Command+Shift+T';
    }
    
    if (blocked) {
      console.log('⚠️ DETECTION: ' + message);
      
      // Try to prevent the action (same as CopyPasteSecurity pattern)
      try {
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
      } catch (error) {
        console.log('Could not prevent key event (expected for system shortcuts)');
      }
      
      // Basic deduplication check
      if (!this.shouldSkipDetection('keyboardShortcut')) {
        this.lastDetectionTime = Date.now();
        this.lastDetectionType = 'keyboardShortcut';
        
        // Report to SecurityManager (same pattern as other security classes)
        this.reportToSecurityManager('tabSwitch', message);
      } else {
        console.log('🛡️ Keyboard shortcut detection skipped - recent detection already processed');
      }
      
      return false;
    }
  }

  // Report to SecurityManager (following same pattern as FullscreenSecurity and CopyPasteSecurity)
  reportToSecurityManager(violationType, message) {
    console.log('🚨 REPORTING TO SECURITY MANAGER: ' + violationType + ' - ' + message);
    
    try {
      if (this.securityManagerRef && this.securityManagerRef.current && this.securityManagerRef.current.isInterviewActive) {
        // Same pattern as CopyPasteSecurity - try different violation handler methods
        if (typeof this.securityManagerRef.current.handleWarningViolation === 'function') {
          this.securityManagerRef.current.handleWarningViolation(violationType, message);
          console.log('✅ Violation reported to SecurityManager via handleWarningViolation');
        } else if (typeof this.securityManagerRef.current.handleViolation === 'function') {
          this.securityManagerRef.current.handleViolation(violationType, message);
          console.log('✅ Violation reported to SecurityManager via handleViolation');
        } else {
          console.log('❌ No violation handler found in SecurityManager');
        }
      } else if (this.trackPreSecurityViolation) {
        // Same pattern as other security classes - track violation even if SecurityManager isn't active yet
        this.trackPreSecurityViolation(violationType, message + ' (Question ' + ((this.currentQuestion || 0) + 1) + ')');
        console.log('✅ Violation tracked via trackPreSecurityViolation');
      } else {
        console.log('❌ No SecurityManager or tracking function available');
      }
    } catch (error) {
      console.error('❌ Error reporting violation to SecurityManager:', error);
    }
  }

  // Setup detection (following same pattern as other security classes)
  setupTabSwitchDetection() {
    console.log('🔄 Setting up Tab Switch Detection - following same pattern as other security classes...');
    
    // Mark setup as starting
    this.isSetupComplete = false;
    
    try {
      // Add event listeners - RE-ENABLED SMART window blur detection
      document.addEventListener('visibilitychange', this.handleVisibilityChange, { 
        capture: true, 
        passive: true 
      });

      // SMART: Window blur detection that ignores tab switch blur
      window.addEventListener('blur', this.handleWindowBlur, {
        capture: true,
        passive: true
      });

      window.addEventListener('keydown', this.handleKeyDown, { 
        capture: true, 
        passive: false 
      });
      
      // Mark setup as complete
      this.isSetupComplete = true;
      console.log('✅ Tab switch event listeners attached');
      
      // Enable detection after delay (same pattern as other security classes)
      var self = this;
      setTimeout(function() {
        self.detectionEnabled = true;
        console.log('✅ Tab Switch Detection ENABLED - will report to SecurityManager');
      }, this.activationDelay);
      
      console.log('✅ Tab Switch Detection setup complete - following same pattern as FullscreenSecurity and CopyPasteSecurity');
      
      // Return cleanup function (same pattern as other security classes)
      return function() {
        self.cleanup();
      };
    } catch (error) {
      console.error('❌ Error setting up tab switch detection:', error);
      return function() {}; // Return empty cleanup function
    }
  }

  // Update options (same pattern as CopyPasteSecurity)
  updateOptions(newOptions) {
    if (newOptions) {
      for (var key in newOptions) {
        if (newOptions.hasOwnProperty(key)) {
          this[key] = newOptions[key];
        }
      }
    }
    console.log('🔄 Tab switch options updated:', newOptions);
  }

  // Get current status (same pattern as other security classes)
  getStatus() {
    return {
      isActive: this.isSecurityActive(),
      isSetupComplete: this.isSetupComplete,
      detectionEnabled: this.detectionEnabled,
      timeSinceSetup: Date.now() - this.detectionStartTime,
      activationDelay: this.activationDelay,
      lastDetectionTime: this.lastDetectionTime
    };
  }

  // Cleanup method (same pattern as CopyPasteSecurity)
  cleanup() {
    console.log('🧹 Tab switch security cleanup initiated');
    
    try {
      // Disable detection
      this.detectionEnabled = false;
      this.isSetupComplete = false;
      
      // Remove event listeners - RE-ENABLED window blur cleanup
      document.removeEventListener('visibilitychange', this.handleVisibilityChange, { capture: true });
      window.removeEventListener('blur', this.handleWindowBlur, { capture: true });
      window.removeEventListener('keydown', this.handleKeyDown, { capture: true });
      
      // Clear any pending timeouts
      if (this.tabSwitchTimeout) {
        clearTimeout(this.tabSwitchTimeout);
        this.tabSwitchTimeout = null;
      }
      
      // Reset detection state
      this.lastDetectionTime = 0;
      this.lastDetectionType = '';
      this.lastVisibilityState = document.visibilityState;
      this.tabSwitchInProgress = false;
      
      // Clear any pending timeouts
      if (this.tabSwitchTimeout) {
        clearTimeout(this.tabSwitchTimeout);
        this.tabSwitchTimeout = null;
      }
      
      console.log('✅ Tab switch security cleanup completed');
    } catch (error) {
      console.error('❌ Error during tab switch cleanup:', error);
    }
  }
}

export default EnhancedTabSwitchSecurity;
