// File: components/security/DevToolsSecurity.js
// COMPLETE FIXED VERSION: Space bar completely unrestricted

export class DevToolsSecurity {
  constructor(options = {}) {
    this.exitingRef = options.exitingRef;
    this.securityManagerRef = options.securityManagerRef;
    
    // Simple state
    this.devToolsState = {
      isOpen: false,
      blockerElement: null,
      checkInterval: null,
      detectionStarted: false
    };
    
    // Bind methods
    this.setupDevToolsDetection = this.setupDevToolsDetection.bind(this);
    this.checkDevTools = this.checkDevTools.bind(this);
    this.showDevToolsBlocker = this.showDevToolsBlocker.bind(this);
    this.hideDevToolsBlocker = this.hideDevToolsBlocker.bind(this);
    this.handleKeyboardShortcuts = this.handleKeyboardShortcuts.bind(this);
    this.reportViolation = this.reportViolation.bind(this);
  }

  // Check if security is active
  isSecurityActive() {
    return !this.exitingRef?.current;
  }

  // Report violation to SecurityManager (simplified)
  reportViolation(reason) {
    try {
      if (this.securityManagerRef?.current?.isInterviewActive) {
        this.securityManagerRef.current.handleWarningViolation('devTools', reason);
      }
      console.log(`🚨 DevTools violation: ${reason}`);
    } catch (error) {
      console.error('Error reporting DevTools violation:', error);
    }
  }

  // ENHANCED: DevTools detection using multiple methods
  checkDevTools() {
    if (!this.isSecurityActive() || !this.devToolsState.detectionStarted) return;
    
    try {
      let devToolsDetected = false;
      
      // Method 1: Window size difference (most reliable)
      const threshold = 160;
      const widthDiff = window.outerWidth - window.innerWidth;
      const heightDiff = window.outerHeight - window.innerHeight;
      
      if (widthDiff > threshold || heightDiff > threshold) {
        devToolsDetected = true;
      }
      
      // Method 2: Check for specific DevTools properties
      if (window.devtools && window.devtools.open) {
        devToolsDetected = true;
      }
      
      // Method 3: Console detection (basic)
      if (typeof window.console !== 'undefined' && window.console.clear) {
        const start = performance.now();
        console.clear();
        const duration = performance.now() - start;
        if (duration > 5) {
          devToolsDetected = true;
        }
      }
      
      // Handle detection state change
      if (devToolsDetected && !this.devToolsState.isOpen) {
        console.log('🚨 DevTools detected - showing blocker');
        this.devToolsState.isOpen = true;
        this.showDevToolsBlocker();
        this.reportViolation('Developer tools opened');
      } else if (!devToolsDetected && this.devToolsState.isOpen) {
        console.log('✅ DevTools closed - hiding blocker');
        this.devToolsState.isOpen = false;
        this.hideDevToolsBlocker();
      }
    } catch (error) {
      console.error('Error in DevTools check:', error);
    }
  }

  // FIXED: Show blocker that COMPLETELY PAUSES the interview
  showDevToolsBlocker() {
    try {
      console.log('🚫 Showing DevTools blocker overlay');
      
      // Remove any existing blocker first
      this.hideDevToolsBlocker();
      
      // Create blocker element that matches your screenshot EXACTLY
      const blocker = document.createElement('div');
      blocker.id = 'devtools-blocker';
      blocker.style.cssText = `
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background: rgba(255,0,0,0.95) !important;
        color: white !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        z-index: 2147483647 !important;
        font-size: 24px !important;
        text-align: center !important;
        pointer-events: all !important;
        font-family: Arial, sans-serif !important;
        user-select: none !important;
      `;
      
      // EXACTLY match your old working screen content
      blocker.innerHTML = `
        <div style="font-size: 48px; margin-bottom: 30px; font-weight: bold;">🛠️ DEVELOPER TOOLS DETECTED 🛠️</div>
        <div style="font-size: 28px; margin-bottom: 20px; font-weight: bold;">Interview Paused</div>
        <div style="font-size: 20px; margin-bottom: 20px;">You have opened Developer Tools (F12)</div>
        <div style="font-size: 18px; margin-bottom: 30px; color: #ffcccc; max-width: 600px; line-height: 1.4;">
          Using developer tools during the interview is strictly prohibited.<br/>
          This security violation has been recorded.<br/>
          Please close the developer tools to continue.
        </div>
        <div style="font-size: 16px; margin-top: 20px; color: #ffcccc;">
          The interview will resume automatically when developer tools are closed
        </div>
        <div style="font-size: 14px; margin-top: 15px; color: #ffdddd;">
          F12, Ctrl+Shift+I, and right-click inspect are disabled during the interview
        </div>
      `;
      
      document.body.appendChild(blocker);
      this.devToolsState.blockerElement = blocker;
      
      console.log('✅ DevTools blocker overlay created - interview PAUSED');
      
    } catch (error) {
      console.error('Error showing DevTools blocker:', error);
    }
  }

  // Hide DevTools blocker overlay
  hideDevToolsBlocker() {
    try {
      if (this.devToolsState.blockerElement) {
        this.devToolsState.blockerElement.remove();
        this.devToolsState.blockerElement = null;
        console.log('✅ DevTools blocker removed - interview RESUMED');
      }
      
      // Remove any other DevTools blockers
      const existingBlockers = document.querySelectorAll('#devtools-blocker');
      existingBlockers.forEach(blocker => {
        blocker.remove();
        console.log('🧹 Removed stray DevTools blocker');
      });
      
    } catch (error) {
      console.error('Error hiding DevTools blocker:', error);
    }
  }

  // COMPLETELY FIXED: Handle F12 and DevTools shortcuts - SPACE BAR UNRESTRICTED
  handleKeyboardShortcuts(e) {
    if (!this.isSecurityActive()) return;
    
    try {
      let blocked = false;
      let reason = '';
      
      // ✅ CRITICAL FIX: IMMEDIATELY ALLOW SPACE BAR AND ALL NORMAL TYPING
      if (e.key === ' ' || e.key === 'Spacebar' || e.code === 'Space') {
        return; // ALWAYS allow space bar - NO PROCESSING
      }
      
      // ✅ ALLOW ALL NORMAL TYPING KEYS (letters, numbers, punctuation)
      const normalTypingKeys = /^[a-zA-Z0-9`~!@#$%^&*()_+\-=\[\]\\{}|;':",./<>?]$/;
      if (normalTypingKeys.test(e.key) && !e.ctrlKey && !e.metaKey && !e.altKey) {
        return; // Allow normal typing
      }
      
      // ✅ ALLOW COMMON EDITING KEYS
      const editingKeys = [
        'Backspace', 'Delete', 'Enter', 'Tab', 'ArrowUp', 'ArrowDown', 
        'ArrowLeft', 'ArrowRight', 'Home', 'End', 'PageUp', 'PageDown',
        'Insert', 'CapsLock', 'NumLock', 'ScrollLock', 'Pause',
        'ContextMenu', 'Meta', 'Control', 'Alt', 'Shift'
      ];
      
      if (editingKeys.includes(e.key)) {
        return; // Allow editing keys
      }
      
      // NOW check ONLY for specific DevTools shortcuts
      
      // F12 key
      if (e.key === 'F12') {
        blocked = true;
        reason = 'F12 key pressed';
      }
      
      // Ctrl+Shift+I (Windows/Linux)
      else if (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'i')) {
        blocked = true;
        reason = 'Ctrl+Shift+I pressed';
      }
      
      // Cmd+Option+I (Mac)
      else if (e.metaKey && e.altKey && (e.key === 'I' || e.key === 'i')) {
        blocked = true;
        reason = 'Cmd+Option+I pressed';
      }
      
      // Ctrl+Shift+J (Console)
      else if (e.ctrlKey && e.shiftKey && (e.key === 'J' || e.key === 'j')) {
        blocked = true;
        reason = 'Ctrl+Shift+J pressed';
      }
      
      // Cmd+Option+J (Mac Console)
      else if (e.metaKey && e.altKey && (e.key === 'J' || e.key === 'j')) {
        blocked = true;
        reason = 'Cmd+Option+J pressed';
      }
      
      // Ctrl+Shift+C (Inspect Element)
      else if (e.ctrlKey && e.shiftKey && (e.key === 'C' || e.key === 'c')) {
        blocked = true;
        reason = 'Ctrl+Shift+C pressed';
      }
      
      // Cmd+Option+C (Mac Inspect)
      else if (e.metaKey && e.altKey && (e.key === 'C' || e.key === 'c')) {
        blocked = true;
        reason = 'Cmd+Option+C pressed';
      }
      
      if (blocked) {
        console.log(`🚫 DevTools shortcut blocked: ${reason}`);
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
        
        // Show blocker immediately and report violation
        this.devToolsState.isOpen = true;
        this.showDevToolsBlocker();
        this.reportViolation(reason);
        
        return false;
      }
      
      // ✅ All other keys are ALLOWED (including space bar)
      return;
    } catch (error) {
      console.error('Error handling keyboard shortcut:', error);
      // Only prevent actual DevTools shortcuts on error
      if (e.key === 'F12' || 
          (e.ctrlKey && e.shiftKey && ['i', 'j', 'c'].includes(e.key.toLowerCase())) ||
          (e.metaKey && e.altKey && ['i', 'j', 'c'].includes(e.key.toLowerCase()))) {
        e.preventDefault();
        this.reportViolation('DevTools shortcut error');
      }
    }
  }

  // ENHANCED: Setup DevTools detection with immediate start
  setupDevToolsDetection() {
    console.log('🛠️ Setting up enhanced DevTools detection...');
    
    try {
      // Mark detection as started
      this.devToolsState.detectionStarted = true;
      
      // Start checking immediately and every 500ms (faster detection)
      this.devToolsState.checkInterval = setInterval(() => {
        this.checkDevTools();
      }, 500);
      
      // Block keyboard shortcuts with highest priority
      document.addEventListener('keydown', this.handleKeyboardShortcuts, { 
        capture: true, 
        passive: false 
      });
      
      window.addEventListener('keydown', this.handleKeyboardShortcuts, { 
        capture: true, 
        passive: false 
      });
      
      // Block right-click context menu
      const blockContextMenu = (e) => {
        if (this.isSecurityActive()) {
          console.log('🚫 Right-click blocked (DevTools protection)');
          e.preventDefault();
          e.stopPropagation();
          return false;
        }
      };
      
      document.addEventListener('contextmenu', blockContextMenu, { 
        capture: true, 
        passive: false 
      });
      
      window.addEventListener('contextmenu', blockContextMenu, { 
        capture: true, 
        passive: false 
      });
      
      // Do immediate check after small delay
      setTimeout(() => {
        this.checkDevTools();
      }, 100);
      
      // Additional check after 1 second
      setTimeout(() => {
        this.checkDevTools();
      }, 1000);
      
      console.log('✅ Enhanced DevTools detection initialized - SPACE BAR UNRESTRICTED');
      
      // Return cleanup function
      return () => {
        console.log('🧹 Cleaning up DevTools detection...');
        
        this.devToolsState.detectionStarted = false;
        
        if (this.devToolsState.checkInterval) {
          clearInterval(this.devToolsState.checkInterval);
          this.devToolsState.checkInterval = null;
        }
        
        document.removeEventListener('keydown', this.handleKeyboardShortcuts, { capture: true });
        window.removeEventListener('keydown', this.handleKeyboardShortcuts, { capture: true });
        document.removeEventListener('contextmenu', blockContextMenu, { capture: true });
        window.removeEventListener('contextmenu', blockContextMenu, { capture: true });
        
        this.hideDevToolsBlocker();
        
        console.log('✅ DevTools detection cleanup completed');
      };
      
    } catch (error) {
      console.error('Error setting up DevTools detection:', error);
      return () => {}; // Return empty cleanup function
    }
  }

  // Get detection status
  getDetectionStatus() {
    return {
      isOpen: this.devToolsState.isOpen,
      blockerVisible: !!this.devToolsState.blockerElement,
      detectionActive: this.devToolsState.detectionStarted
    };
  }

  // Manual check method (can be called externally)
  forceCheck() {
    console.log('🔍 Manual DevTools check requested');
    this.checkDevTools();
  }

  // Cleanup method
  cleanup() {
    console.log('🧹 DevToolsSecurity cleanup initiated');
    
    this.devToolsState.detectionStarted = false;
    this.hideDevToolsBlocker();
    
    if (this.devToolsState.checkInterval) {
      clearInterval(this.devToolsState.checkInterval);
      this.devToolsState.checkInterval = null;
    }
    
    this.devToolsState = {
      isOpen: false,
      blockerElement: null,
      checkInterval: null,
      detectionStarted: false
    };
    
    console.log('✅ DevToolsSecurity cleanup completed');
  }
}

export default DevToolsSecurity;
