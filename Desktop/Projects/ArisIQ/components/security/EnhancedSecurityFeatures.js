// File: components/security/EnhancedSecurityFeatures.js
// Integration of advanced features from security-manager.js into modular system

export class EnhancedSecurityFeatures {
  constructor(options = {}) {
    this.exitingRef = options.exitingRef;
    this.securityManagerRef = options.securityManagerRef;
    this.trackPreSecurityViolation = options.trackPreSecurityViolation;
    this.currentQuestion = options.currentQuestion;
    this.showRequiredAcknowledgmentWarning = options.showRequiredAcknowledgmentWarning;
    
    // Enhanced violation thresholds
    this.VIOLATION_THRESHOLDS = {
      fullscreenExit: 5,
      tabSwitch: 5,
      windowBlur: 5,
      devTools: 5,
      keyboardShortcut: 5,
      rightClick: 5,
      multipleFaces: 5,
      faceNotVisible: 5,
      escapeKey: 5,
      lookingAway: 5,
      screenSharing: 5,
      screenRecording: 5,
      dualMonitor: 5,
      screenshot: 5,
      differentPerson: 5,
      suspiciousTyping: 5,
      contentPasted: 5,
      faceSwitch: 5,
      handsRaised: 5,
      handsOutOfFrame: 5,
      suspiciousHandGesture: 5,
      deviceDetected: 3,        // Stricter for device detection
      phoneDetected: 3,         // Stricter for phone detection  
      phoneGesture: 3           // Stricter for phone gestures
    };
    
    // Face detection state
    this.faceDetectionState = {
      faceNotVisibleCount: 0,
      faceNotVisibleThreshold: 1,
      lookingAwayCount: 0,
      lookingAwayThreshold: 2,
      lastFaceCheck: null,
      FACE_NOT_VISIBLE_TIME_THRESHOLD: 10000,
      LOOKING_AWAY_TIME_THRESHOLD: 15000,
      initialFaceDescriptor: null,
      faceDescriptorHistory: [],
      consecutiveFaceSwitches: 0,
      lastFaceSwitchTime: null,
      faceSwitchThreshold: 3,
      deviceDetectionState: {
        consecutiveDeviceDetections: 0,
        lastDeviceDetectionTime: null,
        deviceDetectionThreshold: 3,
        phoneConfidenceHistory: [],
        combinedConfidenceThreshold: 0.6,
        lastPhoneViolationTime: 0,
        phoneViolationCooldown: 15000,
        gestureConsistencyCount: 0,
        visualDeviceCount: 0
      }
    };
    
    // Screenshot tracking
    this.potentialScreenshotAttempt = {
      inProgress: false,
      method: null,
      timestamp: null,
      timeoutId: null
    };
    
    this.lastClipboardCheck = Date.now();
    this.lastImageCheckTime = Date.now();
    
    // Bind methods
    this.handleFaceDetectionWithDevices = this.handleFaceDetectionWithDevices.bind(this);
    this.handleDeviceDetection = this.handleDeviceDetection.bind(this);
    this.detectFaceSwitch = this.detectFaceSwitch.bind(this);
    this.compareFaceDescriptors = this.compareFaceDescriptors.bind(this);
    this.setupAdvancedScreenshotDetection = this.setupAdvancedScreenshotDetection.bind(this);
    this.checkForScreenshotEvidence = this.checkForScreenshotEvidence.bind(this);
    this.setupImageCaptureMonitoring = this.setupImageCaptureMonitoring.bind(this);
    this.setupPermissionMonitoring = this.setupPermissionMonitoring.bind(this);
    this.checkClipboardForImages = this.checkClipboardForImages.bind(this);
    this.enforceFullscreenModeAdvanced = this.enforceFullscreenModeAdvanced.bind(this);
  }

  // Check if security is active
  isSecurityActive() {
    return !this.exitingRef?.current;
  }

  // Enhanced face detection with comprehensive device analysis
  handleFaceDetectionWithDevices(faceData) {
    if (!this.isSecurityActive()) return;
    
    console.log('👁️ Enhanced face detection with device analysis:', faceData);
    
    const now = Date.now();
    this.faceDetectionState.lastFaceCheck = now;
    
    // Basic face detection logic
    if (faceData.facesDetected === 0) {
      this.faceDetectionState.faceNotVisibleCount++;
      
      if (this.faceDetectionState.faceNotVisibleCount >= this.faceDetectionState.faceNotVisibleThreshold) {
        this.reportViolation('faceNotVisible', 'Face not visible to camera');
        this.faceDetectionState.faceNotVisibleCount = 0;
      }
    } else {
      this.faceDetectionState.faceNotVisibleCount = 0;
    }
    
    // Multiple faces
    if (faceData.facesDetected > 1) {
      this.reportViolation('multipleFaces', `${faceData.facesDetected} faces detected`);
    }
    
    // Looking away detection
    if (faceData.lookingAway) {
      this.faceDetectionState.lookingAwayCount++;
      
      if (this.faceDetectionState.lookingAwayCount >= this.faceDetectionState.lookingAwayThreshold) {
        this.reportViolation('lookingAway', 'Looking away from camera');
        this.faceDetectionState.lookingAwayCount = 0;
      }
    } else {
      this.faceDetectionState.lookingAwayCount = 0;
    }
    
    // Face switching detection
    if (faceData.facesDetected === 1 && faceData.faceDescriptor) {
      this.detectFaceSwitch(faceData.faceDescriptor, faceData.faceConfidence);
    }
    
    // Different person detection
    if (faceData.differentPerson) {
      this.reportViolation('differentPerson', 'Different person detected');
    }
    
    // Enhanced device detection
    this.handleDeviceDetection(faceData, now);
  }

  // Comprehensive device detection handler
  handleDeviceDetection(data, currentTime) {
    try {
      const deviceState = this.faceDetectionState.deviceDetectionState;
      
      console.log('📱 Processing enhanced device detection:', {
        phoneConfidence: data.phoneConfidence || 0,
        combinedConfidence: data.combinedPhoneConfidence || 0,
        phoneGesture: data.phoneGesture || false,
        deviceDetected: data.deviceDetected || false,
        handsDetected: data.handsDetected || 0,
        method: data.detectionMethod || 'none'
      });
      
      // 1. Phone confidence tracking
      const phoneConfidence = data.phoneConfidence || 0;
      const combinedConfidence = data.combinedPhoneConfidence || 0;
      
      deviceState.phoneConfidenceHistory.push({
        phoneConfidence,
        combinedConfidence,
        timestamp: currentTime
      });
      
      // Keep only last 10 seconds of history
      const tenSecondsAgo = currentTime - 10000;
      deviceState.phoneConfidenceHistory = deviceState.phoneConfidenceHistory.filter(
        entry => entry.timestamp > tenSecondsAgo
      );
      
      // 2. High confidence device detection
      if (combinedConfidence > deviceState.combinedConfidenceThreshold) {
        deviceState.consecutiveDeviceDetections++;
        
        console.log(`📱 High confidence device detection (${deviceState.consecutiveDeviceDetections}/${deviceState.deviceDetectionThreshold})`);
        
        if (deviceState.consecutiveDeviceDetections >= deviceState.deviceDetectionThreshold) {
          const timeSinceLastViolation = currentTime - deviceState.lastPhoneViolationTime;
          
          if (timeSinceLastViolation > deviceState.phoneViolationCooldown) {
            this.reportViolation('deviceDetected', 
              `Phone/device detected with ${Math.round(combinedConfidence * 100)}% confidence`);
            
            deviceState.lastPhoneViolationTime = currentTime;
            deviceState.consecutiveDeviceDetections = 0;
          }
        }
      } else {
        deviceState.consecutiveDeviceDetections = Math.max(0, deviceState.consecutiveDeviceDetections - 1);
      }
      
      // 3. Phone gesture detection
      if (data.phoneGesture) {
        deviceState.gestureConsistencyCount++;
        
        console.log(`📞 Phone gesture detected (${deviceState.gestureConsistencyCount} times)`);
        
        const timeSinceLastViolation = currentTime - deviceState.lastPhoneViolationTime;
        if (timeSinceLastViolation > deviceState.phoneViolationCooldown) {
          this.reportViolation('phoneGesture', 'Phone gesture detected - device usage prohibited');
          deviceState.lastPhoneViolationTime = currentTime;
          deviceState.gestureConsistencyCount = 0;
        }
      }
      
      // 4. Visual device detection
      if (data.devicesDetected && data.devicesDetected.length > 0) {
        const highConfidenceDevices = data.devicesDetected.filter(device => device.confidence > 0.7);
        
        if (highConfidenceDevices.length > 0) {
          deviceState.visualDeviceCount++;
          
          const timeSinceLastViolation = currentTime - deviceState.lastPhoneViolationTime;
          if (timeSinceLastViolation > deviceState.phoneViolationCooldown) {
            this.reportViolation('deviceDetected', 
              `Visual device detection: ${highConfidenceDevices.length} device(s) with high confidence`);
            
            deviceState.lastPhoneViolationTime = currentTime;
          }
        }
      }
      
      // 5. Hand gesture analysis
      if (data.suspiciousHandGesture) {
        console.log('🤏 Suspicious hand gesture detected');
        
        const timeSinceLastViolation = currentTime - deviceState.lastPhoneViolationTime;
        if (timeSinceLastViolation > deviceState.phoneViolationCooldown) {
          this.reportViolation('suspiciousHandGesture', 'Suspicious hand gesture suggesting device usage');
          deviceState.lastPhoneViolationTime = currentTime;
        }
      }
      
      // 6. Temporal analysis
      if (deviceState.phoneConfidenceHistory.length >= 5) {
        const recentAverage = deviceState.phoneConfidenceHistory
          .slice(-5)
          .reduce((sum, entry) => sum + entry.combinedConfidence, 0) / 5;
        
        if (recentAverage > 0.4) {
          console.log(`📊 Sustained device detection: ${Math.round(recentAverage * 100)}% average confidence`);
          
          const timeSinceLastViolation = currentTime - deviceState.lastPhoneViolationTime;
          if (timeSinceLastViolation > deviceState.phoneViolationCooldown * 2) {
            this.reportViolation('deviceDetected', 
              `Sustained device usage: ${Math.round(recentAverage * 100)}% average confidence over time`);
            
            deviceState.lastPhoneViolationTime = currentTime;
          }
        }
      }
      
    } catch (error) {
      console.error('Error in enhanced device detection:', error);
    }
  }

  // Face switching detection using descriptors
  detectFaceSwitch(currentDescriptor, confidence) {
    try {
      if (confidence < 0.7) {
        console.log('👁️ Face confidence too low for switching detection:', confidence);
        return;
      }
      
      if (!this.faceDetectionState.initialFaceDescriptor) {
        this.faceDetectionState.initialFaceDescriptor = currentDescriptor;
        this.faceDetectionState.faceDescriptorHistory = [currentDescriptor];
        console.log('👁️ Initial face descriptor stored');
        return;
      }
      
      const similarity = this.compareFaceDescriptors(
        this.faceDetectionState.initialFaceDescriptor, 
        currentDescriptor
      );
      
      console.log('👁️ Face similarity to initial:', similarity);
      
      const SIMILARITY_THRESHOLD = 0.6;
      
      if (similarity < SIMILARITY_THRESHOLD) {
        this.faceDetectionState.consecutiveFaceSwitches++;
        
        console.log(`👁️ Potential face switch detected (${this.faceDetectionState.consecutiveFaceSwitches}/${this.faceDetectionState.faceSwitchThreshold})`);
        
        if (this.faceDetectionState.consecutiveFaceSwitches >= this.faceDetectionState.faceSwitchThreshold) {
          const now = Date.now();
          
          if (!this.faceDetectionState.lastFaceSwitchTime || 
              (now - this.faceDetectionState.lastFaceSwitchTime) > 10000) {
            
            this.reportViolation('faceSwitch', 
              'Face switching detected - different person may be taking the interview');
            
            this.faceDetectionState.lastFaceSwitchTime = now;
          }
          
          this.faceDetectionState.consecutiveFaceSwitches = 0;
        }
      } else {
        this.faceDetectionState.consecutiveFaceSwitches = 0;
      }
      
      this.faceDetectionState.faceDescriptorHistory.push(currentDescriptor);
      if (this.faceDetectionState.faceDescriptorHistory.length > 10) {
        this.faceDetectionState.faceDescriptorHistory.shift();
      }
      
    } catch (error) {
      console.error('Error in face switch detection:', error);
    }
  }

  // Compare face descriptors using cosine similarity
  compareFaceDescriptors(descriptor1, descriptor2) {
    try {
      let vec1, vec2;
      
      if (Array.isArray(descriptor1)) {
        vec1 = descriptor1;
      } else if (descriptor1.descriptor && Array.isArray(descriptor1.descriptor)) {
        vec1 = descriptor1.descriptor;
      } else {
        return 0.5;
      }
      
      if (Array.isArray(descriptor2)) {
        vec2 = descriptor2;
      } else if (descriptor2.descriptor && Array.isArray(descriptor2.descriptor)) {
        vec2 = descriptor2.descriptor;
      } else {
        return 0.5;
      }
      
      if (vec1.length !== vec2.length) {
        return 0.5;
      }
      
      let dotProduct = 0;
      let norm1 = 0;
      let norm2 = 0;
      
      for (let i = 0; i < vec1.length; i++) {
        dotProduct += vec1[i] * vec2[i];
        norm1 += vec1[i] * vec1[i];
        norm2 += vec2[i] * vec2[i];
      }
      
      norm1 = Math.sqrt(norm1);
      norm2 = Math.sqrt(norm2);
      
      if (norm1 === 0 || norm2 === 0) {
        return 0;
      }
      
      const similarity = dotProduct / (norm1 * norm2);
      return (similarity + 1) / 2;
      
    } catch (error) {
      console.error('Error comparing face descriptors:', error);
      return 0.5;
    }
  }

  // Enhanced screenshot detection
  setupAdvancedScreenshotDetection() {
    console.log('🔒 Setting up advanced screenshot detection...');
    
    const cleanupFunctions = [];
    let lastImageDetectionTime = Date.now();
    let lastKeyCombination = null;
    let lastKeyPressTime = Date.now();
    
    const setupKeyboardTracking = () => {
      let keysPressed = new Set();
      
      const handleKeyDown = (e) => {
        if (!this.isSecurityActive()) return;
        
        console.log(`Advanced key down: ${e.key}, Meta: ${e.metaKey}, Shift: ${e.shiftKey}`);
        
        keysPressed.add(e.key);
        if (e.code) keysPressed.add(e.code);
        
        if (e.metaKey || e.key === 'Meta') {
          keysPressed.add('Meta');
          keysPressed.add('Command');
        }
        if (e.shiftKey || e.key === 'Shift') keysPressed.add('Shift');
        if (e.altKey || e.key === 'Alt') keysPressed.add('Alt');
        if (e.ctrlKey || e.key === 'Control') keysPressed.add('Control');
        
        // Direct check for Mac screenshot shortcuts
        if (e.metaKey && e.shiftKey && (e.key === '3' || e.key === '4' || e.key === '5')) {
          console.log(`🚨 Advanced detection of Mac screenshot: Command+Shift+${e.key}`);
          this.reportViolation('screenshot', `Screenshot detected: Command+Shift+${e.key}`);
        }
        
        // Check for Windows PrintScreen
        if (e.key === 'PrintScreen' || e.code === 'PrintScreen') {
          console.log('🚨 Advanced detected Windows screenshot: PrintScreen');
          this.reportViolation('screenshot', 'Screenshot detected: PrintScreen');
        }
        
        // Record key combination for verification
        if ((e.metaKey && e.shiftKey && ['3', '4', '5'].includes(e.key)) || 
            e.key === 'PrintScreen' || e.code === 'PrintScreen' ||
            (e.metaKey && e.shiftKey && (e.key === 's' || e.key === 'S'))) {
              
          let keyCombo = '';
          if (e.metaKey) keyCombo += 'Command+';
          if (e.shiftKey) keyCombo += 'Shift+';
          if (e.altKey) keyCombo += 'Alt+';
          keyCombo += e.key;
          
          lastKeyCombination = keyCombo;
          lastKeyPressTime = Date.now();
          
          console.log(`🔑 Advanced potential screenshot key: ${keyCombo}`);
          
          setTimeout(() => {
            this.checkForScreenshotEvidence(keyCombo);
          }, 300);
        }
      };
      
      const handleKeyUp = (e) => {
        keysPressed.delete(e.key);
        if (e.code) keysPressed.delete(e.code);
        
        if (e.key === 'Meta' || e.key === 'Command') {
          keysPressed.delete('Meta');
          keysPressed.delete('Command');
        }
        if (e.key === 'Shift') keysPressed.delete('Shift');
        if (e.key === 'Alt') keysPressed.delete('Alt');
        if (e.key === 'Control') keysPressed.delete('Control');
      };
      
      document.addEventListener('keydown', handleKeyDown, { capture: true, passive: false });
      cleanupFunctions.push(() => {
        document.removeEventListener('keydown', handleKeyDown, { capture: true });
      });
      
      document.addEventListener('keyup', handleKeyUp, { capture: true });
      cleanupFunctions.push(() => {
        document.removeEventListener('keyup', handleKeyUp, { capture: true });
      });
      
      window.addEventListener('keydown', handleKeyDown, { capture: true, passive: false });
      cleanupFunctions.push(() => {
        window.removeEventListener('keydown', handleKeyDown, { capture: true });
      });
      
      window.addEventListener('keyup', handleKeyUp, { capture: true });
      cleanupFunctions.push(() => {
        window.removeEventListener('keyup', handleKeyUp, { capture: true });
      });
    };
    
    setupKeyboardTracking();
    
    console.log('✅ Advanced screenshot detection initialized');
    
    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }

  // Check for screenshot evidence after key combination
  checkForScreenshotEvidence(keyCombo) {
    console.log(`🔍 Advanced checking for evidence of screenshot after ${keyCombo}...`);
    
    if (!this.isSecurityActive()) {
      return;
    }
    
    this.checkClipboardForImages().then(foundImage => {
      if (foundImage) {
        console.log('🖼️ Advanced found image in clipboard - confirming screenshot');
        this.reportViolation('screenshot', 
          `Screenshot detected: ${keyCombo} (confirmed by clipboard image)`);
      } else {
        console.log('📸 Advanced reporting screenshot based on key combo');
        this.reportViolation('screenshot', `Screenshot detected: ${keyCombo} (key combination)`);
      }
    });
  }

  // Enhanced image capture monitoring
  setupImageCaptureMonitoring() {
    console.log('🖼️ Setting up enhanced image capture monitoring...');
    
    const cleanupFunctions = [];
    
    try {
      // Periodic clipboard check
      const clipboardInterval = setInterval(async () => {
        if (!this.isSecurityActive()) return;
        
        try {
          if (navigator.clipboard && navigator.clipboard.read) {
            try {
              const items = await navigator.clipboard.read();
              
              for (const item of items) {
                if (item.types.some(type => type.startsWith('image/'))) {
                  if (Date.now() - this.lastImageCheckTime > 5000) {
                    console.log('🚨 Enhanced image found in clipboard during monitoring');
                    
                    this.reportViolation('screenshot', 
                      'Screenshot detected: image found in clipboard during monitoring');
                    
                    this.lastImageCheckTime = Date.now();
                    return;
                  }
                }
              }
            } catch (e) {
              // Permission errors expected
            }
          }
        } catch (error) {
          // Ignore errors
        }
      }, 2000);
      
      cleanupFunctions.push(() => {
        clearInterval(clipboardInterval);
      });
      
      // Monitor paste events with images
      const handlePaste = (e) => {
        if (!this.isSecurityActive()) return;
        
        if (e.clipboardData && e.clipboardData.items) {
          for (let i = 0; i < e.clipboardData.items.length; i++) {
            if (e.clipboardData.items[i].type.indexOf('image') !== -1) {
              console.log('🚨 Enhanced image pasted - possible screenshot');
              this.reportViolation('screenshot', 'Screenshot detected: image pasted from clipboard');
              break;
            }
          }
        }
      };
      
      document.addEventListener('paste', handlePaste, { capture: true });
      cleanupFunctions.push(() => {
        document.removeEventListener('paste', handlePaste, { capture: true });
      });
      
    } catch (error) {
      console.error('Error setting up enhanced image capture monitoring:', error);
    }
    
    console.log('✅ Enhanced image capture monitoring initialized');
    
    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }

  // Enhanced permission monitoring
  setupPermissionMonitoring() {
    console.log('🔐 Setting up enhanced permission monitoring...');
    
    const cleanupFunctions = [];
    
    try {
      if (navigator.permissions) {
        // Camera permission monitoring
        navigator.permissions.query({ name: 'camera' }).then(permissionStatus => {
          permissionStatus.onchange = () => {
            console.log(`📸 Enhanced camera permission changed to: ${permissionStatus.state}`);
            
            if (this.potentialScreenshotAttempt.inProgress) {
              console.log('🚨 Enhanced permission change during screenshot attempt');
              this.reportViolation('screenshot', 
                `Screenshot detected after permission change: ${this.potentialScreenshotAttempt.method}`);
              this.clearPotentialScreenshotAttempt();
            }
          };
        }).catch(err => {
          console.log('Enhanced permission query not supported for camera');
        });
        
        // Screen capture permission monitoring
        try {
          navigator.permissions.query({ name: 'display-capture' }).then(permissionStatus => {
            permissionStatus.onchange = () => {
              console.log(`🖥️ Enhanced screen capture permission changed to: ${permissionStatus.state}`);
              
              if (permissionStatus.state === 'granted') {
                this.reportViolation('screenSharing', 'Screen capture permission granted - possible screen sharing');
              }
            };
          }).catch(err => {
            console.log('Enhanced permission query not supported for display-capture');
          });
        } catch (error) {
          console.log('Enhanced screen capture permission monitoring failed:', error);
        }
      }
    } catch (error) {
      console.error('Error setting up enhanced permission monitoring:', error);
    }
    
    console.log('✅ Enhanced permission monitoring initialized');
    
    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }

  // Enhanced clipboard checking
  async checkClipboardForImages() {
    try {
      if (!navigator.clipboard || !navigator.clipboard.read) return false;
      
      try {
        const items = await navigator.clipboard.read();
        
        for (const item of items) {
          if (item.types.some(type => type.startsWith('image/'))) {
            console.log('🚨 Enhanced image found in clipboard - screenshot confirmed');
            return true;
          }
        }
      } catch (e) {
        console.log('Enhanced clipboard permission denied during check');
      }
    } catch (error) {
      console.log('Enhanced error checking clipboard for images');
    }
    
    return false;
  }

  // Clear potential screenshot attempt
  clearPotentialScreenshotAttempt() {
    if (this.potentialScreenshotAttempt.timeoutId) {
      clearTimeout(this.potentialScreenshotAttempt.timeoutId);
    }
    
    this.potentialScreenshotAttempt = {
      inProgress: false,
      method: null,
      timestamp: null,
      timeoutId: null
    };
  }

  // Advanced fullscreen enforcement
  enforceFullscreenModeAdvanced() {
    console.log('🔒 Enforcing fullscreen mode (advanced)...');
    
    const existingBlocker = document.getElementById('fullscreen-blocker-advanced');
    if (existingBlocker) {
      existingBlocker.remove();
    }
    
    const blocker = document.createElement('div');
    blocker.id = 'fullscreen-blocker-advanced';
    blocker.style.cssText = `
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      background: rgba(255,0,0,0.95) !important;
      color: white !important;
      display: flex !important;
      flex-direction: column !important;
      justify-content: center !important;
      align-items: center !important;
      z-index: 2147483647 !important;
      font-size: 24px !important;
      text-align: center !important;
      pointer-events: all !important;
    `;
    
    blocker.innerHTML = `
      <div style="font-size: 32px; margin-bottom: 20px;">⚠️ FULLSCREEN REQUIRED ⚠️</div>
      <div style="font-size: 18px; margin: 20px 0; max-width: 80%;">
        Advanced security requires fullscreen mode.<br/>
        Click below to enter fullscreen and continue the interview.
      </div>
      <button id="enable-fullscreen-advanced-btn" style="
        font-size: 18px !important;
        padding: 15px 30px !important;
        background: white !important;
        color: red !important;
        border: none !important;
        border-radius: 8px !important;
        cursor: pointer !important;
        font-weight: bold !important;
        pointer-events: all !important;
      ">Enable Fullscreen Mode</button>
      <div style="font-size: 14px; margin-top: 20px; color: #ffcccc;">
        Advanced security monitoring paused until fullscreen
      </div>
    `;
    
    document.body.appendChild(blocker);
    
    const button = document.getElementById('enable-fullscreen-advanced-btn');
    if (button) {
      button.addEventListener('click', async () => {
        try {
          console.log('🖥️ Advanced fullscreen button clicked');
          const elem = document.documentElement;
          
          if (elem.requestFullscreen) {
            await elem.requestFullscreen();
          } else if (elem.mozRequestFullScreen) {
            await elem.mozRequestFullScreen();
          } else if (elem.webkitRequestFullscreen) {
            await elem.webkitRequestFullscreen();
          } else if (elem.msRequestFullscreen) {
            await elem.msRequestFullscreen();
          } else {
            throw new Error('Fullscreen API not supported');
          }
          
          console.log('✅ Advanced fullscreen request sent');
        } catch (error) {
          console.error('❌ Advanced fullscreen request failed:', error);
          
          button.innerHTML = 'Press F11 or use browser fullscreen';
          button.style.background = '#ff9500';
          
          setTimeout(() => {
            button.innerHTML = 'Try Fullscreen Again';
            button.style.background = 'white';
          }, 3000);
        }
      });
    }
    
    // Remove blocker when fullscreen is achieved
    const checkAndRemoveBlocker = () => {
      const isFullscreen = !!(
        document.fullscreenElement ||
        document.mozFullScreenElement ||
        document.webkitFullscreenElement ||
        document.msFullscreenElement
      );
      
      if (isFullscreen) {
        const currentBlocker = document.getElementById('fullscreen-blocker-advanced');
        if (currentBlocker) {
          console.log('✅ Advanced fullscreen achieved, removing blocker');
          currentBlocker.remove();
        }
      }
    };
    
    const fullscreenEvents = [
      'fullscreenchange',
      'webkitfullscreenchange', 
      'mozfullscreenchange',
      'MSFullscreenChange'
    ];
    
    fullscreenEvents.forEach(event => {
      document.addEventListener(event, checkAndRemoveBlocker);
    });
    
    console.log('✅ Advanced fullscreen blocker created');
  }

  // Report violation helper
  reportViolation(type, message) {
    if (this.securityManagerRef?.current && this.securityManagerRef.current.isInterviewActive) {
      try {
        if (typeof this.securityManagerRef.current.handleWarningViolation === 'function') {
          this.securityManagerRef.current.handleWarningViolation(type, message);
        } else if (typeof this.securityManagerRef.current.handleViolation === 'function') {
          this.securityManagerRef.current.handleViolation(type, message);
        } else if (typeof this.securityManagerRef.current.reportViolation === 'function') {
          this.securityManagerRef.current.reportViolation(type, message);
        }
      } catch (error) {
        console.error('Error reporting enhanced violation:', error);
      }
    } else if (this.trackPreSecurityViolation) {
      this.trackPreSecurityViolation(type, 
        `${message} (enhanced - question ${(this.currentQuestion || 0) + 1})`);
    }
  }

  // Main setup function
  setupEnhancedSecurityFeatures() {
    console.log('🛡️ Setting up enhanced security features...');
    
    const cleanupFunctions = [];
    
    try {
      cleanupFunctions.push(this.setupAdvancedScreenshotDetection());
      cleanupFunctions.push(this.setupImageCaptureMonitoring());
      cleanupFunctions.push(this.setupPermissionMonitoring());
      
      console.log('✅ Enhanced security features initialized');
    } catch (error) {
      console.error('Error setting up enhanced security features:', error);
    }
    
    return () => {
      console.log('🧹 Cleaning up enhanced security features...');
      cleanupFunctions.forEach(cleanup => {
        try {
          cleanup();
        } catch (error) {
          console.error('Error in enhanced security cleanup:', error);
        }
      });
      this.clearPotentialScreenshotAttempt();
    };
  }

  // Update options
  updateOptions(newOptions) {
    Object.assign(this, newOptions);
  }

  // Cleanup
  cleanup() {
    this.clearPotentialScreenshotAttempt();
  }
}

export default EnhancedSecurityFeatures;
