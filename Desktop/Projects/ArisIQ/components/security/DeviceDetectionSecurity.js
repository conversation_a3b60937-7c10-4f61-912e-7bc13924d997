// File: components/security/DeviceDetectionSecurity.js
// Handles device detection and hand gesture monitoring

export class DeviceDetectionSecurity {
  constructor(options = {}) {
    this.exitingRef = options.exitingRef;
    this.securityManagerRef = options.securityManagerRef;
    this.trackPreSecurityViolation = options.trackPreSecurityViolation;
    this.currentQuestion = options.currentQuestion;
    this.showRequiredAcknowledgmentWarning = options.showRequiredAcknowledgmentWarning;
    
    // Device detection state
    this.deviceDetectionState = {
      consecutiveDeviceDetections: 0,
      lastDeviceDetectionTime: null,
      deviceDetectionThreshold: 3, // 3 consecutive detections
      phoneConfidenceHistory: [],
      combinedConfidenceThreshold: 0.6,
      lastPhoneViolationTime: 0,
      phoneViolationCooldown: 15000, // 15 seconds between phone violations
      gestureConsistencyCount: 0,
      visualDeviceCount: 0
    };
    
    // Hand tracking state
    this.handTrackingState = {
      handsRaisedCount: 0,
      handsOutOfFrameCount: 0,
      lastHandViolationTime: 0,
      handViolationCooldown: 10000, // 10 seconds between hand violations
      suspiciousGestureCount: 0
    };
    
    this.handleFaceDetection = this.handleFaceDetection.bind(this);
    this.handleDeviceDetection = this.handleDeviceDetection.bind(this);
    this.handleHandTracking = this.handleHandTracking.bind(this);
    this.setupDeviceDetection = this.setupDeviceDetection.bind(this);
  }

  // Check if security is active
  isSecurityActive() {
    return !this.exitingRef?.current && 
           (this.securityManagerRef?.current?.isInterviewActive || 
            this.trackPreSecurityViolation);
  }

  // Report device violation
  reportDeviceViolation(type, message) {
    console.log(`🚨 Device violation: ${type} - ${message}`);
    
    if (this.securityManagerRef?.current && this.securityManagerRef.current.isInterviewActive) {
      try {
        if (typeof this.securityManagerRef.current.handleWarningViolation === 'function') {
          this.securityManagerRef.current.handleWarningViolation(type, message);
        } else if (typeof this.securityManagerRef.current.handleViolation === 'function') {
          this.securityManagerRef.current.handleViolation(type, message);
        } else if (typeof this.securityManagerRef.current.reportViolation === 'function') {
          this.securityManagerRef.current.reportViolation(type, message);
        }
      } catch (error) {
        console.error('Error reporting device violation:', error);
      }
    } else if (this.trackPreSecurityViolation) {
      // Track violation even if SecurityManager isn't active yet
      this.trackPreSecurityViolation(type, 
        `${message} (question ${(this.currentQuestion || 0) + 1})`);
    }
    
    // Dispatch custom event for additional handlers
    const event = new CustomEvent('security-device-violation', {
      detail: { 
        timestamp: new Date().toISOString(),
        type: type,
        message: message
      }
    });
    window.dispatchEvent(event);
  }

  // Handle face detection data with device analysis
  handleFaceDetection(faceData) {
    if (!this.isSecurityActive()) return;
    
    console.log('👁️ Face detection data received with device info:', faceData);
    
    const now = Date.now();
    
    // Handle device detection if present
    if (faceData.phoneConfidence !== undefined || 
        faceData.combinedPhoneConfidence !== undefined ||
        faceData.deviceDetected !== undefined) {
      this.handleDeviceDetection(faceData, now);
    }
    
    // Handle hand tracking if present
    if (faceData.handsDetected !== undefined ||
        faceData.handsRaised !== undefined ||
        faceData.suspiciousHandGesture !== undefined) {
      this.handleHandTracking(faceData, now);
    }
  }

  // Comprehensive device detection handler
  handleDeviceDetection(data, currentTime) {
    try {
      const deviceState = this.deviceDetectionState;
      
      console.log('📱 Processing device detection data:', {
        phoneConfidence: data.phoneConfidence || 0,
        combinedConfidence: data.combinedPhoneConfidence || 0,
        phoneGesture: data.phoneGesture || false,
        deviceDetected: data.deviceDetected || false,
        handsDetected: data.handsDetected || 0,
        method: data.detectionMethod || 'none'
      });
      
      // 1. PHONE CONFIDENCE TRACKING
      const phoneConfidence = data.phoneConfidence || 0;
      const combinedConfidence = data.combinedPhoneConfidence || 0;
      
      // Track confidence history for trend analysis
      deviceState.phoneConfidenceHistory.push({
        phoneConfidence,
        combinedConfidence,
        timestamp: currentTime
      });
      
      // Keep only last 10 seconds of history
      const tenSecondsAgo = currentTime - 10000;
      deviceState.phoneConfidenceHistory = deviceState.phoneConfidenceHistory.filter(
        entry => entry.timestamp > tenSecondsAgo
      );
      
      // 2. HIGH CONFIDENCE PHONE DETECTION
      if (combinedConfidence > deviceState.combinedConfidenceThreshold) {
        deviceState.consecutiveDeviceDetections++;
        
        console.log(`📱 High confidence device detection (${deviceState.consecutiveDeviceDetections}/${deviceState.deviceDetectionThreshold}):`, {
          combinedConfidence: combinedConfidence,
          threshold: deviceState.combinedConfidenceThreshold
        });
        
        // Trigger violation after consistent high confidence
        if (deviceState.consecutiveDeviceDetections >= deviceState.deviceDetectionThreshold) {
          const timeSinceLastViolation = currentTime - deviceState.lastPhoneViolationTime;
          
          if (timeSinceLastViolation > deviceState.phoneViolationCooldown) {
            this.reportDeviceViolation('deviceDetected', 
              `Phone/device detected with ${Math.round(combinedConfidence * 100)}% confidence`);
            
            deviceState.lastPhoneViolationTime = currentTime;
            deviceState.consecutiveDeviceDetections = 0; // Reset after reporting
          }
        }
      } else {
        // Reset consecutive count if confidence drops
        deviceState.consecutiveDeviceDetections = Math.max(0, deviceState.consecutiveDeviceDetections - 1);
      }
      
      // 3. PHONE GESTURE DETECTION
      if (data.phoneGesture) {
        deviceState.gestureConsistencyCount++;
        
        console.log(`📞 Phone gesture detected (${deviceState.gestureConsistencyCount} times)`);
        
        // Immediate violation for clear phone gestures
        const timeSinceLastViolation = currentTime - deviceState.lastPhoneViolationTime;
        if (timeSinceLastViolation > deviceState.phoneViolationCooldown) {
          this.reportDeviceViolation('phoneGesture', 'Phone gesture detected - device usage prohibited');
          deviceState.lastPhoneViolationTime = currentTime;
          deviceState.gestureConsistencyCount = 0;
        }
      }
      
      // 4. VISUAL DEVICE DETECTION
      if (data.devicesDetected && data.devicesDetected.length > 0) {
        const highConfidenceDevices = data.devicesDetected.filter(device => device.confidence > 0.7);
        
        if (highConfidenceDevices.length > 0) {
          deviceState.visualDeviceCount++;
          
          console.log(`📱 Visual devices detected: ${highConfidenceDevices.length} high-confidence devices`);
          
          // Report visual device detection
          const timeSinceLastViolation = currentTime - deviceState.lastPhoneViolationTime;
          if (timeSinceLastViolation > deviceState.phoneViolationCooldown) {
            this.reportDeviceViolation('deviceDetected', 
              `Visual device detection: ${highConfidenceDevices.length} device(s) with high confidence`);
            
            deviceState.lastPhoneViolationTime = currentTime;
          }
        }
      }
      
      // 5. TEMPORAL ANALYSIS
      if (deviceState.phoneConfidenceHistory.length >= 5) {
        const recentAverage = deviceState.phoneConfidenceHistory
          .slice(-5)
          .reduce((sum, entry) => sum + entry.combinedConfidence, 0) / 5;
        
        if (recentAverage > 0.4) {
          console.log(`📊 Sustained device detection over time: ${Math.round(recentAverage * 100)}% average confidence`);
          
          // Report sustained detection
          const timeSinceLastViolation = currentTime - deviceState.lastPhoneViolationTime;
          if (timeSinceLastViolation > deviceState.phoneViolationCooldown * 2) { // Longer cooldown for temporal analysis
            this.reportDeviceViolation('deviceDetected', 
              `Sustained device usage detected: ${Math.round(recentAverage * 100)}% average confidence over time`);
            
            deviceState.lastPhoneViolationTime = currentTime;
          }
        }
      }
      
      // 6. LOG COMPREHENSIVE DEVICE STATE
      if (combinedConfidence > 0.2 || data.handsDetected > 0) {
        console.log('📊 Device detection state summary:', {
          combinedConfidence: Math.round(combinedConfidence * 100) + '%',
          phoneGesture: data.phoneGesture,
          consecutiveDetections: deviceState.consecutiveDeviceDetections,
          gestureCount: deviceState.gestureConsistencyCount,
          visualDevices: deviceState.visualDeviceCount,
          recentAverage: deviceState.phoneConfidenceHistory.length >= 3 ? 
            Math.round(deviceState.phoneConfidenceHistory.slice(-3).reduce((sum, entry) => sum + entry.combinedConfidence, 0) / 3 * 100) + '%' : 'N/A'
        });
      }
      
    } catch (error) {
      console.error('Error in device detection handling:', error);
    }
  }

  // Handle hand tracking and gesture analysis
  handleHandTracking(data, currentTime) {
    try {
      const handState = this.handTrackingState;
      
      console.log('🤏 Processing hand tracking data:', {
        handsDetected: data.handsDetected || 0,
        handsRaised: data.handsRaised || false,
        handsOutOfFrame: data.handsOutOfFrame || false,
        suspiciousHandGesture: data.suspiciousHandGesture || false,
        consecutiveHandsRaised: data.consecutiveHandsRaised || 0,
        consecutiveHandsOutOfFrame: data.consecutiveHandsOutOfFrame || 0
      });
      
      // 1. HANDS RAISED DETECTION
      if (data.handsRaised) {
        handState.handsRaisedCount++;
        
        console.log(`🙋 Hands raised detected (${handState.handsRaisedCount} times)`);
        
        // Report if hands are raised consistently
        const timeSinceLastViolation = currentTime - handState.lastHandViolationTime;
        if (timeSinceLastViolation > handState.handViolationCooldown) {
          this.reportDeviceViolation('handsRaised', 
            'Hands raised for extended period - may indicate device usage');
          handState.lastHandViolationTime = currentTime;
          handState.handsRaisedCount = 0;
        }
      }
      
      // 2. HANDS OUT OF FRAME DETECTION
      if (data.handsOutOfFrame || data.consecutiveHandsOutOfFrame > 5) {
        handState.handsOutOfFrameCount++;
        
        console.log(`👐 Hands out of frame detected (${handState.handsOutOfFrameCount} times)`);
        
        // Report if hands are consistently out of frame
        const timeSinceLastViolation = currentTime - handState.lastHandViolationTime;
        if (timeSinceLastViolation > handState.handViolationCooldown) {
          this.reportDeviceViolation('handsOutOfFrame', 
            'Hands consistently out of camera frame - please keep hands visible');
          handState.lastHandViolationTime = currentTime;
          handState.handsOutOfFrameCount = 0;
        }
      }
      
      // 3. SUSPICIOUS HAND GESTURE DETECTION
      if (data.suspiciousHandGesture) {
        handState.suspiciousGestureCount++;
        
        console.log(`🤏 Suspicious hand gesture detected (${handState.suspiciousGestureCount} times)`);
        
        // Report suspicious gestures immediately
        const timeSinceLastViolation = currentTime - handState.lastHandViolationTime;
        if (timeSinceLastViolation > handState.handViolationCooldown) {
          this.reportDeviceViolation('suspiciousHandGesture', 
            'Suspicious hand gesture detected - may indicate device usage');
          handState.lastHandViolationTime = currentTime;
          handState.suspiciousGestureCount = 0;
        }
      }
      
      // 4. COMPREHENSIVE HAND STATE LOGGING
      if (data.handsDetected > 0 || data.handsRaised || data.suspiciousHandGesture) {
        console.log('🤏 Hand tracking state summary:', {
          handsDetected: data.handsDetected || 0,
          handsRaised: data.handsRaised || false,
          handsOutOfFrame: data.handsOutOfFrame || false,
          suspiciousGesture: data.suspiciousHandGesture || false,
          raisedCount: handState.handsRaisedCount,
          outOfFrameCount: handState.handsOutOfFrameCount,
          suspiciousCount: handState.suspiciousGestureCount
        });
      }
      
    } catch (error) {
      console.error('Error in hand tracking handling:', error);
    }
  }

  // Setup device detection monitoring
  setupDeviceDetection() {
    console.log('📱 Setting up device detection monitoring...');
    
    const cleanupFunctions = [];
    
    try {
      // Setup event listener for device detection events
      const handleDeviceViolation = (event) => {
        if (this.isSecurityActive()) {
          console.log('🚨 Device violation event received:', event.detail);
          this.reportDeviceViolation('deviceDetected', 
            'Device violation: ' + (event.detail.method || 'custom detection'));
        }
      };
      
      window.addEventListener('security-device-violation', handleDeviceViolation, { capture: true });
      cleanupFunctions.push(() => {
        window.removeEventListener('security-device-violation', handleDeviceViolation, { capture: true });
      });
      
      // Monitor for specific device-related events
      const handlePhoneDetection = (event) => {
        if (this.isSecurityActive()) {
          console.log('📱 Phone detection event received:', event.detail);
          
          const currentTime = Date.now();
          const timeSinceLastViolation = currentTime - this.deviceDetectionState.lastPhoneViolationTime;
          
          if (timeSinceLastViolation > this.deviceDetectionState.phoneViolationCooldown) {
            this.reportDeviceViolation('phoneDetected', 
              'Phone detected: ' + (event.detail.method || 'custom detection'));
            this.deviceDetectionState.lastPhoneViolationTime = currentTime;
          }
        }
      };
      
      window.addEventListener('security-phone-detected', handlePhoneDetection, { capture: true });
      cleanupFunctions.push(() => {
        window.removeEventListener('security-phone-detected', handlePhoneDetection, { capture: true });
      });
      
      // Monitor for hand gesture events
      const handleHandGesture = (event) => {
        if (this.isSecurityActive()) {
          console.log('🤏 Hand gesture event received:', event.detail);
          
          const currentTime = Date.now();
          const timeSinceLastViolation = currentTime - this.handTrackingState.lastHandViolationTime;
          
          if (timeSinceLastViolation > this.handTrackingState.handViolationCooldown) {
            this.reportDeviceViolation('suspiciousHandGesture', 
              'Suspicious hand gesture: ' + (event.detail.gesture || 'unknown gesture'));
            this.handTrackingState.lastHandViolationTime = currentTime;
          }
        }
      };
      
      window.addEventListener('security-hand-gesture', handleHandGesture, { capture: true });
      cleanupFunctions.push(() => {
        window.removeEventListener('security-hand-gesture', handleHandGesture, { capture: true });
      });
      
      console.log('✅ Device detection monitoring initialized');
    } catch (error) {
      console.error('Error setting up device detection:', error);
    }
    
    // Return cleanup function
    return () => {
      console.log('🧹 Cleaning up device detection...');
      cleanupFunctions.forEach(cleanup => {
        try {
          cleanup();
        } catch (error) {
          console.error('Error in device detection cleanup:', error);
        }
      });
    };
  }

  // Update options (useful for React state updates)
  updateOptions(newOptions) {
    Object.assign(this, newOptions);
  }

  // Reset detection state
  resetDetectionState() {
    this.deviceDetectionState = {
      consecutiveDeviceDetections: 0,
      lastDeviceDetectionTime: null,
      deviceDetectionThreshold: 3,
      phoneConfidenceHistory: [],
      combinedConfidenceThreshold: 0.6,
      lastPhoneViolationTime: 0,
      phoneViolationCooldown: 15000,
      gestureConsistencyCount: 0,
      visualDeviceCount: 0
    };
    
    this.handTrackingState = {
      handsRaisedCount: 0,
      handsOutOfFrameCount: 0,
      lastHandViolationTime: 0,
      handViolationCooldown: 10000,
      suspiciousGestureCount: 0
    };
  }

  // Get detection statistics
  getDetectionStats() {
    return {
      deviceDetection: {
        consecutiveDetections: this.deviceDetectionState.consecutiveDeviceDetections,
        gestureCount: this.deviceDetectionState.gestureConsistencyCount,
        visualDeviceCount: this.deviceDetectionState.visualDeviceCount,
        historyLength: this.deviceDetectionState.phoneConfidenceHistory.length
      },
      handTracking: {
        handsRaisedCount: this.handTrackingState.handsRaisedCount,
        handsOutOfFrameCount: this.handTrackingState.handsOutOfFrameCount,
        suspiciousGestureCount: this.handTrackingState.suspiciousGestureCount
      }
    };
  }

  // Cleanup method
  cleanup() {
    this.resetDetectionState();
  }
}

export default DeviceDetectionSecurity;
