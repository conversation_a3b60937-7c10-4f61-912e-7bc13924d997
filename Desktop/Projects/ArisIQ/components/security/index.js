// File: components/security/index.js
// Main export file for all security modules

// Individual security modules - using named exports
export { FullscreenSecurity } from './FullscreenSecurity';
export { TabSwitchSecurity } from './TabSwitchSecurity';
export { CopyPasteSecurity } from './CopyPasteSecurity';
export { ScreenshotSecurity } from './ScreenshotSecurity';
export { DeviceDetectionSecurity } from './DeviceDetectionSecurity';
export { KeyboardSecurity } from './KeyboardSecurity';
export { AdvancedMonitoringSecurity } from './AdvancedMonitoringSecurity';
export { BehavioralAnalysisSecurity } from './BehavioralAnalysisSecurity';
export { DeepfakeDetectionSecurity } from './DeepfakeDetectionSecurity';

// Note: SecurityManagerIntegration removed - using security-manager.js instead

// Also export as default exports for alternative import syntax
export { default as FullscreenSecurityDefault } from './FullscreenSecurity';
export { default as TabSwitchSecurityDefault } from './TabSwitchSecurity';
export { default as CopyPasteSecurityDefault } from './CopyPasteSecurity';
export { default as ScreenshotSecurityDefault } from './ScreenshotSecurity';
export { default as DeviceDetectionSecurityDefault } from './DeviceDetectionSecurity';
export { default as KeyboardSecurityDefault } from './KeyboardSecurity';
export { default as AdvancedMonitoringSecurityDefault } from './AdvancedMonitoringSecurity';
export { default as BehavioralAnalysisSecurityDefault } from './BehavioralAnalysisSecurity';
export { default as DeepfakeDetectionSecurityDefault } from './DeepfakeDetectionSecurity';

// Utility function to create a complete security setup
export const createSecuritySetup = (options = {}) => {
  console.log('🛡️ Creating complete security setup...');
  
  try {
    // Note: Using existing security-manager.js instead of SecurityManagerIntegration
    console.log('✅ Security setup created (using security-manager.js)');
    
    return {
      security: null, // Using security-manager.js instead
      cleanup: () => {},
      modules: {},
      status: { initialized: true }
    };
    
  } catch (error) {
    console.error('❌ Error creating security setup:', error);
    return null;
  }
};

// Utility function for React hooks integration
export const useSecuritySetup = (options = {}) => {
  console.log('🔗 Setting up security for React component...');
  
  // This would be used in a React hook
  // For now, just return the same as createSecuritySetup
  return createSecuritySetup(options);
};

// Security module registry for dynamic imports
export const SECURITY_MODULES = {
  fullscreen: 'FullscreenSecurity',
  tabSwitch: 'TabSwitchSecurity',
  copyPaste: 'CopyPasteSecurity',
  screenshot: 'ScreenshotSecurity',
  deviceDetection: 'DeviceDetectionSecurity',
  keyboard: 'KeyboardSecurity',
  advancedMonitoring: 'AdvancedMonitoringSecurity',
  behavioralAnalysis: 'BehavioralAnalysisSecurity',
  deepfakeDetection: 'DeepfakeDetectionSecurity',
  integration: 'SecurityManagerIntegration'
};

// Security violation types registry
export const VIOLATION_TYPES = {
  // Fullscreen violations
  fullscreenExit: 'User exited fullscreen mode',
  
  // Tab switch violations
  tabSwitch: 'User switched to another tab',
  windowBlur: 'Window lost focus',
  
  // Keyboard violations
  devTools: 'Developer tools usage detected',
  keyboardShortcut: 'Blocked keyboard shortcut used',
  escapeKey: 'Escape key pressed',
  
  // Copy/paste violations
  copyPaste: 'Copy/paste operation blocked',
  contentPasted: 'External content pasted',
  
  // Face detection violations
  multipleFaces: 'Multiple faces detected',
  faceNotVisible: 'Face not visible in camera',
  lookingAway: 'Looking away from camera',
  differentPerson: 'Different person detected',
  
  // Device detection violations
  deviceDetected: 'Phone or device detected',
  phoneGesture: 'Phone gesture detected',
  handsRaised: 'Hands raised (potential device use)',
  handsOutOfFrame: 'Hands consistently out of frame',
  suspiciousHandGesture: 'Suspicious hand gesture detected',
  
  // Advanced monitoring violations
  dualMonitor: 'Dual monitor setup detected',
  screenSharing: 'Screen sharing detected',
  screenRecording: 'Screen recording detected',
  virtualMachine: 'Virtual machine detected',
  mouseOutOfBounds: 'Mouse left interview window',
  roboticMouse: 'Robotic mouse movement detected',
  
  // Screenshot violations
  screenshot: 'Screenshot attempt detected',
  
  // Behavioral analysis violations
  suspiciousResponseTime: 'Suspiciously fast response',
  suspiciousTypingSpeed: 'Extremely fast typing detected',
  roboticTypingPattern: 'Robotic typing pattern detected',
  typingSpeedChange: 'Dramatic typing speed change',
  rapidCodeAppearance: 'Code appeared too quickly',
  suspiciouslyPerfectCode: 'Suspiciously perfect code',
  multipleVoiceDetection: 'Multiple voices detected',
  lowAttentionScore: 'Low attention score',
  excessiveOffScreenGaze: 'Excessive off-screen gaze',
  
  // Deepfake detection violations
  deepfake: 'Deepfake or artificial video detected',
  lightingAnomaly: 'Inconsistent lighting detected',
  microExpressionAnomaly: 'Unnatural micro-expressions',
  faceInconsistency: 'Face consistency anomaly',
  temporalInconsistency: 'Temporal inconsistency detected',
  compressionArtifacts: 'Video compression artifacts',
  qualityInconsistency: 'Video quality inconsistency'
};

// Security configuration presets
export const SECURITY_PRESETS = {
  // Strict security for high-stakes interviews
  strict: {
    enableAllModules: true,
    violationLimit: 3,
    enableBehavioralAnalysis: true,
    enableDeepfakeDetection: true,
    enableAdvancedMonitoring: true,
    screenshotDetection: 'aggressive',
    deviceDetection: 'strict',
    typingAnalysis: 'enabled'
  },
  
  // Standard security for most interviews
  standard: {
    enableAllModules: true,
    violationLimit: 5,
    enableBehavioralAnalysis: true,
    enableDeepfakeDetection: false,
    enableAdvancedMonitoring: true,
    screenshotDetection: 'standard',
    deviceDetection: 'standard',
    typingAnalysis: 'basic'
  },
  
  // Lenient security for practice interviews
  lenient: {
    enableAllModules: false,
    violationLimit: 10,
    enableBehavioralAnalysis: false,
    enableDeepfakeDetection: false,
    enableAdvancedMonitoring: false,
    screenshotDetection: 'basic',
    deviceDetection: 'basic',
    typingAnalysis: 'disabled'
  }
};

// Utility function to get security module by name
export const getSecurityModule = async (moduleName) => {
  try {
    switch (moduleName) {
      case 'fullscreen':
        return (await import('./FullscreenSecurity')).FullscreenSecurity;
      case 'tabSwitch':
        return (await import('./TabSwitchSecurity')).TabSwitchSecurity;
      case 'copyPaste':
        return (await import('./CopyPasteSecurity')).CopyPasteSecurity;
      case 'screenshot':
        return (await import('./ScreenshotSecurity')).ScreenshotSecurity;
      case 'deviceDetection':
        return (await import('./DeviceDetectionSecurity')).DeviceDetectionSecurity;
      case 'keyboard':
        return (await import('./KeyboardSecurity')).KeyboardSecurity;
      case 'advancedMonitoring':
        return (await import('./AdvancedMonitoringSecurity')).AdvancedMonitoringSecurity;
      case 'behavioralAnalysis':
        return (await import('./BehavioralAnalysisSecurity')).BehavioralAnalysisSecurity;
      case 'deepfakeDetection':
        return (await import('./DeepfakeDetectionSecurity')).DeepfakeDetectionSecurity;
      case 'integration':
        return (await import('./SecurityManagerIntegration')).SecurityManagerIntegration;
      default:
        throw new Error(`Unknown security module: ${moduleName}`);
    }
  } catch (error) {
    console.error(`Error loading security module ${moduleName}:`, error);
    return null;
  }
};

// Utility function to initialize security with preset
export const initializeSecurityWithPreset = (presetName = 'standard', customOptions = {}) => {
  const preset = SECURITY_PRESETS[presetName];
  
  if (!preset) {
    console.error(`Unknown security preset: ${presetName}`);
    return null;
  }
  
  const options = {
    ...preset,
    ...customOptions
  };
  
  console.log(`🛡️ Initializing security with ${presetName} preset:`, options);
  
  return createSecuritySetup(options);
};

// Security event dispatcher utility
export const createSecurityEventDispatcher = () => {
  const listeners = new Map();
  
  return {
    // Add event listener
    on: (eventType, callback) => {
      if (!listeners.has(eventType)) {
        listeners.set(eventType, new Set());
      }
      listeners.get(eventType).add(callback);
      
      return () => {
        listeners.get(eventType)?.delete(callback);
      };
    },
    
    // Remove event listener
    off: (eventType, callback) => {
      listeners.get(eventType)?.delete(callback);
    },
    
    // Dispatch event
    dispatch: (eventType, data) => {
      const eventListeners = listeners.get(eventType);
      if (eventListeners) {
        eventListeners.forEach(callback => {
          try {
            callback(data);
          } catch (error) {
            console.error(`Error in security event listener for ${eventType}:`, error);
          }
        });
      }
    },
    
    // Clear all listeners
    clear: () => {
      listeners.clear();
    }
  };
};

// Security metrics collector
export const createSecurityMetrics = () => {
  const metrics = {
    violations: {},
    events: [],
    startTime: Date.now(),
    moduleStatus: {}
  };
  
  return {
    // Record violation
    recordViolation: (type, details = {}) => {
      if (!metrics.violations[type]) {
        metrics.violations[type] = 0;
      }
      metrics.violations[type]++;
      
      metrics.events.push({
        type: 'violation',
        violationType: type,
        timestamp: Date.now(),
        details
      });
    },
    
    // Record event
    recordEvent: (type, details = {}) => {
      metrics.events.push({
        type,
        timestamp: Date.now(),
        details
      });
    },
    
    // Update module status
    updateModuleStatus: (moduleName, status) => {
      metrics.moduleStatus[moduleName] = {
        status,
        timestamp: Date.now()
      };
    },
    
    // Get metrics summary
    getSummary: () => {
      const totalViolations = Object.values(metrics.violations).reduce((sum, count) => sum + count, 0);
      const sessionDuration = Date.now() - metrics.startTime;
      
      return {
        totalViolations,
        violationBreakdown: { ...metrics.violations },
        sessionDuration,
        eventsCount: metrics.events.length,
        moduleStatus: { ...metrics.moduleStatus },
        violationRate: totalViolations / (sessionDuration / 60000) // violations per minute
      };
    },
    
    // Export data
    exportData: () => {
      return {
        ...metrics,
        summary: metrics.getSummary()
      };
    }
  };
};

// Export default as null since we're using security-manager.js
export default null;

// Version information
export const SECURITY_VERSION = '2.0.0';
export const SECURITY_BUILD = 'complete-integration';

console.log(`🛡️ Security system loaded - Version ${SECURITY_VERSION} (${SECURITY_BUILD})`);
