// File: components/security/AdvancedMonitoringSecurity.js
// Handles advanced monitoring: screen sharing, dual monitor, mouse tracking, etc.

export class AdvancedMonitoringSecurity {
  constructor(options = {}) {
    this.exitingRef = options.exitingRef;
    this.securityManagerRef = options.securityManagerRef;
    this.trackPreSecurityViolation = options.trackPreSecurityViolation;
    this.currentQuestion = options.currentQuestion;
    this.showRequiredAcknowledgmentWarning = options.showRequiredAcknowledgmentWarning;
    
    // Monitoring state
    this.monitoringState = {
      initialScreenConfig: null,
      mouseInWindow: true,
      lastMousePosition: { x: 0, y: 0 },
      mouseOutOfBoundsCount: 0,
      suspiciousMousePatterns: 0,
      lastViolationTime: 0,
      violationCooldown: 10000 // 10 seconds between similar violations
    };
    
    // Screen sharing detection
    this.screenSharingState = {
      webRTCConnections: new Set(),
      suspiciousConnections: 0,
      screenCaptureAttempts: 0
    };
    
    this.setupAdvancedMonitoring = this.setupAdvancedMonitoring.bind(this);
    this.setupDualMonitorDetection = this.setupDualMonitorDetection.bind(this);
    this.setupScreenSharingDetection = this.setupScreenSharingDetection.bind(this);
    this.setupMouseBoundaryTracking = this.setupMouseBoundaryTracking.bind(this);
    this.setupScreenRecordingDetection = this.setupScreenRecordingDetection.bind(this);
    this.detectVirtualMachine = this.detectVirtualMachine.bind(this);
    this.handleAdvancedViolation = this.handleAdvancedViolation.bind(this);
  }

  // Check if security is active
  isSecurityActive() {
    return !this.exitingRef?.current && 
           (this.securityManagerRef?.current?.isInterviewActive || 
            this.trackPreSecurityViolation);
  }

  // Report advanced monitoring violation
  handleAdvancedViolation(type, message) {
    console.log(`🚨 Advanced monitoring violation: ${type} - ${message}`);
    
    const currentTime = Date.now();
    const timeSinceLastViolation = currentTime - this.monitoringState.lastViolationTime;
    
    // Prevent spam violations
    if (timeSinceLastViolation < this.monitoringState.violationCooldown) {
      console.log('Violation cooldown active, skipping report');
      return;
    }
    
    this.monitoringState.lastViolationTime = currentTime;
    
    if (this.securityManagerRef?.current && this.securityManagerRef.current.isInterviewActive) {
      try {
        if (typeof this.securityManagerRef.current.handleWarningViolation === 'function') {
          this.securityManagerRef.current.handleWarningViolation(type, message);
        } else if (typeof this.securityManagerRef.current.handleViolation === 'function') {
          this.securityManagerRef.current.handleViolation(type, message);
        } else if (typeof this.securityManagerRef.current.reportViolation === 'function') {
          this.securityManagerRef.current.reportViolation(type, message);
        }
      } catch (error) {
        console.error('Error reporting advanced monitoring violation:', error);
      }
    } else if (this.trackPreSecurityViolation) {
      this.trackPreSecurityViolation(type, 
        `${message} (question ${(this.currentQuestion || 0) + 1})`);
    }
  }

  // ENHANCED DUAL MONITOR DETECTION
  setupDualMonitorDetection() {
    console.log('🖥️ Setting up enhanced dual monitor detection...');
    
    const cleanupFunctions = [];
    
    try {
      // Store initial screen configuration
      this.monitoringState.initialScreenConfig = {
        width: window.screen.width,
        height: window.screen.height,
        availWidth: window.screen.availWidth,
        availHeight: window.screen.availHeight,
        colorDepth: window.screen.colorDepth,
        pixelDepth: window.screen.pixelDepth,
        orientation: window.screen.orientation?.type || 'unknown'
      };
      
      console.log('📊 Initial screen config:', this.monitoringState.initialScreenConfig);
      
      // Advanced dual monitor detection using modern API
      const detectModernMultipleScreens = async () => {
        try {
          if ('getScreenDetails' in window) {
            const screenDetails = await window.getScreenDetails();
            
            if (screenDetails.screens.length > 1) {
              console.log(`🚨 Multiple screens detected: ${screenDetails.screens.length}`);
              
              this.handleAdvancedViolation('dualMonitor', 
                `Multiple monitors detected: ${screenDetails.screens.length} screens`);
              
              // Create blocking overlay for multiple monitors
              this.createMultipleMonitorBlocker(screenDetails.screens.length);
            }
          }
        } catch (error) {
          console.log('Modern screen detection not available:', error);
        }
      };
      
      // Legacy dual monitor detection
      const detectLegacyMultipleScreens = () => {
        if (!this.isSecurityActive()) return;
        
        const current = {
          width: window.screen.width,
          height: window.screen.height,
          availWidth: window.screen.availWidth,
          availHeight: window.screen.availHeight
        };
        
        const initial = this.monitoringState.initialScreenConfig;
        
        // Check for significant changes (>200 pixels)
        const widthDiff = Math.abs(current.width - initial.width);
        const heightDiff = Math.abs(current.height - initial.height);
        
        if (widthDiff > 200 || heightDiff > 200) {
          console.log('🚨 Screen configuration changed significantly');
          this.handleAdvancedViolation('dualMonitor', 
            `Screen configuration changed: ${current.width}x${current.height} (was ${initial.width}x${initial.height})`);
        }
        
        // Check for unusually wide screens (indicating multiple monitors)
        if (current.width > 3000 || current.height > 2000) {
          console.log('🚨 Unusually large screen detected');
          this.handleAdvancedViolation('dualMonitor', 
            `Unusually large screen detected: ${current.width}x${current.height}`);
        }
        
        // Check window position (negative coordinates indicate secondary monitor)
        const windowLeft = window.screenLeft || window.screenX || 0;
        const windowTop = window.screenTop || window.screenY || 0;
        
        if (windowLeft < 0 || windowLeft > current.width || windowTop < 0 || windowTop > current.height) {
          console.log('🚨 Window positioned on secondary monitor');
          this.handleAdvancedViolation('dualMonitor', 
            `Window positioned outside primary screen: ${windowLeft}, ${windowTop}`);
        }
      };
      
      // Run initial detection
      detectModernMultipleScreens();
      detectLegacyMultipleScreens();
      
      // Periodic checks every 10 seconds
      const screenCheckInterval = setInterval(() => {
        detectModernMultipleScreens();
        detectLegacyMultipleScreens();
      }, 10000);
      
      cleanupFunctions.push(() => {
        clearInterval(screenCheckInterval);
      });
      
      // Monitor window resize events (could indicate screen changes)
      const handleResize = () => {
        setTimeout(detectLegacyMultipleScreens, 1000); // Delay to allow resize to complete
      };
      
      window.addEventListener('resize', handleResize);
      cleanupFunctions.push(() => {
        window.removeEventListener('resize', handleResize);
      });
      
      console.log('✅ Enhanced dual monitor detection initialized');
      
    } catch (error) {
      console.error('Error setting up dual monitor detection:', error);
    }
    
    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }

  // Create blocking overlay for multiple monitors
  createMultipleMonitorBlocker(screenCount) {
    // Remove any existing blocker
    const existingBlocker = document.getElementById('multiple-monitor-blocker');
    if (existingBlocker) {
      existingBlocker.remove();
    }
    
    const blocker = document.createElement('div');
    blocker.id = 'multiple-monitor-blocker';
    blocker.style.cssText = `
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      background: rgba(255,0,0,0.95) !important;
      color: white !important;
      display: flex !important;
      flex-direction: column !important;
      justify-content: center !important;
      align-items: center !important;
      z-index: 2147483647 !important;
      font-size: 24px !important;
      text-align: center !important;
      pointer-events: all !important;
    `;
    
    blocker.innerHTML = `
      <div style="font-size: 32px; margin-bottom: 20px;">⚠️ MULTIPLE MONITORS DETECTED ⚠️</div>
      <div style="font-size: 18px; margin: 20px 0; max-width: 80%;">
        ${screenCount} monitors detected. The interview requires using only a single monitor.<br/>
        Please disconnect or disable your secondary monitors to continue.
      </div>
      <button id="recheck-monitors-btn" style="
        font-size: 18px !important;
        padding: 15px 30px !important;
        background: white !important;
        color: red !important;
        border: none !important;
        border-radius: 8px !important;
        cursor: pointer !important;
        font-weight: bold !important;
        pointer-events: all !important;
      ">I've Disconnected Secondary Monitors</button>
      <div style="font-size: 14px; margin-top: 20px; color: #ffcccc;">
        The interview is paused until you use a single monitor
      </div>
    `;
    
    document.body.appendChild(blocker);
    
    // Add recheck functionality
    document.getElementById('recheck-monitors-btn').addEventListener('click', async () => {
      try {
        if ('getScreenDetails' in window) {
          const screenDetails = await window.getScreenDetails();
          
          if (screenDetails.screens.length <= 1) {
            blocker.remove();
            console.log('✅ Single monitor confirmed, removing blocker');
          } else {
            // Still multiple monitors
            document.getElementById('recheck-monitors-btn').innerHTML = 
              `Still ${screenDetails.screens.length} monitors detected - Please disconnect all secondary monitors`;
            document.getElementById('recheck-monitors-btn').style.background = '#ff9500';
            
            setTimeout(() => {
              document.getElementById('recheck-monitors-btn').innerHTML = 
                "I've Disconnected Secondary Monitors";
              document.getElementById('recheck-monitors-btn').style.background = 'white';
            }, 3000);
          }
        } else {
          // Fallback - just remove blocker
          blocker.remove();
        }
      } catch (error) {
        console.error('Error rechecking monitors:', error);
        blocker.remove();
      }
    });
  }

  // SCREEN SHARING DETECTION
  setupScreenSharingDetection() {
    console.log('📺 Setting up screen sharing detection...');
    
    const cleanupFunctions = [];
    
    try {
      // Monitor WebRTC connections
      const originalRTCPeerConnection = window.RTCPeerConnection;
      
      window.RTCPeerConnection = function(...args) {
        const pc = new originalRTCPeerConnection(...args);
        
        console.log('🔗 New WebRTC connection created');
        this.screenSharingState.webRTCConnections.add(pc);
        
        // Monitor data channels (often used for screen sharing)
        pc.addEventListener('datachannel', () => {
          console.log('🚨 WebRTC data channel created - possible screen sharing');
          this.screenSharingState.suspiciousConnections++;
          
          if (this.screenSharingState.suspiciousConnections > 0) {
            this.handleAdvancedViolation('screenSharing', 
              'WebRTC data channel detected - possible screen sharing');
          }
        });
        
        // Monitor connection state changes
        pc.addEventListener('connectionstatechange', () => {
          if (pc.connectionState === 'connected') {
            console.log('🔗 WebRTC connection established');
            
            // Check if this might be screen sharing
            pc.getStats().then(stats => {
              stats.forEach(stat => {
                if (stat.type === 'outbound-rtp' && stat.mediaType === 'video') {
                  if (stat.frameWidth > 1920 || stat.frameHeight > 1080) {
                    console.log('🚨 High resolution video stream - possible screen sharing');
                    this.handleAdvancedViolation('screenSharing', 
                      'High resolution video stream detected');
                  }
                }
              });
            });
          }
        });
        
        return pc;
      }.bind(this);
      
      cleanupFunctions.push(() => {
        window.RTCPeerConnection = originalRTCPeerConnection;
      });
      
      // Monitor screen capture API usage
      if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
        const originalGetDisplayMedia = navigator.mediaDevices.getDisplayMedia;
        
        navigator.mediaDevices.getDisplayMedia = (...args) => {
          console.log('🚨 Screen capture API called');
          this.screenSharingState.screenCaptureAttempts++;
          
          this.handleAdvancedViolation('screenSharing', 
            'Screen capture API usage detected');
          
          // Block the actual screen sharing
          return Promise.reject(new Error('Screen sharing is not allowed during the interview'));
        };
        
        cleanupFunctions.push(() => {
          navigator.mediaDevices.getDisplayMedia = originalGetDisplayMedia;
        });
      }
      
      console.log('✅ Screen sharing detection initialized');
      
    } catch (error) {
      console.error('Error setting up screen sharing detection:', error);
    }
    
    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }

  // MOUSE BOUNDARY TRACKING (Clicking outside interview screen)
  setupMouseBoundaryTracking() {
    console.log('🖱️ Setting up mouse boundary tracking...');
    
    const cleanupFunctions = [];
    
    try {
      // Track mouse position
      const handleMouseMove = (e) => {
        if (!this.isSecurityActive()) return;
        
        this.monitoringState.lastMousePosition = { x: e.clientX, y: e.clientY };
        this.monitoringState.mouseInWindow = true;
      };
      
      // Detect mouse leaving the window
      const handleMouseLeave = (e) => {
        if (!this.isSecurityActive()) return;
        
        // Check if mouse actually left the browser window
        if (e.relatedTarget === null || e.relatedTarget === document.documentElement) {
          console.log('🚨 Mouse left interview window');
          this.monitoringState.mouseInWindow = false;
          this.monitoringState.mouseOutOfBoundsCount++;
          
          if (this.monitoringState.mouseOutOfBoundsCount >= 3) {
            this.handleAdvancedViolation('mouseOutOfBounds', 
              `Mouse left interview window ${this.monitoringState.mouseOutOfBoundsCount} times`);
            this.monitoringState.mouseOutOfBoundsCount = 0; // Reset after reporting
          }
        }
      };
      
      // Detect mouse entering the window
      const handleMouseEnter = () => {
        if (!this.isSecurityActive()) return;
        
        this.monitoringState.mouseInWindow = true;
      };
      
      // Monitor for clicks outside the window (using focus events)
      const handleWindowBlur = () => {
        if (!this.isSecurityActive()) return;
        
        console.log('🚨 Window lost focus - possible outside click');
        this.handleAdvancedViolation('windowBlur', 
          'Window lost focus - possible click outside interview');
      };
      
      // Add event listeners
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseleave', handleMouseLeave);
      document.addEventListener('mouseenter', handleMouseEnter);
      window.addEventListener('blur', handleWindowBlur);
      
      cleanupFunctions.push(() => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseleave', handleMouseLeave);
        document.removeEventListener('mouseenter', handleMouseEnter);
        window.removeEventListener('blur', handleWindowBlur);
      });
      
      // Monitor mouse patterns for automation detection
      let mouseMovements = [];
      
      document.addEventListener('mousemove', (e) => {
        if (!this.isSecurityActive()) return;
        
        const movement = {
          x: e.clientX,
          y: e.clientY,
          timestamp: Date.now()
        };
        
        mouseMovements.push(movement);
        
        // Keep only last 50 movements
        if (mouseMovements.length > 50) {
          mouseMovements = mouseMovements.slice(-50);
          
          // Analyze for robotic patterns
          this.analyzeMousePatterns(mouseMovements);
        }
      });
      
      console.log('✅ Mouse boundary tracking initialized');
      
    } catch (error) {
      console.error('Error setting up mouse boundary tracking:', error);
    }
    
    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }

  // Analyze mouse patterns for automation
  analyzeMousePatterns(movements) {
    if (movements.length < 10) return;
    
    // Calculate movement vectors
    const vectors = [];
    for (let i = 1; i < movements.length; i++) {
      const dx = movements[i].x - movements[i-1].x;
      const dy = movements[i].y - movements[i-1].y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      const timeDiff = movements[i].timestamp - movements[i-1].timestamp;
      
      vectors.push({
        distance,
        speed: distance / (timeDiff || 1),
        angle: Math.atan2(dy, dx)
      });
    }
    
    // Check for robotic patterns (too uniform)
    const speeds = vectors.map(v => v.speed);
    const avgSpeed = speeds.reduce((a, b) => a + b, 0) / speeds.length;
    const speedVariance = this.calculateVariance(speeds);
    
    // Robotic movements often have very low variance
    if (speedVariance < 0.1 && avgSpeed > 0.5) {
      this.monitoringState.suspiciousMousePatterns++;
      
      if (this.monitoringState.suspiciousMousePatterns >= 3) {
        console.log('🚨 Robotic mouse movement detected');
        this.handleAdvancedViolation('roboticMouse', 
          'Robotic mouse movement patterns detected - possible automation');
        this.monitoringState.suspiciousMousePatterns = 0; // Reset after reporting
      }
    }
  }

  // Calculate variance for an array of numbers
  calculateVariance(numbers) {
    if (numbers.length === 0) return 0;
    
    const avg = numbers.reduce((a, b) => a + b, 0) / numbers.length;
    const squaredDiffs = numbers.map(n => Math.pow(n - avg, 2));
    return squaredDiffs.reduce((a, b) => a + b, 0) / numbers.length;
  }

  // SCREEN RECORDING DETECTION
  setupScreenRecordingDetection() {
    console.log('📹 Setting up screen recording detection...');
    
    const cleanupFunctions = [];
    
    try {
      // Monitor for screen recording APIs
      if ('mediaDevices' in navigator) {
        // Override getUserMedia to detect screen recording attempts
        const originalGetUserMedia = navigator.mediaDevices.getUserMedia;
        
        navigator.mediaDevices.getUserMedia = async (constraints) => {
          if (constraints.video && constraints.video.mediaSource === 'screen') {
            console.log('🚨 Screen recording attempt detected via getUserMedia');
            this.handleAdvancedViolation('screenRecording', 
              'Screen recording attempt detected via getUserMedia');
            
            throw new Error('Screen recording is not allowed during the interview');
          }
          
          return originalGetUserMedia.call(navigator.mediaDevices, constraints);
        };
        
        cleanupFunctions.push(() => {
          navigator.mediaDevices.getUserMedia = originalGetUserMedia;
        });
      }
      
      // Monitor for Media Recorder API usage (often used for screen recording)
      const originalMediaRecorder = window.MediaRecorder;
      
      window.MediaRecorder = function(stream, options) {
        console.log('🔍 MediaRecorder created, checking stream type...');
        
        // Check if this is a screen capture stream
        if (stream && stream.getVideoTracks) {
          const videoTracks = stream.getVideoTracks();
          videoTracks.forEach(track => {
            if (track.label && (track.label.includes('screen') || track.label.includes('display'))) {
              console.log('🚨 Screen recording MediaRecorder detected');
              this.handleAdvancedViolation('screenRecording', 
                'Screen recording MediaRecorder detected');
            }
          });
        }
        
        return new originalMediaRecorder(stream, options);
      }.bind(this);
      
      // Copy static properties
      Object.setPrototypeOf(window.MediaRecorder, originalMediaRecorder);
      Object.defineProperty(window.MediaRecorder, 'isTypeSupported', {
        value: originalMediaRecorder.isTypeSupported.bind(originalMediaRecorder)
      });
      
      cleanupFunctions.push(() => {
        window.MediaRecorder = originalMediaRecorder;
      });
      
      console.log('✅ Screen recording detection initialized');
      
    } catch (error) {
      console.error('Error setting up screen recording detection:', error);
    }
    
    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }

  // VIRTUAL MACHINE DETECTION
  detectVirtualMachine() {
    console.log('💻 Detecting virtual machine...');
    
    const vmIndicators = [];
    
    try {
      // Check user agent for VM signatures
      const userAgent = navigator.userAgent.toLowerCase();
      const vmSignatures = ['virtualbox', 'vmware', 'qemu', 'xen', 'kvm', 'parallels', 'hyper-v'];
      
      vmSignatures.forEach(signature => {
        if (userAgent.includes(signature)) {
          vmIndicators.push(`VM signature: ${signature}`);
        }
      });
      
      // Check hardware characteristics
      if (navigator.hardwareConcurrency <= 2) {
        vmIndicators.push('Low CPU count (suspicious)');
      }
      
      if (navigator.deviceMemory && navigator.deviceMemory <= 4) {
        vmIndicators.push('Low memory allocation (suspicious)');
      }
      
      // Check WebGL renderer for VM signatures
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      
      if (gl) {
        const renderer = gl.getParameter(gl.RENDERER).toLowerCase();
        const vendor = gl.getParameter(gl.VENDOR).toLowerCase();
        
        const vmGpuSignatures = ['virtualbox', 'vmware', 'qemu', 'software', 'llvmpipe', 'mesa'];
        
        vmGpuSignatures.forEach(signature => {
          if (renderer.includes(signature) || vendor.includes(signature)) {
            vmIndicators.push(`VM GPU detected: ${signature}`);
          }
        });
      }
      
      // Check screen resolution (VMs often have specific resolutions)
      const width = window.screen.width;
      const height = window.screen.height;
      const commonVmResolutions = [
        '1024x768', '1280x1024', '1440x900', '1600x900'
      ];
      
      const currentResolution = `${width}x${height}`;
      if (commonVmResolutions.includes(currentResolution)) {
        vmIndicators.push(`Common VM resolution: ${currentResolution}`);
      }
      
      // Check timezone vs likely location (basic check)
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      if (timezone.includes('UTC') || timezone === 'UTC') {
        vmIndicators.push('UTC timezone (often used in VMs)');
      }
      
      // Check for VM-specific browser features
      if (!window.chrome && !window.safari && navigator.userAgent.includes('HeadlessChrome')) {
        vmIndicators.push('Headless browser detected');
      }
      
      if (vmIndicators.length >= 2) {
        console.log('🚨 Virtual machine detected:', vmIndicators);
        this.handleAdvancedViolation('virtualMachine', 
          `Virtual machine detected: ${vmIndicators.join(', ')}`);
      }
      
    } catch (error) {
      console.error('Error detecting virtual machine:', error);
    }
    
    return vmIndicators;
  }

  // Main setup function
  setupAdvancedMonitoring() {
    console.log('🛡️ Setting up advanced monitoring security...');
    
    const cleanupFunctions = [];
    
    try {
      // Initialize all advanced monitoring features
      cleanupFunctions.push(this.setupDualMonitorDetection());
      cleanupFunctions.push(this.setupScreenSharingDetection());
      cleanupFunctions.push(this.setupMouseBoundaryTracking());
      cleanupFunctions.push(this.setupScreenRecordingDetection());
      
      // Run VM detection immediately and periodically
      this.detectVirtualMachine();
      const vmCheckInterval = setInterval(() => {
        this.detectVirtualMachine();
      }, 60000); // Check every minute
      
      cleanupFunctions.push(() => {
        clearInterval(vmCheckInterval);
      });
      
      console.log('✅ Advanced monitoring security initialized');
      
    } catch (error) {
      console.error('Error setting up advanced monitoring:', error);
    }
    
    // Return cleanup function
    return () => {
      console.log('🧹 Cleaning up advanced monitoring...');
      cleanupFunctions.forEach(cleanup => {
        try {
          cleanup();
        } catch (error) {
          console.error('Error in advanced monitoring cleanup:', error);
        }
      });
    };
  }

  // Update options (useful for React state updates)
  updateOptions(newOptions) {
    Object.assign(this, newOptions);
  }

  // Get monitoring statistics
  getMonitoringStats() {
    return {
      mouseOutOfBoundsCount: this.monitoringState.mouseOutOfBoundsCount,
      suspiciousMousePatterns: this.monitoringState.suspiciousMousePatterns,
      suspiciousConnections: this.screenSharingState.suspiciousConnections,
      screenCaptureAttempts: this.screenSharingState.screenCaptureAttempts,
      mouseInWindow: this.monitoringState.mouseInWindow,
      initialScreenConfig: this.monitoringState.initialScreenConfig
    };
  }

  // Cleanup method
  cleanup() {
    // Reset monitoring state
    this.monitoringState = {
      initialScreenConfig: null,
      mouseInWindow: true,
      lastMousePosition: { x: 0, y: 0 },
      mouseOutOfBoundsCount: 0,
      suspiciousMousePatterns: 0,
      lastViolationTime: 0,
      violationCooldown: 10000
    };
    
    this.screenSharingState = {
      webRTCConnections: new Set(),
      suspiciousConnections: 0,
      screenCaptureAttempts: 0
    };
  }
}

export default AdvancedMonitoringSecurity;
