// File: components/Navbar.js
// Fixed with proper dashboard button handling

import Link from "next/link";
import { useRouter } from 'next/router';
import { useSession, signIn, signOut } from "next-auth/react";
import { useState, useEffect } from 'react';

export default function Navbar() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  // Fix hydration by ensuring component only renders after mounting
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Debug session data
  useEffect(() => {
    if (session) {
      console.log('=== SESSION DEBUG ===');
      console.log('Full session object:', session);
      console.log('User object:', session.user);
      console.log('User type:', session.user?.userType);
      console.log('User email:', session.user?.email);
      console.log('===================');
    }
  }, [session]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isDropdownOpen) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [isDropdownOpen]);

  // Don't show navbar on interview screens OR home page
  if (!isMounted) {
    return null; // Return null during server-side rendering to prevent hydration mismatch
  }

  if (router.pathname.includes('/interview') || router.pathname === '/') {
    return null;
  }

  const handleSignOut = async () => {
    await signOut({ callbackUrl: '/' });
  };

  // FIXED: Dashboard click handler that checks database
  const handleDashboardClick = async (e) => {
    e.preventDefault();
    console.log('=== DASHBOARD CLICK ===');
    console.log('Session user type:', session?.user?.userType);
    
    try {
      // Always check database for the most current user type
      const response = await fetch(`/api/debug/check-user?email=${session?.user?.email}`);
      const userData = await response.json();
      
      console.log('Database user type:', userData.user?.userType);
      
      // Use database user type (most reliable)
      const actualUserType = userData.user?.userType;
      
      if (actualUserType === 'recruiter') {
        console.log('Navigating to recruiter dashboard');
        router.push('/recruiter/dashboard');
      } else {
        console.log('Navigating to candidate dashboard');
        router.push('/candidate-dashboard');
      }
    } catch (error) {
      console.error('Error checking user type:', error);
      
      // Fallback to session data if API fails
      const sessionUserType = session?.user?.userType;
      if (sessionUserType === 'recruiter') {
        router.push('/recruiter/dashboard');
      } else {
        router.push('/candidate-dashboard');
      }
    }
    
    console.log('=====================');
  };

  // FIXED: Get user dashboard with database check
  const getUserDashboard = async () => {
    console.log('=== GETTING USER DASHBOARD ===');
    console.log('Session exists:', !!session);
    console.log('User type from session:', session?.user?.userType);
    console.log('User email:', session?.user?.email);
    
    try {
      // Check database for actual user type
      const response = await fetch(`/api/debug/check-user?email=${session?.user?.email}`);
      const userData = await response.json();
      
      console.log('Database user type:', userData.user?.userType);
      
      const actualUserType = userData.user?.userType;
      
      if (actualUserType === 'recruiter') {
        console.log('Routing to: /recruiter/dashboard');
        return '/recruiter/dashboard';
      } else {
        console.log('Routing to: /candidate-dashboard');
        return '/candidate-dashboard';
      }
    } catch (error) {
      console.error('Error getting user dashboard:', error);
      
      // Fallback to session data
      const userType = session?.user?.userType;
      
      if (userType === 'recruiter') {
        console.log('Routing to: /recruiter/dashboard (fallback)');
        return '/recruiter/dashboard';
      } else {
        console.log('Routing to: /candidate-dashboard (fallback)');
        return '/candidate-dashboard';
      }
    }
  };

  const handleSignIn = (userType = 'candidate') => {
    console.log('=== SIGNING IN ===');
    console.log('User type:', userType);
    console.log('Target callback:', userType === 'recruiter' ? '/recruiter/dashboard' : '/candidate-dashboard');
    
    // Set the callback URL based on user type
    const callbackUrl = userType === 'recruiter' ? '/recruiter/dashboard' : '/candidate-dashboard';
    
    // Use Google sign-in with proper callback URL
    signIn('google', {
      prompt: 'select_account',
      callbackUrl: callbackUrl,
      // Add custom state to help identify user type
      state: JSON.stringify({ userType: userType })
    }).then(() => {
      console.log('Sign-in initiated for:', userType);
    }).catch((error) => {
      console.error('Sign-in error:', error);
    });
  };

  const handleEmployerAccess = () => {
    console.log('Employer access clicked - signing in as recruiter');
    handleSignIn('recruiter');
  };

  const handleCandidateAccess = () => {
    console.log('Candidate access clicked - signing in as candidate');
    handleSignIn('candidate');
  };

  const handleHomeClick = () => {
    // If user is logged in, add a parameter to force showing home page
    if (session) {
      router.push('/?view=home');
    } else {
      router.push('/');
    }
  };

  // FIXED: Dropdown dashboard click handler
  const handleDropdownDashboardClick = async () => {
    try {
      const response = await fetch(`/api/debug/check-user?email=${session?.user?.email}`);
      const userData = await response.json();
      
      const actualUserType = userData.user?.userType;
      
      if (actualUserType === 'recruiter') {
        router.push('/recruiter/dashboard');
      } else {
        router.push('/candidate-dashboard');
      }
    } catch (error) {
      console.error('Error:', error);
      const userType = session?.user?.userType;
      if (userType === 'recruiter') {
        router.push('/recruiter/dashboard');
      } else {
        router.push('/candidate-dashboard');
      }
    }
    setIsDropdownOpen(false);
  };

  return (
    <nav className="bg-gradient-to-r from-blue-100/40 via-cyan-100/40 to-teal-100/40 backdrop-blur-lg border-b border-white/30 sticky top-0 z-50 shadow-lg">
      <div className="w-full px-6">
        <div className="flex justify-between items-center h-16">
          
          {/* Left side - Logo and Navigation */}
          <div className="flex items-center space-x-8">
            {/* Navigation Links */}
            <div className="flex items-center space-x-6">
              {session ? (
                <button
                  onClick={handleHomeClick}
                  className="text-gray-800 hover:text-blue-600 transition-colors font-medium"
                >
                  Home
                </button>
              ) : (
                <Link href="/" className="text-gray-800 hover:text-blue-600 transition-colors font-medium">
                  Home
                </Link>
              )}
              
              {session && (
                <button
                  onClick={handleDashboardClick}
                  className={`text-gray-700 hover:text-blue-600 transition-colors font-medium ${
                    router.pathname.includes('/dashboard') ? 'border-b-2 border-blue-600 pb-1' : ''
                  }`}
                >
                  Dashboard
                </button>
              )}
              
              {/* Show Post Job link for recruiters OR if userType is unclear */}
              {(session?.user?.userType === 'recruiter' || 
                session?.user?.email?.includes('recruiter') ||
                session?.user?.email?.includes('hr')) && (
                <Link
                  href="/recruiter/post-job"
                  className={`text-gray-700 hover:text-blue-600 transition-colors font-medium ${
                    router.pathname === '/recruiter/post-job' ? 'border-b-2 border-blue-600 pb-1' : ''
                  }`}
                >
                  Post a Job
                </Link>
              )}
            </div>
          </div>

          {/* Center Logo */}
          <div className="absolute left-1/2 transform -translate-x-1/2">
            <img 
              src="/ArisIQ logo.png" 
              alt="ArisIQ - AI Powered Recruitment Platform" 
              className="h-16 w-auto drop-shadow-lg hover:scale-105 transition-transform duration-300"
            />
          </div>

          {/* Right side - Auth buttons */}
          <div className="flex items-center space-x-1">
            {session ? (
              /* Logged in user menu */
              <div className="relative">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsDropdownOpen(!isDropdownOpen);
                  }}
                  className="flex items-center space-x-2 text-gray-800 hover:text-blue-600 transition-colors"
                >
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-medium text-sm">
                      {session.user.name ? session.user.name.charAt(0).toUpperCase() : 'U'}
                    </span>
                  </div>
                  <span className="hidden md:block font-medium">
                    {session.user.name || session.user.email}
                  </span>
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>

                {/* Dropdown Menu */}
                {isDropdownOpen && (
                  <div 
                    className="absolute right-0 mt-2 w-64 bg-white/90 backdrop-blur-md rounded-md shadow-lg ring-1 ring-black/20 z-50 border border-white/30"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <div className="py-1">
                      <div className="px-4 py-2 text-sm text-gray-700 border-b">
                        <div className="font-medium">{session.user.name}</div>
                        <div className="text-gray-500">{session.user.email}</div>
                        <div className="text-xs text-blue-600 capitalize">
                          Type: {session.user.userType || 'Not set'}
                        </div>
                      </div>
                      
                      <button
                        onClick={() => {
                          handleHomeClick();
                          setIsDropdownOpen(false);
                        }}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        🏠 Home
                      </button>
                      
                      <button
                        onClick={handleDropdownDashboardClick}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        📊 Dashboard ({session.user.userType || 'checking...'})
                      </button>
                      
                      {(session?.user?.userType === 'recruiter' || 
                        session?.user?.email?.includes('recruiter') ||
                        session?.user?.email?.includes('hr')) && (
                        <Link
                          href="/recruiter/post-job"
                          onClick={() => setIsDropdownOpen(false)}
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >
                          ✏️ Post a Job
                        </Link>
                      )}
                      
                      <div className="border-t">
                        <button
                          onClick={handleSignOut}
                          className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                        >
                          🚪 Sign Out
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              /* Not logged in - show auth buttons */
              <>
                <button
                  onClick={handleCandidateAccess}
                  className="px-4 py-2 text-blue-600 hover:text-blue-700 transition-all duration-200 font-medium"
                >
                  I'm looking for a job
                </button>
                <span className="text-gray-500 mx-2">|</span>
                <button
                  onClick={handleEmployerAccess}
                  className="px-4 py-2 text-gray-700 hover:text-blue-600 transition-all duration-200 font-medium"
                >
                  I'm hiring talent
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}
