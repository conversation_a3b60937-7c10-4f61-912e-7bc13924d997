// File: components/WebcamFaceDetection.js - Part 1: IMPORTS AND LIBRARY LOADERS

'use client';
import React, { useEffect, useRef, useState, useImperativeHandle, forwardRef, useCallback } from 'react';

// =============================================================================
// PART 1: LIBRARY LOADERS AND INITIALIZATION
// =============================================================================

// Face-api.js loader with single instance prevention
const ensureFaceApiLoaded = () => {
  return new Promise((resolve, reject) => {
    if (window.faceApiLoaded && window.faceapi && window.faceapi.nets) {
      console.log('Face-api.js already loaded');
      resolve(true);
      return;
    }
    
    if (window.faceApiLoading) {
      console.log('Face-api.js already loading, waiting...');
      const checkInterval = setInterval(() => {
        if (window.faceApiLoaded) {
          clearInterval(checkInterval);
          resolve(true);
        }
      }, 100);
      return;
    }
    
    window.faceApiLoading = true;
    console.log('Loading face-api.js...');
    
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/@vladmandic/face-api/dist/face-api.min.js';
    script.onload = () => {
      console.log('✅ Face-api.js loaded successfully');
      window.faceApiLoaded = true;
      window.faceApiLoading = false;
      setTimeout(() => resolve(true), 500);
    };
    script.onerror = (error) => {
      console.error('❌ Failed to load face-api.js:', error);
      window.faceApiLoading = false;
      reject(error);
    };
    document.head.appendChild(script);
  });
};

// MediaPipe Hands loader
const ensureMediaPipeLoaded = () => {
  return new Promise((resolve, reject) => {
    if (window.Hands && window.drawingUtils && window.mediaPipeLoaded) {
      console.log('MediaPipe already loaded');
      resolve(true);
      return;
    }
    
    if (window.mediaPipeLoading) {
      console.log('MediaPipe already loading, waiting...');
      const checkInterval = setInterval(() => {
        if (window.mediaPipeLoaded) {
          clearInterval(checkInterval);
          resolve(true);
        }
      }, 100);
      return;
    }
    
    window.mediaPipeLoading = true;
    console.log('Loading MediaPipe Hands...');
    
    // Load MediaPipe Hands
    const script1 = document.createElement('script');
    script1.src = 'https://cdn.jsdelivr.net/npm/@mediapipe/hands/hands.js';
    
    const script2 = document.createElement('script');
    script2.src = 'https://cdn.jsdelivr.net/npm/@mediapipe/drawing_utils/drawing_utils.js';
    
    const script3 = document.createElement('script');
    script3.src = 'https://cdn.jsdelivr.net/npm/@mediapipe/camera_utils/camera_utils.js';
    
    let loadedScripts = 0;
    const totalScripts = 3;
    
    const checkAllLoaded = () => {
      loadedScripts++;
      if (loadedScripts === totalScripts) {
        console.log('✅ MediaPipe scripts loaded successfully');
        window.mediaPipeLoaded = true;
        window.mediaPipeLoading = false;
        setTimeout(() => resolve(true), 500);
      }
    };
    
    script1.onload = checkAllLoaded;
    script2.onload = checkAllLoaded;
    script3.onload = checkAllLoaded;
    
    script1.onerror = script2.onerror = script3.onerror = (error) => {
      console.error('❌ Failed to load MediaPipe:', error);
      window.mediaPipeLoading = false;
      reject(error);
    };
    
    document.head.appendChild(script1);
    document.head.appendChild(script2);
    document.head.appendChild(script3);
  });
};

// Model loader with error handling and retry logic
const loadFaceModels = async () => {
  try {
    console.log('🔄 Starting face model loading...');
    
    await ensureFaceApiLoaded();
    
    if (!window.faceapi || !window.faceapi.nets) {
      throw new Error('face-api.js not properly initialized');
    }
    
    console.log('📥 Loading face detection models...');
    
    const loadModel = async (modelLoader, modelName, maxRetries = 3) => {
      for (let i = 0; i < maxRetries; i++) {
        try {
          await modelLoader;
          console.log(`✅ ${modelName} loaded`);
          return true;
        } catch (error) {
          console.warn(`⚠️ ${modelName} failed (attempt ${i + 1}/${maxRetries}):`, error);
          if (i === maxRetries - 1) {
            throw error;
          }
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    };
    
    // Load models with retry logic
    await loadModel(
      window.faceapi.nets.ssdMobilenetv1.loadFromUri('/models'),
      'SSD MobileNet v1'
    );
    
    await loadModel(
      window.faceapi.nets.faceLandmark68TinyNet.loadFromUri('/models'),
      'Face Landmark 68 Tiny'
    );
    
    console.log('✅ Face detection models loaded successfully');
    return true;
  } catch (error) {
    console.error('❌ Error loading face models:', error);
    return false;
  }
};


// File: components/WebcamFaceDetection.js - Part 2: ULTRA-CONSERVATIVE PHONE DETECTOR (FIXED)

// =============================================================================
// PART 2: ULTRA-CONSERVATIVE PHONE DETECTION CLASS (MINIMAL FALSE POSITIVES)
// =============================================================================

class ImprovedPhoneDetector {
  constructor() {
    this.hands = null;
    this.initialized = false;
    this.lastResults = null;
    this.gestureHistory = [];
    this.phoneGestureConfidence = 0;
    this.detectionCount = 0;
    
    // ULTRA-CONSERVATIVE PARAMETERS - Much stricter to prevent false positives
    this.PHONE_CONFIDENCE_THRESHOLD = 0.85; // Increased from 0.7 to 0.85
    this.GESTURE_CONSISTENCY_REQUIRED = 8; // Increased from 5 to 8 consecutive detections
    this.TEMPORAL_SMOOTHING_WINDOW = 15; // Increased smoothing window
    this.ULTRA_CONSERVATIVE_MODE = true; // New ultra-conservative flag
    
    // Validation state tracking with stricter requirements
    this.validationState = {
      consecutivePhoneDetections: 0,
      lastConfidenceSpike: 0,
      handStabilityCounter: 0,
      gestureConsistencyScore: 0,
      falsePositiveCounter: 0, // Track false positive patterns
      lastValidationTime: 0
    };
    
    console.log('📱 ImprovedPhoneDetector initialized in ULTRA-CONSERVATIVE MODE');
  }
  
  async initialize() {
    try {
      await ensureMediaPipeLoaded();
      
      if (!window.Hands) {
        throw new Error('MediaPipe Hands not available');
      }
      
      this.hands = new window.Hands({
        locateFile: (file) => {
          return `https://cdn.jsdelivr.net/npm/@mediapipe/hands/${file}`;
        }
      });
      
      // ULTRA-CONSERVATIVE SETTINGS - highest possible thresholds
      this.hands.setOptions({
        maxNumHands: 2,
        modelComplexity: 1,
        minDetectionConfidence: 0.9, // Increased from 0.8 to 0.9
        minTrackingConfidence: 0.85  // Increased from 0.7 to 0.85
      });
      
      this.hands.onResults((results) => {
        this.lastResults = results;
        this.detectionCount++;
      });
      
      this.initialized = true;
      console.log('✅ Ultra-Conservative PhoneDetector initialized');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize Ultra-Conservative PhoneDetector:', error);
      return false;
    }
  }
  
  async detectHands(videoElement) {
    if (!this.initialized || !this.hands) {
      return this.getDefaultResult();
    }
    
    try {
      await this.hands.send({ image: videoElement });
      return this.analyzeHandResultsUltraConservative(this.lastResults);
    } catch (error) {
      console.warn('Ultra-Conservative phone detection error:', error);
      return this.getDefaultResult();
    }
  }
  
  getDefaultResult() {
    return {
      handsDetected: 0,
      handsRaised: false,
      suspiciousHandGesture: false,
      phoneGesture: false,
      handsOutOfFrame: false,
      phoneConfidence: 0,
      gestureDetails: null,
      detectionMethod: 'none',
      validationPassed: false,
      validationReasons: ['no_hands_detected']
    };
  }
  
  analyzeHandResultsUltraConservative(results) {
    if (!results || !results.multiHandLandmarks || results.multiHandLandmarks.length === 0) {
      this.updateGestureHistory(0);
      this.resetValidationState();
      return this.getDefaultResult();
    }
    
    const hands = results.multiHandLandmarks;
    const handCount = hands.length;
    
    // ULTRA-CONSERVATIVE: Immediately reject if both hands are visible
    if (handCount > 1) {
      console.log('👋 Both hands visible - rejecting phone detection (ultra-conservative)');
      this.updateGestureHistory(0);
      return {
        ...this.getDefaultResult(),
        handsDetected: handCount,
        validationReasons: ['both_hands_visible_impossible_phone_use']
      };
    }
    
    console.log(`👋 Analyzing ${handCount} hand with ULTRA-CONSERVATIVE validation...`);
    
    let maxPhoneConfidence = 0;
    let bestAnalysis = null;
    let handsRaised = false;
    
    // Analyze the single detected hand
    hands.forEach((handLandmarks, index) => {
      const handedness = results.multiHandedness[index]?.label || 'Unknown';
      const analysis = this.analyzePhoneHoldingUltraConservative(handLandmarks, handedness);
      
      if (analysis.phoneConfidence > maxPhoneConfidence) {
        maxPhoneConfidence = analysis.phoneConfidence;
        bestAnalysis = analysis;
      }
      
      if (analysis.isRaised) {
        handsRaised = true;
      }
    });
    
    // ULTRA-CONSERVATIVE VALIDATION PIPELINE
    const validationResult = this.validatePhoneDetectionUltraConservative(maxPhoneConfidence, bestAnalysis, handCount);
    
    // Update gesture history with validated confidence
    this.updateGestureHistory(validationResult.validatedConfidence);
    
    // Use temporal smoothing for final decision with stricter thresholds
    const smoothedConfidence = this.getSmoothedConfidence();
    const finalPhoneGesture = smoothedConfidence > this.PHONE_CONFIDENCE_THRESHOLD && 
                             validationResult.isValid && 
                             this.validationState.consecutivePhoneDetections >= this.GESTURE_CONSISTENCY_REQUIRED;
    
    // ULTRA-CONSERVATIVE LOGGING
    if (validationResult.validatedConfidence > 0.5 || finalPhoneGesture) {
      console.log('📱 ULTRA-CONSERVATIVE VALIDATION RESULT:', {
        rawConfidence: maxPhoneConfidence,
        validatedConfidence: validationResult.validatedConfidence,
        smoothedConfidence: smoothedConfidence,
        phoneGesture: finalPhoneGesture,
        validationPassed: validationResult.isValid,
        consecutiveDetections: this.validationState.consecutivePhoneDetections,
        requiredConsecutive: this.GESTURE_CONSISTENCY_REQUIRED,
        reasons: validationResult.validationReasons
      });
    }
    
    return {
      handsDetected: handCount,
      handsRaised: handsRaised,
      suspiciousHandGesture: false, // DISABLED - too many false positives
      phoneGesture: finalPhoneGesture,
      handsOutOfFrame: false,
      phoneConfidence: smoothedConfidence,
      gestureDetails: bestAnalysis,
      detectionMethod: 'ultra_conservative_validated',
      rawConfidence: maxPhoneConfidence,
      validationPassed: validationResult.isValid,
      validationReasons: validationResult.validationReasons
    };
  }
  
  analyzePhoneHoldingUltraConservative(handLandmarks, handedness) {
    // Get base phone holding analysis
    const baseAnalysis = this.analyzePhoneHoldingGesture(handLandmarks, handedness);
    
    // ULTRA-CONSERVATIVE VALIDATION FILTERS
    const validationFilters = {
      handStability: this.checkHandStabilityStrict(handLandmarks),
      phoneOrientation: this.validatePhoneOrientationStrict(handLandmarks),
      gripConsistency: this.validateGripConsistencyStrict(handLandmarks),
      contextualClues: this.analyzeContextualCluesStrict(handLandmarks),
      temporalConsistency: this.checkTemporalConsistency()
    };
    
    // ULTRA-CONSERVATIVE CONFIDENCE ADJUSTMENTS
    let adjustedConfidence = baseAnalysis.phoneConfidence;
    
    // More aggressive confidence reductions
    if (!validationFilters.handStability.isStable) {
      adjustedConfidence *= 0.3; // Reduced from 0.6 to 0.3
    }
    
    if (!validationFilters.phoneOrientation.isPhonelike) {
      adjustedConfidence *= 0.4; // Heavy penalty for non-phone orientation
    }
    
    if (!validationFilters.gripConsistency.isConsistent) {
      adjustedConfidence *= 0.4; // Reduced from 0.7 to 0.4
    }
    
    if (validationFilters.contextualClues.likelyFalsePositive) {
      adjustedConfidence *= 0.1; // Massive penalty for likely false positives
    }
    
    if (!validationFilters.temporalConsistency.isConsistent) {
      adjustedConfidence *= 0.2; // Heavy penalty for temporal inconsistency
    }
    
    return {
      ...baseAnalysis,
      phoneConfidence: Math.min(adjustedConfidence, 1.0),
      validationFilters
    };
  }
  
  checkHandStabilityStrict(landmarks) {
    // STRICTER hand stability check
    const wrist = landmarks[0];
    const fingertips = [landmarks[4], landmarks[8], landmarks[12], landmarks[16], landmarks[20]];
    
    const boundingBox = this.getBoundingBox(landmarks);
    const handSpread = Math.max(boundingBox.width, boundingBox.height);
    
    // Much stricter stability requirements
    const isStable = handSpread > 0.08 && handSpread < 0.20 && wrist.y > 0.3 && wrist.y < 0.7;
    
    return {
      isStable,
      handSpread,
      boundingBox,
      reason: isStable ? 'stable' : 'unstable_position'
    };
  }
  
  validatePhoneOrientationStrict(landmarks) {
    const wrist = landmarks[0];
    const middleTip = landmarks[12];
    const indexTip = landmarks[8];
    
    const handVector = {
      x: middleTip.x - wrist.x,
      y: middleTip.y - wrist.y
    };
    
    const angle = Math.atan2(handVector.y, handVector.x) * 180 / Math.PI;
    
    // STRICTER angle requirements for phone holding
    const isVerticalish = Math.abs(angle + 90) < 30 || Math.abs(angle - 90) < 30; // Reduced from 60 to 30
    const isHorizontalish = Math.abs(angle) < 30 || Math.abs(angle - 180) < 30; // Reduced from 60 to 30
    
    const isPhonelike = isVerticalish || isHorizontalish;
    
    return {
      isPhonelike,
      angle,
      isVerticalish,
      isHorizontalish,
      reason: isPhonelike ? 'phone_orientation' : 'non_phone_orientation'
    };
  }
  
  validateGripConsistencyStrict(landmarks) {
    // STRICTER grip pattern validation
    const thumb = landmarks[4];
    const index = landmarks[8];
    const middle = landmarks[12];
    const ring = landmarks[16];
    const pinky = landmarks[20];
    
    // Phone holding requires VERY specific grip patterns
    const thumbSide = thumb.x;
    const fingerSide = (index.x + middle.x + ring.x + pinky.x) / 4;
    
    // Much stricter opposite grip requirement
    const isOppositeGrip = Math.abs(thumbSide - fingerSide) > 0.15; // Increased from 0.1
    
    // Stricter finger spacing consistency
    const fingerSpacing = [
      Math.abs(index.y - middle.y),
      Math.abs(middle.y - ring.y),
      Math.abs(ring.y - pinky.y)
    ];
    
    const avgSpacing = fingerSpacing.reduce((a, b) => a + b) / fingerSpacing.length;
    const spacingVariance = fingerSpacing.reduce((sum, spacing) => 
      sum + Math.pow(spacing - avgSpacing, 2), 0) / fingerSpacing.length;
    
    const isConsistent = spacingVariance < 0.005 && isOppositeGrip; // Stricter variance threshold
    
    return {
      isConsistent,
      isOppositeGrip,
      spacingVariance,
      avgSpacing,
      reason: isConsistent ? 'consistent_grip' : 'inconsistent_grip'
    };
  }
  
  analyzeContextualCluesStrict(landmarks) {
    // ENHANCED false positive detection
    const wrist = landmarks[0];
    
    // Detect various natural gestures more aggressively
    const isWaving = this.detectWavingGestureStrict(landmarks);
    const isPointing = this.detectPointingGestureStrict(landmarks);
    const isNaturalRest = this.detectNaturalRestPositionStrict(landmarks);
    const isAdjustingHair = this.detectHairAdjustmentGesture(landmarks);
    const isScratchingFace = this.detectFaceScratchingGesture(landmarks);
    const isNormalHandMovement = this.detectNormalHandMovement(landmarks);
    
    const likelyFalsePositive = isWaving || isPointing || isNaturalRest || 
                               isAdjustingHair || isScratchingFace || isNormalHandMovement;
    
    return {
      likelyFalsePositive,
      isWaving,
      isPointing,
      isNaturalRest,
      isAdjustingHair,
      isScratchingFace,
      isNormalHandMovement,
      reason: likelyFalsePositive ? 'natural_gesture_detected' : 'no_natural_gesture'
    };
  }
  
  // NEW: Additional gesture detection methods
  detectHairAdjustmentGesture(landmarks) {
    const wrist = landmarks[0];
    const indexTip = landmarks[8];
    const middleTip = landmarks[12];
    
    // Hair adjustment: hand near head area
    return wrist.y < 0.3 && (indexTip.y < 0.2 || middleTip.y < 0.2);
  }
  
  detectFaceScratchingGesture(landmarks) {
    const fingertips = [landmarks[8], landmarks[12], landmarks[16], landmarks[20]];
    
    // Face scratching: fingertips in face area
    return fingertips.some(tip => tip.y < 0.4 && tip.x > 0.3 && tip.x < 0.7);
  }
  
  detectNormalHandMovement(landmarks) {
    const wrist = landmarks[0];
    const fingertips = [landmarks[4], landmarks[8], landmarks[12], landmarks[16], landmarks[20]];
    
    // Normal hand movement: relaxed finger positions
    const fingerSpread = this.getBoundingBox(fingertips);
    const isRelaxed = fingerSpread.width > 0.08 && fingerSpread.height > 0.08;
    
    return isRelaxed && wrist.y > 0.4;
  }
  
  checkTemporalConsistency() {
    // Check if detection is consistent over time
    const recentHistory = this.gestureHistory.slice(-5);
    if (recentHistory.length < 3) {
      return { isConsistent: false, reason: 'insufficient_history' };
    }
    
    const avgConfidence = recentHistory.reduce((sum, entry) => sum + entry.confidence, 0) / recentHistory.length;
    const isConsistent = avgConfidence > 0.6 && recentHistory.every(entry => entry.confidence > 0.4);
    
    return {
      isConsistent,
      avgConfidence,
      reason: isConsistent ? 'temporally_consistent' : 'temporally_inconsistent'
    };
  }
  
  validatePhoneDetectionUltraConservative(rawConfidence, analysis, handCount) {
    const validation = this.validationState;
    let validatedConfidence = rawConfidence;
    let isValid = true;
    let validationReasons = [];
    
    // Rule 1: ULTRA-STRICT minimum confidence threshold
    if (rawConfidence < 0.7) { // Increased from 0.5
      isValid = false;
      validationReasons.push('confidence_too_low');
    }
    
    // Rule 2: MUCH STRICTER consistency over time requirement
    if (rawConfidence > 0.75) { // Increased threshold for counting
      validation.consecutivePhoneDetections++;
    } else {
      validation.consecutivePhoneDetections = Math.max(0, validation.consecutivePhoneDetections - 2); // Faster decay
    }
    
    if (validation.consecutivePhoneDetections < this.GESTURE_CONSISTENCY_REQUIRED) {
      isValid = false;
      validationReasons.push(`consistency_insufficient_${validation.consecutivePhoneDetections}/${this.GESTURE_CONSISTENCY_REQUIRED}`);
    }
    
    // Rule 3: ENHANCED validation filter requirements
    if (analysis && analysis.validationFilters) {
      if (!analysis.validationFilters.handStability.isStable) {
        validatedConfidence *= 0.2; // More aggressive penalty
        validationReasons.push('hand_unstable');
      }
      
      if (analysis.validationFilters.contextualClues.likelyFalsePositive) {
        isValid = false;
        validatedConfidence *= 0.05; // Massive penalty
        validationReasons.push('contextual_false_positive');
      }
      
      if (!analysis.validationFilters.temporalConsistency.isConsistent) {
        validatedConfidence *= 0.3;
        validationReasons.push('temporal_inconsistency');
      }
    }
    
    // Rule 4: LONGER cooldown for rapid fire prevention
    const now = Date.now();
    if (now - validation.lastConfidenceSpike < 10000) { // Increased from 5000 to 10000ms
      validatedConfidence *= 0.1; // More aggressive penalty
      validationReasons.push('rapid_fire_prevention');
    }
    
    if (validatedConfidence > 0.8) {
      validation.lastConfidenceSpike = now;
    }
    
    // Rule 5: STRICT multi-hand validation
    if (handCount > 1) {
      isValid = false; // Immediately reject
      validatedConfidence = 0;
      validationReasons.push('multiple_hands_impossible_phone_use');
    }
    
    // Rule 6: NEW - False positive counter
    if (!isValid) {
      validation.falsePositiveCounter++;
      if (validation.falsePositiveCounter > 10) {
        // If too many false positives, become even more conservative
        validatedConfidence *= 0.1;
        validationReasons.push('excessive_false_positives');
      }
    } else {
      validation.falsePositiveCounter = Math.max(0, validation.falsePositiveCounter - 1);
    }
    
    return {
      validatedConfidence: Math.max(0, Math.min(validatedConfidence, 1.0)),
      isValid: isValid && validatedConfidence > 0.75, // Additional threshold check
      validationReasons
    };
  }
  
  resetValidationState() {
    this.validationState.consecutivePhoneDetections = Math.max(0, 
      this.validationState.consecutivePhoneDetections - 2); // Faster decay
    this.validationState.handStabilityCounter = 0;
    this.validationState.gestureConsistencyScore = 0;
  }
  
  getSmoothedConfidence() {
    if (this.gestureHistory.length === 0) return 0;
    
    // More aggressive smoothing with higher decay
    const weights = this.gestureHistory.map((_, index) => 
      Math.pow(0.8, this.gestureHistory.length - 1 - index)); // Reduced from 0.9 to 0.8
    
    const weightedSum = this.gestureHistory.reduce((sum, entry, index) => 
      sum + entry.confidence * weights[index], 0);
    
    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
    
    return totalWeight > 0 ? weightedSum / totalWeight : 0;
  }
  
  updateGestureHistory(phoneConfidence) {
    // Add new confidence entry with timestamp
    this.gestureHistory.push({
      confidence: phoneConfidence,
      timestamp: Date.now()
    });
    
    // Keep only recent entries for temporal smoothing
    const cutoffTime = Date.now() - (this.TEMPORAL_SMOOTHING_WINDOW * 1500);
    this.gestureHistory = this.gestureHistory.filter(
      entry => entry.timestamp > cutoffTime
    );
    
    // Limit history size for performance
    if (this.gestureHistory.length > this.TEMPORAL_SMOOTHING_WINDOW) {
      this.gestureHistory = this.gestureHistory.slice(-this.TEMPORAL_SMOOTHING_WINDOW);
    }
  }
  
  // STRICTER VERSIONS OF EXISTING DETECTION METHODS
  
  detectWavingGestureStrict(landmarks) {
    // More sensitive waving detection
    const fingertips = [landmarks[8], landmarks[12], landmarks[16], landmarks[20]];
    const fingerMCPs = [landmarks[5], landmarks[9], landmarks[13], landmarks[17]];
    
    let extendedFingers = 0;
    for (let i = 0; i < fingertips.length; i++) {
      const tipToMCP = Math.sqrt(
        Math.pow(fingertips[i].x - fingerMCPs[i].x, 2) + 
        Math.pow(fingertips[i].y - fingerMCPs[i].y, 2)
      );
      
      if (tipToMCP > 0.08) { // Reduced threshold from 0.1
        extendedFingers++;
      }
    }
    
    return extendedFingers >= 2; // Reduced from 3 to 2
  }
  
  detectPointingGestureStrict(landmarks) {
    // More sensitive pointing detection
    const indexTip = landmarks[8];
    const indexMCP = landmarks[5];
    const middleTip = landmarks[12];
    const middleMCP = landmarks[9];
    const ringTip = landmarks[16];
    const ringMCP = landmarks[13];
    
    const indexExtended = Math.sqrt(
      Math.pow(indexTip.x - indexMCP.x, 2) + 
      Math.pow(indexTip.y - indexMCP.y, 2)
    ) > 0.1;
    
    const middleCurled = Math.sqrt(
      Math.pow(middleTip.x - middleMCP.x, 2) + 
      Math.pow(middleTip.y - middleMCP.y, 2)
    ) < 0.09;
    
    const ringCurled = Math.sqrt(
      Math.pow(ringTip.x - ringMCP.x, 2) + 
      Math.pow(ringTip.y - ringMCP.y, 2)
    ) < 0.09;
    
    return indexExtended && (middleCurled || ringCurled);
  }
  
  detectNaturalRestPositionStrict(landmarks) {
    // More sensitive natural rest detection
    const fingertips = [landmarks[8], landmarks[12], landmarks[16], landmarks[20]];
    const palm = landmarks[0];
    
    const avgFingerDistance = fingertips.reduce((sum, tip) => {
      return sum + Math.sqrt(
        Math.pow(tip.x - palm.x, 2) + 
        Math.pow(tip.y - palm.y, 2)
      );
    }, 0) / fingertips.length;
    
    // Expanded range for natural rest position
    return avgFingerDistance > 0.06 && avgFingerDistance < 0.18; // Expanded range
  }
  
  // EXISTING CORE ANALYSIS METHODS (unchanged but with stricter thresholds in calling code)
  
  analyzePhoneHoldingGesture(handLandmarks, handedness) {
    const wrist = handLandmarks[0];
    const thumbTip = handLandmarks[4];
    const indexTip = handLandmarks[8];
    const middleTip = handLandmarks[12];
    
    let phoneConfidence = 0;
    const indicators = {};
    
    // 1. Grip pattern analysis (stricter scoring)
    const gripPattern = this.analyzeGripPattern(handLandmarks);
    phoneConfidence += gripPattern.score * 0.25; // Reduced from 0.3
    indicators.grip = gripPattern;
    
    // 2. Hand orientation analysis (stricter requirements)
    const orientation = this.calculateHandOrientation(handLandmarks);
    if (orientation.isVertical) {
      phoneConfidence += 0.15; // Reduced from 0.2
      indicators.orientation = 'vertical';
    }
    
    // 3. Finger position analysis (stricter scoring)
    const fingerCurls = this.analyzeFingersForPhone(handLandmarks);
    phoneConfidence += fingerCurls.phoneScore * 0.25; // Reduced from 0.3
    indicators.fingers = fingerCurls;
    
    // 4. Hand height analysis (stricter requirements)
    const handHeight = wrist.y;
    if (handHeight > 0.25 && handHeight < 0.75) { // Tighter range
      phoneConfidence += 0.15; // Reduced from 0.2
      indicators.height = 'appropriate';
    }
    
    return {
      phoneConfidence: Math.min(phoneConfidence, 1.0),
      isRaised: wrist.y < 0.65, // Adjusted threshold
      handedness,
      indicators,
      timestamp: Date.now()
    };
  }
  
  calculateHandOrientation(landmarks) {
    const wrist = landmarks[0];
    const middleTip = landmarks[12];
    
    const handVector = {
      x: middleTip.x - wrist.x,
      y: middleTip.y - wrist.y
    };
    
    const angle = Math.atan2(handVector.y, handVector.x) * 180 / Math.PI;
    const isVertical = Math.abs(angle + 90) < 35 || Math.abs(angle - 90) < 35; // Stricter
    
    return { isVertical, angle };
  }
  
  analyzeFingersForPhone(landmarks) {
    const indexCurl = this.calculateFingerCurl(landmarks, [5, 6, 7, 8]);
    const middleCurl = this.calculateFingerCurl(landmarks, [9, 10, 11, 12]);
    const ringCurl = this.calculateFingerCurl(landmarks, [13, 14, 15, 16]);
    const pinkyCurl = this.calculateFingerCurl(landmarks, [17, 18, 19, 20]);
    
    let phoneScore = 0;
    
    // Stricter phone holding pattern requirements
    if (indexCurl < 0.4) phoneScore += 0.08; // Reduced scoring
    if (middleCurl > 0.4 && middleCurl < 0.7) phoneScore += 0.12; // Stricter range
    if (ringCurl > 0.5 && pinkyCurl > 0.5) phoneScore += 0.08; // Stricter requirements
    
    return {
      phoneScore,
      indexCurl,
      middleCurl,
      ringCurl,
      pinkyCurl
    };
  }
  
  calculateFingerCurl(landmarks, fingerIndices) {
    const [mcp, pip, dip, tip] = fingerIndices.map(i => landmarks[i]);
    
    const mcpToTip = Math.sqrt(
      Math.pow(tip.x - mcp.x, 2) + Math.pow(tip.y - mcp.y, 2)
    );
    
    const jointDistances = 
      Math.sqrt(Math.pow(pip.x - mcp.x, 2) + Math.pow(pip.y - mcp.y, 2)) +
      Math.sqrt(Math.pow(dip.x - pip.x, 2) + Math.pow(dip.y - pip.y, 2)) +
      Math.sqrt(Math.pow(tip.x - dip.x, 2) + Math.pow(tip.y - dip.y, 2));
    
    return Math.max(0, Math.min(1, 1 - (mcpToTip / jointDistances)));
  }
  
  analyzeGripPattern(landmarks) {
    const thumb = landmarks[4];
    const index = landmarks[8];
    const middle = landmarks[12];
    const ring = landmarks[16];
    const pinky = landmarks[20];
    
    const fingertips = [thumb, index, middle, ring, pinky];
    const boundingBox = this.getBoundingBox(fingertips);
    
    const aspectRatio = boundingBox.width / boundingBox.height;
    const isRectangular = aspectRatio > 0.4 && aspectRatio < 1.0; // Stricter range
    
    return {
      score: isRectangular ? 0.12 : 0.03, // Reduced scoring
      aspectRatio,
      boundingBox
    };
  }
  
  getBoundingBox(points) {
    if (points.length === 0) return { minX: 0, maxX: 0, minY: 0, maxY: 0, width: 0, height: 0 };
    
    const xs = points.map(p => p.x);
    const ys = points.map(p => p.y);
    return {
      minX: Math.min(...xs),
      maxX: Math.max(...xs),
      minY: Math.min(...ys),
      maxY: Math.max(...ys),
      width: Math.max(...xs) - Math.min(...xs),
      height: Math.max(...ys) - Math.min(...ys)
    };
  }
}



// File: components/WebcamFaceDetection.js - Part 3: ULTRA-CONSERVATIVE DEVICE DETECTOR (FIXED)

// =============================================================================
// PART 3: ULTRA-CONSERVATIVE VISUAL DEVICE DETECTION CLASS
// =============================================================================

class ImprovedDeviceDetector {
  constructor() {
    this.initialized = true;
    this.detectionHistory = [];
    this.lastReportTime = 0;
    
    // ULTRA-CONSERVATIVE PARAMETERS - Much stricter to prevent false positives
    this.REPORT_COOLDOWN = 15000; // Increased to 15 seconds between reports
    this.CONFIDENCE_THRESHOLD = 0.9; // Increased from 0.75 to 0.9
    this.CONSECUTIVE_DETECTIONS_REQUIRED = 5; // Increased from 3 to 5
    this.consecutiveDetections = 0;
    this.lastFrameDevices = [];
    
    // Much stricter validation parameters
    this.VALIDATION_ENABLED = true;
    this.MIN_DEVICE_SIZE = { width: 60, height: 100 }; // Increased minimum size
    this.MAX_DEVICE_SIZE = { width: 200, height: 300 }; // Decreased maximum size
    this.ASPECT_RATIO_TOLERANCE = 0.2; // Much stricter aspect ratio
    this.MIN_UNIFORMITY_THRESHOLD = 0.7; // Require high uniformity for screens
    
    console.log('📱 Ultra-Conservative DeviceDetector initialized - very strict thresholds');
  }
  
  detectDevices(canvas, videoElement) {
    try {
      const ctx = canvas.getContext('2d');
      const width = canvas.width;
      const height = canvas.height;
      
      if (width < 200 || height < 200) {
        console.log('Canvas too small for device detection');
        return [];
      }
      
      // Create analysis canvas for image processing
      const analysisCanvas = document.createElement('canvas');
      analysisCanvas.width = width;
      analysisCanvas.height = height;
      const analysisCtx = analysisCanvas.getContext('2d');
      
      // Draw video frame for analysis
      analysisCtx.drawImage(videoElement, 0, 0, width, height);
      const imageData = analysisCtx.getImageData(0, 0, width, height);
      
      // ULTRA-CONSERVATIVE detection pipeline
      const candidates = this.findDeviceCandidatesUltraConservative(imageData, width, height);
      const validatedDevices = this.validateDeviceCandidatesUltraConservative(candidates, width, height);
      const finalDevices = this.applyUltraConservativeTemporalConsistency(validatedDevices);
      
      // Draw detection results on main canvas (minimal visual noise)
      this.drawDetectionResultsMinimal(ctx, finalDevices);
      
      // Update detection history and determine reportable devices
      this.updateDetectionHistory(finalDevices);
      const reportableDevices = this.getReportableDevicesUltraConservative(finalDevices);
      
      if (reportableDevices.length > 0) {
        console.log(`📱 ULTRA-CONSERVATIVE device detection: ${reportableDevices.length} devices after extensive validation`, 
          reportableDevices.map(d => ({ 
            type: d.type, 
            confidence: d.confidence.toFixed(2),
            temporalConsistency: d.temporalConsistency,
            uniformity: d.uniformity?.toFixed(2)
          })));
        return reportableDevices;
      }
      
      return finalDevices;
      
    } catch (error) {
      console.warn('Ultra-conservative device detection error:', error);
      return [];
    }
  }
  
  findDeviceCandidatesUltraConservative(imageData, width, height) {
    const data = imageData.data;
    const candidates = [];
    const visited = new Set();
    
    // MUCH more selective scanning - larger steps, avoid more edges
    for (let y = 50; y < height - 50; y += 20) { // Larger steps, more edge avoidance
      for (let x = 50; x < width - 50; x += 20) {
        const key = `${x},${y}`;
        if (visited.has(key)) continue;
        
        const pixelIndex = (y * width + x) * 4;
        const r = data[pixelIndex];
        const g = data[pixelIndex + 1];
        const b = data[pixelIndex + 2];
        
        // MUCH more restrictive brightness and color criteria
        const brightness = (r + g + b) / 3;
        const colorVariance = Math.abs(r - g) + Math.abs(g - b) + Math.abs(r - b);
        
        // Only look for very bright, very uniform areas (typical of device screens)
        if (brightness > 180 && colorVariance < 30) { // Much stricter criteria
          const region = this.expandBrightRegionConservative(data, width, height, x, y, visited);
          if (this.isValidCandidateSizeUltraConservative(region, width, height)) {
            const uniformity = this.calculateUniformity(data, width, height, region);
            
            // Only keep candidates with high uniformity
            if (uniformity > this.MIN_UNIFORMITY_THRESHOLD) {
              candidates.push({
                ...region,
                brightness,
                colorVariance,
                uniformity
              });
            }
          }
        }
      }
    }
    
    console.log(`📱 Found ${candidates.length} ultra-conservative device candidates`);
    return candidates;
  }
  
  expandBrightRegionConservative(data, width, height, startX, startY, visited) {
    let minX = startX, maxX = startX;
    let minY = startY, maxY = startY;
    
    const queue = [{x: startX, y: startY}];
    const processedPoints = new Set();
    
    // More limited expansion for conservative detection
    while (queue.length > 0 && processedPoints.size < 100) { // Reduced from 200
      const {x, y} = queue.shift();
      const key = `${x},${y}`;
      
      if (processedPoints.has(key) || x < 0 || x >= width || y < 0 || y >= height) {
        continue;
      }
      
      processedPoints.add(key);
      visited.add(key);
      
      const pixelIndex = (y * width + x) * 4;
      const brightness = (data[pixelIndex] + data[pixelIndex + 1] + data[pixelIndex + 2]) / 3;
      
      if (brightness > 160) { // Higher threshold for expansion
        minX = Math.min(minX, x);
        maxX = Math.max(maxX, x);
        minY = Math.min(minY, y);
        maxY = Math.max(maxY, y);
        
        // Larger steps for conservative expansion
        const step = 8;
        queue.push(
          {x: x + step, y}, {x: x - step, y}, 
          {x, y: y + step}, {x, y: y - step}
        );
      }
    }
    
    return {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY,
      area: (maxX - minX) * (maxY - minY),
      pixelCount: processedPoints.size
    };
  }
  
  isValidCandidateSizeUltraConservative(rect, canvasWidth, canvasHeight) {
    const { width, height } = rect;
    
    // MUCH stricter size validation
    if (width < this.MIN_DEVICE_SIZE.width || height < this.MIN_DEVICE_SIZE.height) return false;
    if (width > this.MAX_DEVICE_SIZE.width || height > this.MAX_DEVICE_SIZE.height) return false;
    
    // Reject regions that are too large relative to canvas
    if (width > canvasWidth * 0.3 || height > canvasHeight * 0.4) return false;
    
    const aspectRatio = width / height;
    
    // MUCH stricter aspect ratio validation
    const isPhoneAspect = aspectRatio >= 0.4 && aspectRatio <= 0.7; // Narrower range
    const isTabletAspect = aspectRatio >= 0.7 && aspectRatio <= 1.2; // Narrower range
    
    return isPhoneAspect || isTabletAspect;
  }
  
  calculateUniformity(data, width, height, region) {
    // Calculate color uniformity within the detected region
    const samples = [];
    const sampleStep = 6; // Smaller steps for more thorough checking
    
    for (let y = region.y; y < region.y + region.height; y += sampleStep) {
      for (let x = region.x; x < region.x + region.width; x += sampleStep) {
        if (x < width && y < height) {
          const pixelIndex = (y * width + x) * 4;
          const brightness = (data[pixelIndex] + data[pixelIndex + 1] + data[pixelIndex + 2]) / 3;
          samples.push(brightness);
        }
      }
    }
    
    if (samples.length < 6) return 0;
    
    const avgBrightness = samples.reduce((a, b) => a + b) / samples.length;
    const variance = samples.reduce((sum, brightness) => 
      sum + Math.pow(brightness - avgBrightness, 2), 0) / samples.length;
    
    // Lower variance indicates higher uniformity (characteristic of screens)
    return Math.max(0, 1 - (variance / 800)); // Adjusted for stricter uniformity
  }
  
  validateDeviceCandidatesUltraConservative(candidates, canvasWidth, canvasHeight) {
    return candidates.map(candidate => {
      let confidence = 0.2; // Lower base confidence
      const aspectRatio = candidate.width / candidate.height;
      
      // Device type classification with MUCH stricter confidence scoring
      let deviceType = 'unknown';
      
      if (aspectRatio >= 0.4 && aspectRatio <= 0.7) {
        deviceType = 'phone';
        confidence += 0.2; // Reduced from 0.3
        
        // Much stricter phone characteristics
        if (aspectRatio >= 0.45 && aspectRatio <= 0.65) confidence += 0.1; // Narrower ideal range
        if (candidate.height > candidate.width * 1.6) confidence += 0.05; // Portrait bonus reduced
        
      } else if (aspectRatio >= 0.7 && aspectRatio <= 1.2) {
        deviceType = 'tablet';
        confidence += 0.15; // Reduced from 0.25
        
        // Much stricter tablet characteristics
        if (aspectRatio >= 0.75 && aspectRatio <= 1.0) confidence += 0.05; // Narrower ideal range
      }
      
      // MUCH stricter validation scores
      const sizeScore = this.calculateSizeScoreStrict(candidate, canvasWidth, canvasHeight);
      confidence += sizeScore * 0.15; // Reduced multiplier
      
      const uniformityScore = candidate.uniformity * 0.2; // Increased weight for uniformity
      confidence += uniformityScore;
      
      const brightnessScore = Math.min((candidate.brightness - 150) / 80, 1) * 0.08; // Stricter brightness
      confidence += brightnessScore;
      
      const positionScore = this.calculatePositionScoreStrict(candidate, canvasWidth, canvasHeight) * 0.03;
      confidence += positionScore;
      
      const edgeScore = this.calculateEdgeScore(candidate) * 0.05; // Reduced multiplier
      confidence += edgeScore;
      
      return {
        ...candidate,
        type: deviceType,
        confidence: Math.min(confidence, 1.0),
        aspectRatio,
        validationScores: {
          size: sizeScore,
          uniformity: candidate.uniformity,
          brightness: brightnessScore / 0.08,
          position: positionScore / 0.03,
          edges: edgeScore / 0.05
        }
      };
    }).filter(device => device.confidence > 0.6); // Much higher filter threshold
  }
  
  calculateSizeScoreStrict(candidate, canvasWidth, canvasHeight) {
    const { width, height } = candidate;
    const canvasArea = canvasWidth * canvasHeight;
    const deviceArea = width * height;
    const relativeSizeToCanvas = deviceArea / canvasArea;
    
    // MUCH stricter size scoring
    if (relativeSizeToCanvas >= 0.03 && relativeSizeToCanvas <= 0.12) {
      return 1.0; // Perfect size range (narrower)
    } else if (relativeSizeToCanvas >= 0.02 && relativeSizeToCanvas <= 0.18) {
      return 0.6; // Good size range (narrower)
    } else if (relativeSizeToCanvas >= 0.015 && relativeSizeToCanvas <= 0.25) {
      return 0.3; // Acceptable size range (narrower)
    }
    
    return 0.05; // Poor size
  }
  
  calculatePositionScoreStrict(candidate, canvasWidth, canvasHeight) {
    const centerX = candidate.x + candidate.width / 2;
    const centerY = candidate.y + candidate.height / 2;
    
    const canvasCenterX = canvasWidth / 2;
    const canvasCenterY = canvasHeight / 2;
    
    // Calculate distance from center of canvas
    const distanceFromCenter = Math.sqrt(
      Math.pow(centerX - canvasCenterX, 2) + Math.pow(centerY - canvasCenterY, 2)
    );
    
    const maxDistance = Math.sqrt(Math.pow(canvasWidth, 2) + Math.pow(canvasHeight, 2)) / 2;
    const normalizedDistance = distanceFromCenter / maxDistance;
    
    // MUCH stricter position requirements - prefer center more heavily
    return Math.max(0, 1 - normalizedDistance * 2.0); // Increased penalty for edge positions
  }
  
  calculateEdgeScore(candidate) {
    // Stricter edge score requirements
    const aspectRatio = candidate.width / candidate.height;
    const areaEfficiency = candidate.area / (candidate.width * candidate.height);
    
    // Require better rectangular properties
    const aspectRatioScore = (aspectRatio > 0.4 && aspectRatio < 1.5) ? 0.3 : 0;
    const areaScore = Math.min(areaEfficiency, 0.9);
    
    return aspectRatioScore + areaScore;
  }
  
  applyUltraConservativeTemporalConsistency(devices) {
    // MUCH stricter temporal validation
    const consistentDevices = [];
    
    for (const device of devices) {
      let consistencyScore = 0;
      
      // Check if similar device was detected in previous frame
      for (const prevDevice of this.lastFrameDevices) {
        const overlap = this.calculateOverlap(device, prevDevice);
        const sizeConsistency = Math.abs(device.width - prevDevice.width) < 15 && 
                               Math.abs(device.height - prevDevice.height) < 15; // Stricter
        const confidenceConsistency = Math.abs(device.confidence - prevDevice.confidence) < 0.2;
        
        if (overlap > 0.7 && sizeConsistency && confidenceConsistency) { // Stricter overlap requirement
          consistencyScore = Math.max(consistencyScore, 0.4);
          
          // Smaller confidence boost for consistent detections
          device.confidence = Math.min(device.confidence + 0.1, 1.0); // Reduced from 0.2
          device.temporalConsistency = true;
        }
      }
      
      // MUCH stricter consistency requirements
      if (consistencyScore > 0 || device.confidence > 0.9) { // Higher confidence requirement
        consistentDevices.push({
          ...device,
          consistencyScore
        });
      }
    }
    
    // Store current frame devices for next frame comparison
    this.lastFrameDevices = devices.slice();
    
    return consistentDevices;
  }
  
  calculateOverlap(device1, device2) {
    const x1 = Math.max(device1.x, device2.x);
    const y1 = Math.max(device1.y, device2.y);
    const x2 = Math.min(device1.x + device1.width, device2.x + device2.width);
    const y2 = Math.min(device1.y + device1.height, device2.y + device2.height);
    
    if (x2 <= x1 || y2 <= y1) return 0;
    
    const overlapArea = (x2 - x1) * (y2 - y1);
    const totalArea = device1.area + device2.area - overlapArea;
    
    return overlapArea / totalArea;
  }
  
  updateDetectionHistory(devices) {
    const now = Date.now();
    const highConfidenceDevices = devices.filter(d => d.confidence > this.CONFIDENCE_THRESHOLD);
    
    // Update consecutive detection counter with stricter requirements
    if (highConfidenceDevices.length > 0 && highConfidenceDevices.every(d => d.temporalConsistency)) {
      this.consecutiveDetections++;
      console.log(`📱 Consecutive ULTRA-CONSERVATIVE detections: ${this.consecutiveDetections}/${this.CONSECUTIVE_DETECTIONS_REQUIRED}`);
    } else {
      this.consecutiveDetections = Math.max(0, this.consecutiveDetections - 2); // Faster decay
    }
    
    // Clean up old history entries (keep last 20 seconds)
    this.detectionHistory = this.detectionHistory.filter(
      entry => (now - entry.timestamp) < 20000
    );
    
    // Add current detection to history
    this.detectionHistory.push({
      timestamp: now,
      deviceCount: highConfidenceDevices.length,
      devices: highConfidenceDevices.map(d => ({
        type: d.type,
        confidence: d.confidence,
        size: { width: d.width, height: d.height },
        temporalConsistency: d.temporalConsistency || false,
        uniformity: d.uniformity
      }))
    });
  }
  
  getReportableDevicesUltraConservative(devices) {
    const now = Date.now();
    const ultraHighConfidenceDevices = devices.filter(d => d.confidence > this.CONFIDENCE_THRESHOLD && d.temporalConsistency);
    
    // ULTRA-CONSERVATIVE reporting requirements
    const shouldReport = 
      this.consecutiveDetections >= this.CONSECUTIVE_DETECTIONS_REQUIRED &&
      (now - this.lastReportTime) > this.REPORT_COOLDOWN &&
      ultraHighConfidenceDevices.length > 0 &&
      ultraHighConfidenceDevices.every(d => d.uniformity > this.MIN_UNIFORMITY_THRESHOLD);
    
    if (shouldReport) {
      console.log(`🚨 Reporting ${ultraHighConfidenceDevices.length} ULTRA-VALIDATED devices after ${this.consecutiveDetections} consecutive detections with temporal consistency`);
      this.lastReportTime = now;
      return ultraHighConfidenceDevices;
    }
    
    return [];
  }
  
  drawDetectionResultsMinimal(ctx, devices) {
    // Only draw very high confidence detections to reduce visual noise
    devices.forEach(device => {
      if (device.confidence > 0.85 && device.temporalConsistency) { // Much higher threshold
        // Color based on ultra-high confidence
        let strokeColor = 'red';
        if (device.confidence > 0.95) strokeColor = 'darkred';
        
        ctx.strokeStyle = strokeColor;
        ctx.lineWidth = 3; // Thicker for high confidence
        ctx.strokeRect(device.x, device.y, device.width, device.height);
        
        // Minimal labeling
        ctx.fillStyle = strokeColor;
        ctx.font = 'bold 12px Arial';
        const label = `${device.type} ${Math.round(device.confidence * 100)}%`;
        const labelY = device.y > 25 ? device.y - 8 : device.y + device.height + 20;
        ctx.fillText(label, device.x, labelY);
        
        // Temporal consistency indicator
        if (device.temporalConsistency) {
          ctx.fillStyle = 'lime';
          ctx.fillRect(device.x - 4, device.y - 4, 8, 8); // Larger indicator
        }
      }
    });
  }
  
  // Enhanced debug method
  getDetectionStats() {
    return {
      consecutiveDetections: this.consecutiveDetections,
      historyLength: this.detectionHistory.length,
      lastReportTime: this.lastReportTime,
      timeSinceLastReport: Date.now() - this.lastReportTime,
      thresholds: {
        confidence: this.CONFIDENCE_THRESHOLD,
        consecutive: this.CONSECUTIVE_DETECTIONS_REQUIRED,
        cooldown: this.REPORT_COOLDOWN,
        uniformity: this.MIN_UNIFORMITY_THRESHOLD
      },
      recentDetections: this.detectionHistory.slice(-3).map(entry => ({
        timestamp: new Date(entry.timestamp).toLocaleString(),
        deviceCount: entry.deviceCount,
        devices: entry.devices.map(d => ({
          type: d.type,
          confidence: d.confidence.toFixed(2),
          temporalConsistency: d.temporalConsistency,
          uniformity: d.uniformity?.toFixed(2)
        }))
      }))
    };
  }
}




// File: components/WebcamFaceDetection.js - Part 4: MAIN COMPONENT IMPLEMENTATION

// =============================================================================
// PART 4: MAIN WEBCAM FACE DETECTION COMPONENT
// =============================================================================

const WebcamFaceDetection = forwardRef((props, ref) => {
  const { onStatusUpdate, onDeviceViolation } = props;
  
  // =============================================================================
  // REFS AND STATE MANAGEMENT
  // =============================================================================
  
  // Component refs
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const streamRef = useRef(null);
  const detectionIntervalRef = useRef(null);
  const isComponentMountedRef = useRef(false);
  const modelsLoadedRef = useRef(false);
  const detectionRunningRef = useRef(false);
  const initializingRef = useRef(false);
  const statusUpdateTimeoutRef = useRef(null);
  
  // Detector instances
  const phoneDetectorRef = useRef(null);
  const deviceDetectorRef = useRef(null);
  
  // Face detection state
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState(null);
  const [facesDetected, setFacesDetected] = useState(0);
  const [lookingAway, setLookingAway] = useState(false);
  const [faceQuality, setFaceQuality] = useState('unknown');
  const [faceConfidence, setFaceConfidence] = useState(0);
  const [modelLoadingStatus, setModelLoadingStatus] = useState('loading');
  
  // Enhanced phone/device detection state
  const [handsDetected, setHandsDetected] = useState(0);
  const [handsRaised, setHandsRaised] = useState(false);
  const [suspiciousHandGesture, setSuspiciousHandGesture] = useState(false);
  const [phoneGesture, setPhoneGesture] = useState(false);
  const [phoneConfidence, setPhoneConfidence] = useState(0);
  const [devicesDetected, setDevicesDetected] = useState([]);
  const [combinedPhoneConfidence, setCombinedPhoneConfidence] = useState(0);
  
  // Ultra-conservative detection state using useRef for persistence
  const detectionStateRef = useRef({
    // Face detection tracking
    consecutiveFaceNotVisible: 0,
    consecutiveLookingAway: 0,
    consecutiveMultipleFaces: 0,
    lastFaceDetectionTime: Date.now(),
    faceNotVisibleStartTime: null,
    faceNotVisibleDuration: 0,
    FACE_NOT_VISIBLE_THRESHOLD: 30000, // 30 seconds before flagging
    LOOKING_AWAY_THRESHOLD: 20000, // 20 seconds before flagging
    lastViolationReportTime: 0,
    MIN_VIOLATION_INTERVAL: 30000, // 30 seconds between same violation types
    
    // Enhanced phone detection tracking
    consecutiveHandsRaised: 0,
    consecutiveHandsOutOfFrame: 0,
    consecutiveSuspiciousGestures: 0,
    handsRaisedStartTime: null,
    handsOutOfFrameStartTime: null,
    HANDS_RAISED_THRESHOLD: 3000, // 3 seconds
    HANDS_OUT_OF_FRAME_THRESHOLD: 8000, // 8 seconds
    
    // Device detection tracking
    consecutiveDevicesDetected: 0,
    devicesDetectedStartTime: null,
    DEVICE_DETECTION_THRESHOLD: 2000, // 2 seconds
    lastPhoneViolationTime: 0,
    PHONE_VIOLATION_COOLDOWN: 10000 // 10 seconds between phone violations
  });
  
  // =============================================================================
  // STATUS UPDATE FUNCTION
  // =============================================================================
  
  const updateStatus = useCallback((status) => {
    if (!onStatusUpdate || !isComponentMountedRef.current) {
      console.error('❌ [WEBCAM] Cannot send status - callback missing or unmounted');
      return;
    }
    
    try {
      // Clear any pending timeout
      if (statusUpdateTimeoutRef.current) {
        clearTimeout(statusUpdateTimeoutRef.current);
      }
      
      // Comprehensive status object with all detection data
      const statusWithDefaults = {
        isDetecting: Boolean(status.isDetecting),
        facesDetected: Number(status.facesDetected || 0),
        lookingAway: Boolean(status.lookingAway),
        faceConfidence: Number(status.faceConfidence || 0),
        faceQuality: String(status.faceQuality || 'unknown'),
        consecutiveFaceNotVisible: Number(status.consecutiveFaceNotVisible || 0),
        consecutiveLookingAway: Number(status.consecutiveLookingAway || 0),
        consecutiveMultipleFaces: Number(status.consecutiveMultipleFaces || 0),
        
        // Enhanced phone detection data
        handsDetected: Number(status.handsDetected || 0),
        handsRaised: Boolean(status.handsRaised || false),
        suspiciousHandGesture: Boolean(status.suspiciousHandGesture || false),
        phoneGesture: Boolean(status.phoneGesture || false),
        phoneConfidence: Number(status.phoneConfidence || 0),
        rawPhoneConfidence: Number(status.rawPhoneConfidence || 0),
        consecutiveHandsRaised: Number(status.consecutiveHandsRaised || 0),
        consecutiveHandsOutOfFrame: Number(status.consecutiveHandsOutOfFrame || 0),
        motionLevel: Number(status.motionLevel || 0),
        screenshotRisk: Number(status.screenshotRisk || 0),
        
        // Device detection data
        devicesDetected: Array.isArray(status.devicesDetected) ? status.devicesDetected : [],
        deviceDetected: Boolean(status.deviceDetected || false),
        consecutiveDevicesDetected: Number(status.consecutiveDevicesDetected || 0),
        highConfidenceDevices: Array.isArray(status.highConfidenceDevices) ? status.highConfidenceDevices : [],
        combinedPhoneConfidence: Number(status.combinedPhoneConfidence || 0),
        detectionMethod: String(status.detectionMethod || 'none'),
        
        // Validation data
        validationPassed: Boolean(status.validationPassed || false),
        validationReasons: Array.isArray(status.validationReasons) ? status.validationReasons : [],
        
        timestamp: Date.now(),
        ...status
      };
      
      console.log('📤 [WEBCAM] Sending comprehensive status update:', {
        faces: statusWithDefaults.facesDetected,
        phoneConfidence: statusWithDefaults.phoneConfidence,
        phoneGesture: statusWithDefaults.phoneGesture,
        validationPassed: statusWithDefaults.validationPassed,
        devicesCount: statusWithDefaults.devicesDetected.length,
        combinedConfidence: statusWithDefaults.combinedPhoneConfidence
      });
      
      // Send status update immediately
      onStatusUpdate(statusWithDefaults);
      console.log('✅ [WEBCAM] Status update sent successfully');
      
    } catch (error) {
      console.error('❌ [WEBCAM] Error sending status update:', error);
    }
  }, [onStatusUpdate]);
  
  // =============================================================================
  // CAMERA INITIALIZATION
  // =============================================================================
  
  const initializeCamera = useCallback(async () => {
    if (initializingRef.current) {
      console.log('Camera already initializing, skipping...');
      return false;
    }
    
    try {
      initializingRef.current = true;
      console.log('🎥 Initializing camera...');
      
      updateStatus({
        isDetecting: false,
        facesDetected: 0,
        lookingAway: false,
        faceConfidence: 0,
        faceQuality: 'initializing'
      });
      
      // Stop existing stream if any (but preserve saved interview stream)
      if (streamRef.current && streamRef.current !== window.savedInterviewStream) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
      
      let stream = null;
      
      // Try to use existing saved stream first
      if (window.savedInterviewStream && window.savedInterviewStream.active) {
        console.log('✅ Using saved interview stream');
        stream = window.savedInterviewStream;
      } else {
        console.log('⚠️ Creating new camera stream');
        try {
          stream = await navigator.mediaDevices.getUserMedia({
            video: {
              width: { ideal: 640, max: 1280 },
              height: { ideal: 480, max: 720 },
              frameRate: { ideal: 15, max: 30 },
              facingMode: 'user'
            },
            audio: false
          });
        } catch (mediaError) {
          console.error('❌ Media access error:', mediaError);
          setError(`Camera access denied: ${mediaError.message}`);
          updateStatus({
            isDetecting: false,
            facesDetected: 0,
            lookingAway: false,
            faceConfidence: 0,
            faceQuality: 'camera_error',
            error: mediaError.message
          });
          return false;
        }
      }
      
      if (!isComponentMountedRef.current) {
        if (stream && stream !== window.savedInterviewStream) {
          stream.getTracks().forEach(track => track.stop());
        }
        return false;
      }
      
      streamRef.current = stream;
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        
        const setupVideo = () => {
          return new Promise((resolve, reject) => {
            const video = videoRef.current;
            
            const onLoadedMetadata = () => {
              console.log('✅ Video metadata loaded');
              console.log('Video dimensions:', video.videoWidth, 'x', video.videoHeight);
              
              if (canvasRef.current) {
                canvasRef.current.width = video.videoWidth;
                canvasRef.current.height = video.videoHeight;
                console.log('Canvas size set to:', canvasRef.current.width, 'x', canvasRef.current.height);
              }
              
              video.removeEventListener('loadedmetadata', onLoadedMetadata);
              video.removeEventListener('error', onError);
              resolve();
            };
            
            const onError = (error) => {
              console.error('Video error:', error);
              video.removeEventListener('loadedmetadata', onLoadedMetadata);
              video.removeEventListener('error', onError);
              reject(error);
            };
            
            video.addEventListener('loadedmetadata', onLoadedMetadata);
            video.addEventListener('error', onError);
          });
        };
        
        try {
          await setupVideo();
          await videoRef.current.play();
          console.log('✅ Video playing');
          
          setIsInitialized(true);
          setError(null);
          
          // Send initial status update that camera is now active
          updateStatus({
            isDetecting: true,
            facesDetected: 0,
            lookingAway: false,
            faceConfidence: 0,
            faceQuality: 'camera_ready'
          });
          
          if (modelsLoadedRef.current) {
            console.log('🚀 Starting detection (everything ready)');
            startComprehensiveDetection();
          }
        } catch (playError) {
          console.error('Error setting up video:', playError);
          // Retry video play after a delay
          setTimeout(async () => {
            if (videoRef.current && isComponentMountedRef.current) {
              try {
                await videoRef.current.play();
                console.log('✅ Video playing (retry successful)');
              } catch (retryError) {
                console.error('Video retry failed:', retryError);
              }
            }
          }, 1000);
        }
      }
      
      return true;
    } catch (error) {
      console.error('❌ Camera initialization error:', error);
      setError(`Camera error: ${error.message}`);
      
      updateStatus({
        isDetecting: false,
        facesDetected: 0,
        lookingAway: false,
        faceConfidence: 0,
        faceQuality: 'camera_error',
        error: error.message
      });
      
      return false;
    } finally {
      initializingRef.current = false;
    }
  }, [updateStatus]);




// File: components/WebcamFaceDetection.js - Part 5: ULTRA-CONSERVATIVE DETECTION WITH BETTER FACE FILTERING (FIXED)

// =============================================================================
// PART 5: ULTRA-CONSERVATIVE DETECTION ENGINE (FIXED FACE FILTERING)
// =============================================================================

  // Helper function to filter valid faces (distinguish real faces from artwork/statues)
  const filterValidFaces = useCallback((detections, canvasWidth, canvasHeight) => {
    if (!detections || detections.length <= 1) {
      return detections; // No filtering needed for 0 or 1 face
    }
    
    console.log(`🔍 Filtering ${detections.length} detected faces...`);
    
    const validFaces = [];
    
    detections.forEach((detection, index) => {
      const box = detection.detection.box;
      const confidence = detection.detection.score;
      
      // Calculate face metrics
      const faceArea = box.width * box.height;
      const canvasArea = canvasWidth * canvasHeight;
      const relativeSize = faceArea / canvasArea;
      
      // Face position (center region is more likely to be the real person)
      const centerX = box.x + box.width / 2;
      const centerY = box.y + box.height / 2;
      const distanceFromCenter = Math.sqrt(
        Math.pow(centerX - canvasWidth / 2, 2) + 
        Math.pow(centerY - canvasHeight / 2, 2)
      );
      const normalizedDistance = distanceFromCenter / Math.sqrt(canvasWidth * canvasWidth + canvasHeight * canvasHeight);
      
      // Face aspect ratio (real faces have typical proportions)
      const aspectRatio = box.width / box.height;
      const isReasonableAspectRatio = aspectRatio > 0.6 && aspectRatio < 1.4;
      
      // Scoring system for face validity
      let validityScore = 0;
      
      // High confidence = more likely real
      if (confidence > 0.9) validityScore += 3;
      else if (confidence > 0.8) validityScore += 2;
      else if (confidence > 0.7) validityScore += 1;
      
      // Reasonable size = more likely real (not too small like background artwork)
      if (relativeSize > 0.05 && relativeSize < 0.4) validityScore += 2;
      else if (relativeSize > 0.02 && relativeSize < 0.6) validityScore += 1;
      
      // Center position = more likely the main person
      if (normalizedDistance < 0.3) validityScore += 2;
      else if (normalizedDistance < 0.5) validityScore += 1;
      
      // Reasonable proportions = more likely real
      if (isReasonableAspectRatio) validityScore += 1;
      
      // Face in upper portion of frame = more likely real person
      if (centerY < canvasHeight * 0.7) validityScore += 1;
      
      console.log(`Face ${index}: confidence=${confidence.toFixed(2)}, size=${relativeSize.toFixed(3)}, center-dist=${normalizedDistance.toFixed(2)}, aspect=${aspectRatio.toFixed(2)}, score=${validityScore}`);
      
      // Keep faces with high validity scores
      if (validityScore >= 4) { // Increased threshold for stricter filtering
        validFaces.push(detection);
        console.log(`✅ Face ${index} kept (score: ${validityScore})`);
      } else {
        console.log(`❌ Face ${index} filtered out (score: ${validityScore}) - likely artwork/statue`);
      }
    });
    
    console.log(`🔍 Filtered result: ${validFaces.length} valid faces from ${detections.length} detections`);
    return validFaces;
  }, []);

  const startComprehensiveDetection = useCallback(async () => {
    if (detectionRunningRef.current) {
      console.log('Detection already running, skipping...');
      return;
    }
    
    console.log('🎯 Starting ULTRA-CONSERVATIVE detection with enhanced face filtering...');
    
    if (!modelsLoadedRef.current || !window.faceapi?.nets?.ssdMobilenetv1?.isLoaded) {
      console.log('⚠️ Models not ready, retrying in 1 second...');
      setTimeout(() => {
        if (isComponentMountedRef.current && !detectionRunningRef.current) {
          startComprehensiveDetection();
        }
      }, 1000);
      return;
    }
    
    // Initialize ultra-conservative phone detector
    if (!phoneDetectorRef.current) {
      phoneDetectorRef.current = new ImprovedPhoneDetector();
      const phoneInitialized = await phoneDetectorRef.current.initialize();
      console.log('👋 Ultra-Conservative PhoneDetector initialized:', phoneInitialized);
    }
    
    // Initialize improved device detector with stricter settings
    if (!deviceDetectorRef.current) {
      deviceDetectorRef.current = new ImprovedDeviceDetector();
      console.log('📱 ImprovedDeviceDetector initialized');
    }
    
    if (detectionIntervalRef.current) {
      clearInterval(detectionIntervalRef.current);
      console.log('Cleared existing detection interval');
    }
    
    console.log('🚀 Starting ULTRA-CONSERVATIVE detection loop with enhanced face filtering!');
    detectionRunningRef.current = true;
    
    const runUltraConservativeDetection = async () => {
      if (!videoRef.current || !canvasRef.current || !isComponentMountedRef.current) {
        console.log('Detection prerequisites missing');
        return;
      }
      
      try {
        const video = videoRef.current;
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');
        
        if (video.readyState !== 4 || video.videoWidth === 0) {
          console.log('Video not ready, readyState:', video.readyState);
          return;
        }
        
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // =============================================================================
        // 1. ENHANCED FACE DETECTION WITH BETTER FILTERING
        // =============================================================================
        
        console.log('Running enhanced face detection with filtering...');
        let detections = [];
        
        try {
          // Use higher score threshold to reduce false face detections
          detections = await window.faceapi
            .detectAllFaces(video, new window.faceapi.SsdMobilenetv1Options({ 
              scoreThreshold: 0.7 // Increased from 0.5 to 0.7 to reduce false faces
            }));
          
          console.log(`Initial SSD detection: ${detections.length} faces (threshold 0.7)`);
          
          if (detections.length > 0 && window.faceapi.nets.faceLandmark68TinyNet.isLoaded) {
            console.log('Adding tiny landmarks...');
            detections = await window.faceapi
              .detectAllFaces(video, new window.faceapi.SsdMobilenetv1Options({ 
                scoreThreshold: 0.7 // Consistent higher threshold
              }))
              .withFaceLandmarks('TinyNet');
          }
          
          // ENHANCED: Filter out faces that are likely false positives (statues, artwork, etc.)
          const filteredDetections = filterValidFaces(detections, canvas.width, canvas.height);
          detections = filteredDetections;
          
          console.log(`Final filtered detection: ${detections.length} valid faces`);
          
        } catch (detectionError) {
          console.error('Face detection error:', detectionError);
          return;
        }
        
        // =============================================================================
        // 2. ULTRA-CONSERVATIVE PHONE DETECTION (UNCHANGED)
        // =============================================================================
        
        let phoneAnalysis = {
          handsDetected: 0,
          handsRaised: false,
          suspiciousHandGesture: false,
          phoneGesture: false,
          handsOutOfFrame: false,
          phoneConfidence: 0,
          rawConfidence: 0,
          validationPassed: false,
          validationReasons: []
        };
        
        try {
          if (phoneDetectorRef.current) {
            phoneAnalysis = await phoneDetectorRef.current.detectHands(video);
            
            // ULTRA-CONSERVATIVE LOGGING - Only log significant detections
            if (phoneAnalysis.phoneConfidence > 0.7 || phoneAnalysis.phoneGesture) {
              console.log('👋 SIGNIFICANT phone analysis result:', {
                handsDetected: phoneAnalysis.handsDetected,
                phoneConfidence: phoneAnalysis.phoneConfidence,
                phoneGesture: phoneAnalysis.phoneGesture,
                validationPassed: phoneAnalysis.validationPassed,
                method: phoneAnalysis.detectionMethod,
                reasons: phoneAnalysis.validationReasons
              });
            }
          }
        } catch (phoneError) {
          console.warn('Ultra-conservative phone detection error:', phoneError);
        }
        
        // =============================================================================
        // 3. ULTRA-CONSERVATIVE VISUAL DEVICE DETECTION
        // =============================================================================
        
        let deviceAnalysis = [];
        try {
          if (deviceDetectorRef.current) {
            deviceAnalysis = deviceDetectorRef.current.detectDevices(canvas, video);
            
            // Only log high-confidence detections to reduce noise
            const highConfidenceDevices = deviceAnalysis.filter(d => d.confidence > 0.85);
            if (highConfidenceDevices.length > 0) {
              console.log('📱 HIGH-CONFIDENCE visual device analysis:', highConfidenceDevices.map(d => ({
                type: d.type,
                confidence: d.confidence.toFixed(2),
                size: `${d.width}x${d.height}`,
                temporalConsistency: d.temporalConsistency || false
              })));
            }
          }
        } catch (deviceError) {
          console.warn('Ultra-conservative device detection error:', deviceError);
        }
        
        // =============================================================================
        // 4. ULTRA-CONSERVATIVE COMBINED CONFIDENCE CALCULATION (UNCHANGED)
        // =============================================================================
        
        const handPhoneConfidence = phoneAnalysis.phoneConfidence || 0;
        const visualDeviceConfidence = deviceAnalysis.length > 0 ? 
          Math.max(...deviceAnalysis.map(d => d.confidence || 0)) : 0;
        
        // ULTRA-CONSERVATIVE: Only combine if both have high confidence AND validation passed
        let combinedConfidence = 0;
        
        if (phoneAnalysis.validationPassed && handPhoneConfidence > 0.8) {
          combinedConfidence = handPhoneConfidence * 0.9;
        }
        
        if (visualDeviceConfidence > 0.85 && deviceAnalysis.some(d => d.temporalConsistency)) {
          combinedConfidence = Math.max(combinedConfidence, visualDeviceConfidence * 0.7);
        }
        
        if (handPhoneConfidence > 0.8 && visualDeviceConfidence > 0.8 && phoneAnalysis.validationPassed) {
          combinedConfidence = Math.min((handPhoneConfidence + visualDeviceConfidence) / 2 * 1.1, 1.0);
        }
        
        // Only log if significant detection
        if (combinedConfidence > 0.6) {
          console.log('📊 ULTRA-CONSERVATIVE phone detection summary:', {
            handConfidence: handPhoneConfidence,
            visualConfidence: visualDeviceConfidence,
            combinedConfidence: combinedConfidence,
            validationPassed: phoneAnalysis.validationPassed,
            validationReasons: phoneAnalysis.validationReasons,
            willTrigger: combinedConfidence > 0.85 && phoneAnalysis.validationPassed
          });
        }
        
        // =============================================================================
        // 5. ULTRA-CONSERVATIVE VIOLATION DETECTION AND REPORTING (UNCHANGED)
        // =============================================================================
        
        const state = detectionStateRef.current;
        const now = Date.now();
        
        // ULTRA-CONSERVATIVE TRIGGER CONDITIONS
        const shouldTriggerPhoneViolation = 
          combinedConfidence > 0.85 && 
          phoneAnalysis.validationPassed &&
          phoneAnalysis.phoneGesture && 
          (visualDeviceConfidence > 0.8 || handPhoneConfidence > 0.9) && 
          (now - state.lastPhoneViolationTime) > (state.PHONE_VIOLATION_COOLDOWN * 2);
        
        if (shouldTriggerPhoneViolation && onDeviceViolation) {
          
          console.log('🚨 TRIGGERING ULTRA-CONSERVATIVE PHONE/DEVICE VIOLATION:', {
            combinedConfidence,
            handPhoneConfidence,
            visualDeviceConfidence,
            validationPassed: phoneAnalysis.validationPassed,
            validationReasons: phoneAnalysis.validationReasons,
            phoneGesture: phoneAnalysis.phoneGesture,
            triggerMethod: 'ultra_conservative_validated'
          });
          
          state.lastPhoneViolationTime = now;
          
          try {
            onDeviceViolation({
              method: 'ultra_conservative_validated_detection',
              phoneAnalysis: {
                phoneConfidence: handPhoneConfidence,
                phoneGesture: phoneAnalysis.phoneGesture,
                handsDetected: phoneAnalysis.handsDetected,
                validationPassed: phoneAnalysis.validationPassed,
                validationReasons: phoneAnalysis.validationReasons
              },
              deviceAnalysis: deviceAnalysis.filter(d => d.confidence > 0.8),
              combinedConfidence: combinedConfidence,
              timestamp: now,
              confidenceBreakdown: {
                hand: handPhoneConfidence,
                visual: visualDeviceConfidence,
                combined: combinedConfidence,
                validated: phoneAnalysis.validationPassed,
                ultraConservative: true
              }
            });
          } catch (violationError) {
            console.error('Error triggering ultra-conservative device violation:', violationError);
          }
        }
        
        // =============================================================================
        // 6. ENHANCED FACE DETECTION PROCESSING WITH BETTER VALIDATION
        // =============================================================================
        
        const currentIsLookingAway = false;
        let currentFaceQuality = 'not_visible';
        let currentFaceConfidence = 0;
        
        // ENHANCED: More conservative face processing to avoid false multiple face detections
        if (detections.length === 0) {
          // No face detected - same logic as before
          if (state.faceNotVisibleStartTime === null) {
            state.faceNotVisibleStartTime = now;
            console.log('👁️ Face disappeared - starting 30s timer');
          }
          
          state.faceNotVisibleDuration = now - state.faceNotVisibleStartTime;
          
          if (state.faceNotVisibleDuration >= state.FACE_NOT_VISIBLE_THRESHOLD) {
            const timeSinceLastViolation = now - state.lastViolationReportTime;
            
            if (timeSinceLastViolation >= state.MIN_VIOLATION_INTERVAL) {
              state.consecutiveFaceNotVisible++;
              console.log(`⚠️ Face not visible for ${(state.faceNotVisibleDuration / 1000).toFixed(1)}s - counting violation`);
              state.lastViolationReportTime = now;
              state.faceNotVisibleStartTime = now;
            }
          }
          
          setFacesDetected(0);
          setLookingAway(false);
          setFaceConfidence(0);
          setFaceQuality('not_visible');
          
          ctx.strokeStyle = state.faceNotVisibleDuration >= state.FACE_NOT_VISIBLE_THRESHOLD ? 'red' : 'yellow';
          ctx.lineWidth = 3;
          ctx.strokeRect(0, 0, canvas.width, canvas.height);
          ctx.fillStyle = ctx.strokeStyle;
          ctx.font = '18px Arial';
          ctx.fillText(`No Face (${(state.faceNotVisibleDuration / 1000).toFixed(1)}s / 30s)`, 10, 30);
          
        } else if (detections.length === 1) {
          // Single face detected - reset timers
          state.faceNotVisibleStartTime = null;
          state.faceNotVisibleDuration = 0;
          state.consecutiveMultipleFaces = 0;
          
          const detection = detections[0];
          const confidence = detection.detection.score;
          currentFaceConfidence = confidence;
          
          console.log(`✅ Single face detected! Confidence: ${(confidence * 100).toFixed(1)}%`);
          
          setFacesDetected(1);
          setLookingAway(currentIsLookingAway);
          setFaceConfidence(confidence);
          
          currentFaceQuality = 'poor';
          if (confidence > 0.9) currentFaceQuality = 'excellent';
          else if (confidence > 0.8) currentFaceQuality = 'good';
          else if (confidence > 0.6) currentFaceQuality = 'fair';
          setFaceQuality(currentFaceQuality);
          
          const box = detection.detection.box;
          ctx.strokeStyle = 'green';
          ctx.lineWidth = 3;
          ctx.strokeRect(box.x, box.y, box.width, box.height);
          
          ctx.fillStyle = 'green';
          ctx.font = '16px Arial';
          ctx.fillText('Face OK', 10, 30);
          ctx.fillText(`Confidence: ${(confidence * 100).toFixed(0)}%`, 10, 55);
          
        } else {
          // ENHANCED: Multiple faces detected - more conservative filtering
          console.log(`⚠️ Multiple faces detected: ${detections.length}, filtering for real faces...`);
          
          // Filter for very high confidence faces that are likely real people
          const veryHighConfidenceFaces = detections.filter(d => d.detection.score > 0.85);
          const highConfidenceFaces = detections.filter(d => d.detection.score > 0.75);
          
          console.log(`High confidence faces (>75%): ${highConfidenceFaces.length}`);
          console.log(`Very high confidence faces (>85%): ${veryHighConfidenceFaces.length}`);
          
          // ULTRA-CONSERVATIVE: If we have exactly one very high confidence face, treat as single face
          if (veryHighConfidenceFaces.length === 1 && highConfidenceFaces.length <= 2) {
            console.log('✅ Treating as single face - one very high confidence detection');
            
            // Reset multiple face counter
            state.consecutiveMultipleFaces = 0;
            
            const detection = veryHighConfidenceFaces[0];
            setFacesDetected(1);
            setFaceQuality('good');
            setFaceConfidence(detection.detection.score);
            
            const box = detection.detection.box;
            ctx.strokeStyle = 'green';
            ctx.lineWidth = 3;
            ctx.strokeRect(box.x, box.y, box.width, box.height);
            
            ctx.fillStyle = 'green';
            ctx.font = '16px Arial';
            ctx.fillText('Face OK (Filtered)', 10, 30);
            ctx.fillText(`Confidence: ${(detection.detection.score * 100).toFixed(0)}%`, 10, 55);
            
          } else if (highConfidenceFaces.length > 1) {
            // Multiple high-confidence faces - this is a real violation
            state.consecutiveMultipleFaces++;
            
            console.log(`⚠️ Real multiple faces violation: ${highConfidenceFaces.length} high-confidence faces`);
            
            setFacesDetected(highConfidenceFaces.length);
            setLookingAway(false);
            setFaceConfidence(0);
            currentFaceQuality = 'multiple_faces';
            setFaceQuality(currentFaceQuality);
            
            // Draw all high-confidence face boxes
            highConfidenceFaces.forEach((detection, index) => {
              const box = detection.detection.box;
              ctx.strokeStyle = 'red';
              ctx.lineWidth = 2;
              ctx.strokeRect(box.x, box.y, box.width, box.height);
              ctx.fillStyle = 'red';
              ctx.font = '12px Arial';
              ctx.fillText(`Face ${index + 1}`, box.x, box.y - 5);
            });
            
          } else {
            // Low confidence detections - treat as no face
            console.log('✅ Treating as no face - all detections low confidence');
            state.consecutiveMultipleFaces = 0;
            setFacesDetected(0);
            setFaceQuality('not_visible');
          }
        }
        
        // =============================================================================
        // 7. UPDATE COMPONENT STATE (ULTRA-CONSERVATIVE) - UNCHANGED
        // =============================================================================
        
        setHandsDetected(phoneAnalysis.handsDetected);
        setHandsRaised(phoneAnalysis.handsRaised);
        setSuspiciousHandGesture(false); // DISABLED
        setPhoneGesture(combinedConfidence > 0.85 && phoneAnalysis.validationPassed && phoneAnalysis.phoneGesture);
        setPhoneConfidence(handPhoneConfidence);
        setDevicesDetected(deviceAnalysis);
        setCombinedPhoneConfidence(combinedConfidence);
        
        // =============================================================================
        // 8. ENHANCED CANVAS DRAWING (LESS VISUAL NOISE)
        // =============================================================================
        
        // Only show warnings for VERY high confidence detections
        if (combinedConfidence > 0.8 && phoneAnalysis.validationPassed) {
          const isUltraHighConfidence = combinedConfidence > 0.9;
          ctx.fillStyle = isUltraHighConfidence ? 'red' : 'orange';
          ctx.font = 'bold 14px Arial';
          
          let confidenceText = `📱 DEVICE: ${Math.round(combinedConfidence * 100)}%`;
          if (phoneAnalysis.validationPassed) {
            confidenceText += ' ✓VALIDATED';
          }
          
          ctx.fillText(confidenceText, 10, canvas.height - 70);
          
          if (isUltraHighConfidence && phoneAnalysis.phoneGesture) {
            ctx.fillStyle = 'red';
            ctx.font = 'bold 12px Arial';
            ctx.fillText('🚨 CONFIRMED DEVICE DETECTED', 10, canvas.height - 50);
          }
        }
        
        // Show hand detection info only for significant detections
        if (phoneAnalysis.handsDetected > 0 && phoneAnalysis.phoneConfidence > 0.5) {
          ctx.fillStyle = phoneAnalysis.phoneGesture ? 'red' : 'blue';
          ctx.font = '14px Arial';
          ctx.fillText(`👋 Hands: ${phoneAnalysis.handsDetected}`, 10, 80);
          
          if (phoneAnalysis.phoneConfidence > 0.7) {
            ctx.fillStyle = phoneAnalysis.phoneConfidence > 0.85 ? 'red' : 'orange';
            ctx.fillText(`📱 Phone: ${Math.round(phoneAnalysis.phoneConfidence * 100)}%`, 10, 100);
            
            if (phoneAnalysis.validationPassed) {
              ctx.fillStyle = 'lime';
              ctx.fillText('✓ ULTRA-VALIDATED', 10, 120);
            } else {
              ctx.fillStyle = 'gray';
              ctx.fillText('⚠ NOT VALIDATED', 10, 120);
            }
          }
        }
        
        // =============================================================================
        // 9. ULTRA-CONSERVATIVE STATUS UPDATE
        // =============================================================================
        
        const statusUpdate = {
          isDetecting: true,
          facesDetected: detections.length,
          lookingAway: detections.length === 1 ? currentIsLookingAway : false,
          faceConfidence: detections.length === 1 ? currentFaceConfidence : 0,
          faceQuality: currentFaceQuality,
          consecutiveFaceNotVisible: state.consecutiveFaceNotVisible,
          consecutiveLookingAway: state.consecutiveLookingAway,
          consecutiveMultipleFaces: state.consecutiveMultipleFaces,
          faceNotVisibleDuration: state.faceNotVisibleDuration,
          faceNotVisibleThreshold: state.FACE_NOT_VISIBLE_THRESHOLD,
          
          // Ultra-conservative phone detection data
          handsDetected: phoneAnalysis.handsDetected,
          handsRaised: phoneAnalysis.handsRaised,
          suspiciousHandGesture: false, // DISABLED
          phoneGesture: combinedConfidence > 0.85 && phoneAnalysis.validationPassed && phoneAnalysis.phoneGesture,
          phoneConfidence: handPhoneConfidence,
          rawPhoneConfidence: phoneAnalysis.rawConfidence || 0,
          consecutiveHandsRaised: state.consecutiveHandsRaised,
          consecutiveHandsOutOfFrame: state.consecutiveHandsOutOfFrame,
          motionLevel: 0,
          screenshotRisk: combinedConfidence > 0.9 ? 1 : 0,
          
          // Ultra-conservative device detection data
          devicesDetected: deviceAnalysis.filter(device => device.confidence > 0.85), // Higher threshold
          deviceDetected: combinedConfidence > 0.85 && phoneAnalysis.validationPassed,
          consecutiveDevicesDetected: state.consecutiveDevicesDetected,
          highConfidenceDevices: deviceAnalysis.filter(device => device.confidence > 0.9),
          combinedPhoneConfidence: combinedConfidence,
          detectionMethod: phoneAnalysis.detectionMethod || 'none',
          
          // Validation data
          validationPassed: phoneAnalysis.validationPassed,
          validationReasons: phoneAnalysis.validationReasons || []
        };
        
        // MINIMAL LOGGING: Only log significant detections or issues
        if (combinedConfidence > 0.7 || phoneAnalysis.phoneGesture || detections.length > 1) {
          console.log('📊 [ULTRA-CONSERVATIVE] Significant detection results:', {
            faces: statusUpdate.facesDetected,
            hands: statusUpdate.handsDetected,
            phoneConfidence: statusUpdate.phoneConfidence,
            combinedConfidence: statusUpdate.combinedPhoneConfidence,
            phoneGesture: statusUpdate.phoneGesture,
            validationPassed: statusUpdate.validationPassed,
            suspiciousHandGesture: statusUpdate.suspiciousHandGesture,
            visualDevices: statusUpdate.devicesDetected.length,
            detectionMethod: statusUpdate.detectionMethod
          });
        }
        
        // Send the ultra-conservative status update
        updateStatus(statusUpdate);
        
      } catch (error) {
        console.error('Ultra-conservative detection loop error:', error);
        setError(`Detection error: ${error.message}`);
      }
    };
    
    // Run first detection immediately
    console.log('Running first ultra-conservative detection...');
    runUltraConservativeDetection();
    
    // Set up interval - 2 second intervals for stability
    detectionIntervalRef.current = setInterval(() => {
      console.log('Running periodic ultra-conservative detection...');
      runUltraConservativeDetection();
    }, 2000);
    
    console.log('✅ Ultra-conservative detection with enhanced face filtering set up (2s intervals)');
  }, [updateStatus, onDeviceViolation, filterValidFaces]);



// File: components/WebcamFaceDetection.js - Part 6: COMPONENT LIFECYCLE AND UI RENDERING

// =============================================================================
// PART 6: COMPONENT LIFECYCLE MANAGEMENT AND UI RENDERING
// =============================================================================

  // =============================================================================
  // CAMERA CONTROL FUNCTIONS
  // =============================================================================
  
  const stopCamera = useCallback(() => {
    console.log('🛑 Stopping camera...');
    
    detectionRunningRef.current = false;
    
    if (detectionIntervalRef.current) {
      clearInterval(detectionIntervalRef.current);
      detectionIntervalRef.current = null;
    }
    
    if (statusUpdateTimeoutRef.current) {
      clearTimeout(statusUpdateTimeoutRef.current);
      statusUpdateTimeoutRef.current = null;
    }
    
    if (streamRef.current && streamRef.current !== window.savedInterviewStream) {
      streamRef.current.getTracks().forEach(track => track.stop());
    }
    streamRef.current = null;
    
    if (videoRef.current) {
      videoRef.current.srcObject = null;
      videoRef.current.pause();
    }
    
    if (canvasRef.current) {
      const ctx = canvasRef.current.getContext('2d');
      ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);
    }
    
    // Reset all state
    setIsInitialized(false);
    setFacesDetected(0);
    setLookingAway(false);
    setFaceConfidence(0);
    setFaceQuality('camera_stopped');
    setHandsDetected(0);
    setHandsRaised(false);
    setSuspiciousHandGesture(false);
    setPhoneGesture(false);
    setPhoneConfidence(0);
    setDevicesDetected([]);
    setCombinedPhoneConfidence(0);
    
    updateStatus({
      isDetecting: false,
      facesDetected: 0,
      lookingAway: false,
      faceConfidence: 0,
      faceQuality: 'camera_stopped',
      phoneConfidence: 0,
      combinedPhoneConfidence: 0,
      validationPassed: false
    });
  }, [updateStatus]);
  
  // =============================================================================
  // IMPERATIVE HANDLE (PARENT COMPONENT INTERFACE)
  // =============================================================================
  
  useImperativeHandle(ref, () => ({
    stopCamera,
    startCamera: initializeCamera,
    video: videoRef.current,
    canvas: canvasRef.current,
    getStatus: () => ({
      isDetecting: isInitialized && detectionRunningRef.current,
      facesDetected,
      lookingAway,
      faceConfidence,
      faceQuality,
      // Enhanced phone detection data
      handsDetected,
      handsRaised,
      suspiciousHandGesture,
      phoneGesture,
      phoneConfidence,
      motionLevel: 0,
      // Device detection data
      devicesDetected,
      deviceDetected: devicesDetected.length > 0 || combinedPhoneConfidence > 0.7,
      combinedPhoneConfidence,
      validationPassed: phoneGesture // Simplified validation check
    }),
    getDetectorStats: () => ({
      phoneDetector: phoneDetectorRef.current?.validationState || {},
      deviceDetector: deviceDetectorRef.current?.getDetectionStats?.() || {}
    })
  }), [
    stopCamera, initializeCamera, isInitialized, facesDetected, lookingAway, 
    faceConfidence, faceQuality, handsDetected, handsRaised, suspiciousHandGesture, 
    phoneGesture, phoneConfidence, devicesDetected, combinedPhoneConfidence
  ]);
  
  // =============================================================================
  // COMPONENT LIFECYCLE EFFECTS
  // =============================================================================
  
  // Initialize on mount
  useEffect(() => {
    isComponentMountedRef.current = true;
    console.log('🚀 WebcamFaceDetection component mounted (ENHANCED WITH IMPROVED VALIDATION)');
    console.log('🔗 onStatusUpdate callback provided:', !!onStatusUpdate);
    console.log('🔗 onDeviceViolation callback provided:', !!onDeviceViolation);
    
    const initializeComponent = async () => {
      try {
        setModelLoadingStatus('loading');
        console.log('📥 Starting model loading...');
        
        const modelsLoaded = await loadFaceModels();
        modelsLoadedRef.current = modelsLoaded;
        
        if (modelsLoaded) {
          setModelLoadingStatus('loaded');
          console.log('✅ Models loaded successfully');
          
          // Start camera after a short delay
          setTimeout(() => {
            if (isComponentMountedRef.current) {
              initializeCamera();
            }
          }, 500);
        } else {
          setModelLoadingStatus('error');
          setError('Failed to load face detection models - continuing with camera only');
          console.error('❌ Failed to load models');
          
          // Still try camera even if models failed
          setTimeout(() => {
            if (isComponentMountedRef.current) {
              initializeCamera();
            }
          }, 1000);
        }
      } catch (error) {
        console.error('Initialization error:', error);
        setModelLoadingStatus('error');
        setError(`Initialization error: ${error.message}`);
        
        // Still try camera
        setTimeout(() => {
          if (isComponentMountedRef.current) {
            initializeCamera();
          }
        }, 2000);
      }
    };
    
    initializeComponent();
    
    return () => {
      console.log('🧹 WebcamFaceDetection cleanup');
      isComponentMountedRef.current = false;
      stopCamera();
    };
  }, [initializeCamera, stopCamera]);
  
  // Monitor readiness and start detection when everything is ready
  useEffect(() => {
    const checkAndStartDetection = () => {
      console.log('🔍 Checking if we should start detection...');
      console.log('- Models loaded:', modelsLoadedRef.current);
      console.log('- Camera initialized:', isInitialized);
      console.log('- Detection running:', detectionRunningRef.current);
      console.log('- Video ready:', videoRef.current?.readyState === 4);
      
      if (modelsLoadedRef.current && isInitialized && !detectionRunningRef.current) {
        console.log('🚀 All conditions met, starting comprehensive detection!');
        setTimeout(() => {
          if (isComponentMountedRef.current && !detectionRunningRef.current) {
            startComprehensiveDetection();
          }
        }, 500);
      }
    };
    
    checkAndStartDetection();
  }, [modelsLoadedRef.current, isInitialized, startComprehensiveDetection]);
  
  // Periodic check to ensure detection is running
  useEffect(() => {
    const forceStartInterval = setInterval(() => {
      if (modelsLoadedRef.current && isInitialized && !detectionRunningRef.current) {
        console.log('⚡ Forcing detection start (periodic check)');
        startComprehensiveDetection();
      }
    }, 3000);
    
    return () => clearInterval(forceStartInterval);
  }, [modelsLoadedRef.current, isInitialized, startComprehensiveDetection]);
  
  // =============================================================================
  // COMPONENT RENDERING
  // =============================================================================
  
  return (
    <div className="webcam-container relative overflow-hidden rounded-lg bg-black">
      {/* Main video element */}
      <video 
        ref={videoRef} 
        autoPlay 
        playsInline
        muted 
        className="w-full h-full object-cover"
        style={{ transform: 'scaleX(-1)' }}
      />
      
      {/* Detection overlay canvas */}
      <canvas
        ref={canvasRef}
        className="absolute top-0 left-0 w-full h-full pointer-events-none"
        style={{ transform: 'scaleX(-1)' }}
      />
      
      {/* Error display */}
      {error && (
        <div className="absolute top-2 left-2 bg-red-600 bg-opacity-90 text-white p-2 rounded text-sm max-w-[90%]">
          <div>⚠️ {error}</div>
        </div>
      )}
      
      {/* Status indicators */}
      <div className="absolute top-2 right-2 space-y-1">
        {/* Detection status */}
        <div className={`px-2 py-1 rounded text-xs font-semibold ${
          detectionRunningRef.current ? 'bg-green-600 text-white' : 'bg-yellow-600 text-white'
        }`}>
          {detectionRunningRef.current ? 'Enhanced Detection Active' : 'Initializing...'}
        </div>
        
        {/* Face detection status */}
        {isInitialized && (
          <div className={`px-2 py-1 rounded text-xs font-semibold ${
            facesDetected === 1 ? 'bg-green-600 text-white' :
            facesDetected > 1 ? 'bg-red-600 text-white' :
            'bg-red-600 text-white'
          }`}>
            {facesDetected === 0 ? 'No Face' :
             facesDetected === 1 ? 'Face OK' :
             `${facesDetected} Faces`}
          </div>
        )}
        
        {/* Face quality indicators */}
        {isInitialized && facesDetected === 1 && (
          <>
            <div className={`px-2 py-1 rounded text-xs font-semibold ${
              lookingAway ? 'bg-orange-600 text-white' : 'bg-green-600 text-white'
            }`}>
              {lookingAway ? 'Looking Away' : 'Looking Good'}
            </div>
            
            <div className={`px-2 py-1 rounded text-xs font-semibold ${
              faceQuality === 'excellent' ? 'bg-green-600 text-white' :
              faceQuality === 'good' ? 'bg-blue-600 text-white' :
              faceQuality === 'fair' ? 'bg-yellow-600 text-white' :
              'bg-red-600 text-white'
            }`}>
              Quality: {faceQuality}
            </div>
          </>
        )}
        
        {/* Hand detection indicators */}
        <div className={`px-2 py-1 rounded text-xs font-semibold ${
          handsDetected > 0 ? 'bg-blue-600 text-white' : 'bg-gray-600 text-white'
        }`}>
          👋 Hands: {handsDetected}
        </div>
        
        {/* Enhanced phone confidence indicator */}
        {phoneConfidence > 0.3 && (
          <div className={`px-2 py-1 rounded text-xs font-semibold ${
            phoneConfidence > 0.7 ? 'bg-red-600 text-white animate-pulse' :
            phoneConfidence > 0.5 ? 'bg-orange-600 text-white' :
            'bg-yellow-600 text-white'
          }`}>
            📱 Phone: {Math.round(phoneConfidence * 100)}%
          </div>
        )}
        
        {/* Combined confidence indicator with validation */}
        {combinedPhoneConfidence > 0.4 && (
          <div className={`px-2 py-1 rounded text-xs font-semibold ${
            combinedPhoneConfidence > 0.7 ? 'bg-red-600 text-white animate-pulse' :
            'bg-orange-600 text-white'
          }`}>
            🚨 DEVICE: {Math.round(combinedPhoneConfidence * 100)}%
            {phoneGesture && ' ✓'} {/* Checkmark for validated detections */}
          </div>
        )}
        
        {/* Hand gesture indicators */}
        {handsRaised && (
          <div className="px-2 py-1 rounded text-xs font-semibold bg-orange-600 text-white">
            ⬆️ Hands Raised
          </div>
        )}
        
        {suspiciousHandGesture && (
          <div className="px-2 py-1 rounded text-xs font-semibold bg-red-600 text-white">
            📱 Suspicious Gesture
          </div>
        )}
        
        {phoneGesture && (
          <div className="px-2 py-1 rounded text-xs font-semibold bg-red-600 text-white animate-pulse">
            📞 PHONE DETECTED ✓
          </div>
        )}
        
        {/* Visual device detection indicators */}
        {devicesDetected.filter(device => device.confidence > 0.7).length > 0 && (
          <div className="px-2 py-1 rounded text-xs font-semibold bg-red-600 text-white">
            📱 {devicesDetected.filter(device => device.confidence > 0.7).length} Visual Device(s)
          </div>
        )}
        
        {/* Enhanced detection mode indicator */}
        <div className="px-2 py-1 rounded text-xs font-semibold bg-purple-600 text-white">
          Enhanced AI Detection
        </div>
        
        {/* Validation indicator */}
        {(phoneGesture || combinedPhoneConfidence > 0.5) && (
          <div className={`px-2 py-1 rounded text-xs font-semibold ${
            phoneGesture ? 'bg-green-600 text-white' : 'bg-yellow-600 text-white'
          }`}>
            {phoneGesture ? '✓ VALIDATED' : '? UNVALIDATED'}
          </div>
        )}
      </div>
      
      {/* Enhanced debug info for development */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute bottom-2 left-2 bg-black bg-opacity-75 text-white p-2 rounded text-xs font-mono max-w-sm">
          <div>Models: {modelLoadingStatus}</div>
          <div>Camera: {isInitialized ? '✅' : '❌'}</div>
          <div>Detection: {detectionRunningRef.current ? '✅' : '❌'}</div>
          <div>Face API: {window.faceapi ? '✅' : '❌'}</div>
          <div>MediaPipe: {window.mediaPipeLoaded ? '✅' : '❌'}</div>
          <div>Video Ready: {videoRef.current?.readyState === 4 ? '✅' : '❌'}</div>
          <div>Faces: {facesDetected}</div>
          <div>Confidence: {(faceConfidence * 100).toFixed(0)}%</div>
          <div>Quality: {faceQuality}</div>
          
          <div className="border-t pt-1 mt-1 text-blue-400">
            <div>👋 Hands: {handsDetected}</div>
            <div>⬆️ Raised: {handsRaised ? '⚠️' : '✅'}</div>
            <div>📱 Phone Conf: {Math.round(phoneConfidence * 100)}%</div>
            <div>🤏 Gesture: {suspiciousHandGesture ? '⚠️' : '✅'}</div>
            <div>📞 Phone: {phoneGesture ? '⚠️' : '✅'}</div>
            <div>✓ Validated: {phoneGesture ? '✅' : '❌'}</div>
          </div>
          
          <div className="border-t pt-1 mt-1 text-purple-400">
            <div>📱 Visual Devices: {devicesDetected.length}</div>
            <div>High Conf: {devicesDetected.filter(d => d.confidence > 0.8).length}</div>
            <div>🎯 Combined: {Math.round(combinedPhoneConfidence * 100)}%</div>
            {devicesDetected.filter(d => d.confidence > 0.6).map((device, i) => (
              <div key={i}>
                {device.type}: {(device.confidence * 100).toFixed(0)}%
                {device.temporalConsistency && ' ✓'}
              </div>
            ))}
          </div>
          
          <div className="border-t pt-1 mt-1 text-green-400">
            <div>Status Callback: {onStatusUpdate ? '✅' : '❌'}</div>
            <div>Device Callback: {onDeviceViolation ? '✅' : '❌'}</div>
            <div>ENHANCED DETECTION: ✅ Active</div>
            <div>VALIDATION SYSTEM: ✅ Enhanced</div>
            <div>FALSE POSITIVE FILTER: ✅ Active</div>
          </div>
        </div>
      )}
    </div>
  );
});

WebcamFaceDetection.displayName = 'WebcamFaceDetection';

export default WebcamFaceDetection;
