// File: components/RoleProtection.js

import { useSession } from "next-auth/react";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";

// Higher-order component for protecting recruiter pages
export const RequireRecruiterRole = ({ children }) => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);

  useEffect(() => {
    if (status === "loading") return; // Still loading

    if (status === "unauthenticated") {
      router.push("/api/auth/signin");
      return;
    }

    if (session?.user?.userType !== "recruiter") {
      // Redirect unauthorized users to appropriate dashboard
      router.push("/candidate-dashboard");
      return;
    }

    setIsAuthorized(true);
  }, [session, status, router]);

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-200 via-blue-100 to-purple-200 flex items-center justify-center">
        <div className="text-slate-800 text-xl flex items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
          Verifying access...
        </div>
      </div>
    );
  }

  if (!isAuthorized) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-200 via-blue-100 to-purple-200 flex items-center justify-center">
        <div className="bg-white/80 backdrop-blur-md border border-white/40 rounded-2xl p-8 shadow-xl max-w-md text-center">
          <div className="text-4xl mb-4">🚫</div>
          <h2 className="text-xl font-semibold text-slate-800 mb-2">Access Denied</h2>
          <p className="text-slate-600 mb-4">You don't have permission to access this page.</p>
          <button 
            onClick={() => router.push("/candidate-dashboard")}
            className="px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-lg"
          >
            Go to Candidate Dashboard
          </button>
        </div>
      </div>
    );
  }

  return children;
};

// Higher-order component for protecting candidate pages
export const RequireCandidateRole = ({ children }) => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);

  useEffect(() => {
    if (status === "loading") return; // Still loading

    if (status === "unauthenticated") {
      router.push("/api/auth/signin");
      return;
    }

    if (session?.user?.userType !== "candidate") {
      // Redirect unauthorized users to appropriate dashboard
      router.push("/recruiter/dashboard");
      return;
    }

    setIsAuthorized(true);
  }, [session, status, router]);

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-200 via-blue-100 to-purple-200 flex items-center justify-center">
        <div className="text-slate-800 text-xl flex items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
          Verifying access...
        </div>
      </div>
    );
  }

  if (!isAuthorized) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-200 via-blue-100 to-purple-200 flex items-center justify-center">
        <div className="bg-white/80 backdrop-blur-md border border-white/40 rounded-2xl p-8 shadow-xl max-w-md text-center">
          <div className="text-4xl mb-4">🚫</div>
          <h2 className="text-xl font-semibold text-slate-800 mb-2">Access Denied</h2>
          <p className="text-slate-600 mb-4">You don't have permission to access this page.</p>
          <button 
            onClick={() => router.push("/recruiter/dashboard")}
            className="px-6 py-2 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg hover:from-purple-600 hover:to-purple-700 transition-all duration-200 shadow-lg"
          >
            Go to Recruiter Dashboard
          </button>
        </div>
      </div>
    );
  }

  return children;
};

// Universal role checker hook
export const useRoleAccess = () => {
  const { data: session, status } = useSession();
  
  return {
    isRecruiter: session?.user?.userType === "recruiter",
    isCandidate: session?.user?.userType === "candidate",
    userType: session?.user?.userType,
    isLoading: status === "loading",
    isAuthenticated: status === "authenticated",
    session
  };
};

// Navigation component that shows appropriate dashboard links
export const RoleBasedNavigation = () => {
  const { isRecruiter, isCandidate, isLoading } = useRoleAccess();
  const router = useRouter();

  if (isLoading) return null;

  return (
    <div className="flex items-center space-x-4">
      {isRecruiter && (
        <button
          onClick={() => router.push("/recruiter/dashboard")}
          className="px-4 py-2 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg hover:from-purple-600 hover:to-purple-700 transition-all duration-200 shadow-lg"
        >
          📊 Recruiter Dashboard
        </button>
      )}
      
      {isCandidate && (
        <button
          onClick={() => router.push("/candidate-dashboard")}
          className="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-lg"
        >
          🏠 Candidate Dashboard
        </button>
      )}
    </div>
  );
};
