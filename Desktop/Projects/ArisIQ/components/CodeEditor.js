// File: components/CodeEditor.js

import React, { useEffect, useState } from 'react';
import Editor from '@monaco-editor/react';

const CodeEditor = ({ 
  language = 'javascript', 
  value = '', 
  onChange, 
  theme = 'vs-dark',
  height = '100%', 
  readOnly = false 
}) => {
  const [mounted, setMounted] = useState(false);
  const [editorInstance, setEditorInstance] = useState(null);
  
  // Set mounted after component mounts
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);
  
  // Handle language change
  useEffect(() => {
    if (editorInstance) {
      const model = editorInstance.getModel();
      if (model) {
        // Map language values to Monaco editor language identifiers
        let monacoLanguage = language;
        
        if (language === 'python') {
          monacoLanguage = 'python';
        } else if (language === 'cpp') {
          monacoLanguage = 'cpp';
        } else if (language === 'java') {
          monacoLanguage = 'java';
        } else {
          monacoLanguage = 'javascript';
        }
        
        // Set the language
        monaco.editor.setModelLanguage(model, monacoLanguage);
      }
    }
  }, [language, editorInstance]);
  
  // Handle editor mounting
  const handleEditorDidMount = (editor, monaco) => {
    setEditorInstance(editor);
    
    // Configure Monaco options
    editor.updateOptions({
      minimap: { enabled: false },
      scrollBeyondLastLine: false,
      automaticLayout: true,
      wordWrap: 'on',
      readOnly: readOnly,
      // Adjust to your requirements
      fontSize: 14,
      tabSize: 2
    });
    
    // Add keyboard shortcuts
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      // Prevent default save behavior
      console.log('Save shortcut triggered');
      // You can add a custom action here
    });
    
    // Focus the editor
    editor.focus();
  };
  
  // Handle editor content change
  const handleEditorChange = (newValue) => {
    if (onChange) {
      onChange(newValue);
    }
  };
  
  return (
    <div className="h-full w-full">
      {mounted && (
        <Editor
          height={height}
          language={language}
          value={value}
          theme={theme}
          onChange={handleEditorChange}
          onMount={handleEditorDidMount}
          options={{
            readOnly,
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            automaticLayout: true,
            wordWrap: 'on',
            fontSize: 14,
            tabSize: 2
          }}
        />
      )}
    </div>
  );
};

export default CodeEditor;
