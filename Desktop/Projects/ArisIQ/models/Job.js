// File: models/Job.js

import mongoose from 'mongoose';

const JobSchema = new mongoose.Schema({
  title: String,
  company: String,
  industry: String,
  experience: String,
  employmentType: String,
  workMode: String,
  salary: String,
  country: String,
  city: String,
  location: String,
  description: String,
  requiredSkills: [String],
  preferredSkills: [String],
  applicationDeadline: Date,
  screeningDeadline: Date,
  postedBy: String,
  questionSource: String,
  visaSponsorship: Boolean,
  questionBankGenerating: {
    type: Boolean,
    default: false
  },
  questionBankGenerated: {
    type: Boolean,
    default: false
  },
  questionBankGeneratedAt: Date,
  questionBankGenerationFailed: {
    type: Boolean,
    default: false
  }
}, { timestamps: true });

// Virtual for jobCode
JobSchema.virtual('jobCode').get(function () {
  return this._id.toString().slice(-6).toUpperCase();
});

// Make sure virtuals are serialized
JobSchema.set('toJSON', { virtuals: true });
JobSchema.set('toObject', { virtuals: true });

export default mongoose.models.Job || mongoose.model('Job', JobSchema);

