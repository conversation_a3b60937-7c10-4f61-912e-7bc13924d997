// File: models/Application.js

import mongoose from "mongoose";

const ApplicationSchema = new mongoose.Schema(
  {
    jobId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: "Job",
    },
    jobTitle: String,
    companyName: String,
    location: String,
    fullName: String,
    firstName: String,
    lastName: String,
    email: String,
    phone: String,
    linkedIn: String,
    portfolio: String,
    experienceYears: String,
    coverLetter: String,
    expectedSalary: String,
    resumeId: mongoose.Schema.Types.ObjectId,

    scanResult: {
      scannedAt: Date,
      canTakeInterview: Boolean,
      resumeScore: Number,
      coverLetterScore: Number,
      linkedInScore: Number,
      educationScore: Number,
      certificationScore: Number,
      matchedSkills: [String],
      missingSkills: [String],
    },

    questions: Array,
    questionsGeneratedAt: Date,
    interviewStartedAt: Date,
    interviewStatus: String,
    interviewAbortedAt: Date,
    lastViolation: Date,

    violationHistory: Array,
    violations: {
      devTools: Number,
      total: Number,
      responseTime: Number,
      functionModification: Number,
      virtualEnvironment: Number,
      mouseMovement: Number,
      fullscreenExit: Number,
      apiModification: Number,
      copyPaste: Number,
      externalBrowsing: Number,
    },

    status: {
      type: String,
      enum: ["Applied", "Selected for Interview", "Rejected"],
      default: "Applied",
    },
  },
  { timestamps: true }
);

export default mongoose.models.Application || mongoose.model("Application", ApplicationSchema);

