// File: public/js/security-manager.js - ENHANCED VERSION WITH COMPREHENSIVE DEVICE DETECTION

(function() {
  'use strict';
  
  // Prevent multiple instances
  if (typeof window.SecurityManager !== 'undefined') {
    console.log('SecurityManager already exists, reusing instance');
    return;
  }
  
  class SecurityManager {
    constructor() {
      // Singleton pattern
      if (SecurityManager.instance) {
        return SecurityManager.instance;
      }
      SecurityManager.instance = this;
      window.securityManagerInstance = this;
      
      this.MAX_WARNINGS = 5;
      this.isInterviewActive = false;
      this.applicationId = null;
      this.startDelay = 5000; // 5 seconds grace period
      this.interviewStartTime = null;
      this.exiting = false; // Flag to prevent violations during exit
      
      // ENHANCED: Updated violation thresholds to include device detection
      this.VIOLATION_THRESHOLDS = {
        // ALL violations now follow the same pattern: 5 warnings, then disqualify on 6th
        fullscreenExit: 5,
        tabSwitch: 5,
        windowBlur: 5,
        devTools: 5,
        keyboardShortcut: 5,
        rightClick: 5,
        multipleFaces: 5,
        faceNotVisible: 5,
        escapeKey: 5,
        lookingAway: 5,
        screenSharing: 5,
        screenRecording: 5,
        dualMonitor: 5,
        screenshot: 5,
        differentPerson: 5,
        suspiciousTyping: 5,
        contentPasted: 5,
        faceSwitch: 5,
        // ENHANCED: Device detection thresholds
        handsRaised: 5,
        handsOutOfFrame: 5,
        suspiciousHandGesture: 5,
        deviceDetected: 3,        // STRICTER: Only 3 warnings for device detection
        phoneDetected: 3,         // STRICTER: Only 3 warnings for phone detection  
        phoneGesture: 3           // STRICTER: Only 3 warnings for phone gestures
      };
      
      // ENHANCED: Updated warning violations to include device detection
      this.warningViolations = {
        fullscreenExit: 0,
        tabSwitch: 0,
        windowBlur: 0,
        devTools: 0,
        keyboardShortcut: 0,
        rightClick: 0,
        multipleFaces: 0,
        faceNotVisible: 0,
        escapeKey: 0,
        lookingAway: 0,
        deepfake: 0,
        screenSharing: 0,
        screenRecording: 0,
        dualMonitor: 0,
        screenshot: 0,
        differentPerson: 0,
        suspiciousTyping: 0,
        contentPasted: 0,
        faceSwitch: 0,
        // ENHANCED: Device detection violations
        handsRaised: 0,
        handsOutOfFrame: 0,
        suspiciousHandGesture: 0,
        deviceDetected: 0,        // ENHANCED: General device detection
        phoneDetected: 0,         // ENHANCED: Specific phone detection
        phoneGesture: 0           // ENHANCED: Phone gesture detection
      };
      
      // Analytics violations (recorded but don't cause warnings)
      this.analyticsOnlyViolations = {
        suspiciousTyping: 0,
        codeAnomaly: 0,
        eyeTrackingIssue: 0,
        suspiciousResponseTime: 0,
        microExpressionAnomaly: 0,
        suspiciousLighting: 0,
        lipSyncIssue: 0,
        environmentChange: 0,
        multipleVoices: 0
      };
      
      // Info-only violations (show warnings but don't count towards disqualification)
      this.infoOnlyViolations = {
        copyPaste: 0,
        copy: 0,
        paste: 0,
        cut: 0,
        selectAll: 0
      };
      
      this.suspiciousEvents = [];
      
      // ENHANCED: Face detection state with comprehensive device tracking
      this.faceDetectionState = {
        faceNotVisibleCount: 0,
        faceNotVisibleThreshold: 1,
        lookingAwayCount: 0,
        lookingAwayThreshold: 2,
        lastFaceCheck: null,
        // TIMING: Face detection timing thresholds
        FACE_NOT_VISIBLE_TIME_THRESHOLD: 10000, // 10 seconds
        LOOKING_AWAY_TIME_THRESHOLD: 15000, // 15 seconds
        // Face switching detection
        initialFaceDescriptor: null,
        faceDescriptorHistory: [],
        consecutiveFaceSwitches: 0,
        lastFaceSwitchTime: null,
        faceSwitchThreshold: 3,
        // ENHANCED: Device detection state
        deviceDetectionState: {
          consecutiveDeviceDetections: 0,
          lastDeviceDetectionTime: null,
          deviceDetectionThreshold: 3, // 3 consecutive detections
          phoneConfidenceHistory: [],
          combinedConfidenceThreshold: 0.6,
          lastPhoneViolationTime: 0,
          phoneViolationCooldown: 15000, // 15 seconds between phone violations
          gestureConsistencyCount: 0,
          visualDeviceCount: 0
        }
      };
      
      // Keys currently pressed (for combo detection)
      this.keysPressed = new Set();
      
      // Track potential screenshot attempts
      this.potentialScreenshotAttempt = {
        inProgress: false,
        method: null,
        timestamp: null,
        timeoutId: null
      };
      
      // Track clipboard content for changes
      this.lastClipboardCheck = Date.now();
      this.lastImageCheckTime = Date.now();
      
      // Initialize immediately
      this.init();
    }

    init() {
      console.log('🛡️ Initializing Enhanced Security Manager with Device Detection...');
      
      // Set up all security measures immediately
      this.setupFullscreenSecurity();
      this.setupTabSwitchDetection();
      this.setupDevToolsDetection();
      this.setupKeyboardBlocking();
      this.setupMouseAndContextBlocking();
      this.setupCopyPasteMonitoring();
      this.setupScreenSharingDetection();
      this.setupVoiceDetection();
      this.setupScreenshotDetection();
      this.setupPermissionMonitoring();
      this.setupImageCapture();
      
      // Setup event listener for custom screenshot detection events
      window.addEventListener('security-screenshot-attempt', (event) => {
        if (this.isInterviewActive && !this.exiting) {
          console.log('🚨 Screenshot attempt event received:', event.detail);
          this.handleWarningViolation('screenshot', 'Screenshot detected: ' + (event.detail.method || 'custom event'));
        }
      }, { capture: true });
      
      // ENHANCED: Setup event listener for device detection events
      window.addEventListener('security-device-violation', (event) => {
        if (this.isInterviewActive && !this.exiting) {
          console.log('🚨 Device violation event received:', event.detail);
          this.handleWarningViolation('deviceDetected', 'Device violation: ' + (event.detail.method || 'custom detection'));
        }
      }, { capture: true });
      
      console.log('✅ Enhanced Security Manager with Device Detection initialized');
    }

    setApplicationId(id) {
      this.applicationId = id;
      console.log(`Application ID set: ${id}`);
    }

    startInterview() {
      console.log('🚀 Starting interview monitoring with enhanced device detection...');
      this.interviewStartTime = Date.now();
      this.isInterviewActive = true;
      
      console.log('✅ Interview monitoring active with device detection');
      
      this.dispatchEvent('interview-started', {
        applicationId: this.applicationId,
        timestamp: new Date().toISOString()
      });
    }

    // Flag to prevent violations during exit
    setExiting(exiting) {
      this.exiting = exiting;
      console.log(`Security manager exit flag: ${exiting}`);
    }

    // ENHANCED: Face detection handler with comprehensive device analysis
    handleFaceDetection(faceData) {
      if (!this.isInterviewActive || !this.isEnforcementActive()) return;
      
      console.log('👁️ Face detection data received with device info:', faceData);
      
      const now = Date.now();
      this.faceDetectionState.lastFaceCheck = now;
      
      // EXISTING: Face detection logic (unchanged)
      // No face visible
      if (faceData.facesDetected === 0) {
        this.faceDetectionState.faceNotVisibleCount++;
        
        if (this.faceDetectionState.faceNotVisibleCount >= this.faceDetectionState.faceNotVisibleThreshold) {
          this.handleWarningViolation('faceNotVisible', 'Face not visible to camera');
          this.faceDetectionState.faceNotVisibleCount = 0;
        }
      } else {
        this.faceDetectionState.faceNotVisibleCount = 0;
      }
      
      // Multiple faces
      if (faceData.facesDetected > 1) {
        this.handleWarningViolation('multipleFaces', `${faceData.facesDetected} faces detected`);
      }
      
      // Looking away
      if (faceData.lookingAway) {
        this.faceDetectionState.lookingAwayCount++;
        
        if (this.faceDetectionState.lookingAwayCount >= this.faceDetectionState.lookingAwayThreshold) {
          this.handleWarningViolation('lookingAway', 'Looking away from camera');
          this.faceDetectionState.lookingAwayCount = 0;
        }
      } else {
        this.faceDetectionState.lookingAwayCount = 0;
      }
      
      // ENHANCED: Face switching detection
      if (faceData.facesDetected === 1 && faceData.faceDescriptor) {
        this.detectFaceSwitch(faceData.faceDescriptor, faceData.faceConfidence);
      }
      
      // Different person (legacy)
      if (faceData.differentPerson) {
        this.handleWarningViolation('differentPerson', 'Different person detected');
      }
      
      // ENHANCED: Comprehensive device detection handling
      this.handleDeviceDetection(faceData, now);
    }

    // NEW: Comprehensive device detection handler
    handleDeviceDetection(data, currentTime) {
      try {
        const deviceState = this.faceDetectionState.deviceDetectionState;
        
        console.log('📱 Processing device detection data:', {
          phoneConfidence: data.phoneConfidence || 0,
          combinedConfidence: data.combinedPhoneConfidence || 0,
          phoneGesture: data.phoneGesture || false,
          deviceDetected: data.deviceDetected || false,
          handsDetected: data.handsDetected || 0,
          method: data.detectionMethod || 'none'
        });
        
        // 1. PHONE CONFIDENCE TRACKING
        const phoneConfidence = data.phoneConfidence || 0;
        const combinedConfidence = data.combinedPhoneConfidence || 0;
        
        // Track confidence history for trend analysis
        deviceState.phoneConfidenceHistory.push({
          phoneConfidence,
          combinedConfidence,
          timestamp: currentTime
        });
        
        // Keep only last 10 seconds of history
        const tenSecondsAgo = currentTime - 10000;
        deviceState.phoneConfidenceHistory = deviceState.phoneConfidenceHistory.filter(
          entry => entry.timestamp > tenSecondsAgo
        );
        
        // 2. HIGH CONFIDENCE PHONE DETECTION
        if (combinedConfidence > deviceState.combinedConfidenceThreshold) {
          deviceState.consecutiveDeviceDetections++;
          
          console.log(`📱 High confidence device detection (${deviceState.consecutiveDeviceDetections}/${deviceState.deviceDetectionThreshold}):`, {
            combinedConfidence: combinedConfidence,
            threshold: deviceState.combinedConfidenceThreshold
          });
          
          // Trigger violation after consistent high confidence
          if (deviceState.consecutiveDeviceDetections >= deviceState.deviceDetectionThreshold) {
            const timeSinceLastViolation = currentTime - deviceState.lastPhoneViolationTime;
            
            if (timeSinceLastViolation > deviceState.phoneViolationCooldown) {
              this.handleWarningViolation('deviceDetected', 
                `Phone/device detected with ${Math.round(combinedConfidence * 100)}% confidence`);
              
              deviceState.lastPhoneViolationTime = currentTime;
              deviceState.consecutiveDeviceDetections = 0; // Reset after reporting
            }
          }
        } else {
          // Reset consecutive count if confidence drops
          deviceState.consecutiveDeviceDetections = Math.max(0, deviceState.consecutiveDeviceDetections - 1);
        }
        
        // 3. PHONE GESTURE DETECTION
        if (data.phoneGesture) {
          deviceState.gestureConsistencyCount++;
          
          console.log(`📞 Phone gesture detected (${deviceState.gestureConsistencyCount} times)`);
          
          // Immediate violation for clear phone gestures
          const timeSinceLastViolation = currentTime - deviceState.lastPhoneViolationTime;
          if (timeSinceLastViolation > deviceState.phoneViolationCooldown) {
            this.handleWarningViolation('phoneGesture', 'Phone gesture detected - device usage prohibited');
            deviceState.lastPhoneViolationTime = currentTime;
            deviceState.gestureConsistencyCount = 0;
          }
        }
        
        // 4. VISUAL DEVICE DETECTION
        if (data.devicesDetected && data.devicesDetected.length > 0) {
          const highConfidenceDevices = data.devicesDetected.filter(device => device.confidence > 0.7);
          
          if (highConfidenceDevices.length > 0) {
            deviceState.visualDeviceCount++;
            
            console.log(`📱 Visual devices detected: ${highConfidenceDevices.length} high-confidence devices`);
            
            // Report visual device detection
            const timeSinceLastViolation = currentTime - deviceState.lastPhoneViolationTime;
            if (timeSinceLastViolation > deviceState.phoneViolationCooldown) {
              this.handleWarningViolation('deviceDetected', 
                `Visual device detection: ${highConfidenceDevices.length} device(s) with high confidence`);
              
              deviceState.lastPhoneViolationTime = currentTime;
            }
          }
        }
        
        // 5. HAND GESTURE ANALYSIS
        if (data.suspiciousHandGesture) {
          console.log('🤏 Suspicious hand gesture detected');
          
          // Only report if we haven't reported recently
          const timeSinceLastViolation = currentTime - deviceState.lastPhoneViolationTime;
          if (timeSinceLastViolation > deviceState.phoneViolationCooldown) {
            this.handleWarningViolation('suspiciousHandGesture', 'Suspicious hand gesture suggesting device usage');
            deviceState.lastPhoneViolationTime = currentTime;
          }
        }
        
        // 6. TEMPORAL ANALYSIS
        if (deviceState.phoneConfidenceHistory.length >= 5) {
          const recentAverage = deviceState.phoneConfidenceHistory
            .slice(-5)
            .reduce((sum, entry) => sum + entry.combinedConfidence, 0) / 5;
          
          if (recentAverage > 0.4) {
            console.log(`📊 Sustained device detection over time: ${Math.round(recentAverage * 100)}% average confidence`);
            
            // Report sustained detection
            const timeSinceLastViolation = currentTime - deviceState.lastPhoneViolationTime;
            if (timeSinceLastViolation > deviceState.phoneViolationCooldown * 2) { // Longer cooldown for temporal analysis
              this.handleWarningViolation('deviceDetected', 
                `Sustained device usage detected: ${Math.round(recentAverage * 100)}% average confidence over time`);
              
              deviceState.lastPhoneViolationTime = currentTime;
            }
          }
        }
        
        // 7. LOG COMPREHENSIVE DEVICE STATE
        if (combinedConfidence > 0.2 || data.handsDetected > 0) {
          console.log('📊 Device detection state summary:', {
            combinedConfidence: Math.round(combinedConfidence * 100) + '%',
            phoneGesture: data.phoneGesture,
            consecutiveDetections: deviceState.consecutiveDeviceDetections,
            gestureCount: deviceState.gestureConsistencyCount,
            visualDevices: deviceState.visualDeviceCount,
            recentAverage: deviceState.phoneConfidenceHistory.length >= 3 ? 
              Math.round(deviceState.phoneConfidenceHistory.slice(-3).reduce((sum, entry) => sum + entry.combinedConfidence, 0) / 3 * 100) + '%' : 'N/A'
          });
        }
        
      } catch (error) {
        console.error('Error in device detection handling:', error);
      }
    }

    // ENHANCED: Face switching detection using face descriptors
    detectFaceSwitch(currentDescriptor, confidence) {
      try {
        // Only process if we have good confidence
        if (confidence < 0.7) {
          console.log('👁️ Face confidence too low for switching detection:', confidence);
          return;
        }
        
        // Store initial face if this is the first good detection
        if (!this.faceDetectionState.initialFaceDescriptor) {
          this.faceDetectionState.initialFaceDescriptor = currentDescriptor;
          this.faceDetectionState.faceDescriptorHistory = [currentDescriptor];
          console.log('👁️ Initial face descriptor stored');
          return;
        }
        
        // Compare with initial face descriptor
        const similarity = this.compareFaceDescriptors(
          this.faceDetectionState.initialFaceDescriptor, 
          currentDescriptor
        );
        
        console.log('👁️ Face similarity to initial:', similarity);
        
        // If similarity is below threshold, this might be a different person
        const SIMILARITY_THRESHOLD = 0.6; // Adjust this value (0.6 = 60% similarity required)
        
        if (similarity < SIMILARITY_THRESHOLD) {
          this.faceDetectionState.consecutiveFaceSwitches++;
          
          console.log(`👁️ Potential face switch detected (${this.faceDetectionState.consecutiveFaceSwitches}/${this.faceDetectionState.faceSwitchThreshold})`);
          
          // If we have multiple consecutive face switches, it's likely a different person
          if (this.faceDetectionState.consecutiveFaceSwitches >= this.faceDetectionState.faceSwitchThreshold) {
            const now = Date.now();
            
            // Don't spam violations - only report once every 10 seconds
            if (!this.faceDetectionState.lastFaceSwitchTime || 
                (now - this.faceDetectionState.lastFaceSwitchTime) > 10000) {
              
              this.handleWarningViolation('faceSwitch', 
                `Face switching detected - different person may be taking the interview`);
              
              this.faceDetectionState.lastFaceSwitchTime = now;
            }
            
            // Reset counter after reporting
            this.faceDetectionState.consecutiveFaceSwitches = 0;
          }
        } else {
          // Good match - reset the switch counter
          this.faceDetectionState.consecutiveFaceSwitches = 0;
        }
        
        // Keep a rolling history of recent face descriptors (last 10)
        this.faceDetectionState.faceDescriptorHistory.push(currentDescriptor);
        if (this.faceDetectionState.faceDescriptorHistory.length > 10) {
          this.faceDetectionState.faceDescriptorHistory.shift();
        }
        
      } catch (error) {
        console.error('Error in face switch detection:', error);
      }
    }

    // Compare two face descriptors using cosine similarity
    compareFaceDescriptors(descriptor1, descriptor2) {
      try {
        // Handle both array and object formats
        let vec1, vec2;
        
        if (Array.isArray(descriptor1)) {
          vec1 = descriptor1;
        } else if (descriptor1.descriptor && Array.isArray(descriptor1.descriptor)) {
          vec1 = descriptor1.descriptor;
        } else {
          console.warn('Invalid face descriptor format 1');
          return 0.5; // Neutral similarity
        }
        
        if (Array.isArray(descriptor2)) {
          vec2 = descriptor2;
        } else if (descriptor2.descriptor && Array.isArray(descriptor2.descriptor)) {
          vec2 = descriptor2.descriptor;
        } else {
          console.warn('Invalid face descriptor format 2');
          return 0.5; // Neutral similarity
        }
        
        // Ensure both vectors have the same length
        if (vec1.length !== vec2.length) {
          console.warn('Face descriptor length mismatch:', vec1.length, 'vs', vec2.length);
          return 0.5; // Neutral similarity
        }
        
        // Calculate cosine similarity
        let dotProduct = 0;
        let norm1 = 0;
        let norm2 = 0;
        
        for (let i = 0; i < vec1.length; i++) {
          dotProduct += vec1[i] * vec2[i];
          norm1 += vec1[i] * vec1[i];
          norm2 += vec2[i] * vec2[i];
        }
        
        norm1 = Math.sqrt(norm1);
        norm2 = Math.sqrt(norm2);
        
        if (norm1 === 0 || norm2 === 0) {
          return 0; // No similarity if either vector is zero
        }
        
        const similarity = dotProduct / (norm1 * norm2);
        
        // Convert from [-1, 1] to [0, 1] range
        return (similarity + 1) / 2;
        
      } catch (error) {
        console.error('Error comparing face descriptors:', error);
        return 0.5; // Neutral similarity on error
      }
    }

    // FULLSCREEN SECURITY - Fixed to handle exit properly
    setupFullscreenSecurity() {
      const fullscreenEvents = [
        'fullscreenchange',
        'webkitfullscreenchange',
        'mozfullscreenchange',
        'MSFullscreenChange'
      ];

      fullscreenEvents.forEach(event => {
        document.addEventListener(event, () => {
          // Don't flag violations during exit
          if (this.exiting) {
            console.log('Fullscreen change during exit - not flagging');
            return;
          }
          
          if (!this.isDocumentFullscreen() && this.isInterviewActive && this.isEnforcementActive()) {
            this.handleWarningViolation('fullscreenExit', 'Exited fullscreen mode');
            
            // Check if there's a potential screenshot in progress
            if (this.potentialScreenshotAttempt.inProgress) {
              console.log('🚨 Fullscreen exit detected during potential screenshot attempt');
              
              // This strongly confirms a screenshot was taken
              this.handleWarningViolation('screenshot', 
                `Screenshot confirmed: fullscreen exit during ${this.potentialScreenshotAttempt.method}`);
              
              this.clearPotentialScreenshotAttempt();
            }
          }
        }, { passive: true });
      });

      // Block ESC key at document level before other handlers
      const blockEscape = (e) => {
        if (e.key === 'Escape') {
          console.log('🚫 Escape key blocked by SecurityManager');
          e.preventDefault();
          e.stopPropagation();
          e.stopImmediatePropagation();
          
          // Only flag if interview is active and enforcement is active
          if (this.isInterviewActive && this.isEnforcementActive()) {
            this.handleWarningViolation('escapeKey', 'Attempted to press Escape key');
          }
          return false;
        }
      };
      
      // Add listener with capture=true to catch escape before any other handlers
      document.addEventListener('keydown', blockEscape, { 
        capture: true, 
        passive: false 
      });
      
      // Also add at window level as fallback
      window.addEventListener('keydown', blockEscape, { 
        capture: true, 
        passive: false 
      });
    }

    // TAB SWITCH DETECTION
    setupTabSwitchDetection() {
      let tabSwitchTimeout;
      let lastVisibilityState = document.visibilityState;
      
      // Track key combinations that indicate tab switching
      const tabSwitchKeys = new Set();
      
      // DISABLE WINDOW BLUR DETECTION to prevent false positives
      const DISABLE_WINDOW_BLUR = true;
      
      // Primary: Visibility change detection - focus on tab switch
      document.addEventListener('visibilitychange', () => {
        if (!this.isInterviewActive || !this.isEnforcementActive() || this.exiting) return;
        
        // Only process if this is a genuine change
        if (document.visibilityState !== lastVisibilityState) {
          lastVisibilityState = document.visibilityState;
          
          if (document.hidden) {
            console.log('🚨 Document hidden - tab switch detected');
            
            // Clear any existing timeout
            if (tabSwitchTimeout) clearTimeout(tabSwitchTimeout);
            
            // Shorter timeout for faster detection
            tabSwitchTimeout = setTimeout(() => {
              console.log('⚠️ Tab switch confirmed (visibility)');
              this.handleWarningViolation('tabSwitch', 'Switched to another tab');
            }, 300);
          } else {
            // Document became visible again
            if (tabSwitchTimeout) {
              clearTimeout(tabSwitchTimeout);
              tabSwitchTimeout = null;
            }
          }
        }
      }, { capture: true, passive: true });

      // Window blur detection - DISABLED to prevent false positives
      window.addEventListener('blur', (e) => {
        if (!this.isInterviewActive || !this.isEnforcementActive() || this.exiting) return;
        
        // Check if tab switching keys were pressed
        const isTabSwitchKeyCombo = tabSwitchKeys.has('Control+Tab') || 
                                   tabSwitchKeys.has('Alt+Tab') ||
                                   tabSwitchKeys.has('Command+Tab');
        
        if (isTabSwitchKeyCombo) {
          console.log('⚠️ Tab switching key combination detected during blur');
          this.handleWarningViolation('tabSwitch', 'Switched tabs using keyboard shortcut');
          tabSwitchKeys.clear();
          return;
        }
        
        // DISABLED: Window blur violations
        if (DISABLE_WINDOW_BLUR) {
          console.log('Window blur detected but reporting disabled to prevent false positives');
          return;
        }
      }, { capture: true, passive: true });
      
      // Key combination detection
      window.addEventListener('keydown', (e) => {
        if (!this.isInterviewActive || !this.isEnforcementActive() || this.exiting) return;
        
        // Enhanced detection logic for tab switching key combinations
        if ((e.metaKey || e.ctrlKey) && (e.key === 'Tab' || e.keyCode === 9)) {
          console.log('⚠️ Tab switching key combination detected: ' + (e.metaKey ? 'Command+Tab' : 'Control+Tab'));
          
          // Store the combination
          tabSwitchKeys.add(e.metaKey ? 'Command+Tab' : 'Control+Tab');
          
          // Try to prevent default behavior
          try {
            e.preventDefault();
            e.stopPropagation();
          } catch (error) {
            console.log('Could not prevent key event (expected for system shortcuts)');
          }
          
          // Report immediately
          this.handleWarningViolation('tabSwitch', 
            `Attempted to switch tabs using ${e.metaKey ? 'Command+Tab' : 'Control+Tab'}`);
          
          return false;
        }
        
        // Watch for Alt+Tab (Windows task switching)
        if (e.altKey && (e.key === 'Tab' || e.keyCode === 9)) {
          console.log('⚠️ Alt+Tab detected');
          
          tabSwitchKeys.add('Alt+Tab');
          
          try {
            e.preventDefault();
            e.stopPropagation();
          } catch (error) {
            console.log('Could not prevent key event (expected for system shortcuts)');
          }
          
          this.handleWarningViolation('tabSwitch', 'Attempted to switch tasks using Alt+Tab');
          return false;
        }
      }, { capture: true, passive: false });
      
      // Clear tab switch keys on keyup
      window.addEventListener('keyup', (e) => {
        if ((e.key === 'Control' || e.key === 'Meta' || e.key === 'Alt' || e.key === 'Tab') ||
            (e.keyCode === 17 || e.keyCode === 91 || e.keyCode === 18 || e.keyCode === 9)) {
          tabSwitchKeys.clear();
        }
      }, { capture: true, passive: true });
    }

    // DEVELOPER TOOLS DETECTION
    setupDevToolsDetection() {
      let devToolsOpen = false;
      
      // Method 1: Window size inspection
      const checkWindowSize = () => {
        if (!this.isInterviewActive || !this.isEnforcementActive()) return;
        
        const threshold = 200;
        if (window.outerHeight - window.innerHeight > threshold || 
            window.outerWidth - window.innerWidth > threshold) {
          if (!devToolsOpen) {
            devToolsOpen = true;
            this.handleWarningViolation('devTools', 'Developer tools opened');
          }
        } else {
          devToolsOpen = false;
        }
      };
      
      // Check every 3 seconds
      setInterval(() => {
        checkWindowSize();
      }, 3000);
    }

    // KEYBOARD BLOCKING
    setupKeyboardBlocking() {
      document.addEventListener('keydown', (e) => {
        if (!this.isInterviewActive || !this.isEnforcementActive()) return;
        
        // List of blocked key combinations
        const blockedCombinations = [
          // Developer tools
          { key: 'F12' },
          { key: 'I', ctrl: true, shift: true },
          { key: 'C', ctrl: true, shift: true },
          { key: 'J', ctrl: true, shift: true },
          // Navigation
          { key: 'Tab', alt: true },
          { key: 'Tab', ctrl: true },
          { key: 'w', ctrl: true },
          { key: 'W', ctrl: true },
          { key: 'n', ctrl: true },
          { key: 'N', ctrl: true },
          { key: 't', ctrl: true },
          { key: 'T', ctrl: true },
          // Refresh
          { key: 'r', ctrl: true },
          { key: 'R', ctrl: true },
          { key: 'F5' },
          // Other
          { key: 'F4', alt: true },
          { key: 'F11' }
        ];
        
        const isBlocked = blockedCombinations.some(combo => {
          if (combo.key === e.key) {
            if (combo.ctrl && combo.shift) {
              return e.ctrlKey && e.shiftKey;
            } else if (combo.ctrl) {
              return e.ctrlKey;
            } else if (combo.alt) {
              return e.altKey;
            } else if (combo.shift) {
              return e.shiftKey;
            }
            return true;
          }
          return false;
        });
        
        if (isBlocked) {
          e.preventDefault();
          e.stopPropagation();
          e.stopImmediatePropagation();
          this.handleWarningViolation('keyboardShortcut', `Blocked shortcut: ${e.key}`);
          return false;
        }
      }, { capture: true, passive: false });
    }

    // MOUSE AND CONTEXT MENU BLOCKING
    setupMouseAndContextBlocking() {
      document.addEventListener('contextmenu', (e) => {
        if (!this.isInterviewActive || !this.isEnforcementActive()) return;
        
        // Allow right-click in specific areas
        const allowedSelectors = [
          '.monaco-editor',
          'textarea',
          'input[type="text"]',
          '[data-allow-select]'
        ];
        
        const isAllowed = allowedSelectors.some(selector => {
          return e.target.closest(selector) !== null;
        });
        
        if (!isAllowed) {
          e.preventDefault();
          e.stopPropagation();
          this.handleWarningViolation('rightClick', 'Right-click attempted');
          return false;
        }
      }, true);
    }

    // COPY PASTE MONITORING (INFO ONLY)
    setupCopyPasteMonitoring() {
      console.log('📋 Setting up copy/paste monitoring (info only)...');
      
      ['copy', 'cut', 'paste'].forEach(eventType => {
        document.addEventListener(eventType, (e) => {
          if (!this.isInterviewActive || this.exiting) return;
          
          // Check if this is in an allowed area
          const allowedSelectors = [
            '.monaco-editor',
            'textarea[data-allow-clipboard]',
            'input[data-allow-clipboard]',
            '[data-allow-clipboard]'
          ];
          
          const isAllowed = allowedSelectors.some(selector => {
            return e.target.closest(selector) !== null;
          });
          
          if (!isAllowed) {
            console.log(`📋 ${eventType} operation detected (monitoring only)`);
            
            // Handle as info-only violation (doesn't count towards disqualification)
            this.handleInfoOnlyViolation(eventType, `${eventType} operation detected`);
          }
        }, { capture: true, passive: true });
      });
      
      // Monitor keyboard shortcuts for copy/paste (info only)
      document.addEventListener('keydown', (e) => {
        if (!this.isInterviewActive || this.exiting) return;
        
        // Check for copy/paste keyboard shortcuts
        if ((e.ctrlKey || e.metaKey) && ['c', 'v', 'x', 'a'].includes(e.key.toLowerCase())) {
          // Check if this is in an allowed area
          const allowedSelectors = [
            '.monaco-editor',
            'textarea[data-allow-clipboard]',
            'input[data-allow-clipboard]',
            '[data-allow-clipboard]'
          ];
          
          const isAllowed = allowedSelectors.some(selector => {
            return e.target.closest(selector) !== null;
          });
          
          if (!isAllowed) {
            const actionMap = {
              'c': 'copy',
              'v': 'paste',
              'x': 'cut',
              'a': 'select all'
            };
            
            console.log(`📋 ${actionMap[e.key.toLowerCase()]} keyboard shortcut detected (monitoring only)`);
            
            // Handle as info-only violation
            this.handleInfoOnlyViolation(actionMap[e.key.toLowerCase()], 
              `${actionMap[e.key.toLowerCase()]} keyboard shortcut detected`);
          }
        }
      }, { capture: true, passive: true });
      
      console.log('✅ Copy/paste monitoring (info only) initialized');
    }

    // SCREEN SHARING DETECTION
    setupScreenSharingDetection() {
      // Store initial screen configuration
      let initialScreenConfig = null;
      let detectionStarted = false;
      
      // Check for screen sharing every 5 seconds
      setInterval(() => {
        if (!this.isInterviewActive || !this.isEnforcementActive()) return;
        
        // Initialize screen config on first run
        if (!initialScreenConfig && !detectionStarted) {
          initialScreenConfig = {
            width: window.screen.width,
            height: window.screen.height,
            availWidth: window.screen.availWidth,
            availHeight: window.screen.availHeight
          };
          detectionStarted = true;
          console.log('Initial screen config:', initialScreenConfig);
          return;
        }
        
        // More sophisticated dual monitor detection
        if (initialScreenConfig) {
          const currentWidth = window.screen.width;
          const currentHeight = window.screen.height;
          
          // Only flag significant changes (>200 pixels)
          const widthDiff = Math.abs(currentWidth - initialScreenConfig.width);
          const heightDiff = Math.abs(currentHeight - initialScreenConfig.height);
          
          if (widthDiff > 200 || heightDiff > 200) {
            this.handleWarningViolation('dualMonitor', 
              `Screen configuration changed: ${currentWidth}x${currentHeight} (was ${initialScreenConfig.width}x${initialScreenConfig.height})`);
          }
        }
      }, 5000);
    }

    // VOICE DETECTION - Simplified
    setupVoiceDetection() {
      console.log('🎤 Voice detection setup (simplified)');
    }

    // SCREENSHOT DETECTION
    setupScreenshotDetection() {
      console.log('🔒 Setting up enhanced screenshot detection...');
      
      let lastImageDetectionTime = Date.now();
      let lastKeyCombination = null;
      let lastKeyPressTime = Date.now();
      
      // Enhanced keyboard tracking
      const setupKeyboardTracking = () => {
        let keysPressed = new Set();
        
        const handleKeyDown = (e) => {
          if (!this.isInterviewActive || this.exiting) return;
          
          console.log(`Key down: ${e.key}, Meta: ${e.metaKey}, Shift: ${e.shiftKey}`);
          
          keysPressed.add(e.key);
          if (e.code) keysPressed.add(e.code);
          
          // Add modifier keys
          if (e.metaKey || e.key === 'Meta') {
            keysPressed.add('Meta');
            keysPressed.add('Command');
          }
          if (e.shiftKey || e.key === 'Shift') keysPressed.add('Shift');
          if (e.altKey || e.key === 'Alt') keysPressed.add('Alt');
          if (e.ctrlKey || e.key === 'Control') keysPressed.add('Control');
          
          // Direct check for Mac screenshot shortcuts
          if (e.metaKey && e.shiftKey && (e.key === '3' || e.key === '4' || e.key === '5')) {
            console.log(`🚨 Direct detection of Mac screenshot: Command+Shift+${e.key}`);
            this.handleWarningViolation('screenshot', `Screenshot detected: Command+Shift+${e.key}`);
          }
          
          // Check for Windows PrintScreen
          if (e.key === 'PrintScreen' || e.code === 'PrintScreen') {
            console.log('🚨 Detected Windows screenshot: PrintScreen');
            this.handleWarningViolation('screenshot', 'Screenshot detected: PrintScreen');
          }
          
          // Record key combination for later verification
          if ((e.metaKey && e.shiftKey && ['3', '4', '5'].includes(e.key)) || 
              e.key === 'PrintScreen' || e.code === 'PrintScreen' ||
              (e.metaKey && e.shiftKey && (e.key === 's' || e.key === 'S'))) {
                
            let keyCombo = '';
            if (e.metaKey) keyCombo += 'Command+';
            if (e.shiftKey) keyCombo += 'Shift+';
            if (e.altKey) keyCombo += 'Alt+';
            keyCombo += e.key;
            
            lastKeyCombination = keyCombo;
            lastKeyPressTime = Date.now();
            
            console.log(`🔑 Potential screenshot key combination detected: ${keyCombo}`);
            
            // Check for evidence after a short delay
            setTimeout(() => {
              this.checkForScreenshotEvidence(keyCombo);
            }, 300);
          }
        };
        
        const handleKeyUp = (e) => {
          keysPressed.delete(e.key);
          if (e.code) keysPressed.delete(e.code);
          
          if (e.key === 'Meta' || e.key === 'Command') {
            keysPressed.delete('Meta');
            keysPressed.delete('Command');
          }
          if (e.key === 'Shift') keysPressed.delete('Shift');
          if (e.key === 'Alt') keysPressed.delete('Alt');
          if (e.key === 'Control') keysPressed.delete('Control');
        };
        
        // Add handlers at multiple levels
        document.addEventListener('keydown', handleKeyDown.bind(this), { 
          capture: true, 
          passive: false 
        });
        document.addEventListener('keyup', handleKeyUp, { capture: true });
        
        window.addEventListener('keydown', handleKeyDown.bind(this), { 
          capture: true, 
          passive: false 
        });
        window.addEventListener('keyup', handleKeyUp, { capture: true });
      };
      
      setupKeyboardTracking.call(this);
      
      console.log('✅ Enhanced screenshot detection initialized');
    }

    // Check for screenshot evidence
    checkForScreenshotEvidence(keyCombo) {
      console.log(`🔍 Checking for evidence of screenshot after ${keyCombo}...`);
      
      if (!this.isInterviewActive || this.exiting) {
        console.log('Not checking evidence - interview not active');
        return;
      }
      
      // Check clipboard for new images
      this.checkClipboardForImages().then(foundImage => {
        if (foundImage) {
          console.log('🖼️ Found image in clipboard after key combo - confirming screenshot');
          this.handleWarningViolation('screenshot', 
            `Screenshot detected: ${keyCombo} (confirmed by clipboard image)`);
        } else {
          // Report based on key combo alone
          console.log('📸 Reporting screenshot based on key combo');
          this.handleWarningViolation('screenshot', `Screenshot detected: ${keyCombo} (key combination)`);
        }
      });
    }

    // PERMISSION MONITORING
    setupPermissionMonitoring() {
      try {
        if (navigator.permissions) {
          navigator.permissions.query({ name: 'camera' }).then(permissionStatus => {
            permissionStatus.onchange = () => {
              console.log(`📸 Camera permission status changed to: ${permissionStatus.state}`);
              
              if (this.potentialScreenshotAttempt.inProgress) {
                console.log('🚨 Permission change detected during potential screenshot attempt');
                this.handleWarningViolation('screenshot', 
                  `Screenshot detected after permission change: ${this.potentialScreenshotAttempt.method}`);
                this.clearPotentialScreenshotAttempt();
              }
            };
          }).catch(err => {
            console.log('Permission query not supported for camera');
          });
        }
      } catch (error) {
        console.error('Error setting up permission monitoring:', error);
      }
      
      console.log('✅ Permission monitoring initialized');
    }

    // IMAGE CAPTURE MONITORING
    setupImageCapture() {
      try {
        // Periodic check for clipboard content
        setInterval(async () => {
          if (!this.isInterviewActive || this.exiting) {
            return;
          }
          
          try {
            if (navigator.clipboard && navigator.clipboard.read) {
              try {
                const items = await navigator.clipboard.read();
                
                for (const item of items) {
                  if (item.types.some(type => type.startsWith('image/'))) {
                    if (Date.now() - this.lastImageCheckTime > 5000) {
                      console.log('🚨 Image found in clipboard during monitoring');
                      
                      this.handleWarningViolation('screenshot', 
                        'Screenshot detected: image found in clipboard');
                      
                      this.lastImageCheckTime = Date.now();
                      return;
                    }
                  }
                }
              } catch (e) {
                // Permission errors expected
              }
            }
          } catch (error) {
            // Ignore errors
          }
        }, 2000);
        
        // Monitor for paste events with images
        document.addEventListener('paste', (e) => {
          if (!this.isInterviewActive || this.exiting) return;
          
          if (e.clipboardData && e.clipboardData.items) {
            for (let i = 0; i < e.clipboardData.items.length; i++) {
              if (e.clipboardData.items[i].type.indexOf('image') !== -1) {
                console.log('🚨 Image pasted - possible screenshot');
                this.handleWarningViolation('screenshot', 'Screenshot detected: image pasted from clipboard');
                break;
              }
            }
          }
        }, { capture: true });
      } catch (error) {
        console.error('Error setting up image capture monitoring:', error);
      }
      
      console.log('✅ Image capture monitoring initialized');
    }

    // ENHANCED: Track potential screenshot attempt
    startPotentialScreenshotTracking(method) {
      this.clearPotentialScreenshotAttempt();
      
      console.log(`🔍 Tracking potential screenshot attempt: ${method}`);
      
      this.potentialScreenshotAttempt = {
        inProgress: true,
        method: method,
        timestamp: Date.now(),
        timeoutId: setTimeout(() => {
          if (this.potentialScreenshotAttempt.inProgress) {
            console.log('⏱️ Screenshot timeout reached - assuming screenshot was taken');
            this.handleWarningViolation('screenshot', `Screenshot detected: ${method} (timeout)`);
            this.clearPotentialScreenshotAttempt();
          }
        }, 500)
      };
      
      if (!this.isDocumentFullscreen()) {
        console.log('🚨 Fullscreen exit detected with screenshot attempt');
        this.handleWarningViolation('screenshot', 
          `Screenshot detected with fullscreen exit: ${method}`);
        this.clearPotentialScreenshotAttempt();
      }
      
      this.checkClipboardForImages();
    }

    // Monitor clipboard for images
    async checkClipboardForImages() {
      try {
        if (!navigator.clipboard || !navigator.clipboard.read) return false;
        
        try {
          const items = await navigator.clipboard.read();
          
          for (const item of items) {
            if (item.types.some(type => type.startsWith('image/'))) {
              console.log('🚨 Image found in clipboard - screenshot confirmed');
              this.handleWarningViolation('screenshot', 'Screenshot confirmed: image in clipboard');
              return true;
            }
          }
        } catch (e) {
          console.log('Clipboard permission denied during check');
        }
      } catch (error) {
        console.log('Error checking clipboard for images');
      }
      
      return false;
    }

    clearPotentialScreenshotAttempt() {
      if (this.potentialScreenshotAttempt.timeoutId) {
        clearTimeout(this.potentialScreenshotAttempt.timeoutId);
      }
      
      this.potentialScreenshotAttempt = {
        inProgress: false,
        method: null,
        timestamp: null,
        timeoutId: null
      };
    }

    // ENHANCED: Updated violation handler with device-specific logic
    handleWarningViolation(type, message) {
      // Always log violations, even during grace period
      console.warn(`🚨 Warning Violation: ${type} - ${message}`);
      
      const timestamp = new Date().toISOString();
      
      // Quick exit check
      if (this.exiting) {
        console.log(`Ignoring violation during exit: ${type}`);
        return;
      }
      
      // Grace period check
      if (!this.isEnforcementActive()) {
        console.log(`Grace period active - not counting violation: ${type}`);
        
        // Still dispatch event during grace period for tracking
        this.dispatchEvent('security-violation', {
          type,
          message,
          count: 0,
          totalViolations: 0,
          timestamp,
          category: 'warning',
          gracePeriod: true
        });
        
        return;
      }
      
      // Increment violation count
      this.warningViolations[type] = (this.warningViolations[type] || 0) + 1;
      
      // Add to suspicious events
      this.suspiciousEvents.push({
        type,
        message,
        timestamp,
        category: 'warning',
        priority: this.isDeviceViolation(type) ? 'high' : 'normal' // ENHANCED: Higher priority for device violations
      });
      
      // Calculate total violations
      const totalViolations = Object.values(this.warningViolations).reduce((a, b) => a + b, 0);
      
      // Get threshold for this violation type
      const threshold = this.VIOLATION_THRESHOLDS[type] || this.MAX_WARNINGS;
      
      console.log(`📊 Violation count: ${type} = ${this.warningViolations[type]}/${threshold}, Total = ${totalViolations}/${this.MAX_WARNINGS}`);
      
      // ENHANCED: Special handling for device violations
      if (this.isDeviceViolation(type)) {
        console.log(`📱 Device violation detected: ${type} (${this.warningViolations[type]}/${threshold})`);
      }
      
      // Dispatch security violation event
      this.dispatchEvent('security-violation', {
        type,
        message,
        count: this.warningViolations[type],
        totalViolations,
        threshold,
        timestamp,
        category: 'warning',
        priority: this.isDeviceViolation(type) ? 'high' : 'normal',
        isDeviceViolation: this.isDeviceViolation(type)
      });
      
      // Check for final warning (at 5 total violations OR device violation threshold reached)
      if (totalViolations === this.MAX_WARNINGS || 
          (this.isDeviceViolation(type) && this.warningViolations[type] === threshold - 1)) {
        this.dispatchEvent('final-warning', {
          message: this.isDeviceViolation(type) 
            ? 'FINAL WARNING: One more device violation will result in disqualification!'
            : 'FINAL WARNING: One more violation will result in disqualification!',
          timestamp,
          violationType: type
        });
      }
      
      // ENHANCED: Check for disqualification with device-specific thresholds
      const shouldDisqualify = totalViolations > this.MAX_WARNINGS || 
                              this.warningViolations[type] > threshold;
      
      if (shouldDisqualify) {
        const reason = this.isDeviceViolation(type) 
          ? `Device violation limit exceeded: ${this.warningViolations[type]} ${type} violations (limit: ${threshold})`
          : `Maximum violations reached: ${totalViolations} total violations (limit: ${this.MAX_WARNINGS})`;
        
        this.disqualifyUser(reason);
        return;
      }
    }

    handleAnalyticsViolation(type, message) {
      const timestamp = new Date().toISOString();
      
      // Increment analytics violation count
      this.analyticsOnlyViolations[type] = (this.analyticsOnlyViolations[type] || 0) + 1;
      
      // Add to suspicious events
      this.suspiciousEvents.push({
        type,
        message,
        timestamp,
        category: 'analytics'
      });
      
      console.log(`📊 Analytics Violation: ${type} - ${message}`);
      
      // Dispatch analytics violation event
      this.dispatchEvent('analytics-violation', {
        type,
        message,
        count: this.analyticsOnlyViolations[type],
        timestamp,
        category: 'analytics'
      });
    }

    // Handle info-only violations (show in logs but don't count towards disqualification)
    handleInfoOnlyViolation(type, message) {
      const timestamp = new Date().toISOString();
      
      console.log(`📋 Info-Only Violation: ${type} - ${message}`);
      
      // Increment info-only violation count
      this.infoOnlyViolations[type] = (this.infoOnlyViolations[type] || 0) + 1;
      
      // Add to suspicious events with special category
      this.suspiciousEvents.push({
        type,
        message,
        timestamp,
        category: 'info-only'
      });
      
      // Dispatch info-only violation event (for logging/tracking purposes)
      this.dispatchEvent('info-only-violation', {
        type,
        message,
        count: this.infoOnlyViolations[type],
        timestamp,
        category: 'info-only'
      });
    }

    // NEW: Helper function to identify device-related violations
    isDeviceViolation(type) {
      const deviceViolationTypes = [
        'deviceDetected',
        'phoneDetected', 
        'phoneGesture',
        'suspiciousHandGesture',
        'handsRaised',
        'handsOutOfFrame'
      ];
      
      return deviceViolationTypes.includes(type);
    }

    // DISQUALIFICATION
    disqualifyUser(reason = 'Maximum violations exceeded') {
      console.error('🚫 DISQUALIFYING USER - ' + reason);
      
      this.isInterviewActive = false;
      this.exiting = true; // Prevent further violations
      
      this.dispatchEvent('interview-disqualified', {
        reason,
        warningViolations: { ...this.warningViolations },
        analyticsViolations: { ...this.analyticsOnlyViolations },
        infoOnlyViolations: { ...this.infoOnlyViolations },
        totalWarningViolations: Object.values(this.warningViolations).reduce((a, b) => a + b, 0),
        suspiciousEvents: [...this.suspiciousEvents],
        timestamp: new Date().toISOString()
      });
    }

    // UTILITY METHODS
    isEnforcementActive() {
      if (!this.interviewStartTime) return false;
      return (Date.now() - this.interviewStartTime) > this.startDelay;
    }

    isDocumentFullscreen() {
      return !!(
        document.fullscreenElement ||
        document.mozFullScreenElement ||
        document.webkitFullscreenElement ||
        document.msFullscreenElement
      );
    }

    dispatchEvent(eventName, detail) {
      try {
        const event = new CustomEvent(eventName, { detail });
        window.dispatchEvent(event);
      } catch (error) {
        console.error('Error dispatching event:', error);
      }
    }

    getViolationSummary() {
      return {
        warningViolations: { ...this.warningViolations },
        analyticsViolations: { ...this.analyticsOnlyViolations },
        infoOnlyViolations: { ...this.infoOnlyViolations },
        totalWarningViolations: Object.values(this.warningViolations).reduce((a, b) => a + b, 0),
        totalAnalyticsViolations: Object.values(this.analyticsOnlyViolations).reduce((a, b) => a + b, 0),
        totalInfoOnlyViolations: Object.values(this.infoOnlyViolations).reduce((a, b) => a + b, 0),
        suspiciousEvents: [...this.suspiciousEvents],
        applicationId: this.applicationId
      };
    }

    endInterview() {
      this.isInterviewActive = false;
      this.exiting = true; // Prevent violations during cleanup
      console.log('🔚 Interview ended');
      
      this.dispatchEvent('interview-ended', {
        applicationId: this.applicationId,
        timestamp: new Date().toISOString(),
        warningViolations: { ...this.warningViolations },
        analyticsViolations: { ...this.analyticsOnlyViolations },
        infoOnlyViolations: { ...this.infoOnlyViolations }
      });
    }

    // Simple fullscreen enforcement function
    enforceFullscreenModeSimple() {
      console.log('🔒 Enforcing fullscreen mode (simplified)...');
      
      // Remove any existing blocker first
      const existingBlocker = document.getElementById('fullscreen-blocker-simple');
      if (existingBlocker) {
        existingBlocker.remove();
      }
      
      const blocker = document.createElement('div');
      blocker.id = 'fullscreen-blocker-simple';
      blocker.style.cssText = `
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background: rgba(255,0,0,0.95) !important;
        color: white !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        z-index: 2147483647 !important;
        font-size: 24px !important;
        text-align: center !important;
        pointer-events: all !important;
      `;
      
      blocker.innerHTML = `
        <div style="font-size: 32px; margin-bottom: 20px;">⚠️ FULLSCREEN REQUIRED ⚠️</div>
        <div style="font-size: 18px; margin: 20px 0; max-width: 80%;">
          You must be in fullscreen mode to continue the interview.<br/>
          Click the button below to enter fullscreen.
        </div>
        <button id="enable-fullscreen-simple-btn" style="
          font-size: 18px !important;
          padding: 15px 30px !important;
          background: white !important;
          color: red !important;
          border: none !important;
          border-radius: 8px !important;
          cursor: pointer !important;
          font-weight: bold !important;
          pointer-events: all !important;
        ">Enable Fullscreen Mode</button>
        <div style="font-size: 14px; margin-top: 20px; color: #ffcccc;">
          The interview is paused until you enter fullscreen
        </div>
      `;
      
      document.body.appendChild(blocker);
      
      // Add click handler with proper error handling
      const button = document.getElementById('enable-fullscreen-simple-btn');
      if (button) {
        button.addEventListener('click', async () => {
          try {
            console.log('🖥️ Fullscreen button clicked');
            const elem = document.documentElement;
            
            if (elem.requestFullscreen) {
              await elem.requestFullscreen();
            } else if (elem.mozRequestFullScreen) {
              await elem.mozRequestFullScreen();
            } else if (elem.webkitRequestFullscreen) {
              await elem.webkitRequestFullscreen();
            } else if (elem.msRequestFullscreen) {
              await elem.msRequestFullscreen();
            } else {
              throw new Error('Fullscreen API not supported');
            }
            
            console.log('✅ Fullscreen request sent');
          } catch (error) {
            console.error('❌ Fullscreen request failed:', error);
            
            // Show fallback instructions
            button.innerHTML = 'Press F11 or use browser fullscreen';
            button.style.background = '#ff9500';
            
            setTimeout(() => {
              button.innerHTML = 'Try Fullscreen Again';
              button.style.background = 'white';
            }, 3000);
          }
        });
      }
      
      // Remove blocker when fullscreen is achieved
      const checkAndRemoveBlocker = () => {
        if (this.isDocumentFullscreen()) {
          const currentBlocker = document.getElementById('fullscreen-blocker-simple');
          if (currentBlocker) {
            console.log('✅ Fullscreen achieved, removing blocker');
            currentBlocker.remove();
          }
        }
      };
      
      // Listen for fullscreen changes
      const fullscreenEvents = [
        'fullscreenchange',
        'webkitfullscreenchange', 
        'mozfullscreenchange',
        'MSFullscreenChange'
      ];
      
      fullscreenEvents.forEach(event => {
        document.addEventListener(event, checkAndRemoveBlocker);
      });
      
      console.log('✅ Simple fullscreen blocker created');
    }

    // Simple fullscreen request function
    requestFullscreenSimple() {
      return new Promise(async (resolve) => {
        try {
          console.log('🖥️ Simple fullscreen request...');
          const elem = document.documentElement;
          
          if (elem.requestFullscreen) {
            await elem.requestFullscreen();
          } else if (elem.mozRequestFullScreen) {
            await elem.mozRequestFullScreen();
          } else if (elem.webkitRequestFullscreen) {
            await elem.webkitRequestFullscreen();
          } else if (elem.msRequestFullscreen) {
            await elem.msRequestFullscreen();
          } else {
            throw new Error('Fullscreen not supported');
          }
          
          console.log('✅ Fullscreen request successful');
          resolve(true);
        } catch (error) {
          console.error('❌ Fullscreen request failed:', error);
          resolve(false);
        }
      });
    }

  } // End of SecurityManager class
  
  // Create global reference
  window.SecurityManager = SecurityManager;
  console.log('✅ Enhanced SecurityManager with Device Detection loaded successfully');

})();
