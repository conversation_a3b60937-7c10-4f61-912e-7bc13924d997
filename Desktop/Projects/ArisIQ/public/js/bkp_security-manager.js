// File: public/js/security-manager.js - ENHANCED VERSION WITH COPY/PASTE WARNING ONLY

(function() {
  'use strict';
  
  // Prevent multiple instances
  if (typeof window.SecurityManager !== 'undefined') {
    console.log('SecurityManager already exists, reusing instance');
    return;
  }
  
  class SecurityManager {
    constructor() {
      // Singleton pattern
      if (SecurityManager.instance) {
        return SecurityManager.instance;
      }
      SecurityManager.instance = this;
      window.securityManagerInstance = this;
      
      this.MAX_WARNINGS = 5;
      this.isInterviewActive = false;
      this.applicationId = null;
      this.startDelay = 5000; // 5 seconds grace period
      this.interviewStartTime = null;
      this.exiting = false; // Flag to prevent violations during exit
      
      // FIXED: Set ALL violations to 5 warnings before disqualification
      this.VIOLATION_THRESHOLDS = {
        // ALL violations now follow the same pattern: 5 warnings, then disqualify on 6th
        fullscreenExit: 5,
        tabSwitch: 5,
        windowBlur: 5,
        devTools: 5,
        keyboardShortcut: 5,
        rightClick: 5,
        multipleFaces: 5,
        faceNotVisible: 5,
        escapeKey: 5,
        lookingAway: 5,
        screenSharing: 5,
        screenRecording: 5,
        dualMonitor: 5,
        screenshot: 5,
        differentPerson: 5,      // CHANGED: Now 5 warnings instead of immediate
        suspiciousTyping: 5,
        contentPasted: 5,
        faceSwitch: 5,           // NEW: Face switching detection
        // NEW: Hand detection thresholds
        handsRaised: 5,          // Hands raised for extended time
        handsOutOfFrame: 5,      // Hands hidden for extended time
        suspiciousHandGesture: 5, // Phone-holding gestures
        deviceDetected: 5        // Physical devices detected
      };
      
      // Warning violations (cause warnings and disqualification)
      this.warningViolations = {
        fullscreenExit: 0,
        tabSwitch: 0,
        windowBlur: 0,
        devTools: 0,
        keyboardShortcut: 0,
        rightClick: 0,
        multipleFaces: 0,
        faceNotVisible: 0,
        escapeKey: 0,
        lookingAway: 0,
        deepfake: 0,
        screenSharing: 0,
        screenRecording: 0,
        dualMonitor: 0,
        screenshot: 0,
        differentPerson: 0,
        suspiciousTyping: 0,
        contentPasted: 0,
        faceSwitch: 0,
        // NEW: Hand detection violations
        handsRaised: 0,
        handsOutOfFrame: 0,
        suspiciousHandGesture: 0,
        deviceDetected: 0
      };
      
      // Analytics violations (recorded but don't cause warnings)
      this.analyticsOnlyViolations = {
        suspiciousTyping: 0,
        codeAnomaly: 0,
        eyeTrackingIssue: 0,
        suspiciousResponseTime: 0,
        microExpressionAnomaly: 0,
        suspiciousLighting: 0,
        lipSyncIssue: 0,
        environmentChange: 0,
        multipleVoices: 0
      };
      
      // NEW: Info-only violations (show warnings but don't count towards disqualification)
      this.infoOnlyViolations = {
        copyPaste: 0,
        copy: 0,
        paste: 0,
        cut: 0,
        selectAll: 0
      };
      
      this.suspiciousEvents = [];
      
      // Face tracking state with face switching detection
  this.faceDetectionState = {
  faceNotVisibleCount: 0,
  faceNotVisibleThreshold: 1, // Only 1 count needed since timing handled in component
  lookingAwayCount: 0,
  lookingAwayThreshold: 2,
  lastFaceCheck: null,
  // TIMING: Face detection timing thresholds
  FACE_NOT_VISIBLE_TIME_THRESHOLD: 10000, // 10 seconds
  LOOKING_AWAY_TIME_THRESHOLD: 15000, // 15 seconds
  // Face switching detection
  initialFaceDescriptor: null,
  faceDescriptorHistory: [],
  consecutiveFaceSwitches: 0,
  lastFaceSwitchTime: null,
  faceSwitchThreshold: 3  // 3 consecutive different faces = violation
};      
      // Keys currently pressed (for combo detection)
      this.keysPressed = new Set();
      
      // Track potential screenshot attempts
      this.potentialScreenshotAttempt = {
        inProgress: false,
        method: null,
        timestamp: null,
        timeoutId: null
      };
      
      // Track clipboard content for changes
      this.lastClipboardCheck = Date.now();
      this.lastImageCheckTime = Date.now();
      
      // Initialize immediately
      this.init();
    }

    init() {
      console.log('🛡️ Initializing Security Manager...');
      
      // Set up all security measures immediately
      this.setupFullscreenSecurity();
      this.setupTabSwitchDetection();
      this.setupDevToolsDetection();
      this.setupKeyboardBlocking();
      this.setupMouseAndContextBlocking();
      this.setupCopyPasteMonitoring(); // UPDATED: Now only monitors, doesn't block
      this.setupScreenSharingDetection();
      this.setupVoiceDetection();
      this.setupScreenshotDetection();
      this.setupPermissionMonitoring();
      this.setupImageCapture();
      
      // Setup event listener for custom screenshot detection events
      window.addEventListener('security-screenshot-attempt', (event) => {
        if (this.isInterviewActive && !this.exiting) {
          console.log('🚨 Screenshot attempt event received:', event.detail);
          this.handleWarningViolation('screenshot', 'Screenshot detected: ' + (event.detail.method || 'custom event'));
        }
      }, { capture: true });
      
      console.log('✅ Security Manager initialized');
    }

    setApplicationId(id) {
      this.applicationId = id;
      console.log(`Application ID set: ${id}`);
    }

    startInterview() {
      console.log('🚀 Starting interview monitoring immediately...');
      this.interviewStartTime = Date.now();
      this.isInterviewActive = true;
      
      console.log('✅ Interview monitoring active');
      
      this.dispatchEvent('interview-started', {
        applicationId: this.applicationId,
        timestamp: new Date().toISOString()
      });
    }

    // Flag to prevent violations during exit
    setExiting(exiting) {
      this.exiting = exiting;
      console.log(`Security manager exit flag: ${exiting}`);
    }

    // Monitor permission changes
    setupPermissionMonitoring() {
      try {
        // Watch for permission changes using Permissions API if available
        if (navigator.permissions) {
          // Monitor screen capture permission
          navigator.permissions.query({ name: 'camera' }).then(permissionStatus => {
            permissionStatus.onchange = () => {
              console.log(`📸 Camera permission status changed to: ${permissionStatus.state}`);
              
              // If we had a potential screenshot in progress, this might be the permission grant
              if (this.potentialScreenshotAttempt.inProgress) {
                console.log('🚨 Permission change detected during potential screenshot attempt');
                this.handleWarningViolation('screenshot', 
                  `Screenshot detected after permission change: ${this.potentialScreenshotAttempt.method}`);
                this.clearPotentialScreenshotAttempt();
              }
            };
          }).catch(err => {
            console.log('Permission query not supported for camera');
          });
          
          // Try to monitor screen capture permission if supported
          try {
            navigator.permissions.query({ name: 'display-capture' }).then(permissionStatus => {
              permissionStatus.onchange = () => {
                console.log(`🖥️ Screen capture permission status changed to: ${permissionStatus.state}`);
                
                // If screen capture permission was granted, this is likely a screenshot
                if (permissionStatus.state === 'granted') {
                  this.handleWarningViolation('screenshot', 'Screenshot detected: display-capture permission granted');
                }
              };
            }).catch(err => {
              console.log('Permission query not supported for display-capture');
            });
          } catch (error) {
            console.log('display-capture permission monitoring not supported');
          }
        }
      } catch (error) {
        console.error('Error setting up permission monitoring:', error);
      }
      
      console.log('✅ Permission monitoring initialized');
    }

    // Monitor image capture events
    setupImageCapture() {
      try {
        // Monitor clipboard for images (might indicate screenshots)
        let lastImageCheckTime = Date.now();
        
        // Set up a periodic check for clipboard content
        setInterval(async () => {
          // Skip if intensive monitoring is active or SecurityManager not ready
          if (!this.isInterviewActive || this.exiting) {
            return;
          }
          
          try {
            // Check clipboard for images
            if (navigator.clipboard && navigator.clipboard.read) {
              try {
                const items = await navigator.clipboard.read();
                
                for (const item of items) {
                  if (item.types.some(type => type.startsWith('image/'))) {
                    // Only report if no recent image check
                    if (Date.now() - this.lastImageCheckTime > 5000) {
                      console.log('🚨 Image found in clipboard during monitoring');
                      
                      this.handleWarningViolation('screenshot', 
                        'Screenshot detected: image found in clipboard');
                      
                      this.lastImageCheckTime = Date.now();
                      return;
                    }
                  }
                }
              } catch (e) {
                // Permission errors expected
              }
            }
          } catch (error) {
            // Ignore errors
          }
        }, 2000); // Check every 2 seconds
        
        // Monitor for paste events with images
        document.addEventListener('paste', (e) => {
          if (!this.isInterviewActive || this.exiting) return;
          
          if (e.clipboardData && e.clipboardData.items) {
            for (let i = 0; i < e.clipboardData.items.length; i++) {
              if (e.clipboardData.items[i].type.indexOf('image') !== -1) {
                console.log('🚨 Image pasted - possible screenshot');
                this.handleWarningViolation('screenshot', 'Screenshot detected: image pasted from clipboard');
                break;
              }
            }
          }
        }, { capture: true });
      } catch (error) {
        console.error('Error setting up image capture monitoring:', error);
      }
      
      console.log('✅ Image capture monitoring initialized');
    }

    // Track potential screenshot attempt with follow-up monitoring
    startPotentialScreenshotTracking(method) {
      // Clear any existing attempt first
      this.clearPotentialScreenshotAttempt();
      
      console.log(`🔍 Tracking potential screenshot attempt: ${method}`);
      
      this.potentialScreenshotAttempt = {
        inProgress: true,
        method: method,
        timestamp: Date.now(),
        // Auto-confirm screenshot after delay even without other evidence
        timeoutId: setTimeout(() => {
          if (this.potentialScreenshotAttempt.inProgress) {
            console.log('⏱️ Screenshot timeout reached - assuming screenshot was taken');
            this.handleWarningViolation('screenshot', `Screenshot detected: ${method} (timeout)`);
            this.clearPotentialScreenshotAttempt();
          }
        }, 500) // 0.5 second timeout (reduced from 2 seconds for faster response)
      };
      
      // Immediately look for fullscreen exit, which often happens with screenshots
      if (!this.isDocumentFullscreen()) {
        console.log('🚨 Fullscreen exit detected with screenshot attempt');
        this.handleWarningViolation('screenshot', 
          `Screenshot detected with fullscreen exit: ${method}`);
        this.clearPotentialScreenshotAttempt();
      }
      
      // Check clipboard for images
      this.checkClipboardForImages();
    }

    // Monitor clipboard for images
    async checkClipboardForImages() {
      try {
        if (!navigator.clipboard || !navigator.clipboard.read) return false;
        
        try {
          const items = await navigator.clipboard.read();
          
          for (const item of items) {
            if (item.types.some(type => type.startsWith('image/'))) {
              console.log('🚨 Image found in clipboard - screenshot confirmed');
              this.handleWarningViolation('screenshot', 'Screenshot confirmed: image in clipboard');
              return true;
            }
          }
        } catch (e) {
          // Permission errors expected
          console.log('Clipboard permission denied during check');
        }
      } catch (error) {
        console.log('Error checking clipboard for images');
      }
      
      return false;
    }

    clearPotentialScreenshotAttempt() {
      if (this.potentialScreenshotAttempt.timeoutId) {
        clearTimeout(this.potentialScreenshotAttempt.timeoutId);
      }
      
      this.potentialScreenshotAttempt = {
        inProgress: false,
        method: null,
        timestamp: null,
        timeoutId: null
      };
    }

    // ENHANCED SCREENSHOT DETECTION
    setupScreenshotDetection() {
      console.log('🔒 Setting up enhanced screenshot detection with reduced false positives...');
      
      // Set up debouncing to avoid duplicate detections
      let lastImageDetectionTime = Date.now();
      let lastKeyCombination = null;
      let lastKeyPressTime = Date.now();
      
      // Track key states
      let metaKeyPressed = false;
      let shiftKeyPressed = false;
      let number3Pressed = false;
      let number4Pressed = false;
      let number5Pressed = false;
      
      // APPROACH 1: KEYBOARD TRACKING WITH ACTION-BASED VERIFICATION
      const setupKeyboardTracking = () => {
        // Create sets to track key combinations
        let keysPressed = new Set();
        
        // Direct handling for screenshot key combinations
        const handleKeyDown = (e) => {
          // Only process if active
          if (!this.isInterviewActive || this.exiting) return;
          
          console.log(`Key down: ${e.key}, Meta: ${e.metaKey}, Shift: ${e.shiftKey}`);
          
          // Update key states
          if (e.key === 'Meta' || e.key === 'Command') metaKeyPressed = true;
          if (e.key === 'Shift') shiftKeyPressed = true;
          if (e.key === '3') number3Pressed = true;
          if (e.key === '4') number4Pressed = true;
          if (e.key === '5') number5Pressed = true;
          
          // Add to pressed keys
          keysPressed.add(e.key);
          if (e.code) keysPressed.add(e.code);
          
          // Add modifier keys
          if (e.metaKey || e.key === 'Meta') {
            keysPressed.add('Meta');
            keysPressed.add('Command');
          }
          if (e.shiftKey || e.key === 'Shift') keysPressed.add('Shift');
          if (e.altKey || e.key === 'Alt') keysPressed.add('Alt');
          if (e.ctrlKey || e.key === 'Control') keysPressed.add('Control');
          
          // Check for Mac screenshot combinations
          if (metaKeyPressed && shiftKeyPressed) {
            if (number3Pressed) {
              console.log('🚨 Detected Mac screenshot combination: Command+Shift+3');
              this.handleWarningViolation('screenshot', 'Screenshot detected: Command+Shift+3');
            }
            else if (number4Pressed) {
              console.log('🚨 Detected Mac screenshot combination: Command+Shift+4');
              this.handleWarningViolation('screenshot', 'Screenshot detected: Command+Shift+4');
            }
            else if (number5Pressed) {
              console.log('🚨 Detected Mac screenshot combination: Command+Shift+5');
              this.handleWarningViolation('screenshot', 'Screenshot detected: Command+Shift+5');
            }
          }
          
          // Direct check for Mac screenshot shortcuts
          if (e.metaKey && e.shiftKey && (e.key === '3' || e.key === '4' || e.key === '5')) {
            console.log(`🚨 Direct detection of Mac screenshot: Command+Shift+${e.key}`);
            this.handleWarningViolation('screenshot', `Screenshot detected: Command+Shift+${e.key}`);
          }
          
          // Check for Windows PrintScreen
          if (e.key === 'PrintScreen' || e.code === 'PrintScreen') {
            console.log('🚨 Detected Windows screenshot: PrintScreen');
            this.handleWarningViolation('screenshot', 'Screenshot detected: PrintScreen');
          }
          
          // Record the key combination and time for verification later
          if ((e.metaKey && e.shiftKey && ['3', '4', '5'].includes(e.key)) || 
              e.key === 'PrintScreen' || e.code === 'PrintScreen' ||
              (e.metaKey && e.shiftKey && (e.key === 's' || e.key === 'S'))) {
                
            let keyCombo = '';
            if (e.metaKey) keyCombo += 'Command+';
            if (e.shiftKey) keyCombo += 'Shift+';
            if (e.altKey) keyCombo += 'Alt+';
            keyCombo += e.key;
            
            lastKeyCombination = keyCombo;
            lastKeyPressTime = Date.now();
            
            console.log(`🔑 Potential screenshot key combination detected: ${keyCombo}`);
            
            // Instead of reporting immediately, we'll check for evidence after a short delay
            setTimeout(() => {
              checkForScreenshotEvidence(keyCombo);
            }, 300);
          }
        };
        
        // Reset keys on key up
        const handleKeyUp = (e) => {
          // Reset key states
          if (e.key === 'Meta' || e.key === 'Command') metaKeyPressed = false;
          if (e.key === 'Shift') shiftKeyPressed = false;
          if (e.key === '3') number3Pressed = false;
          if (e.key === '4') number4Pressed = false;
          if (e.key === '5') number5Pressed = false;
          
          keysPressed.delete(e.key);
          if (e.code) keysPressed.delete(e.code);
          
          // Reset modifier keys
          if (e.key === 'Meta' || e.key === 'Command') {
            keysPressed.delete('Meta');
            keysPressed.delete('Command');
          }
          if (e.key === 'Shift') keysPressed.delete('Shift');
          if (e.key === 'Alt') keysPressed.delete('Alt');
          if (e.key === 'Control') keysPressed.delete('Control');
        };
        
        // Add handlers at multiple levels for better interception
        // Document level with capture phase
        document.addEventListener('keydown', handleKeyDown.bind(this), { 
          capture: true, 
          passive: false 
        });
        document.addEventListener('keyup', handleKeyUp, { capture: true });
        
        // Window level with capture phase
        window.addEventListener('keydown', handleKeyDown.bind(this), { 
          capture: true, 
          passive: false 
        });
        window.addEventListener('keyup', handleKeyUp, { capture: true });
        
        // Body level with capture phase
        if (document.body) {
          document.body.addEventListener('keydown', handleKeyDown.bind(this), { 
            capture: true, 
            passive: false 
          });
          document.body.addEventListener('keyup', handleKeyUp, { capture: true });
        }
        
        // Root level element - last resort
        const rootElement = document.documentElement;
        rootElement.addEventListener('keydown', handleKeyDown.bind(this), { 
          capture: true, 
          passive: false
        });
      };
      
      // IMPROVED: Evidence-based verification of screenshots
      const checkForScreenshotEvidence = async (keyCombo) => {
        console.log(`🔍 Checking for evidence of screenshot after ${keyCombo}...`);
        
        // Check if we're still in active mode
        if (!this.isInterviewActive || this.exiting) {
          console.log('Not checking evidence - interview not active');
          return;
        }
        
        let evidenceFound = false;
        
        // 1. Check clipboard for new images
        if (navigator.clipboard && navigator.clipboard.read) {
          try {
            const items = await navigator.clipboard.read();
            
            for (const item of items) {
              if (item.types.some(type => type.startsWith('image/'))) {
                // This is an image in clipboard - potential screenshot!
                
                // Check if we've recently reported this image to avoid duplicates
                const now = Date.now();
                if (now - lastImageDetectionTime > 2000) {
                  console.log('🖼️ Found image in clipboard after key combo - confirming screenshot');
                  
                  // Only report if this happened shortly after key press
                  if (now - lastKeyPressTime < 2000) {
                    this.handleWarningViolation('screenshot', 
                      `Screenshot detected: ${keyCombo} (confirmed by clipboard image)`);
                    
                    lastImageDetectionTime = now;
                    evidenceFound = true;
                  }
                }
                break;
              }
            }
          } catch (error) {
            // Permissions or other errors expected
            console.log('Clipboard permission error during evidence check:', error.name);
          }
        }
        
        // 2. Check for visibility state changes
        // This is already handled by the visibility change listener
        
        // If no evidence found but it was a known screenshot combination, 
        // report with lower confidence
        if (!evidenceFound && keyCombo && lastKeyPressTime && 
            Date.now() - lastKeyPressTime < 1000) {
          console.log('📸 Reporting screenshot based on key combo alone (lower confidence)');
          this.handleWarningViolation('screenshot', `Screenshot detected: ${keyCombo} (key combination)`);
        }
      };
      
      // APPROACH 2: RESULT-BASED DETECTION WITH IMPROVED DEBOUNCING
      const setupClipboardMonitoring = () => {
        // Function to check clipboard for images with debouncing
        const checkClipboard = async () => {
          // Skip if not in active mode
          if (!this.isInterviewActive || this.exiting) return false;
          
          try {
            if (navigator.clipboard && navigator.clipboard.read) {
              const items = await navigator.clipboard.read();
              
              let foundImage = false;
              
              for (const item of items) {
                if (item.types.some(type => type.startsWith('image/'))) {
                  foundImage = true;
                  
                  // Only report if enough time has passed since last detection
                  // This avoids duplicate reports for the same screenshot
                  const now = Date.now();
                  if (now - lastImageDetectionTime > 3000) {
                    console.log('🚨 New image found in clipboard during monitoring');
                    
                    // Check if there's been a recent key combination
                    if (lastKeyCombination && now - lastKeyPressTime < 2000) {
                      this.handleWarningViolation('screenshot', 
                        `Screenshot detected: ${lastKeyCombination} (confirmed by clipboard)`);
                    } else {
                      // If no recent key combo, report as generic detection
                      this.handleWarningViolation('screenshot', 'Screenshot detected: new image in clipboard');
                    }
                    
                    // Update timestamp to avoid duplicate reports
                    lastImageDetectionTime = now;
                    return true;
                  }
                  break;
                }
              }
              
              // If we found an image but didn't report it (due to debounce), 
              // still return true for tracking
              if (foundImage) return true;
            }
          } catch (error) {
            // Permission errors are expected
            // console.log('Clipboard permission error:', error.name);
          }
          
          return false;
        };
        
        // Check clipboard periodically, but not too frequently
        // This is a fallback for cases where key combos aren't caught
        const interval = setInterval(() => {
          if (!this.isInterviewActive || this.exiting) return;
          
          // Only check clipboard if no recent detection
          if (Date.now() - lastImageDetectionTime > 5000) {
            checkClipboard();
          }
        }, 5000); // Check every 5 seconds instead of 1 second
        
        return interval;
      };
      
      // APPROACH 3: VISIBILITY CHANGE MONITORING WITH VALIDATION
      const setupVisibilityMonitoring = () => {
        let lastVisibilityChange = Date.now();
        
        const handleVisibilityChange = () => {
          const now = Date.now();
          
          if (document.visibilityState === 'hidden') {
            lastVisibilityChange = now;
          } else if (document.visibilityState === 'visible') {
            const timeDiff = now - lastVisibilityChange;
            
            // Very brief visibility changes often indicate screenshots
            // But only if they're in a specific time range
            if (timeDiff > 5 && timeDiff < 300 && this.isInterviewActive && !this.exiting) {
              console.log(`⚡ Brief visibility change (${timeDiff}ms) - possible screenshot`);
              
              // Verify with clipboard check after small delay
              setTimeout(() => {
                // Only report if we haven't reported recently
                if (now - lastImageDetectionTime > 2000) {
                  // Check clipboard for confirmation
                  checkForScreenshotEvidence(`visibility change (${timeDiff}ms)`);
                }
              }, 300);
            }
          }
        };
        
        document.addEventListener('visibilitychange', handleVisibilityChange, { passive: true });
      };
      
      // APPROACH 4: PASTE EVENT MONITORING
      const setupPasteMonitoring = () => {
        const handlePaste = (e) => {
          if (!this.isInterviewActive || this.exiting) return;
          
          if (e.clipboardData && e.clipboardData.items) {
            for (let i = 0; i < e.clipboardData.items.length; i++) {
              if (e.clipboardData.items[i].type.indexOf('image') !== -1) {
                // Only report if no recent detection (debounce)
                if (Date.now() - lastImageDetectionTime > 2000) {
                  console.log('📋 Image pasted - likely from screenshot');
                  this.handleWarningViolation('screenshot', 'Screenshot detected: image pasted from clipboard');
                  lastImageDetectionTime = Date.now();
                }
                break;
              }
            }
          }
        };
        
        document.addEventListener('paste', handlePaste, { capture: true });
      };
      
      // Initialize all approaches
      setupKeyboardTracking.call(this);
      setupClipboardMonitoring.call(this);
      setupVisibilityMonitoring.call(this);
      setupPasteMonitoring.call(this);
      
      console.log('✅ Enhanced screenshot detection with reduced false positives initialized');
    }

    // SCREEN SHARING DETECTION
    setupScreenSharingDetection() {
      // Store initial screen configuration
      let initialScreenConfig = null;
      let detectionStarted = false;
      
      // Check for screen sharing every 5 seconds
      setInterval(() => {
        if (!this.isInterviewActive || !this.isEnforcementActive()) return;
        
        // Initialize screen config on first run
        if (!initialScreenConfig && !detectionStarted) {
          initialScreenConfig = {
            width: window.screen.width,
            height: window.screen.height,
            availWidth: window.screen.availWidth,
            availHeight: window.screen.availHeight
          };
          detectionStarted = true;
          console.log('Initial screen config:', initialScreenConfig);
          return;
        }
        
        // More sophisticated dual monitor detection
        if (initialScreenConfig) {
          const currentWidth = window.screen.width;
          const currentHeight = window.screen.height;
          
          // Only flag significant changes (>200 pixels)
          const widthDiff = Math.abs(currentWidth - initialScreenConfig.width);
          const heightDiff = Math.abs(currentHeight - initialScreenConfig.height);
          
          if (widthDiff > 200 || heightDiff > 200) {
            this.handleWarningViolation('dualMonitor', 
              `Screen configuration changed: ${currentWidth}x${currentHeight} (was ${initialScreenConfig.width}x${initialScreenConfig.height})`);
          }
        }
      }, 5000);
    }

    // VOICE DETECTION - Simplified and more reliable
    setupVoiceDetection() {
      console.log('🎤 Voice detection setup (simplified)');
      // Voice detection is temporarily disabled to prevent false positives
      // This can be re-enabled with more sophisticated filtering later
    }

    // ENHANCED: Face detection handler with hand detection integration
    handleFaceDetection(faceData) {
      if (!this.isInterviewActive || !this.isEnforcementActive()) return;
      
      console.log('👁️ Face detection data received:', faceData);
      
      const now = Date.now();
      this.faceDetectionState.lastFaceCheck = now;
      
      // No face visible
      if (faceData.facesDetected === 0) {
        this.faceDetectionState.faceNotVisibleCount++;
        
        if (this.faceDetectionState.faceNotVisibleCount >= this.faceDetectionState.faceNotVisibleThreshold) {
          this.handleWarningViolation('faceNotVisible', 'Face not visible to camera');
          this.faceDetectionState.faceNotVisibleCount = 0;
        }
      } else {
        this.faceDetectionState.faceNotVisibleCount = 0;
      }
      
      // Multiple faces
      if (faceData.facesDetected > 1) {
        this.handleWarningViolation('multipleFaces', `${faceData.facesDetected} faces detected`);
      }
      
      // Looking away
      if (faceData.lookingAway) {
        this.faceDetectionState.lookingAwayCount++;
        
        if (this.faceDetectionState.lookingAwayCount >= this.faceDetectionState.lookingAwayThreshold) {
          this.handleWarningViolation('lookingAway', 'Looking away from camera');
          this.faceDetectionState.lookingAwayCount = 0;
        }
      } else {
        this.faceDetectionState.lookingAwayCount = 0;
      }
      
      // ENHANCED: Face switching detection with descriptor comparison
      if (faceData.facesDetected === 1 && faceData.faceDescriptor) {
        this.detectFaceSwitch(faceData.faceDescriptor, faceData.faceConfidence);
      }
      
      // Different person (legacy - now handled by face switching)
      if (faceData.differentPerson) {
        this.handleWarningViolation('differentPerson', 'Different person detected');
      }
      
      // NEW: Hand detection violations
      this.handleHandDetection(faceData);
    }

    // NEW: Handle hand detection violations
    handleHandDetection(data) {
      try {
        // Check for hands raised violation
        if (data.handsRaised && data.consecutiveHandsRaised >= 3) {
          this.handleWarningViolation('handsRaised', 
            `Hands raised for ${data.consecutiveHandsRaised} consecutive detections - possible photo taking`);
        }
        
        // Check for hands out of frame violation
        if (data.consecutiveHandsOutOfFrame >= 5) {
          this.handleWarningViolation('handsOutOfFrame', 
            `Hands not visible for ${data.consecutiveHandsOutOfFrame} detections - suspicious activity`);
        }
        
        // Check for suspicious hand gestures
        if (data.suspiciousHandGesture) {
          this.handleWarningViolation('suspiciousHandGesture', 
            'Hand gesture consistent with holding a device (phone/camera)');
        }
        
        console.log('👋 Hand detection processed:', {
          handsDetected: data.handsDetected,
          handsRaised: data.handsRaised,
          suspiciousGesture: data.suspiciousHandGesture,
          consecutiveRaised: data.consecutiveHandsRaised,
          consecutiveOutOfFrame: data.consecutiveHandsOutOfFrame
        });
        
      } catch (error) {
        console.error('Error handling hand detection:', error);
      }
    }

    // NEW: Face switching detection using face descriptors
    detectFaceSwitch(currentDescriptor, confidence) {
      try {
        // Only process if we have good confidence
        if (confidence < 0.7) {
          console.log('👁️ Face confidence too low for switching detection:', confidence);
          return;
        }
        
        // Store initial face if this is the first good detection
        if (!this.faceDetectionState.initialFaceDescriptor) {
          this.faceDetectionState.initialFaceDescriptor = currentDescriptor;
          this.faceDetectionState.faceDescriptorHistory = [currentDescriptor];
          console.log('👁️ Initial face descriptor stored');
          return;
        }
        
        // Compare with initial face descriptor
        const similarity = this.compareFaceDescriptors(
          this.faceDetectionState.initialFaceDescriptor, 
          currentDescriptor
        );
        
        console.log('👁️ Face similarity to initial:', similarity);
        
        // If similarity is below threshold, this might be a different person
        const SIMILARITY_THRESHOLD = 0.6; // Adjust this value (0.6 = 60% similarity required)
        
        if (similarity < SIMILARITY_THRESHOLD) {
          this.faceDetectionState.consecutiveFaceSwitches++;
          
          console.log(`👁️ Potential face switch detected (${this.faceDetectionState.consecutiveFaceSwitches}/${this.faceDetectionState.faceSwitchThreshold})`);
          
          // If we have multiple consecutive face switches, it's likely a different person
          if (this.faceDetectionState.consecutiveFaceSwitches >= this.faceDetectionState.faceSwitchThreshold) {
            const now = Date.now();
            
            // Don't spam violations - only report once every 10 seconds
            if (!this.faceDetectionState.lastFaceSwitchTime || 
                (now - this.faceDetectionState.lastFaceSwitchTime) > 10000) {
              
              this.handleWarningViolation('faceSwitch', 
                `Face switching detected - different person may be taking the interview`);
              
              this.faceDetectionState.lastFaceSwitchTime = now;
            }
            
            // Reset counter after reporting
            this.faceDetectionState.consecutiveFaceSwitches = 0;
          }
        } else {
          // Good match - reset the switch counter
          this.faceDetectionState.consecutiveFaceSwitches = 0;
        }
        
        // Keep a rolling history of recent face descriptors (last 10)
        this.faceDetectionState.faceDescriptorHistory.push(currentDescriptor);
        if (this.faceDetectionState.faceDescriptorHistory.length > 10) {
          this.faceDetectionState.faceDescriptorHistory.shift();
        }
        
      } catch (error) {
        console.error('Error in face switch detection:', error);
      }
    }

    // NEW: Compare two face descriptors using cosine similarity
    compareFaceDescriptors(descriptor1, descriptor2) {
      try {
        // Handle both array and object formats
        let vec1, vec2;
        
        if (Array.isArray(descriptor1)) {
          vec1 = descriptor1;
        } else if (descriptor1.descriptor && Array.isArray(descriptor1.descriptor)) {
          vec1 = descriptor1.descriptor;
        } else {
          console.warn('Invalid face descriptor format 1');
          return 0.5; // Neutral similarity
        }
        
        if (Array.isArray(descriptor2)) {
          vec2 = descriptor2;
        } else if (descriptor2.descriptor && Array.isArray(descriptor2.descriptor)) {
          vec2 = descriptor2.descriptor;
        } else {
          console.warn('Invalid face descriptor format 2');
          return 0.5; // Neutral similarity
        }
        
        // Ensure both vectors have the same length
        if (vec1.length !== vec2.length) {
          console.warn('Face descriptor length mismatch:', vec1.length, 'vs', vec2.length);
          return 0.5; // Neutral similarity
        }
        
        // Calculate cosine similarity
        let dotProduct = 0;
        let norm1 = 0;
        let norm2 = 0;
        
        for (let i = 0; i < vec1.length; i++) {
          dotProduct += vec1[i] * vec2[i];
          norm1 += vec1[i] * vec1[i];
          norm2 += vec2[i] * vec2[i];
        }
        
        norm1 = Math.sqrt(norm1);
        norm2 = Math.sqrt(norm2);
        
        if (norm1 === 0 || norm2 === 0) {
          return 0; // No similarity if either vector is zero
        }
        
        const similarity = dotProduct / (norm1 * norm2);
        
        // Convert from [-1, 1] to [0, 1] range
        return (similarity + 1) / 2;
        
      } catch (error) {
        console.error('Error comparing face descriptors:', error);
        return 0.5; // Neutral similarity on error
      }
    }

    // FULLSCREEN SECURITY - Fixed to handle exit properly
    setupFullscreenSecurity() {
      const fullscreenEvents = [
        'fullscreenchange',
        'webkitfullscreenchange',
        'mozfullscreenchange',
        'MSFullscreenChange'
      ];

      fullscreenEvents.forEach(event => {
        document.addEventListener(event, () => {
          // Don't flag violations during exit
          if (this.exiting) {
            console.log('Fullscreen change during exit - not flagging');
            return;
          }
          
          if (!this.isDocumentFullscreen() && this.isInterviewActive && this.isEnforcementActive()) {
            this.handleWarningViolation('fullscreenExit', 'Exited fullscreen mode');
            
            // Check if there's a potential screenshot in progress
            if (this.potentialScreenshotAttempt.inProgress) {
              console.log('🚨 Fullscreen exit detected during potential screenshot attempt');
              
              // This strongly confirms a screenshot was taken
              this.handleWarningViolation('screenshot', 
                `Screenshot confirmed: fullscreen exit during ${this.potentialScreenshotAttempt.method}`);
              
              this.clearPotentialScreenshotAttempt();
            }
          }
        }, { passive: true });
      });

      // COMPLETELY FIXED: Block ESC key at document level before other handlers
      const blockEscape = (e) => {
        if (e.key === 'Escape') {
          console.log('🚫 Escape key blocked by SecurityManager');
          e.preventDefault();
          e.stopPropagation();
          e.stopImmediatePropagation();
          
          // Only flag if interview is active and enforcement is active
          if (this.isInterviewActive && this.isEnforcementActive()) {
            this.handleWarningViolation('escapeKey', 'Attempted to press Escape key');
          }
          return false;
        }
      };
      
      // Add listener with capture=true to catch escape before any other handlers
      document.addEventListener('keydown', blockEscape, { 
        capture: true, 
        passive: false 
      });
      
      // Also add at window level as fallback
      window.addEventListener('keydown', blockEscape, { 
        capture: true, 
        passive: false 
      });
    }

    // TAB SWITCH DETECTION
    setupTabSwitchDetection() {
      let tabSwitchTimeout;
      let lastVisibilityState = document.visibilityState;
      
      // Track key combinations that indicate tab switching
      const tabSwitchKeys = new Set();
      
      // CRITICAL CONFIGURATION: COMPLETELY DISABLE WINDOW BLUR DETECTION
      // This variable completely prevents window blur violations if true
      const DISABLE_WINDOW_BLUR = true;
      
      // Primary: Visibility change detection - focus on tab switch
      document.addEventListener('visibilitychange', () => {
        if (!this.isInterviewActive || !this.isEnforcementActive() || this.exiting) return;
        
        // Only process if this is a genuine change
        if (document.visibilityState !== lastVisibilityState) {
          lastVisibilityState = document.visibilityState;
          
          if (document.hidden) {
            console.log('🚨 Document hidden - tab switch detected');
            
            // Clear any existing timeout
            if (tabSwitchTimeout) clearTimeout(tabSwitchTimeout);
            
            // Shorter timeout for faster detection
            tabSwitchTimeout = setTimeout(() => {
              console.log('⚠️ Tab switch confirmed (visibility)');
              this.handleWarningViolation('tabSwitch', 'Switched to another tab');
            }, 300);
          } else {
            // Document became visible again
            if (tabSwitchTimeout) {
              clearTimeout(tabSwitchTimeout);
              tabSwitchTimeout = null;
            }
          }
        }
      }, { capture: true, passive: true });

      // Window blur detection - COMPLETELY DISABLED to prevent false positives
      window.addEventListener('blur', (e) => {
        if (!this.isInterviewActive || !this.isEnforcementActive() || this.exiting) return;
        
        // Check if tab switching keys were pressed
        const isTabSwitchKeyCombo = tabSwitchKeys.has('Control+Tab') || 
                                   tabSwitchKeys.has('Alt+Tab') ||
                                   tabSwitchKeys.has('Command+Tab');
        
        if (isTabSwitchKeyCombo) {
          console.log('⚠️ Tab switching key combination detected during blur');
          this.handleWarningViolation('tabSwitch', 'Switched tabs using keyboard shortcut');
          tabSwitchKeys.clear();
          return;
        }
        
        // DISABLED: Window blur violations
        if (DISABLE_WINDOW_BLUR) {
          console.log('Window blur detected but reporting disabled to prevent false positives');
          return;
        }
      }, { capture: true, passive: true });
      
      // Tertiary: Key combination detection - enhanced for better tab switch detection
      window.addEventListener('keydown', (e) => {
        if (!this.isInterviewActive || !this.isEnforcementActive() || this.exiting) return;
        
        // Enhanced detection logic for tab switching key combinations
        // Watch for Command+Tab or Ctrl+Tab
        if ((e.metaKey || e.ctrlKey) && (e.key === 'Tab' || e.keyCode === 9)) {
          console.log('⚠️ Tab switching key combination detected: ' + (e.metaKey ? 'Command+Tab' : 'Control+Tab'));
          
          // Store the combination
          tabSwitchKeys.add(e.metaKey ? 'Command+Tab' : 'Control+Tab');
          
          // Try to prevent default behavior
          try {
            e.preventDefault();
            e.stopPropagation();
          } catch (error) {
            console.log('Could not prevent key event (expected for system shortcuts)');
          }
          
          // Report immediately - don't wait for blur event
          this.handleWarningViolation('tabSwitch', 
            `Attempted to switch tabs using ${e.metaKey ? 'Command+Tab' : 'Control+Tab'}`);
          
          return false;
        }
        
        // Watch for Alt+Tab (Windows task switching)
        if (e.altKey && (e.key === 'Tab' || e.keyCode === 9)) {
          console.log('⚠️ Alt+Tab detected');
          
          tabSwitchKeys.add('Alt+Tab');
          
          try {
            e.preventDefault();
            e.stopPropagation();
          } catch (error) {
            console.log('Could not prevent key event (expected for system shortcuts)');
          }
          
          this.handleWarningViolation('tabSwitch', 'Attempted to switch tasks using Alt+Tab');
          return false;
        }
      }, { capture: true, passive: false });
      
      // Clear tab switch keys on keyup
      window.addEventListener('keyup', (e) => {
        if ((e.key === 'Control' || e.key === 'Meta' || e.key === 'Alt' || e.key === 'Tab') ||
            (e.keyCode === 17 || e.keyCode === 91 || e.keyCode === 18 || e.keyCode === 9)) {
          tabSwitchKeys.clear();
        }
      }, { capture: true, passive: true });
    }

    // DEVELOPER TOOLS DETECTION
    setupDevToolsDetection() {
      let devToolsOpen = false;
      
      // Method 1: Window size inspection
      const checkWindowSize = () => {
        if (!this.isInterviewActive || !this.isEnforcementActive()) return;
        
        const threshold = 200;
        if (window.outerHeight - window.innerHeight > threshold || 
            window.outerWidth - window.innerWidth > threshold) {
          if (!devToolsOpen) {
            devToolsOpen = true;
            this.handleWarningViolation('devTools', 'Developer tools opened');
          }
        } else {
          devToolsOpen = false;
        }
      };
      
      // Check every 3 seconds
      setInterval(() => {
        checkWindowSize();
      }, 3000);
    }

    // KEYBOARD BLOCKING - Fixed to properly block all shortcuts
    setupKeyboardBlocking() {
      document.addEventListener('keydown', (e) => {
        if (!this.isInterviewActive || !this.isEnforcementActive()) return;
        
        // List of blocked key combinations
        const blockedCombinations = [
          // Developer tools
          { key: 'F12' },
          { key: 'I', ctrl: true, shift: true },
          { key: 'C', ctrl: true, shift: true },
          { key: 'J', ctrl: true, shift: true },
          // Navigation
          { key: 'Tab', alt: true },
          { key: 'Tab', ctrl: true },
          { key: 'w', ctrl: true },
          { key: 'W', ctrl: true },
          { key: 'n', ctrl: true },
          { key: 'N', ctrl: true },
          { key: 't', ctrl: true },
          { key: 'T', ctrl: true },
          // Refresh
          { key: 'r', ctrl: true },
          { key: 'R', ctrl: true },
          { key: 'F5' },
          // Other
          { key: 'F4', alt: true },
          { key: 'F11' }
        ];
        
        const isBlocked = blockedCombinations.some(combo => {
          if (combo.key === e.key) {
            if (combo.ctrl && combo.shift) {
              return e.ctrlKey && e.shiftKey;
            } else if (combo.ctrl) {
              return e.ctrlKey;
            } else if (combo.alt) {
              return e.altKey;
            } else if (combo.shift) {
              return e.shiftKey;
            }
            return true;
          }
          return false;
        });
        
        if (isBlocked) {
          e.preventDefault();
          e.stopPropagation();
          e.stopImmediatePropagation();
          this.handleWarningViolation('keyboardShortcut', `Blocked shortcut: ${e.key}`);
          return false;
        }
      }, { capture: true, passive: false });
    }

    // MOUSE AND CONTEXT MENU BLOCKING
    setupMouseAndContextBlocking() {
      document.addEventListener('contextmenu', (e) => {
        if (!this.isInterviewActive || !this.isEnforcementActive()) return;
        
        // Allow right-click in specific areas
        const allowedSelectors = [
          '.monaco-editor',
          'textarea',
          'input[type="text"]',
          '[data-allow-select]'
        ];
        
        const isAllowed = allowedSelectors.some(selector => {
          return e.target.closest(selector) !== null;
        });
        
        if (!isAllowed) {
          e.preventDefault();
          e.stopPropagation();
          this.handleWarningViolation('rightClick', 'Right-click attempted');
          return false;
        }
      }, true);
    }

    // UPDATED: COPY PASTE MONITORING (INFO ONLY - NO BLOCKING)
    setupCopyPasteMonitoring() {
      console.log('📋 Setting up copy/paste monitoring (info only)...');
      
      ['copy', 'cut', 'paste'].forEach(eventType => {
        document.addEventListener(eventType, (e) => {
          if (!this.isInterviewActive || this.exiting) return;
          
          // Check if this is in an allowed area
          const allowedSelectors = [
            '.monaco-editor',
            'textarea[data-allow-clipboard]',
            'input[data-allow-clipboard]',
            '[data-allow-clipboard]'
          ];
          
          const isAllowed = allowedSelectors.some(selector => {
            return e.target.closest(selector) !== null;
          });
          
          if (!isAllowed) {
            console.log(`📋 ${eventType} operation detected (monitoring only)`);
            
            // Handle as info-only violation (doesn't count towards disqualification)
            this.handleInfoOnlyViolation(eventType, `${eventType} operation detected`);
            
            // DON'T prevent the event - let the main app handle blocking
            // e.preventDefault();
            // e.stopPropagation();
          }
        }, { capture: true, passive: true }); // Changed to passive: true since we're not preventing
      });
      
      // Monitor keyboard shortcuts for copy/paste (info only)
      document.addEventListener('keydown', (e) => {
        if (!this.isInterviewActive || this.exiting) return;
        
        // Check for copy/paste keyboard shortcuts
        if ((e.ctrlKey || e.metaKey) && ['c', 'v', 'x', 'a'].includes(e.key.toLowerCase())) {
          // Check if this is in an allowed area
          const allowedSelectors = [
            '.monaco-editor',
            'textarea[data-allow-clipboard]',
            'input[data-allow-clipboard]',
            '[data-allow-clipboard]'
          ];
          
          const isAllowed = allowedSelectors.some(selector => {
            return e.target.closest(selector) !== null;
          });
          
          if (!isAllowed) {
            const actionMap = {
              'c': 'copy',
              'v': 'paste',
              'x': 'cut',
              'a': 'select all'
            };
            
            console.log(`📋 ${actionMap[e.key.toLowerCase()]} keyboard shortcut detected (monitoring only)`);
            
            // Handle as info-only violation
            this.handleInfoOnlyViolation(actionMap[e.key.toLowerCase()], 
              `${actionMap[e.key.toLowerCase()]} keyboard shortcut detected`);
            
            // DON'T prevent the event - let the main app handle blocking
          }
        }
      }, { capture: true, passive: true }); // Changed to passive: true
      
      console.log('✅ Copy/paste monitoring (info only) initialized');
    }

    // NEW: Handle info-only violations (show in logs but don't count towards disqualification)
    handleInfoOnlyViolation(type, message) {
      const timestamp = new Date().toISOString();
      
      console.log(`📋 Info-Only Violation: ${type} - ${message}`);
      
      // Increment info-only violation count
      this.infoOnlyViolations[type] = (this.infoOnlyViolations[type] || 0) + 1;
      
      // Add to suspicious events with special category
      this.suspiciousEvents.push({
        type,
        message,
        timestamp,
        category: 'info-only'
      });
      
      // Dispatch info-only violation event (for logging/tracking purposes)
      this.dispatchEvent('info-only-violation', {
        type,
        message,
        count: this.infoOnlyViolations[type],
        timestamp,
        category: 'info-only'
      });
    }

    // UPDATED: Enhanced violation handler with consistent 5-warning system
    handleWarningViolation(type, message) {
      // Always log violations, even during grace period
      console.warn(`🚨 Warning Violation: ${type} - ${message}`);
      
      const timestamp = new Date().toISOString();
      
      // Quick exit check
      if (this.exiting) {
        console.log(`Ignoring violation during exit: ${type}`);
        return;
      }
      
      // Grace period check for non-critical violations (but no exceptions now - all follow same pattern)
      if (!this.isEnforcementActive()) {
        console.log(`Grace period active - not counting violation: ${type}`);
        
        // Still dispatch event during grace period for tracking
        this.dispatchEvent('security-violation', {
          type,
          message,
          count: 0,
          totalViolations: 0,
          timestamp,
          category: 'warning',
          gracePeriod: true
        });
        
        return;
      }
      
      // Increment violation count
      this.warningViolations[type] = (this.warningViolations[type] || 0) + 1;
      
      // Add to suspicious events
      this.suspiciousEvents.push({
        type,
        message,
        timestamp,
        category: 'warning',
        priority: 'normal' // All violations now have normal priority
      });
      
      // Calculate total violations
      const totalViolations = Object.values(this.warningViolations).reduce((a, b) => a + b, 0);
      
      // Get threshold for this violation type (all are now 5)
      const threshold = this.VIOLATION_THRESHOLDS[type] || this.MAX_WARNINGS;
      
      console.log(`📊 Violation count: ${type} = ${this.warningViolations[type]}/${threshold}, Total = ${totalViolations}/${this.MAX_WARNINGS}`);
      
      // Dispatch security violation event
      this.dispatchEvent('security-violation', {
        type,
        message,
        count: this.warningViolations[type],
        totalViolations,
        timestamp,
        category: 'warning',
        priority: 'normal'
      });
      
      // For screenshots, also dispatch a screenshot-specific event
      if (type === 'screenshot') {
        this.dispatchEvent('security-screenshot-attempt', {
          method: message.replace('Screenshot detected: ', ''),
          timestamp
        });
      }
      
      // Check for final warning (at 5 total violations)
      if (totalViolations === this.MAX_WARNINGS) {
        this.dispatchEvent('final-warning', {
          message: 'FINAL WARNING: One more violation will result in disqualification!',
          timestamp
        });
      }
      
      // UPDATED: Check for disqualification (at 6 total violations OR if any single type exceeds threshold)
      if (totalViolations > this.MAX_WARNINGS || this.warningViolations[type] > threshold) {
        this.disqualifyUser(`Maximum violations reached: ${totalViolations} total violations (limit: ${this.MAX_WARNINGS})`);
        return;
      }
    }

    handleAnalyticsViolation(type, message) {
      const timestamp = new Date().toISOString();
      
      // Increment analytics violation count
      this.analyticsOnlyViolations[type] = (this.analyticsOnlyViolations[type] || 0) + 1;
      
      // Add to suspicious events
      this.suspiciousEvents.push({
        type,
        message,
        timestamp,
        category: 'analytics'
      });
      
      console.log(`📊 Analytics Violation: ${type} - ${message}`);
      
      // Dispatch analytics violation event
      this.dispatchEvent('analytics-violation', {
        type,
        message,
        count: this.analyticsOnlyViolations[type],
        timestamp,
        category: 'analytics'
      });
    }

    // DISQUALIFICATION
    disqualifyUser(reason = 'Maximum violations exceeded') {
      console.error('🚫 DISQUALIFYING USER - ' + reason);
      
      this.isInterviewActive = false;
      this.exiting = true; // Prevent further violations
      
      this.dispatchEvent('interview-disqualified', {
        reason,
        warningViolations: { ...this.warningViolations },
        analyticsViolations: { ...this.analyticsOnlyViolations },
        infoOnlyViolations: { ...this.infoOnlyViolations }, // Include info-only violations in summary
        totalWarningViolations: Object.values(this.warningViolations).reduce((a, b) => a + b, 0),
        suspiciousEvents: [...this.suspiciousEvents],
        timestamp: new Date().toISOString()
      });
    }

    // UTILITY METHODS
    isEnforcementActive() {
      if (!this.interviewStartTime) return false;
      return (Date.now() - this.interviewStartTime) > this.startDelay;
    }

    isDocumentFullscreen() {
      return !!(
        document.fullscreenElement ||
        document.mozFullScreenElement ||
        document.webkitFullscreenElement ||
        document.msFullscreenElement
      );
    }

    dispatchEvent(eventName, detail) {
      try {
        const event = new CustomEvent(eventName, { detail });
        window.dispatchEvent(event);
      } catch (error) {
        console.error('Error dispatching event:', error);
      }
    }

    getViolationSummary() {
      return {
        warningViolations: { ...this.warningViolations },
        analyticsViolations: { ...this.analyticsOnlyViolations },
        infoOnlyViolations: { ...this.infoOnlyViolations }, // Include info-only violations
        totalWarningViolations: Object.values(this.warningViolations).reduce((a, b) => a + b, 0),
        totalAnalyticsViolations: Object.values(this.analyticsOnlyViolations).reduce((a, b) => a + b, 0),
        totalInfoOnlyViolations: Object.values(this.infoOnlyViolations).reduce((a, b) => a + b, 0),
        suspiciousEvents: [...this.suspiciousEvents],
        applicationId: this.applicationId
      };
    }

endInterview() {
      this.isInterviewActive = false;
      this.exiting = true; // Prevent violations during cleanup
      console.log('🔚 Interview ended');
      
      this.dispatchEvent('interview-ended', {
        applicationId: this.applicationId,
        timestamp: new Date().toISOString(),
        warningViolations: { ...this.warningViolations },
        analyticsViolations: { ...this.analyticsOnlyViolations },
        infoOnlyViolations: { ...this.infoOnlyViolations }
      });
    }

    // ADD: Simple fullscreen enforcement function
    enforceFullscreenModeSimple() {
      console.log('🔒 Enforcing fullscreen mode (simplified)...');
      
      // Remove any existing blocker first
      const existingBlocker = document.getElementById('fullscreen-blocker-simple');
      if (existingBlocker) {
        existingBlocker.remove();
      }
      
      const blocker = document.createElement('div');
      blocker.id = 'fullscreen-blocker-simple';
      blocker.style.cssText = `
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background: rgba(255,0,0,0.95) !important;
        color: white !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        z-index: 2147483647 !important;
        font-size: 24px !important;
        text-align: center !important;
        pointer-events: all !important;
      `;
      
      blocker.innerHTML = `
        <div style="font-size: 32px; margin-bottom: 20px;">⚠️ FULLSCREEN REQUIRED ⚠️</div>
        <div style="font-size: 18px; margin: 20px 0; max-width: 80%;">
          You must be in fullscreen mode to continue the interview.<br/>
          Click the button below to enter fullscreen.
        </div>
        <button id="enable-fullscreen-simple-btn" style="
          font-size: 18px !important;
          padding: 15px 30px !important;
          background: white !important;
          color: red !important;
          border: none !important;
          border-radius: 8px !important;
          cursor: pointer !important;
          font-weight: bold !important;
          pointer-events: all !important;
        ">Enable Fullscreen Mode</button>
        <div style="font-size: 14px; margin-top: 20px; color: #ffcccc;">
          The interview is paused until you enter fullscreen
        </div>
      `;
      
      document.body.appendChild(blocker);
      
      // Add click handler with proper error handling
      const button = document.getElementById('enable-fullscreen-simple-btn');
      if (button) {
        button.addEventListener('click', async () => {
          try {
            console.log('🖥️ Fullscreen button clicked');
            const elem = document.documentElement;
            
            if (elem.requestFullscreen) {
              await elem.requestFullscreen();
            } else if (elem.mozRequestFullScreen) {
              await elem.mozRequestFullScreen();
            } else if (elem.webkitRequestFullscreen) {
              await elem.webkitRequestFullscreen();
            } else if (elem.msRequestFullscreen) {
              await elem.msRequestFullscreen();
            } else {
              throw new Error('Fullscreen API not supported');
            }
            
            console.log('✅ Fullscreen request sent');
          } catch (error) {
            console.error('❌ Fullscreen request failed:', error);
            
            // Show fallback instructions
            button.innerHTML = 'Press F11 or use browser fullscreen';
            button.style.background = '#ff9500';
            
            setTimeout(() => {
              button.innerHTML = 'Try Fullscreen Again';
              button.style.background = 'white';
            }, 3000);
          }
        });
      }
      
      // Remove blocker when fullscreen is achieved
      const checkAndRemoveBlocker = () => {
        if (this.isDocumentFullscreen()) {
          const currentBlocker = document.getElementById('fullscreen-blocker-simple');
          if (currentBlocker) {
            console.log('✅ Fullscreen achieved, removing blocker');
            currentBlocker.remove();
          }
        }
      };
      
      // Listen for fullscreen changes
      const fullscreenEvents = [
        'fullscreenchange',
        'webkitfullscreenchange', 
        'mozfullscreenchange',
        'MSFullscreenChange'
      ];
      
      fullscreenEvents.forEach(event => {
        document.addEventListener(event, checkAndRemoveBlocker);
      });
      
      console.log('✅ Simple fullscreen blocker created');
    }

    // ADD: Simple fullscreen request function
    requestFullscreenSimple() {
      return new Promise(async (resolve) => {
        try {
          console.log('🖥️ Simple fullscreen request...');
          const elem = document.documentElement;
          
          if (elem.requestFullscreen) {
            await elem.requestFullscreen();
          } else if (elem.mozRequestFullScreen) {
            await elem.mozRequestFullScreen();
          } else if (elem.webkitRequestFullscreen) {
            await elem.webkitRequestFullscreen();
          } else if (elem.msRequestFullscreen) {
            await elem.msRequestFullscreen();
          } else {
            throw new Error('Fullscreen not supported');
          }
          
          console.log('✅ Fullscreen request successful');
          resolve(true);
        } catch (error) {
          console.error('❌ Fullscreen request failed:', error);
          resolve(false);
        }
      });
    }

  } // End of SecurityManager class
  
  // Create global reference
  window.SecurityManager = SecurityManager;
  console.log('✅ SecurityManager loaded successfully');

})();
