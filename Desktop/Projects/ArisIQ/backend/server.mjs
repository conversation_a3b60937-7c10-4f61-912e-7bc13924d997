// File: server.mjs
import express from 'express';
import multer from 'multer';
import fetch from 'node-fetch';
import fs from 'fs';
import dotenv from 'dotenv';
import FormData from 'form-data';
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables
dotenv.config();

// Setup Express app
const app = express();
const port = process.env.PORT || 3000;

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configure multer for file uploads with better options
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // Create uploads directory if it doesn't exist
    const uploadDir = path.join(__dirname, 'uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // Create unique filename with timestamp
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname) || '.webm';
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

// Setup file upload middleware with larger file size limits (50MB)
const upload = multer({
  storage: storage,
  limits: { fileSize: 50 * 1024 * 1024 },  // 50MB limit
  fileFilter: (req, file, cb) => {
    // Accept audio/video files only
    if (file.mimetype.startsWith('audio/') || file.mimetype.startsWith('video/')) {
      cb(null, true);
    } else {
      cb(new Error('Only audio and video files are allowed'));
    }
  }
});

// Basic middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Endpoint for audio transcription using OpenAI Whisper API
app.post('/api/transcribe', upload.single('file'), async (req, res) => {
  console.log('Received transcription request');
  
  if (!req.file) {
    console.error('No file uploaded');
    return res.status(400).json({ error: 'No file uploaded' });
  }
  
  const filePath = req.file.path;
  console.log(`File saved to: ${filePath}`);

  try {
    // Create form data for OpenAI API request
    const formData = new FormData();
    formData.append('file', fs.createReadStream(filePath));
    formData.append('model', 'whisper-1');
    
    // Add optional parameters if provided
    if (req.body.language) {
      formData.append('language', req.body.language);
    }
    
    if (req.body.prompt) {
      formData.append('prompt', req.body.prompt);
    }
    
    // Higher temperature for more creative transcriptions
    formData.append('temperature', req.body.temperature || '0.2');
    
    // Add application and question information to response
    const applicationId = req.body.applicationId;
    const questionId = req.body.questionId;
    
    // Log request details
    console.log('Sending request to OpenAI Whisper API');
    console.log(`API Key length: ${process.env.OPENAI_API_KEY?.length || 0}`);
    
    // Call OpenAI Whisper API
    const response = await fetch('https://api.openai.com/v1/audio/transcriptions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
      },
      body: formData,
      timeout: 30000 // 30 second timeout for larger files
    });

    // Parse response
    if (!response.ok) {
      const errorText = await response.text();
      console.error('OpenAI API error:', response.status, errorText);
      return res.status(response.status).json({ 
        error: `OpenAI API error: ${response.status}`,
        details: errorText
      });
    }
    
    const result = await response.json();
    
    // Clean up the temporary file
    fs.unlinkSync(filePath);
    
    // Return transcription result with additional metadata
    res.json({
      text: result.text,
      applicationId,
      questionId,
      timestamp: new Date().toISOString()
    });
    
    console.log('Transcription successful');
    
  } catch (err) {
    console.error('Transcription error:', err);
    
    // Clean up file if it exists
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    
    res.status(500).json({ error: `Transcription failed: ${err.message}` });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Start the server
app.listen(port, () => {
  console.log(`🟢 Whisper API listening on http://localhost:${port}`);
  console.log(`OpenAI API Key configured: ${process.env.OPENAI_API_KEY ? 'Yes' : 'No'}`);
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({ error: `Server error: ${err.message}` });
});
