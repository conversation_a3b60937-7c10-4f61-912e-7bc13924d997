{"name": "next-auth-test", "version": "0.1.0", "private": true, "main": "main.js", "scripts": {"dev": "concurrently \"npm run backend\" \"npm run frontend\" \"npm run electron\"", "backend": "cd backend && node server.mjs", "frontend": "next dev", "electron": "wait-on http://localhost:3001 && electron .", "build": "next build", "start": "next start", "tailwind": "tailwindcss -i ./styles/globals.css -o ./styles/output.css --watch", "lint": "next lint", "start:interview": "electron ."}, "dependencies": {"@anthropic-ai/sdk": "^0.40.1", "@google/generative-ai": "^0.24.1", "@monaco-editor/react": "^4.7.0", "@tensorflow/tfjs": "^3.21.0", "@tensorflow/tfjs-backend-wasm": "^3.21.0", "@tensorflow/tfjs-backend-webgl": "^3.21.0", "@tensorflow/tfjs-converter": "^3.21.0", "@tensorflow/tfjs-core": "^3.21.0", "@vladmandic/face-api": "^1.7.15", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cheerio": "^1.0.0", "crypto-js": "^4.2.0", "csv-parser": "^3.2.0", "dotenv": "^16.5.0", "express": "^5.1.0", "face-api.js": "^0.22.2", "formidable": "^2.1.5", "gridfs-stream": "^1.1.1", "mongodb": "^6.16.0", "mongoose": "^8.12.1", "multer": "^1.4.4", "multer-gridfs-storage": "^5.0.2", "next": "15.2.3", "next-auth": "^4.24.11", "next-connect": "^0.10.2", "node-fetch": "^3.3.2", "nodemailer": "^6.10.0", "openai": "^4.104.0", "pdf-parse": "^1.1.1", "portfinder": "^1.0.36", "puppeteer": "^24.8.0", "python-shell": "^5.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "recharts": "^2.15.3", "uuid": "^11.1.0", "vm2": "^3.9.19"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "concurrently": "^9.1.2", "electron": "^35.1.5", "electron-packager": "^17.1.2", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5", "wait-on": "^7.2.0"}, "description": "ArisIQ is a smart hiring platform that allows recruiters to post jobs and candidates to apply with resumes. It includes:", "directories": {"lib": "lib"}, "keywords": [], "author": "", "license": "ISC"}