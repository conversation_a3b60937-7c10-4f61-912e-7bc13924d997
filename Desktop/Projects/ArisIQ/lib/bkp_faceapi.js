let faceapi = null;

export async function loadFaceAPI() {
  if (!faceapi) {
    const mod = await import('@vladmandic/face-api');
    faceapi = mod;
    await faceapi.nets.tinyFaceDetector.loadFromUri('/models');
    await faceapi.nets.faceLandmark68Net.loadFromUri('/models');
    await faceapi.nets.faceRecognitionNet.loadFromUri('/models');
    // Add other models if needed
  }
  return faceapi;
}

