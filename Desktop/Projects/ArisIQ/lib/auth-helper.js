// File: lib/auth-helper.js
// Helper function to get user email from various auth systems

import { getToken } from "next-auth/jwt";

export async function getUserEmail(req) {
  let userEmail = null;
  
  try {
    // Method 1: NextAuth JWT Token
    const token = await getToken({ 
      req, 
      secret: process.env.NEXTAUTH_SECRET 
    });
    
    if (token?.email) {
      console.log("✅ Got email from NextAuth JWT:", token.email);
      return token.email;
    }
  } catch (error) {
    console.warn("⚠️ NextAuth JWT failed:", error.message);
  }
  
  try {
    // Method 2: NextAuth Session Cookie (manual decode)
    const sessionCookie = req.cookies?.['next-auth.session-token'] || 
                          req.cookies?.['__Secure-next-auth.session-token'];
    
    if (sessionCookie) {
      // You might need to decode this based on your NextAuth setup
      console.log("📝 Found NextAuth session cookie");
      // const decoded = jwt.decode(sessionCookie);
      // return decoded?.email;
    }
  } catch (error) {
    console.warn("⚠️ Session cookie decode failed:", error.message);
  }
  
  try {
    // Method 3: Custom Authorization Header
    if (req.headers.authorization) {
      const authHeader = req.headers.authorization;
      
      if (authHeader.startsWith('Bearer ')) {
        const token = authHeader.replace('Bearer ', '');
        // Decode your custom JWT token here
        // const decoded = jwt.verify(token, process.env.JWT_SECRET);
        // return decoded.email;
        console.log("📝 Found Bearer token but no decoder implemented");
      }
    }
  } catch (error) {
    console.warn("⚠️ Bearer token decode failed:", error.message);
  }
  
  try {
    // Method 4: Custom Session Cookie
    if (req.cookies?.session) {
      const sessionData = JSON.parse(req.cookies.session);
      if (sessionData?.user?.email) {
        console.log("✅ Got email from custom session:", sessionData.user.email);
        return sessionData.user.email;
      }
    }
  } catch (error) {
    console.warn("⚠️ Custom session parse failed:", error.message);
  }
  
  // Method 5: Request body (from frontend form)
  if (req.body?.userEmail || req.body?.postedBy) {
    const email = req.body.userEmail || req.body.postedBy;
    console.log("📝 Got email from request body:", email);
    return email;
  }
  
  // Method 6: Query parameters (as fallback)
  if (req.query?.email) {
    console.log("📝 Got email from query param:", req.query.email);
    return req.query.email;
  }
  
  console.warn("❌ Could not determine user email from any source");
  return null;
}

// Helper to get user email for API routes
export async function requireAuth(req, res) {
  const email = await getUserEmail(req);
  
  if (!email) {
    return res.status(401).json({
      success: false,
      message: "Authentication required",
      details: "Could not determine user email. Please log in."
    });
  }
  
  return email;
}
