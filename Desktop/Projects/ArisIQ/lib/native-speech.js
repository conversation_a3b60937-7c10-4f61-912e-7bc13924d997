// File: lib/native-speech.js

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const { ipcMain } = require('electron');

// Configuration for native speech recognition
const speechConfig = {
  // Default values
  language: 'en-US',
  continuous: true,
  interimResults: true,
  maxAlternatives: 1,
  recordingDuration: 30000, // 30 seconds per recording chunk
  silenceThreshold: 0.5,    // Silence detection threshold
  tempDirectory: path.join(process.cwd(), 'temp')
};

// Create temp directory if it doesn't exist
if (!fs.existsSync(speechConfig.tempDirectory)) {
  fs.mkdirSync(speechConfig.tempDirectory, { recursive: true });
}

// Class for native speech recognition
class NativeSpeechRecognition {
  constructor() {
    this.isRecording = false;
    this.recordingProcess = null;
    this.recognitionProcess = null;
    this.recordingFile = null;
    this.mainWindow = null;
    this.recordingChunks = [];
    this.transcriptBuffer = '';
    this.recognitionActive = false;
    this.initialized = false;
  }
  
  // Initialize the speech recognition module
  initialize(mainWindow) {
    if (this.initialized) return true;
    
    this.mainWindow = mainWindow;
    this.setupIpcHandlers();
    
    // Check for required dependencies
    try {
      // Verify required dependencies are installed
      if (process.platform === 'win32') {
        // For Windows, check if required DLLs exist
        // This is a simplified check - in a real implementation,
        // you would check for the specific speech recognition libraries
        if (!fs.existsSync(path.join(process.env.WINDIR, 'System32', 'speech', 'engines', 'sr.dll'))) {
          console.error('Windows Speech Recognition components not found');
          return false;
        }
      } else if (process.platform === 'darwin') {
        // For macOS, check for SpeechRecognition framework
        try {
          // Execute a test command to check if speech recognition is available
          const testResult = spawn('say', ['-v', '?']).status;
          if (testResult !== 0) {
            console.error('macOS speech components not found');
            return false;
          }
        } catch (error) {
          console.error('Error testing macOS speech components:', error);
          return false;
        }
      } else if (process.platform === 'linux') {
        // For Linux, check for speech-tools or similar
        try {
          const testResult = spawn('which', ['pocketsphinx_continuous']).status;
          if (testResult !== 0) {
            console.error('Linux speech components not found');
            return false;
          }
        } catch (error) {
          console.error('Error testing Linux speech components:', error);
          return false;
        }
      }
      
      this.initialized = true;
      return true;
    } catch (error) {
      console.error('Error initializing native speech recognition:', error);
      return false;
    }
  }
  
  // Set up IPC handlers for communication with renderer process
  setupIpcHandlers() {
    // Start speech recognition
    ipcMain.on('native-speech-start', (event, options = {}) => {
      this.start(options)
        .then(result => {
          if (this.mainWindow) {
            this.mainWindow.webContents.send('native-speech-started', { success: result });
          }
        })
        .catch(error => {
          console.error('Error starting speech recognition:', error);
          if (this.mainWindow) {
            this.mainWindow.webContents.send('native-speech-error', { 
              error: error.message || 'Failed to start speech recognition' 
            });
          }
        });
    });
    
    // Stop speech recognition
    ipcMain.on('native-speech-stop', () => {
      this.stop()
        .then(() => {
          if (this.mainWindow) {
            this.mainWindow.webContents.send('native-speech-stopped');
          }
        })
        .catch(error => {
          console.error('Error stopping speech recognition:', error);
          if (this.mainWindow) {
            this.mainWindow.webContents.send('native-speech-error', { 
              error: error.message || 'Failed to stop speech recognition' 
            });
          }
        });
    });
  }
  
  // Start recording audio for speech recognition
  async start(options = {}) {
    if (this.isRecording) {
      console.warn('Speech recognition is already running');
      return false;
    }
    
    // Merge options with defaults
    const config = { ...speechConfig, ...options };
    
    try {
      this.isRecording = true;
      this.recognitionActive = true;
      
      // Generate a unique filename for this recording
      const timestamp = new Date().toISOString().replace(/:/g, '-');
      this.recordingFile = path.join(speechConfig.tempDirectory, `speech-${timestamp}.wav`);
      
      // Start audio recording based on platform
      if (process.platform === 'win32') {
        // Windows implementation
        this.startWindowsSpeechRecognition(config);
      } else if (process.platform === 'darwin') {
        // macOS implementation
        this.startMacOSSpeechRecognition(config);
      } else if (process.platform === 'linux') {
        // Linux implementation
        this.startLinuxSpeechRecognition(config);
      } else {
        throw new Error(`Unsupported platform: ${process.platform}`);
      }
      
      return true;
    } catch (error) {
      this.isRecording = false;
      this.recognitionActive = false;
      console.error('Error starting speech recognition:', error);
      throw error;
    }
  }
  
  // Stop speech recognition
  async stop() {
    if (!this.isRecording) {
      console.warn('Speech recognition is not running');
      return false;
    }
    
    try {
      // Stop recording process
      if (this.recordingProcess && !this.recordingProcess.killed) {
        this.recordingProcess.kill();
        this.recordingProcess = null;
      }
      
      // Stop recognition process
      if (this.recognitionProcess && !this.recognitionProcess.killed) {
        this.recognitionProcess.kill();
        this.recognitionProcess = null;
      }
      
      // Clean up the recording file
      if (this.recordingFile && fs.existsSync(this.recordingFile)) {
        fs.unlinkSync(this.recordingFile);
        this.recordingFile = null;
      }
      
      this.isRecording = false;
      this.recognitionActive = false;
      
      return true;
    } catch (error) {
      console.error('Error stopping speech recognition:', error);
      throw error;
    }
  }
  
  // Process recognition results
  processRecognitionResult(text, isFinal = false) {
    if (!this.mainWindow) return;
    
    if (isFinal) {
      // Add to transcript buffer
      this.transcriptBuffer += ' ' + text;
      
      // Send final result to renderer
      this.mainWindow.webContents.send('native-speech-result', {
        transcript: text,
        isFinal: true
      });
    } else {
      // Send interim result to renderer
      this.mainWindow.webContents.send('native-speech-result', {
        transcript: text,
        isFinal: false
      });
    }
  }
  
  // Windows-specific implementation
  startWindowsSpeechRecognition(config) {
    // This is a placeholder for Windows implementation
    // In a real implementation, you would use the Windows Speech Recognition API
    console.log('Starting Windows Speech Recognition with config:', config);
    
    // Simulate recognition with mock data for testing
    this.simulateRecognition();
  }
  
  // macOS-specific implementation
  startMacOSSpeechRecognition(config) {
    // This is a placeholder for macOS implementation
    // In a real implementation, you would use the macOS Speech Recognition API
    console.log('Starting macOS Speech Recognition with config:', config);
    
    // Simulate recognition with mock data for testing
    this.simulateRecognition();
  }
  
  // Linux-specific implementation
  startLinuxSpeechRecognition(config) {
    // This is a placeholder for Linux implementation
    // In a real implementation, you would use a library like PocketSphinx
    console.log('Starting Linux Speech Recognition with config:', config);
    
    // Simulate recognition with mock data for testing
    this.simulateRecognition();
  }
  
  // Simulate speech recognition for testing
  simulateRecognition() {
    // This is just for testing purposes
    // In a real implementation, this would be replaced with actual speech recognition
    
    let count = 0;
    const sampleTexts = [
      "Hello, this is a test.",
      "I am testing the speech recognition system.",
      "This is a simulated response from the native speech recognition engine.",
      "The system appears to be working correctly.",
      "Please speak clearly for better recognition results."
    ];
    
    // Simulate interval for receiving recognition results
    const interval = setInterval(() => {
      if (!this.recognitionActive) {
        clearInterval(interval);
        return;
      }
      
      // Send interim result
      this.processRecognitionResult(sampleTexts[count % sampleTexts.length] + "...", false);
      
      // Every 3 seconds, send a final result
      if (count % 3 === 0) {
        this.processRecognitionResult(sampleTexts[count % sampleTexts.length], true);
      }
      
      count++;
      
      // Stop after 10 iterations for this example
      if (count > 10) {
        clearInterval(interval);
        
        // If speech recognition is still active, restart simulation
        if (this.recognitionActive) {
          setTimeout(() => this.simulateRecognition(), 1000);
        }
      }
    }, 2000);
  }
}

// Export the speech recognition instance
module.exports = new NativeSpeechRecognition();
