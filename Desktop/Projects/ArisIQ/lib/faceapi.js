// lib/faceapi.js
// Simple mock implementation to make the interview app work without errors

// Simple mock for face-api.js
const mockApi = {
  nets: {
    tinyFaceDetector: {
      loadFromUri: async () => {
        console.log('Mock: TinyFaceDetector model loaded');
        return true;
      }
    },
    faceLandmark68Net: {
      loadFromUri: async () => {
        console.log('Mock: faceLandmark68Net model loaded');
        return true;
      }
    },
    faceExpressionNet: {
      loadFromUri: async () => {
        console.log('Mock: faceExpressionNet model loaded');
        return true;
      }
    },
    ssdMobilenetv1: {
      loadFromUri: async () => {
        console.log('Mock: ssdMobilenetv1 model loaded');
        return true;
      }
    }
  },
  draw: {
    drawFaceLandmarks: (canvas, detections) => {
      if (!canvas || !canvas.getContext) return;
      
      const ctx = canvas.getContext('2d');
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // Draw a simple face representation
      detections.forEach(detection => {
        const { box } = detection.detection;
        
        // Draw face box
        ctx.strokeStyle = 'green';
        ctx.lineWidth = 2;
        ctx.strokeRect(box.x, box.y, box.width, box.height);
        
        // Draw eyes
        ctx.fillStyle = 'blue';
        ctx.beginPath();
        ctx.arc(box.x + box.width * 0.3, box.y + box.height * 0.4, 5, 0, 2 * Math.PI);
        ctx.fill();
        ctx.beginPath();
        ctx.arc(box.x + box.width * 0.7, box.y + box.height * 0.4, 5, 0, 2 * Math.PI);
        ctx.fill();
        
        // Draw mouth
        ctx.strokeStyle = 'red';
        ctx.beginPath();
        ctx.moveTo(box.x + box.width * 0.3, box.y + box.height * 0.7);
        ctx.quadraticCurveTo(
          box.x + box.width * 0.5, 
          box.y + box.height * 0.8, 
          box.x + box.width * 0.7, 
          box.y + box.height * 0.7
        );
        ctx.stroke();
      });
    }
  }
};

// Mock TinyFaceDetectorOptions class
export class TinyFaceDetectorOptions {
  constructor(options = {}) {
    this.inputSize = options.inputSize || 416;
    this.scoreThreshold = options.scoreThreshold || 0.5;
  }
}

// Helper function to load Face API
export const loadFaceAPI = async () => {
  console.log('Mock Face API loaded successfully');
  return mockApi;
};

// Helper function to detect faces
export const detectAllFaces = () => {
  return {
    withFaceLandmarks: () => ({
      withFaceExpressions: () => {
        // Return a mock detection with one face
        return [{
          detection: {
            score: 0.95,
            box: { x: 100, y: 100, width: 200, height: 200 }
          },
          landmarks: {
            positions: Array(68).fill().map((_, i) => ({ 
              x: 100 + (i % 10) * 20, 
              y: 100 + Math.floor(i / 10) * 20 
            })),
            getJawOutline: () => Array(17).fill().map((_, i) => ({ 
              x: 100 + i * 10, 
              y: 200 + (i > 8 ? (16 - i) * 5 : i * 5)
            })),
            getNose: () => Array(9).fill().map((_, i) => ({ 
              x: 180 + (i % 3) * 10, 
              y: 140 + Math.floor(i / 3) * 15
            }))
          },
          expressions: {
            neutral: 0.8,
            happy: 0.15,
            sad: 0.05
          }
        }];
      }
    })
  };
};

// Helper function to match dimensions
export const matchDimensions = (canvas, dimensions) => {
  if (canvas && canvas.getContext) {
    canvas.width = dimensions.width;
    canvas.height = dimensions.height;
  }
};

// Helper function to resize results
export const resizeResults = (detections) => {
  return detections; // Just return the same detections
};

// Helper function to check if a person is looking away
export const isLookingAway = (() => {
  let lastChange = Date.now();
  let currentState = false;
  
  return () => {
    // Only change state every 10-20 seconds
    const now = Date.now();
    if (now - lastChange > (10000 + Math.random() * 10000)) {
      currentState = Math.random() < 0.2; // 20% chance of looking away
      lastChange = now;
    }
    
    return currentState;
  };
})();

// Export functions for loadSsdMobilenetv1Model, etc.
export const loadSsdMobilenetv1Model = async () => {
  console.log('Mock: SSD MobileNet model loaded');
  return true;
};

export const loadFaceLandmarkModel = async () => {
  console.log('Mock: Face Landmark model loaded');
  return true;
};

export const loadFaceExpressionModel = async () => {
  console.log('Mock: Face Expression model loaded');
  return true;
};

// Export nets so it can be accessed directly
export const nets = mockApi.nets;

// Export draw so it can be accessed directly
export const draw = mockApi.draw;

// Export default object
export default {
  loadFaceAPI,
  loadSsdMobilenetv1Model,
  loadFaceLandmarkModel,
  loadFaceExpressionModel,
  detectAllFaces,
  matchDimensions,
  resizeResults,
  draw,
  nets,
  TinyFaceDetectorOptions,
  isLookingAway
};
