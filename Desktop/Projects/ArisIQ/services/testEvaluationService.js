// services/testEvaluationService.js
import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import { GoogleGenerativeAI } from '@google/generative-ai';

class TestEvaluationService {
  constructor() {
    // Initialize AI clients using your existing API key names
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    this.anthropic = new Anthropic({
      apiKey: process.env.CLAUDE_API_KEY, // Using your existing key name
    });

    this.googleAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY); // Using your existing key name

    // Configuration
    this.config = {
      models: {
        claude: 'claude-3-5-haiku-20241022',     // Budget model
        gpt: 'gpt-4o-mini',                      // Budget model  
        gemini: 'gemini-1.5-flash'               // Budget model
      },
      weights: {
        claude: 0.40,
        gpt: 0.35,
        gemini: 0.25
      },
      categoryWeights: {
        technical: 0.40,
        communication: 0.25,
        problemSolving: 0.20,
        experience: 0.15
      },
      maxRetries: 3,
      timeoutMs: 30000
    };
  }

  /**
   * Main evaluation function - evaluates completed test
   */
  async evaluateTest(applicationId) {
    try {
      console.log(`Starting AI evaluation for application: ${applicationId}`);
      
      // 1. Fetch application and test data
      const application = await this.fetchApplicationData(applicationId);
      if (!application || !application.testAnswers || application.testAnswers.length === 0) {
        throw new Error('No test answers found for evaluation');
      }

      // 2. Update status to in_progress
      await this.updateEvaluationStatus(applicationId, 'in_progress');

      // 3. Get job context for evaluation
      const jobContext = await this.getJobContext(application.jobId);

      // 4. Run parallel AI evaluations
      const evaluations = await this.runParallelEvaluations(
        application.testAnswers, 
        jobContext
      );

      // 5. Consolidate scores
      const finalScore = this.consolidateScores(evaluations);

      // 6. Save results to database
      await this.saveEvaluationResults(applicationId, evaluations, finalScore);

      // 7. Update status to completed
      await this.updateEvaluationStatus(applicationId, 'completed');

      console.log(`AI evaluation completed for application: ${applicationId}`, {
        finalScore: finalScore.overall,
        confidence: finalScore.confidence,
        recommendation: finalScore.recommendation
      });

      return {
        success: true,
        finalScore,
        evaluations
      };

    } catch (error) {
      console.error(`AI evaluation failed for application: ${applicationId}`, error);
      
      // Update status to failed
      await this.updateEvaluationStatus(applicationId, 'failed', error.message);
      
      throw error;
    }
  }

  /**
   * Run all AI evaluations in parallel
   */
  async runParallelEvaluations(testAnswers, jobContext) {
    const evaluationPromises = [
      this.evaluateWithClaude(testAnswers, jobContext),
      this.evaluateWithGPT(testAnswers, jobContext),
      this.evaluateWithGemini(testAnswers, jobContext)
    ];

    // Run evaluations in parallel with timeout
    const results = await Promise.allSettled(
      evaluationPromises.map(promise => 
        this.withTimeout(promise, this.config.timeoutMs)
      )
    );

    const evaluations = {};
    
    // Process results
    if (results[0].status === 'fulfilled') {
      evaluations.claude = results[0].value;
    } else {
      console.error('Claude evaluation failed:', results[0].reason);
    }

    if (results[1].status === 'fulfilled') {
      evaluations.gpt = results[1].value;
    } else {
      console.error('GPT evaluation failed:', results[1].reason);
    }

    if (results[2].status === 'fulfilled') {
      evaluations.gemini = results[2].value;
    } else {
      console.error('Gemini evaluation failed:', results[2].reason);
    }

    // Ensure we have at least one successful evaluation
    if (Object.keys(evaluations).length === 0) {
      throw new Error('All AI evaluations failed');
    }

    return evaluations;
  }

  /**
   * Claude evaluation
   */
  async evaluateWithClaude(testAnswers, jobContext) {
    const startTime = Date.now();
    
    try {
      const prompt = this.buildClaudePrompt(testAnswers, jobContext);
      
      const response = await this.anthropic.messages.create({
        model: this.config.models.claude,
        max_tokens: 2000,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ]
      });

      const evaluation = this.parseClaudeResponse(response.content[0].text);
      
      return {
        ...evaluation,
        evaluatedAt: new Date(),
        model: this.config.models.claude,
        processingTime: Date.now() - startTime
      };

    } catch (error) {
      console.error('Claude evaluation error:', error);
      throw new Error(`Claude evaluation failed: ${error.message}`);
    }
  }

  /**
   * GPT evaluation  
   */
  async evaluateWithGPT(testAnswers, jobContext) {
    const startTime = Date.now();
    
    try {
      const prompt = this.buildGPTPrompt(testAnswers, jobContext);
      
      const response = await this.openai.chat.completions.create({
        model: this.config.models.gpt,
        messages: [
          {
            role: 'system',
            content: 'You are an expert technical interviewer evaluating candidate responses.'
          },
          {
            role: 'user', 
            content: prompt
          }
        ],
        max_tokens: 2000,
        temperature: 0.3
      });

      const evaluation = this.parseGPTResponse(response.choices[0].message.content);
      
      return {
        ...evaluation,
        evaluatedAt: new Date(),
        model: this.config.models.gpt,
        processingTime: Date.now() - startTime
      };

    } catch (error) {
      console.error('GPT evaluation error:', error);
      throw new Error(`GPT evaluation failed: ${error.message}`);
    }
  }

  /**
   * Gemini evaluation
   */
  async evaluateWithGemini(testAnswers, jobContext) {
    const startTime = Date.now();
    
    try {
      const model = this.googleAI.getGenerativeModel({ 
        model: this.config.models.gemini 
      });
      
      const prompt = this.buildGeminiPrompt(testAnswers, jobContext);
      
      const result = await model.generateContent(prompt);
      const response = await result.response;
      
      const evaluation = this.parseGeminiResponse(response.text());
      
      return {
        ...evaluation,
        evaluatedAt: new Date(),
        model: this.config.models.gemini,
        processingTime: Date.now() - startTime
      };

    } catch (error) {
      console.error('Gemini evaluation error:', error);
      throw new Error(`Gemini evaluation failed: ${error.message}`);
    }
  }

  /**
   * Build Claude evaluation prompt
   */
  buildClaudePrompt(testAnswers, jobContext) {
    const formattedAnswers = testAnswers.map((answer, index) => 
      `Question ${index + 1}: ${answer.questionText}\n` +
      `Answer: ${answer.answerText}\n` +
      `Time Spent: ${answer.timeSpent || 'N/A'} seconds\n`
    ).join('\n---\n');

    return `As a senior technical recruiter, analyze this candidate's screening test responses with focus on detailed evaluation.

JOB CONTEXT:
- Position: ${jobContext.title}
- Required Skills: ${jobContext.requiredSkills}
- Experience Level: ${jobContext.experienceLevel}
- Company: ${jobContext.company}

CANDIDATE RESPONSES:
${formattedAnswers}

EVALUATION CRITERIA:
Evaluate across these dimensions with scores 0-100:
1. Technical Knowledge & Accuracy (40% weight)
2. Problem-Solving Approach (20% weight) 
3. Communication Skills (25% weight)
4. Experience Relevance (15% weight)

Provide structured feedback in this EXACT JSON format:
{
  "score": [overall_score_0_to_100],
  "evaluation": "[comprehensive 300-500 word analysis]",
  "categoryScores": {
    "technical": [0_to_100],
    "communication": [0_to_100], 
    "problemSolving": [0_to_100],
    "experience": [0_to_100]
  },
  "strengths": ["strength1", "strength2", "strength3"],
  "weaknesses": ["weakness1", "weakness2", "weakness3"],
  "recommendations": ["recommendation1", "recommendation2", "recommendation3"]
}

Be thorough but concise. Focus on actionable insights for hiring decision.`;
  }

  /**
   * Build GPT evaluation prompt
   */
  buildGPTPrompt(testAnswers, jobContext) {
    const formattedAnswers = testAnswers.map((answer, index) => 
      `Q${index + 1}: ${answer.questionText}\n` +
      `A${index + 1}: ${answer.answerText}\n` +
      `Time: ${answer.timeSpent || 'N/A'}s\n`
    ).join('\n');

    return `Evaluate this candidate's screening test responses as an expert technical interviewer.

JOB CONTEXT:
Position: ${jobContext.title}
Required Skills: ${jobContext.requiredSkills}
Experience Level: ${jobContext.experienceLevel}

CANDIDATE RESPONSES:
${formattedAnswers}

EVALUATION CRITERIA (score each 0-100):
- Technical Accuracy (40%)
- Problem-Solving Approach (20%)
- Communication Clarity (25%)
- Experience Relevance (15%)

Return evaluation in this exact JSON format:
{
  "score": [overall_score_0_to_100],
  "evaluation": "[detailed 300-500 word analysis]",
  "categoryScores": {
    "technical": [0_to_100],
    "communication": [0_to_100],
    "problemSolving": [0_to_100], 
    "experience": [0_to_100]
  },
  "strengths": ["top 3 strengths"],
  "weaknesses": ["top 3 weaknesses"],
  "recommendations": ["3 hiring recommendations"]
}

Focus on factual accuracy, logical reasoning, and clear hiring recommendations.`;
  }

  /**
   * Build Gemini evaluation prompt
   */
  buildGeminiPrompt(testAnswers, jobContext) {
    const formattedAnswers = testAnswers.map((answer, index) => 
      `Question ${index + 1}: ${answer.questionText}\n` +
      `Response: ${answer.answerText}\n` +
      `Duration: ${answer.timeSpent || 'N/A'} seconds\n`
    ).join('\n---\n');

    return `Analyze candidate screening test responses for hiring evaluation.

POSITION DETAILS:
Title: ${jobContext.title}
Skills Required: ${jobContext.requiredSkills}
Experience: ${jobContext.experienceLevel}

TEST RESPONSES:
${formattedAnswers}

SCORING FRAMEWORK (0-100 each):
• Technical Knowledge (40% weight)
• Problem-Solving (20% weight) 
• Communication (25% weight)
• Experience Match (15% weight)

OUTPUT FORMAT (strict JSON):
{
  "score": [overall_score_0_to_100],
  "evaluation": "[comprehensive analysis 300-500 words]",
  "categoryScores": {
    "technical": [score],
    "communication": [score],
    "problemSolving": [score],
    "experience": [score]
  },
  "strengths": ["strength1", "strength2", "strength3"],
  "weaknesses": ["weakness1", "weakness2", "weakness3"], 
  "recommendations": ["rec1", "rec2", "rec3"]
}

Provide objective, detailed assessment focusing on hiring suitability.`;
  }

  /**
   * Parse Claude response
   */
  parseClaudeResponse(responseText) {
    try {
      // Extract JSON from response
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in Claude response');
      }
      
      const parsed = JSON.parse(jsonMatch[0]);
      return this.validateEvaluationResponse(parsed);
    } catch (error) {
      console.error('Failed to parse Claude response:', error);
      return this.createFallbackEvaluation('Claude parsing failed');
    }
  }

  /**
   * Parse GPT response
   */
  parseGPTResponse(responseText) {
    try {
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in GPT response');
      }
      
      const parsed = JSON.parse(jsonMatch[0]);
      return this.validateEvaluationResponse(parsed);
    } catch (error) {
      console.error('Failed to parse GPT response:', error);
      return this.createFallbackEvaluation('GPT parsing failed');
    }
  }

  /**
   * Parse Gemini response
   */
  parseGeminiResponse(responseText) {
    try {
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in Gemini response');
      }
      
      const parsed = JSON.parse(jsonMatch[0]);
      return this.validateEvaluationResponse(parsed);
    } catch (error) {
      console.error('Failed to parse Gemini response:', error);
      return this.createFallbackEvaluation('Gemini parsing failed');
    }
  }

  /**
   * Validate evaluation response structure
   */
  validateEvaluationResponse(evaluation) {
    const required = ['score', 'evaluation', 'categoryScores', 'strengths', 'weaknesses', 'recommendations'];
    
    for (const field of required) {
      if (!evaluation[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // Ensure score is valid
    if (typeof evaluation.score !== 'number' || evaluation.score < 0 || evaluation.score > 100) {
      evaluation.score = Math.max(0, Math.min(100, parseInt(evaluation.score) || 50));
    }

    // Validate category scores
    for (const category of ['technical', 'communication', 'problemSolving', 'experience']) {
      if (!evaluation.categoryScores[category]) {
        evaluation.categoryScores[category] = 50; // Default fallback
      }
    }

    return evaluation;
  }

  /**
   * Create fallback evaluation when parsing fails
   */
  createFallbackEvaluation(reason) {
    return {
      score: 50,
      evaluation: `Evaluation could not be completed due to parsing error: ${reason}. Manual review recommended.`,
      categoryScores: {
        technical: 50,
        communication: 50,
        problemSolving: 50,
        experience: 50
      },
      strengths: ['Requires manual review'],
      weaknesses: ['AI evaluation failed'],
      recommendations: ['Conduct manual evaluation', 'Review test answers directly', 'Consider re-running evaluation']
    };
  }

  /**
   * Consolidate scores from multiple AIs
   */
  consolidateScores(evaluations) {
    const availableAIs = Object.keys(evaluations);
    
    if (availableAIs.length === 0) {
      throw new Error('No evaluations to consolidate');
    }

    // Calculate weighted average
    let totalWeight = 0;
    let weightedScore = 0;
    
    for (const ai of availableAIs) {
      const weight = this.config.weights[ai] || (1 / availableAIs.length);
      weightedScore += evaluations[ai].score * weight;
      totalWeight += weight;
    }
    
    const overall = Math.round(weightedScore / totalWeight);

    // Calculate category breakdowns
    const categoryBreakdown = {};
    for (const category of ['technical', 'communication', 'problemSolving', 'experience']) {
      let categoryScore = 0;
      let categoryWeight = 0;
      
      for (const ai of availableAIs) {
        const weight = this.config.weights[ai] || (1 / availableAIs.length);
        categoryScore += evaluations[ai].categoryScores[category] * weight;
        categoryWeight += weight;
      }
      
      categoryBreakdown[category] = Math.round(categoryScore / categoryWeight);
    }

    // Calculate confidence (agreement between AIs)
    const scores = availableAIs.map(ai => evaluations[ai].score);
    const avgScore = scores.reduce((a, b) => a + b) / scores.length;
    const variance = scores.reduce((sum, score) => 
      sum + Math.pow(score - avgScore, 2), 0) / scores.length;
    const confidence = Math.max(0, Math.round(100 - (variance * 2)));

    // Determine recommendation
    let recommendation;
    if (overall >= 80 && confidence >= 70) {
      recommendation = 'STRONG_HIRE';
    } else if (overall >= 70) {
      recommendation = 'HIRE';
    } else if (overall >= 60) {
      recommendation = 'MAYBE';
    } else {
      recommendation = 'NO_HIRE';
    }

    // Determine AI agreement level
    let aiAgreement;
    if (confidence >= 80) {
      aiAgreement = 'HIGH';
    } else if (confidence >= 60) {
      aiAgreement = 'MEDIUM';
    } else {
      aiAgreement = 'LOW';
    }

    return {
      overall,
      categoryBreakdown,
      confidence,
      recommendation,
      aiAgreement,
      calculatedAt: new Date(),
      version: 'v1.0',
      evaluationCount: availableAIs.length
    };
  }

  /**
   * Helper functions for database operations
   */
  async fetchApplicationData(applicationId) {
    // Use dynamic import to avoid module loading issues
    const { default: Application } = await import('../models/Application');
    return await Application.findById(applicationId);
  }

  async getJobContext(jobId) {
    const { default: Job } = await import('../models/Job');
    const job = await Job.findById(jobId);
    
    return {
      title: job.title,
      requiredSkills: job.requiredSkills?.join(', ') || 'Not specified',
      experienceLevel: job.experienceLevel || 'Not specified',
      company: job.company
    };
  }

  async updateEvaluationStatus(applicationId, status, error = null) {
    const { default: Application } = await import('../models/Application');
    const update = {
      evaluationStatus: status,
      [`evaluation${status.charAt(0).toUpperCase() + status.slice(1)}At`]: new Date()
    };
    
    if (error) {
      update.evaluationErrors = [error];
    }
    
    await Application.findByIdAndUpdate(applicationId, { $set: update });
  }

  async saveEvaluationResults(applicationId, evaluations, finalScore) {
    const { default: Application } = await import('../models/Application');
    
    await Application.findByIdAndUpdate(applicationId, {
      $set: {
        aiEvaluations: evaluations,
        finalTestScore: finalScore,
        testScore: finalScore.overall, // Update legacy field for compatibility
        evaluationCompletedAt: new Date()
      }
    });
  }

  /**
   * Utility function to add timeout to promises
   */
  withTimeout(promise, timeoutMs) {
    return Promise.race([
      promise,
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('AI evaluation timeout')), timeoutMs)
      )
    ]);
  }
}

export default TestEvaluationService;
