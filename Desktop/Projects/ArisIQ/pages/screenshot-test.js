// pages/screenshot-test.js
import { useEffect, useState, useRef } from 'react';
import Head from 'next/head';

export default function ScreenshotTest() {
  const [screenshotCount, setScreenshotCount] = useState(0);
  const [logs, setLogs] = useState([]);
  const [clipboardPermission, setClipboardPermission] = useState('unknown');
  const logContainerRef = useRef(null);
  const securityManagerRef = useRef(null);

  // Add log message
  const logMessage = (message, isError = false) => {
    setLogs(prev => [...prev, { message, isError, time: new Date().toLocaleTimeString() }]);
    // Also log to console
    if (isError) {
      console.error(message);
    } else {
      console.log(message);
    }
  };

  // Clear counter
  const clearCounter = () => {
    setScreenshotCount(0);
    logMessage('Counter cleared');
  };

  // Request clipboard permission - IMPORTANT for Screenshot Detection
  const requestClipboardPermission = async () => {
    try {
      logMessage('Requesting clipboard permission...');
      
      // This will trigger the permission prompt
      if (navigator.clipboard && navigator.clipboard.read) {
        try {
          const items = await navigator.clipboard.read();
          logMessage('✅ Clipboard permission granted!', false);
          setClipboardPermission('granted');
          
          // Log what's in clipboard for testing
          if (items.length > 0) {
            let types = [];
            for (const item of items) {
              types = [...types, ...item.types];
            }
            logMessage(`Current clipboard content types: ${types.join(', ')}`);
          } else {
            logMessage('Clipboard is empty');
          }
        } catch (error) {
          if (error.name === 'NotAllowedError') {
            logMessage('❌ Clipboard permission denied', true);
            setClipboardPermission('denied');
          } else {
            logMessage(`Error accessing clipboard: ${error.message}`, true);
            setClipboardPermission('error');
          }
        }
      } else {
        logMessage('❌ Clipboard API not supported in this browser', true);
        setClipboardPermission('unsupported');
      }
    } catch (error) {
      logMessage(`Error requesting clipboard permission: ${error.message}`, true);
      setClipboardPermission('error');
    }
  };

  // Main initialization effect
  useEffect(() => {
    // Load the security manager
    const loadSecurityManager = () => {
      if (typeof window !== 'undefined' && !window.SecurityManager) {
        logMessage('Loading SecurityManager script...');
        
        const script = document.createElement('script');
        script.src = '/js/security-manager.js';
        
        script.onload = () => {
          logMessage('✅ SecurityManager script loaded successfully');
          
          if (window.SecurityManager) {
            // Create instance
            const securityManager = new window.SecurityManager();
            securityManagerRef.current = securityManager;
            
            // Start monitoring immediately
            securityManager.startInterview();
            
            // Force active state
            setTimeout(() => {
              securityManager.isInterviewActive = true;
              logMessage('SecurityManager forced to active state');
            }, 200);
            
            logMessage('SecurityManager initialized and started');
          }
        };
        
        script.onerror = (error) => {
          console.error('Failed to load security manager:', error);
          logMessage('Failed to load security manager', true);
        };
        
        document.head.appendChild(script);
      } else if (window.SecurityManager) {
        logMessage('SecurityManager already loaded');
        
        // If already loaded, make sure it's active
        if (window.SecurityManager.instance) {
          securityManagerRef.current = window.SecurityManager.instance;
          securityManagerRef.current.startInterview();
          
          // Force active state
          setTimeout(() => {
            securityManagerRef.current.isInterviewActive = true;
            logMessage('Existing SecurityManager forced to active state');
          }, 200);
        } else {
          // Create a new instance if for some reason instance doesn't exist
          const securityManager = new window.SecurityManager();
          securityManagerRef.current = securityManager;
          securityManager.startInterview();
          
          setTimeout(() => {
            securityManager.isInterviewActive = true;
            logMessage('New SecurityManager forced to active state');
          }, 200);
        }
      }
    };

    loadSecurityManager();
    
    // Handle security violation events
    const handleSecurityViolation = (event) => {
      logMessage(`Security violation: ${event.detail.type} - ${event.detail.message}`, true);
      
      if (event.detail.type === 'screenshot') {
        setScreenshotCount(prev => prev + 1);
      }
    };
    
    // Handle screenshot attempt events
    const handleScreenshotAttempt = (event) => {
      logMessage(`Screenshot attempt detected: ${event.detail.method || 'Unknown method'}`, true);
      setScreenshotCount(prev => prev + 1);
    };
    
    // Handle interview-started event
    const handleInterviewStarted = (event) => {
      logMessage('SecurityManager interview monitoring started', true);
    };
    
    // Register event handlers
    window.addEventListener('security-violation', handleSecurityViolation);
    window.addEventListener('security-screenshot-attempt', handleScreenshotAttempt);
    window.addEventListener('interview-started', handleInterviewStarted);

    // Key tracking for combinations
    let cmdKeyPressed = false;
    let shiftKeyPressed = false;
    let numberKeyPressed = null;

    // Direct tracking for Mac screenshot combinations
    const handleKeyDownDirect = (e) => {
      // Track modifier keys
      if (e.key === 'Meta' || e.key === 'Command') cmdKeyPressed = true;
      if (e.key === 'Shift') shiftKeyPressed = true;
      
      // Track number keys (3, 4, 5)
      if (['3', '4', '5'].includes(e.key)) numberKeyPressed = e.key;
      
      // Log the key state
      logMessage(`Key pressed: ${e.key} (Meta: ${e.metaKey}, Shift: ${e.shiftKey}, Alt: ${e.altKey})`);
      
      // Check for Mac screenshot combination
      if (cmdKeyPressed && shiftKeyPressed && ['3', '4', '5'].includes(numberKeyPressed)) {
        const combination = `Command+Shift+${numberKeyPressed}`;
        logMessage(`Mac screenshot detected: ${combination}`, true);
        setScreenshotCount(prev => prev + 1);
        
        // Reset tracking
        setTimeout(() => {
          numberKeyPressed = null;
        }, 300);
        
        // Manually trigger security manager
        if (window.SecurityManager && window.SecurityManager.instance) {
          window.SecurityManager.instance.handleWarningViolation('screenshot', 
            `Screenshot detected: ${combination}`);
        } else if (securityManagerRef.current) {
          securityManagerRef.current.handleWarningViolation('screenshot', 
            `Screenshot detected: ${combination}`);
        }
      }
    };

    // Reset keys on key up
    const handleKeyUpDirect = (e) => {
      if (e.key === 'Meta' || e.key === 'Command') cmdKeyPressed = false;
      if (e.key === 'Shift') shiftKeyPressed = false;
      if (['3', '4', '5'].includes(e.key)) numberKeyPressed = null;
    };
    
    // Direct interception of OS-level screenshot shortcuts
    const setupDirectShortcutInterception = () => {
      const handleDirectShortcuts = function(e) {
        // Mac screenshot: Command (Meta) + Shift + 3/4/5
        if (e.metaKey && e.shiftKey && ['3', '4', '5'].includes(e.key)) {
          logMessage(`Intercepted direct Mac screenshot: Command+Shift+${e.key}`, true);
          setScreenshotCount(prev => prev + 1);
          
          // Manually trigger security manager
          if (window.SecurityManager && window.SecurityManager.instance) {
            window.SecurityManager.instance.handleWarningViolation('screenshot', 
              `Screenshot detected: Command+Shift+${e.key}`);
          } else if (securityManagerRef.current) {
            securityManagerRef.current.handleWarningViolation('screenshot',
              `Screenshot detected: Command+Shift+${e.key}`);
          }
        }
        
        // Windows screenshot: PrintScreen
        if (e.key === 'PrintScreen' || e.code === 'PrintScreen') {
          logMessage('Intercepted direct Windows screenshot: PrintScreen', true);
          setScreenshotCount(prev => prev + 1);
          
          // Manually trigger security manager
          if (window.SecurityManager && window.SecurityManager.instance) {
            window.SecurityManager.instance.handleWarningViolation('screenshot',
              'Screenshot detected: PrintScreen');
          } else if (securityManagerRef.current) {
            securityManagerRef.current.handleWarningViolation('screenshot',
              'Screenshot detected: PrintScreen');
          }
        }
      };
      
      // Add at three different levels for maximum interception chance
      document.addEventListener('keydown', handleDirectShortcuts, { capture: true, passive: false });
      window.addEventListener('keydown', handleDirectShortcuts, { capture: true, passive: false });
      if (document.body) {
        document.body.addEventListener('keydown', handleDirectShortcuts, { capture: true, passive: false });
      }
      
      return handleDirectShortcuts;
    };
    
    // Install the direct shortcut interception
    const directShortcutHandler = setupDirectShortcutInterception();
    
    // Handle paste events (might be screenshots)
    const handlePaste = (e) => {
      if (e.clipboardData && e.clipboardData.items) {
        for (let i = 0; i < e.clipboardData.items.length; i++) {
          if (e.clipboardData.items[i].type.indexOf('image') !== -1) {
            logMessage('Screenshot detected: Image pasted from clipboard', true);
            setScreenshotCount(prev => prev + 1);
            
            // Manually trigger SecurityManager if available
            if (window.SecurityManager && window.SecurityManager.instance) {
              window.SecurityManager.instance.handleWarningViolation('screenshot', 
                'Screenshot detected: Image pasted from clipboard');
            } else if (securityManagerRef.current) {
              securityManagerRef.current.handleWarningViolation('screenshot', 
                'Screenshot detected: Image pasted from clipboard');
            }
            
            break;
          }
        }
      }
    };
    
    // Global screenshot detection observer
    const setupGlobalScreenshotObserver = () => {
      // Create an observer to look for sudden changes in clipboard or visibility
      // that might indicate screenshots
      let lastVisibilityChange = Date.now();
      let recentClipboardCheck = false;
      
      // Visibility change can indicate screenshot
      const handleVisibilityChange = () => {
        const now = Date.now();
        
        if (document.visibilityState === 'hidden') {
          lastVisibilityChange = now;
        } else if (document.visibilityState === 'visible') {
          const timeDiff = now - lastVisibilityChange;
          
          // Very brief visibility change often indicates a screenshot
          if (timeDiff > 5 && timeDiff < 300) {
            logMessage(`Brief visibility change (${timeDiff}ms) - likely screenshot`, true);
            setScreenshotCount(prev => prev + 1);
            
            // Trigger security manager
            if (window.SecurityManager && window.SecurityManager.instance) {
              window.SecurityManager.instance.handleWarningViolation('screenshot',
                `Screenshot detected: visibility change (${timeDiff}ms)`);
            } else if (securityManagerRef.current) {
              securityManagerRef.current.handleWarningViolation('screenshot',
                `Screenshot detected: visibility change (${timeDiff}ms)`);
            }
          }
        }
      };
      
      // Check clipboard after key combinations
      const handleDelayedClipboardCheck = () => {
        if (recentClipboardCheck) return;
        recentClipboardCheck = true;
        
        // Wait briefly for clipboard to update
        setTimeout(async () => {
          try {
            if (navigator.clipboard && navigator.clipboard.read) {
              try {
                const items = await navigator.clipboard.read();
                
                for (const item of items) {
                  if (item.types.some(type => type.startsWith('image/'))) {
                    logMessage('Image found in clipboard after key press - likely screenshot', true);
                    setScreenshotCount(prev => prev + 1);
                    
                    // Trigger security manager
                    if (window.SecurityManager && window.SecurityManager.instance) {
                      window.SecurityManager.instance.handleWarningViolation('screenshot',
                        'Screenshot detected: image in clipboard after key press');
                    } else if (securityManagerRef.current) {
                      securityManagerRef.current.handleWarningViolation('screenshot',
                        'Screenshot detected: image in clipboard after key press');
                    }
                  }
                }
              } catch (e) {
                // Permission errors expected
              }
            }
          } catch (error) {
            // Ignore errors
          }
          
          recentClipboardCheck = false;
        }, 300);
      };
      
      // Install handlers
      document.addEventListener('visibilitychange', handleVisibilityChange);
      document.addEventListener('keyup', handleDelayedClipboardCheck);
      
      return { handleVisibilityChange, handleDelayedClipboardCheck };
    };
    
    // Start global detection
    const globalObserver = setupGlobalScreenshotObserver();
    
    // Add basic listeners
    document.addEventListener('keydown', handleKeyDownDirect, { capture: true });
    document.addEventListener('keyup', handleKeyUpDirect, { capture: true });
    document.addEventListener('paste', handlePaste, { capture: true });
    
    // Log initialization
    logMessage('Screenshot detection initialized with multiple methods');
    
    // Monitor clipboard for changes
    const monitorClipboard = async () => {
      let previousClipboardText = '';
      
      const checkClipboard = async () => {
        try {
          if (navigator.clipboard && navigator.clipboard.readText) {
            const text = await navigator.clipboard.readText();
            if (text !== previousClipboardText) {
              previousClipboardText = text;
              
              // If clipboard content changed without any selection, might be a screenshot
              const selection = window.getSelection();
              if (!selection || selection.toString().trim() === '') {
                logMessage('Clipboard content changed without selection - possible screenshot', true);
                setScreenshotCount(prev => prev + 1);
                
                // Manually trigger SecurityManager if available
                if (window.SecurityManager && window.SecurityManager.instance) {
                  window.SecurityManager.instance.handleWarningViolation('screenshot', 
                    'Screenshot detected: Clipboard changed without selection');
                } else if (securityManagerRef.current) {
                  securityManagerRef.current.handleWarningViolation('screenshot', 
                    'Screenshot detected: Clipboard changed without selection');
                }
              }
            }
          }
        } catch (error) {
          // Clipboard permissions may cause errors - ignore
        }
      };
      
      // Check clipboard every second
      const interval = setInterval(checkClipboard, 1000);
      
      return () => clearInterval(interval);
    };
    
    // Start clipboard monitoring
    const clipboardCleanup = monitorClipboard();
    
    // Clean up
    return () => {
      clipboardCleanup();
      document.removeEventListener('keydown', handleKeyDownDirect, { capture: true });
      document.removeEventListener('keyup', handleKeyUpDirect, { capture: true });
      document.removeEventListener('paste', handlePaste, { capture: true });
      document.removeEventListener('keydown', directShortcutHandler, true);
      window.removeEventListener('keydown', directShortcutHandler, true);
      if (document.body) {
        document.body.removeEventListener('keydown', directShortcutHandler, true);
      }
      document.removeEventListener('visibilitychange', globalObserver.handleVisibilityChange);
      document.removeEventListener('keyup', globalObserver.handleDelayedClipboardCheck);
      window.removeEventListener('security-violation', handleSecurityViolation);
      window.removeEventListener('security-screenshot-attempt', handleScreenshotAttempt);
      window.removeEventListener('interview-started', handleInterviewStarted);
    };
  }, []);

  // Scroll logs to bottom when new logs are added
  useEffect(() => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [logs]);

  // Manually trigger SecurityManager test
  const testSecurityManager = () => {
    if (window.SecurityManager && window.SecurityManager.instance) {
      window.SecurityManager.instance.handleWarningViolation('screenshot', 'Manual test screenshot violation');
      logMessage('Manually triggered screenshot violation', true);
      setScreenshotCount(prev => prev + 1);
    } else if (securityManagerRef.current) {
      securityManagerRef.current.handleWarningViolation('screenshot', 'Manual test screenshot violation');
      logMessage('Manually triggered screenshot violation using ref', true);
      setScreenshotCount(prev => prev + 1);
    } else {
      logMessage('SecurityManager not available for manual test', true);
    }
  };

  // Simulate a Command+Shift+3 screenshot detection
  const simulateCmdShift3 = () => {
    logMessage('Simulated Command+Shift+3 screenshot', true);
    setScreenshotCount(prev => prev + 1);
    
    // Manually trigger security manager
    if (window.SecurityManager && window.SecurityManager.instance) {
      window.SecurityManager.instance.handleWarningViolation('screenshot',
        'Screenshot detected: Command+Shift+3 (simulated)');
    } else if (securityManagerRef.current) {
      securityManagerRef.current.handleWarningViolation('screenshot',
        'Screenshot detected: Command+Shift+3 (simulated)');
    }
  };

  return (
    <div style={{ fontFamily: 'Arial, sans-serif', maxWidth: 800, margin: '0 auto', padding: 20 }}>
      <Head>
        <title>Screenshot Test</title>
      </Head>

      <h1>Simple Screenshot Test</h1>
      <p>Try to take a screenshot using your keyboard shortcuts.</p>
      
      <div style={{ 
        border: '1px solid #ddd', 
        borderRadius: 8, 
        padding: 16, 
        marginBottom: 16, 
        backgroundColor: '#f5f5f5' 
      }}>
        <h3>Clipboard Permission Status: <span style={{ 
          color: clipboardPermission === 'granted' ? 'green' : 
                 clipboardPermission === 'denied' ? 'red' : 
                 clipboardPermission === 'error' ? 'orange' : 'gray'
        }}>
          {clipboardPermission === 'granted' ? '✅ Granted' : 
           clipboardPermission === 'denied' ? '❌ Denied' : 
           clipboardPermission === 'error' ? '⚠️ Error' :
           clipboardPermission === 'unsupported' ? '❌ Unsupported' : 'Unknown'}
        </span></h3>
        
        <button 
          onClick={requestClipboardPermission}
          style={{ 
            backgroundColor: '#28a745', 
            color: 'white', 
            border: 'none', 
            padding: '8px 16px', 
            borderRadius: 4, 
            cursor: 'pointer',
            marginBottom: '10px'
          }}
        >
          Request Clipboard Permission (Required for Detection)
        </button>
        <p style={{ fontSize: '12px', color: '#666' }}>
          Note: Clipboard permission is required for screenshot detection to work properly. 
          Click the button above to grant permission.
        </p>
      </div>
      
      <div style={{ 
        border: '1px solid #ddd', 
        borderRadius: 8, 
        padding: 16, 
        marginBottom: 16, 
        backgroundColor: '#f9f9f9' 
      }}>
        <h2>Screenshot Counter: <span style={{ fontSize: 24, fontWeight: 'bold', color: 'red' }}>{screenshotCount}</span></h2>
        <p>Keyboard Shortcuts to Test:</p>
        <ul>
          <li>Windows: PrintScreen or Win+Shift+S</li>
          <li>Mac: Command+Shift+3 or Command+Shift+4</li>
        </ul>
        <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
          <button 
            onClick={clearCounter}
            style={{ 
              backgroundColor: '#0066cc', 
              color: 'white', 
              border: 'none', 
              padding: '8px 16px', 
              borderRadius: 4, 
              cursor: 'pointer' 
            }}
          >
            Clear Counter
          </button>
          
          <button 
            onClick={testSecurityManager}
            style={{ 
              backgroundColor: '#ff4500', 
              color: 'white', 
              border: 'none', 
              padding: '8px 16px', 
              borderRadius: 4, 
              cursor: 'pointer' 
            }}
          >
            Test SecurityManager
          </button>
          
          <button 
            onClick={simulateCmdShift3}
            style={{ 
              backgroundColor: '#4CAF50', 
              color: 'white', 
              border: 'none', 
              padding: '8px 16px', 
              borderRadius: 4, 
              cursor: 'pointer' 
            }}
          >
            Simulate Cmd+Shift+3
          </button>
        </div>
      </div>
      
      <div style={{ 
        border: '1px solid #ddd', 
        borderRadius: 8, 
        padding: 16, 
        marginBottom: 16, 
        backgroundColor: '#f9f9f9' 
      }}>
        <h2>Console Log</h2>
        <div 
          ref={logContainerRef}
          style={{ 
            height: 300, 
            overflowY: 'auto', 
            backgroundColor: '#f1f1f1', 
            borderRadius: 4, 
            padding: 8, 
            fontFamily: 'monospace' 
          }}
        >
          {logs.map((log, index) => (
            <div key={index} style={{ color: log.isError ? 'red' : 'black', marginBottom: '4px' }}>
              <span style={{ color: '#888', fontSize: '0.85em' }}>[{log.time}]</span> {log.message}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
