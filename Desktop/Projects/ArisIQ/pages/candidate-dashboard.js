// File: pages/candidate-dashboard.js
// Clean rebuild with Job ID display

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { useRouter } from "next/router";

export default function CandidateDashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const tabFromQuery = router.query.tab || "overview";

  const [activeTab, setActiveTab] = useState(tabFromQuery);
  const [applications, setApplications] = useState([]);
  const [recommendedJobs, setRecommendedJobs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterLocation, setFilterLocation] = useState("");
  const [filterRole, setFilterRole] = useState("");
  const [sortBy, setSortBy] = useState("recent");
  const [skills, setSkills] = useState([""]);
  const [isLaunching, setIsLaunching] = useState(false);
  const [hoveredCard, setHoveredCard] = useState(null);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");

  useEffect(() => {
    if (status === "unauthenticated") router.push("/");
  }, [status, router]);

  useEffect(() => {
    if (session?.user) {
      setLoading(true);
      
      const timestamp = new Date().getTime();
      
      fetch(`/api/candidate/applications?email=${encodeURIComponent(session.user.email)}&t=${timestamp}`)
        .then((res) => {
          if (!res.ok) {
            throw new Error(`Error fetching applications: ${res.status}`);
          }
          return res.json();
        })
        .then((data) => {
          console.log("Applications data:", data);
          setApplications(data.applications || []);
        })
        .catch((err) => {
          console.error("Failed to fetch applications:", err);
          setError("Failed to load your applications. Please try again later.");
        })
        .finally(() => setLoading(false));

      fetch(`/api/jobs?t=${timestamp}`)
        .then((res) => res.json())
        .then((data) => setRecommendedJobs(data.jobs || []))
        .catch((err) => console.error("Failed to fetch jobs:", err));
    }
  }, [session]);

  const showToastMessage = (message) => {
    setToastMessage(message);
    setShowToast(true);
    setTimeout(() => setShowToast(false), 3000);
  };

  const handleTakeInterview = async (applicationId) => {
    setIsLaunching(true);
    try {
      const response = await fetch("/api/interview/launch", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ applicationId }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || "Failed to validate interview");
      }
      
      if (data.success) {
        console.log("Interview validation successful, navigating to interview setup");
        router.push(`/candidate/interview-setup?id=${applicationId}`);
      } else {
        showToastMessage("Failed to start interview: " + (data.message || "Unknown error"));
      }
    } catch (err) {
      console.error("Error starting interview:", err);
      showToastMessage("Error starting interview: " + (err.message || "Unknown error"));
    } finally {
      setIsLaunching(false);
    }
  };

  const addSkill = () => setSkills([...skills, ""]);
  const updateSkill = (index, value) => {
    const updated = [...skills];
    updated[index] = value;
    setSkills(updated);
  };

  const getEligibleJobs = () => {
    if (!applications.length || !recommendedJobs.length) return recommendedJobs;
    
    const appliedJobIds = applications.map(app => {
      if (app.jobId) {
        return app.jobId.toString ? app.jobId.toString() : app.jobId;
      }
      return null;
    }).filter(Boolean);
    
    return recommendedJobs.filter(job => {
      const jobId = job._id.toString ? job._id.toString() : job._id;
      return !appliedJobIds.includes(jobId);
    });
  };

  const eligibleJobs = getEligibleJobs();

  const filteredApplications = applications
    .filter(
      (app) =>
        (app.jobTitle ? app.jobTitle.toLowerCase().includes(searchQuery.toLowerCase()) : true) &&
        (filterLocation === "" || (app.location && app.location.toLowerCase().includes(filterLocation.toLowerCase()))) &&
        (filterRole === "" || (app.jobTitle && app.jobTitle.toLowerCase().includes(filterRole.toLowerCase())))
    )
    .sort((a, b) =>
      sortBy === "recent"
        ? new Date(b.createdAt || b.appliedAt || 0) - new Date(a.createdAt || a.appliedAt || 0)
        : (a.jobTitle || "").localeCompare(b.jobTitle || "")
    );

  const filteredEligibleJobs = eligibleJobs.filter((job) =>
    (job.title || "").toLowerCase().includes((filterRole || "").toLowerCase()) &&
    (job.location || "").toLowerCase().includes((filterLocation || "").toLowerCase())
  );

  // Analytics calculations
  const totalApplications = applications.length;
  const pendingApplications = applications.filter(app => !app.status || app.status === 'applied').length;
  const interviewInvites = applications.filter(app => 
    app.canTakeInterview || app.screeningInviteSent || app.status === 'screening'
  ).length;
  const completedInterviews = applications.filter(app => 
    app.interviewStatus === 'completed'
  ).length;

  const getApplicationStatus = (app) => {
    if (app.status === 'hired') return { label: 'Hired', color: 'emerald', icon: '🎉' };
    if (app.status === 'rejected') return { label: 'Rejected', color: 'red', icon: '❌' };
    if (app.status === 'disqualified' || app.interviewStatus === 'disqualified') return { label: 'Disqualified', color: 'red', icon: '⛔' };
    if (app.interviewStatus === 'completed') return { label: 'Interview Complete', color: 'blue', icon: '✅' };
    if (app.canTakeInterview || app.screeningInviteSent || app.status === 'screening') return { label: 'Interview Ready', color: 'green', icon: '🎯' };
    if (app.interviewStatus === 'in_progress') return { label: 'Interview in Progress', color: 'yellow', icon: '⏳' };
    return { label: 'Under Review', color: 'gray', icon: '📋' };
  };

  const getScoreColor = (score) => {
    if (score >= 80) return 'from-green-500 to-emerald-600';
    if (score >= 60) return 'from-yellow-500 to-orange-500';
    return 'from-red-500 to-pink-600';
  };

  // Component for metric cards
  const MetricCard = ({ title, value, subtitle, icon, gradient, delay = 0 }) => (
    <div 
      className={`relative overflow-hidden rounded-2xl bg-white/50 backdrop-blur-xl border border-white/60 p-6 transform transition-all duration-500 hover:scale-105 hover:shadow-2xl cursor-pointer animate-fade-in-up`}
      style={{ 
        animationDelay: `${delay}ms`,
        boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)'
      }}
      onMouseEnter={() => setHoveredCard(title)}
      onMouseLeave={() => setHoveredCard(null)}
    >
      <div 
        className="absolute top-0 left-0 right-0 h-2 rounded-t-xl"
        style={{ background: gradient }}
      ></div>
      
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-400/30 to-purple-400/30 transform rotate-12 translate-x-full hover:translate-x-0 transition-transform duration-1000"></div>
      </div>
      
      <div className="relative z-10">
        <div className="flex items-center justify-between mb-4">
          <div className="text-slate-700 text-sm font-medium">{title}</div>
          <div className="text-2xl">{icon}</div>
        </div>
        <div className="text-3xl font-bold text-slate-900 mb-2">{value}</div>
        {subtitle && <div className="text-slate-600 text-sm">{subtitle}</div>}
      </div>
      
      {hoveredCard === title && (
        <div className="absolute inset-0 bg-gradient-to-r from-blue-100/40 to-purple-100/40 rounded-2xl"></div>
      )}
    </div>
  );

  // Component for application cards
  const ApplicationCard = ({ app, index }) => {
    const status = getApplicationStatus(app);
    const resumeScore = app.scanResult?.compare?.match_score || 0;
    const testScore = app.testScore || 0;
    const totalScore = resumeScore + testScore;

    return (
      <div 
        className="relative overflow-hidden rounded-2xl bg-white/60 backdrop-blur-md border border-white/40 p-6 hover:shadow-xl transition-all duration-500 animate-fade-in-up group"
        style={{ 
          animationDelay: `${index * 100}ms`,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)'
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/20 via-purple-50/20 to-pink-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl pointer-events-none"></div>
        
        <div className="relative z-10">
          <div className="flex justify-between items-start mb-4">
            <div className="flex-1">
              <h3 className="text-xl font-semibold text-slate-800 mb-2">
                {app.jobTitle || "Job Title Not Available"}
                {app.jobId && (
                  <span className="ml-2 text-sm text-blue-600 bg-blue-100/60 px-2 py-1 rounded-full font-medium">
                    {app.jobId}
                  </span>
                )}
              </h3>
              <div className="space-y-1 text-sm text-slate-600">
                <p className="flex items-center">
                  <span className="w-6">🏢</span>
                  {app.companyName || "Company Not Available"}
                </p>
                <p className="flex items-center">
                  <span className="w-6">📍</span>
                  {app.location || "Location Not Available"}
                </p>
                <p className="flex items-center">
                  <span className="w-6">📅</span>
                  Applied: {new Date(app.createdAt || app.appliedAt).toLocaleDateString()}
                </p>
              </div>
            </div>
            
            {/* Score circle */}
            {totalScore > 0 && (
              <div className="relative">
                <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${getScoreColor(totalScore)} flex items-center justify-center text-white font-bold text-lg shadow-lg`}>
                  {totalScore}
                </div>
                <div className="absolute -bottom-2 -right-2 bg-white rounded-full p-1 shadow-lg">
                  <div className="text-xs text-slate-600 font-medium px-2 py-1">
                    {totalScore >= 80 ? '🎯' : totalScore >= 60 ? '⚡' : '📊'}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Status badge */}
          <div className="mb-4">
            <span className={`inline-flex items-center px-3 py-1 text-sm font-medium rounded-full bg-${status.color}-100/80 text-${status.color}-700 backdrop-blur-sm`}>
              <span className="mr-2">{status.icon}</span>
              {status.label}
            </span>
          </div>

          {/* Score breakdown */}
          {totalScore > 0 && (
            <div className="bg-slate-50/60 rounded-xl p-4 mb-4">
              <h4 className="font-medium text-slate-800 mb-2">Score Breakdown</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-slate-600">Resume Score</p>
                  <p className="font-semibold text-slate-800">{resumeScore}</p>
                </div>
                <div>
                  <p className="text-slate-600">Test Score</p>
                  <p className="font-semibold text-slate-800">{testScore || 'Not taken'}</p>
                </div>
              </div>
            </div>
          )}

          {/* Action buttons */}
          <div className="flex flex-wrap gap-2">
            {/* Take Interview button */}
            {((app.scanResult && app.scanResult.canTakeInterview) || 
              app.canTakeInterview === true || 
              app.screeningInviteSent === true || 
              app.status === 'screening') && (
              <button 
                onClick={() => handleTakeInterview(app._id)} 
                disabled={isLaunching} 
                className="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-200 text-sm font-medium shadow-lg disabled:from-gray-400 disabled:to-gray-500"
              >
                {isLaunching ? "🚀 Launching..." : "🎯 Take Interview"}
              </button>
            )}
            
            <Link href={`/candidate/${app.jobId}`}>
              <button className="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 text-sm font-medium shadow-lg">
                📋 View Details
              </button>
            </Link>
          </div>

          {/* Status message for pending applications */}
          {!((app.scanResult && app.scanResult.canTakeInterview) || 
            app.canTakeInterview === true || 
            app.screeningInviteSent === true || 
            app.status === 'screening') && 
            status.label === 'Under Review' && (
            <div className="mt-4 p-3 bg-blue-50/60 rounded-lg border border-blue-200/60">
              <p className="text-sm text-blue-700 flex items-center">
                <span className="mr-2">ℹ️</span>
                Your application is being reviewed. You'll be notified if selected for an interview.
              </p>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Component for job cards
  const JobCard = ({ job, index }) => (
    <div 
      className="relative overflow-hidden rounded-2xl bg-white/60 backdrop-blur-md border border-white/40 p-6 hover:shadow-xl transition-all duration-500 animate-fade-in-up group"
      style={{ 
        animationDelay: `${index * 100}ms`,
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)'
      }}
    >
      <div className="absolute inset-0 bg-gradient-to-r from-green-50/20 via-blue-50/20 to-purple-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl pointer-events-none"></div>
      
      <div className="relative z-10">
        <div className="mb-4">
          <h3 className="text-xl font-semibold text-slate-800 mb-2 flex items-center">
            {job.title}
            {job.jobId && (
              <span className="ml-2 text-sm text-blue-600 bg-blue-100/60 px-2 py-1 rounded-full font-medium">
                {job.jobId}
              </span>
            )}
          </h3>
          <div className="space-y-1 text-sm text-slate-600">
            <p className="flex items-center">
              <span className="w-6">🏢</span>
              {job.company}
            </p>
            <p className="flex items-center">
              <span className="w-6">📍</span>
              {job.location}
            </p>
            <p className="flex items-center">
              <span className="w-6">💼</span>
              {job.experience} experience
            </p>
            {job.applicationDeadline && (
              <p className="flex items-center text-orange-600">
                <span className="w-6">⏰</span>
                Deadline: {new Date(job.applicationDeadline).toLocaleDateString()}
              </p>
            )}
          </div>
        </div>

        <div className="flex justify-between items-center">
          <Link href={`/candidate/${job._id}?tab=browse`}>
            <button className="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 text-sm font-medium shadow-lg">
              📋 View Details
            </button>
          </Link>
          
          <Link href={`/candidate/apply/${job._id}`}>
            <button className="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-200 text-sm font-medium shadow-lg">
              📝 Apply Now
            </button>
          </Link>
        </div>
      </div>
    </div>
  );

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    router.push(`/candidate-dashboard?tab=${tab}`, undefined, { shallow: true });
  };

  const renderOverview = () => {
    if (loading) {
      return (
        <div className="text-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-slate-600">Loading your dashboard...</p>
        </div>
      );
    }

    return (
      <div className="space-y-8">
        {/* Metrics Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MetricCard
            title="Total Applications"
            value={totalApplications}
            subtitle="Applications submitted"
            icon="📊"
            gradient="linear-gradient(135deg, #3B82F6, #1D4ED8)"
            delay={0}
          />
          <MetricCard
            title="Interview Invites"
            value={interviewInvites}
            subtitle="Ready for screening"
            icon="🎯"
            gradient="linear-gradient(135deg, #10B981, #059669)"
            delay={100}
          />
          <MetricCard
            title="Interviews Done"
            value={completedInterviews}
            subtitle="Completed interviews"
            icon="✅"
            gradient="linear-gradient(135deg, #8B5CF6, #7C3AED)"
            delay={200}
          />
          <MetricCard
            title="Pending Review"
            value={pendingApplications}
            subtitle="Under review"
            icon="⏳"
            gradient="linear-gradient(135deg, #F59E0B, #D97706)"
            delay={300}
          />
        </div>

        {/* Recent Applications */}
        <div className="animate-fade-in-up" style={{animationDelay: '400ms'}}>
          <h3 className="text-2xl font-bold text-slate-800 mb-6">Recent Applications</h3>
          {applications.length === 0 ? (
            <div className="text-center py-12 bg-white/60 backdrop-blur-md border border-white/40 rounded-2xl shadow-lg">
              <div className="text-6xl mb-4">📭</div>
              <h4 className="text-xl font-semibold text-slate-800 mb-2">No Applications Yet</h4>
              <p className="text-slate-600 mb-4">Start your career journey by applying to jobs!</p>
              <button 
                onClick={() => handleTabChange("browse")} 
                className="px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 font-medium shadow-lg"
              >
                🚀 Browse Jobs
              </button>
            </div>
          ) : (
            <div className="grid gap-6">
              {applications.slice(0, 3).map((app, index) => (
                <ApplicationCard key={app._id} app={app} index={index} />
              ))}
              {applications.length > 3 && (
                <div className="text-center">
                  <button 
                    onClick={() => handleTabChange("applications")}
                    className="px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium shadow-lg"
                  >
                    View All Applications ({applications.length})
                  </button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Recommended Jobs */}
        <div className="animate-fade-in-up" style={{animationDelay: '500ms'}}>
          <h3 className="text-2xl font-bold text-slate-800 mb-6">Recommended Jobs</h3>
          {eligibleJobs.length === 0 ? (
            <div className="text-center py-12 bg-white/60 backdrop-blur-md border border-white/40 rounded-2xl shadow-lg">
              <div className="text-6xl mb-4">🔍</div>
              <h4 className="text-xl font-semibold text-slate-800 mb-2">No New Jobs Available</h4>
              <p className="text-slate-600">Check back later for new opportunities!</p>
            </div>
          ) : (
            <div className="grid gap-6">
              {eligibleJobs.slice(0, 3).map((job, index) => (
                <JobCard key={job._id} job={job} index={index} />
              ))}
              {eligibleJobs.length > 3 && (
                <div className="text-center">
                  <button 
                    onClick={() => handleTabChange("browse")}
                    className="px-6 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 font-medium shadow-lg"
                  >
                    View All Jobs ({eligibleJobs.length})
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderApplications = () => {
    if (loading) {
      return (
        <div className="text-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-slate-600">Loading your applications...</p>
        </div>
      );
    }

    if (error) {
      return (
        <div className="text-center py-12 bg-red-50/60 backdrop-blur-md border border-red-200/40 rounded-2xl shadow-lg">
          <div className="text-6xl mb-4">⚠️</div>
          <h4 className="text-xl font-semibold text-red-800 mb-2">Error Loading Applications</h4>
          <p className="text-red-600">{error}</p>
        </div>
      );
    }

    if (applications.length === 0) {
      return (
        <div className="text-center py-12 bg-white/60 backdrop-blur-md border border-white/40 rounded-2xl shadow-lg">
          <div className="text-6xl mb-4">📭</div>
          <h4 className="text-xl font-semibold text-slate-800 mb-2">No Applications Yet</h4>
          <p className="text-slate-600 mb-4">You haven't applied to any jobs yet.</p>
          <button 
            onClick={() => handleTabChange("browse")} 
            className="px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 font-medium shadow-lg"
          >
            🚀 Browse Available Jobs
          </button>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {/* Filters */}
        <div className="bg-white/50 backdrop-blur-xl border border-white/60 rounded-2xl shadow-xl p-6">
          <h3 className="text-lg font-semibold mb-4 text-slate-900">🔍 Filter Applications</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <input 
              type="text" 
              placeholder="🔍 Search Job Title" 
              className="bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-2 text-slate-900 placeholder-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg" 
              value={searchQuery} 
              onChange={(e) => setSearchQuery(e.target.value)} 
            />
            <input 
              type="text" 
              placeholder="📍 Location" 
              className="bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-2 text-slate-900 placeholder-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg" 
              value={filterLocation} 
              onChange={(e) => setFilterLocation(e.target.value)} 
            />
            <input 
              type="text" 
              placeholder="💼 Role" 
              className="bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-2 text-slate-900 placeholder-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg" 
              value={filterRole} 
              onChange={(e) => setFilterRole(e.target.value)} 
            />
            <select 
              className="bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-2 text-slate-900 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg" 
              value={sortBy} 
              onChange={(e) => setSortBy(e.target.value)}
            >
              <option value="recent">📅 Sort by Recent</option>
              <option value="title">🔤 Sort by Title</option>
            </select>
          </div>
        </div>

        {/* Applications Grid */}
        <div className="grid gap-6">
          {filteredApplications.map((app, index) => (
            <ApplicationCard key={app._id} app={app} index={index} />
          ))}
        </div>
      </div>
    );
  };

  const renderBrowseJobs = () => {
    if (loading) {
      return (
        <div className="text-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-slate-600">Loading available jobs...</p>
        </div>
      );
    }
    
    return (
      <div className="space-y-6">
        {/* Filters */}
        <div className="bg-white/50 backdrop-blur-xl border border-white/60 rounded-2xl shadow-xl p-6">
          <h3 className="text-lg font-semibold mb-4 text-slate-900">🔍 Find Your Perfect Job</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <input 
              type="text" 
              placeholder="💼 Role" 
              className="bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-2 text-slate-900 placeholder-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg" 
              value={filterRole} 
              onChange={(e) => setFilterRole(e.target.value)} 
            />
            <input 
              type="text" 
              placeholder="📍 Location" 
              className="bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-2 text-slate-900 placeholder-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg" 
              value={filterLocation} 
              onChange={(e) => setFilterLocation(e.target.value)} 
            />
            <input 
              type="text" 
              placeholder="💰 Salary Range" 
              className="bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-2 text-slate-900 placeholder-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg" 
            />
            <select className="bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-2 text-slate-900 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg">
              <option>📈 Experience Level</option>
              <option>🌟 Entry Level</option>
              <option>⚡ Mid Level</option>
              <option>🚀 Senior Level</option>
            </select>
            <button className="px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium shadow-lg">
              🔍 Search
            </button>
          </div>
        </div>

        {/* Jobs Grid */}
        {filteredEligibleJobs.length === 0 ? (
          <div className="text-center py-12 bg-white/60 backdrop-blur-md border border-white/40 rounded-2xl shadow-lg">
            <div className="text-6xl mb-4">🔍</div>
            <h4 className="text-xl font-semibold text-slate-800 mb-2">No Jobs Found</h4>
            <p className="text-slate-600">
              {eligibleJobs.length === 0 ? 
                "No new jobs available at the moment. Check back later!" : 
                "No jobs matching your search criteria. Try adjusting your filters."}
            </p>
          </div>
        ) : (
          <div className="grid gap-6">
            {filteredEligibleJobs.map((job, index) => (
              <JobCard key={job._id} job={job} index={index} />
            ))}
          </div>
        )}
      </div>
    );
  };

  const renderProfile = () => (
    <div className="bg-white/50 backdrop-blur-xl border border-white/60 rounded-2xl shadow-xl p-8 animate-fade-in-up">
      <h3 className="text-2xl font-bold text-slate-800 mb-8 flex items-center">
        👤 My Profile
      </h3>
      
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">Full Name</label>
            <input 
              type="text" 
              className="bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-3 w-full text-slate-900 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg" 
              placeholder="Enter your full name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">Email</label>
            <input 
              type="email" 
              value={session?.user?.email || ''} 
              readOnly 
              className="bg-gray-100/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-3 w-full text-slate-900 shadow-lg" 
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">Phone</label>
            <input 
              type="text" 
              className="bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-3 w-full text-slate-900 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg" 
              placeholder="Your phone number"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">Experience</label>
            <input 
              type="text" 
              className="bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-3 w-full text-slate-900 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg" 
              placeholder="Years of experience"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">Preferred Role</label>
            <input 
              type="text" 
              className="bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-3 w-full text-slate-900 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg" 
              placeholder="Your preferred job role"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">Preferred Location</label>
            <input 
              type="text" 
              className="bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-3 w-full text-slate-900 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg" 
              placeholder="Your preferred work location"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">Salary Expectation</label>
            <input 
              type="text" 
              className="bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-3 w-full text-slate-900 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg" 
              placeholder="Expected salary range"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">Resume</label>
            <input 
              type="file" 
              className="bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-3 w-full text-slate-900 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg" 
            />
          </div>
        </div>
        
        {/* Skills Section */}
        <div className="border-t border-white/30 pt-6">
          <h4 className="text-lg font-semibold text-slate-800 mb-4 flex items-center">
            🛠️ Skills
          </h4>
          <div className="space-y-3">
            {skills.map((skill, index) => (
              <input 
                key={index} 
                type="text" 
                value={skill} 
                onChange={(e) => updateSkill(index, e.target.value)} 
                className="bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-3 w-full text-slate-900 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg" 
                placeholder="Enter a skill" 
              />
            ))}
            <button 
              onClick={addSkill} 
              className="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium shadow-lg"
            >
              ➕ Add Skill
            </button>
          </div>
        </div>
        
        <div className="flex justify-end pt-6">
          <button 
            onClick={() => showToastMessage("Profile saved successfully! 🎉")}
            className="px-8 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 font-medium shadow-lg"
          >
            💾 Save Profile
          </button>
        </div>
      </div>
    </div>
  );

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-200 via-blue-100 to-purple-200 flex items-center justify-center">
        <div className="text-slate-800 text-xl flex items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
          Loading your dashboard...
        </div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-200 via-blue-100 to-purple-200 flex items-center justify-center">
        <div className="text-slate-800 text-xl">Please sign in to view your dashboard.</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-200 via-blue-100 to-purple-200">
      {/* Animated background elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-400/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-400/30 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/2 left-1/2 w-60 h-60 bg-pink-400/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '4s'}}></div>
      </div>

      {/* Header */}
      <header className="relative z-10 bg-white/40 backdrop-blur-xl border-b border-white/60 shadow-xl">
        <div className="max-w-7xl mx-auto p-6 flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-700 via-purple-600 to-pink-600 bg-clip-text text-transparent">
              Career Dashboard
            </h1>
            <p className="text-slate-700 mt-1">Welcome back, {session?.user?.name || 'Candidate'}! Track your job search progress</p>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center font-bold text-white shadow-xl">
              {session?.user?.name?.[0] || 'C'}
            </div>
          </div>
        </div>
      </header>

      <div className="relative z-10 max-w-7xl mx-auto p-6">
        {/* Toast Message */}
        {showToast && (
          <div className="fixed top-6 right-6 z-50 bg-white/80 backdrop-blur-md border border-white/40 text-slate-800 px-6 py-3 rounded-xl shadow-xl animate-fade-in-up">
            {toastMessage}
          </div>
        )}

        {/* Navigation Tabs */}
        <div className="flex items-center space-x-1 bg-white/50 backdrop-blur-xl rounded-2xl p-2 mb-8 w-fit mx-auto shadow-xl animate-fade-in-up">
          {[
            { id: 'overview', label: '🏠 Overview', },
            { id: 'applications', label: '📋 My Applications' },
            { id: 'browse', label: '🔍 Browse Jobs' },
            { id: 'profile', label: '👤 Profile' }
          ].map((tab) => (
            <button 
              key={tab.id}
              onClick={() => handleTabChange(tab.id)} 
              className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
                activeTab === tab.id 
                  ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg' 
                  : 'text-slate-600 hover:bg-white/70'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="animate-fade-in-up" style={{animationDelay: '200ms'}}>
          {activeTab === "overview" && renderOverview()}
          {activeTab === "applications" && renderApplications()}
          {activeTab === "browse" && renderBrowseJobs()}
          {activeTab === "profile" && renderProfile()}
        </div>
      </div>

      <style jsx>{`
        @keyframes fade-in-up {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-fade-in-up {
          animation: fade-in-up 0.6s ease-out forwards;
          opacity: 0;
        }
      `}</style>
    </div>
  );
}
