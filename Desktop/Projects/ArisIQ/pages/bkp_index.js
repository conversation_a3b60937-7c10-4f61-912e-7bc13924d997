// File: pages/index.js

import { useRouter } from 'next/router';
import { signIn, signOut, useSession } from 'next-auth/react';
import { useState, useEffect } from 'react';

export default function Home() {
  const router = useRouter();
  const { data: session, status } = useSession();
  
  // UI States
  const [isMounted, setIsMounted] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  
  // Animation effects
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [currentFlowStep, setCurrentFlowStep] = useState(0);
  
  // Auth form states
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showEmployerModal, setShowEmployerModal] = useState(false);
  const [isLogin, setIsLogin] = useState(true);
  const [userType, setUserType] = useState('candidate');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isEmployerLogin, setIsEmployerLogin] = useState(false);
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    company: '',
  });

  const [employerData, setEmployerData] = useState({
    companyName: '',
    location: '',
    industry: '',
    website: '',
    description: '',
    contactName: '',
    businessEmail: '',
    password: '',
  });

  // Hiring flow animation steps
  const flowSteps = [
    {
      title: "Recruiter Posts a Job",
      icon: "🏢",
      description: "Company creates job posting with requirements",
      color: "from-blue-500 to-cyan-600"
    },
    {
      title: "Candidate Applies",
      icon: "👤",
      description: "Job seekers submit applications with resumes",
      color: "from-cyan-500 to-teal-600"
    },
    {
      title: "AI Resume Scanning",
      icon: "🤖",
      description: "Automatic resume analysis and scoring",
      color: "from-teal-500 to-blue-600"
    },
    {
      title: "Screening Test Sent",
      icon: "📝",
      description: "High-scoring candidates receive screening tests",
      color: "from-blue-500 to-indigo-600"
    },
    {
      title: "Secure AI Interview",
      icon: "🛡️",
      description: "Protected interview with real-time cheat detection and analysis",
      color: "from-indigo-500 to-purple-600"
    },
    {
      title: "Final Selection",
      icon: "⭐",
      description: "Recruiter picks top candidates for final interview",
      color: "from-teal-500 to-cyan-600"
    }
  ];

  const stats = [
    { value: "Growing", label: "User Base", icon: "👥" },
    { value: "Smart", label: "AI Matching", icon: "🎯" },
    { value: "Faster", label: "Hiring Process", icon: "⏱️" },
    { value: "Trusted", label: "Platform", icon: "🏢" }
  ];

  // Mouse tracking effect and mounting
  useEffect(() => {
    setIsMounted(true);
    
    const handleMouseMove = (e) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Flow animation
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFlowStep((prev) => (prev + 1) % flowSteps.length);
    }, 3000);
    return () => clearInterval(interval);
  }, [flowSteps.length]);

  // Auto-redirect users based on their session
  useEffect(() => {
    if (session?.user) {
      const userType = session.user.userType;
      const currentPath = router.pathname;
      
      if (currentPath === '/' && !router.query.modal && !router.query.view) {
        if (userType === 'recruiter') {
          router.push('/recruiter/dashboard');
        } else if (userType === 'candidate') {
          router.push('/candidate-dashboard');
        }
      }
    }
  }, [session, router]);

  // Handle modal opening from URL parameters
  useEffect(() => {
    const { modal, view } = router.query;
    
    if (modal === 'signin') {
      setUserType('candidate');
      setShowAuthModal(true);
      router.replace('/', undefined, { shallow: true });
    } else if (modal === 'employer') {
      setShowEmployerModal(true);
      setIsEmployerLogin(false);
      router.replace('/', undefined, { shallow: true });
    } else if (view === 'home') {
      router.replace('/', undefined, { shallow: true });
    }
  }, [router.query, router]);

  // Form handlers
  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    setError('');
  };

  const handleEmployerInputChange = (e) => {
    setEmployerData({
      ...employerData,
      [e.target.name]: e.target.value
    });
    setError('');
  };

  const validateBusinessEmail = (email) => {
    const personalDomains = [
      'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 
      'aol.com', 'icloud.com', 'protonmail.com', 'live.com'
    ];
    
    const domain = email.split('@')[1]?.toLowerCase();
    return !personalDomains.includes(domain);
  };

  const handleEmployerSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    if (!isEmployerLogin && !validateBusinessEmail(employerData.businessEmail)) {
      setError('Please use a business email address. Personal emails (Gmail, Yahoo, etc.) are not allowed for employer accounts.');
      setLoading(false);
      return;
    }

    try {
      if (isEmployerLogin) {
        const result = await signIn('credentials', {
          email: employerData.businessEmail,
          password: employerData.password,
          redirect: false,
        });

        if (result?.error) {
          setError(result.error);
        } else {
          setShowEmployerModal(false);
          router.push('/recruiter/dashboard');
        }
      } else {
        const response = await fetch('/api/auth/register-employer', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: employerData.contactName,
            email: employerData.businessEmail,
            password: employerData.password,
            userType: 'recruiter',
            company: employerData.companyName,
            location: employerData.location,
            industry: employerData.industry,
            website: employerData.website,
            description: employerData.description,
          }),
        });

        const data = await response.json();

        if (!response.ok) {
          setError(data.error);
        } else {
          const result = await signIn('credentials', {
            email: employerData.businessEmail,
            password: employerData.password,
            redirect: false,
          });

          if (!result?.error) {
            setShowEmployerModal(false);
            router.push('/recruiter/dashboard');
          } else {
            alert('Employer account created successfully! Please sign in.');
            setIsEmployerLogin(true);
          }
        }
      }
    } catch (error) {
      setError('Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAuthSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      if (isLogin) {
        const result = await signIn('credentials', {
          email: formData.email,
          password: formData.password,
          redirect: false,
        });

        if (result?.error) {
          setError(result.error);
        } else {
          setShowAuthModal(false);
          window.location.reload();
        }
      } else {
        const response = await fetch('/api/auth/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...formData,
            userType,
          }),
        });

        const data = await response.json();

        if (!response.ok) {
          setError(data.error);
        } else {
          const result = await signIn('credentials', {
            email: formData.email,
            password: formData.password,
            redirect: false,
          });

          if (!result?.error) {
            setShowAuthModal(false);
            if (userType === 'recruiter') {
              router.push('/recruiter/dashboard');
            } else {
              router.push('/candidate-dashboard');
            }
          }
        }
      }
    } catch (error) {
      setError('Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCandidate = () => {
    if (!session) {
      setUserType('candidate');
      setShowAuthModal(true);
    } else {
      if (session.user?.userType === 'recruiter') {
        router.push('/recruiter/dashboard');
      } else {
        router.push('/candidate-dashboard');
      }
    }
  };

  const handleRecruiter = () => {
    if (!session) {
      setShowEmployerModal(true);
      setIsEmployerLogin(false);
    } else {
      if (session.user?.userType === 'recruiter') {
        router.push('/recruiter/dashboard');
      } else {
        router.push('/candidate-dashboard');
      }
    }
  };

  const handleGoogleSignIn = async (type) => {
    const callbackUrl = type === 'recruiter' ? '/recruiter/dashboard' : '/candidate-dashboard';
    await signIn('google', { callbackUrl });
  };

  // Don't render until mounted to prevent hydration mismatch
  if (!isMounted) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-teal-200 via-cyan-200 via-blue-200 via-purple-200 to-pink-200 text-gray-800 overflow-hidden backdrop-blur-sm relative">
      {/* Enhanced animated background elements with more vibrant colors */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-96 h-96 bg-gradient-to-br from-purple-400/40 to-pink-400/40 rounded-full blur-3xl animate-pulse shadow-2xl"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-br from-blue-400/40 to-cyan-400/40 rounded-full blur-3xl animate-pulse shadow-2xl" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/3 left-1/3 w-80 h-80 bg-gradient-to-br from-indigo-400/30 to-purple-400/30 rounded-full blur-3xl animate-pulse shadow-xl" style={{animationDelay: '4s'}}></div>
        <div className="absolute bottom-1/3 right-1/3 w-72 h-72 bg-gradient-to-br from-teal-400/30 to-blue-400/30 rounded-full blur-3xl animate-pulse shadow-xl" style={{animationDelay: '6s'}}></div>
        
        {/* Enhanced mouse follower effect */}
        <div 
          className="absolute w-96 h-96 bg-gradient-to-r from-purple-300/20 via-blue-300/20 to-cyan-300/20 rounded-full blur-3xl transition-all duration-300 ease-out shadow-2xl"
          style={{
            left: mousePosition.x - 192,
            top: mousePosition.y - 192,
          }}
        ></div>
        
        {/* Enhanced glossy overlay with rainbow gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/30 via-purple-100/20 via-blue-100/20 via-cyan-100/20 to-white/20 backdrop-blur-[2px]"></div>
        
        {/* Additional floating orbs */}
        <div className="absolute top-1/4 right-1/4 w-32 h-32 bg-gradient-to-br from-yellow-300/20 to-orange-300/20 rounded-full blur-2xl animate-pulse shadow-lg" style={{animationDelay: '1s'}}></div>
        <div className="absolute bottom-1/4 left-1/4 w-40 h-40 bg-gradient-to-br from-green-300/20 to-emerald-300/20 rounded-full blur-2xl animate-pulse shadow-lg" style={{animationDelay: '3s'}}></div>
      </div>

      {/* Enhanced Navigation Header with glossy effect */}
      <header className="relative z-50">
        <div className="w-full px-6">
          <div className="flex justify-between items-start h-52 pt-6">
            {/* Left side - Navigation with enhanced styling */}
            <div className="flex items-center space-x-6">
              <div className="bg-gradient-to-r from-white/70 to-blue-50/70 backdrop-blur-md border border-white/50 rounded-xl px-6 py-3 text-black hover:text-blue-700 hover:bg-white/80 transition-all duration-300 font-semibold cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-105">
                Home
              </div>
              
              {session && (
                <button
                  onClick={() => {
                    const userType = session.user?.userType;
                    if (userType === 'recruiter') {
                      router.push('/recruiter/dashboard');
                    } else {
                      router.push('/candidate-dashboard');
                    }
                  }}
                  className="bg-gradient-to-r from-white/70 to-purple-50/70 backdrop-blur-md border border-white/50 rounded-xl px-6 py-3 text-black hover:text-purple-700 hover:bg-white/80 transition-all duration-300 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  Dashboard
                </button>
              )}
              
              {session?.user?.userType === 'recruiter' && (
                <button
                  onClick={() => router.push('/recruiter/post-job')}
                  className="bg-gradient-to-r from-white/70 to-cyan-50/70 backdrop-blur-md border border-white/50 rounded-xl px-6 py-3 text-black hover:text-cyan-700 hover:bg-white/80 transition-all duration-300 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  Post a Job
                </button>
              )}
            </div>

            {/* Center - Large Logo */}
            <div className="absolute left-1/2 transform -translate-x-1/2">
              <img 
                src="/ArisIQ logo.png" 
                alt="ArisIQ - AI Powered Recruitment Platform" 
                className="h-48 w-auto drop-shadow-2xl hover:scale-110 transition-transform duration-500 filter brightness-110"
              />
            </div>

            {/* Right side - Enhanced Auth buttons */}
            <div className="flex items-center space-x-4">
              {session ? (
                /* Logged in user menu */
                <div className="relative">
                  <button
                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                    className="flex items-center space-x-3 text-black hover:text-blue-700 transition-colors bg-gradient-to-r from-white/70 to-blue-50/70 backdrop-blur-md border border-white/50 rounded-xl px-4 py-2 shadow-lg hover:shadow-xl transform hover:scale-105"
                  >
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center shadow-md">
                      <span className="text-white font-bold text-sm">
                        {session.user.name ? session.user.name.charAt(0).toUpperCase() : 'U'}
                      </span>
                    </div>
                    <span className="hidden md:block font-semibold text-black">
                      {session.user.name || session.user.email}
                    </span>
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>

                  {/* Enhanced Dropdown Menu */}
                  {isDropdownOpen && (
                    <div className="absolute right-0 mt-2 w-64 bg-gradient-to-br from-white/95 to-blue-50/95 backdrop-blur-xl rounded-2xl shadow-2xl ring-1 ring-white/50 z-50 border border-white/60 overflow-hidden">
                      <div className="py-2">
                        <div className="px-6 py-4 text-sm text-black border-b border-white/30 bg-gradient-to-r from-blue-50/50 to-purple-50/50">
                          <div className="font-bold text-lg">{session.user.name}</div>
                          <div className="text-gray-700">{session.user.email}</div>
                          <div className="text-sm text-blue-700 capitalize font-semibold">{session.user.userType}</div>
                        </div>
                        
                        <button
                          onClick={() => {
                            setIsDropdownOpen(false);
                          }}
                          className="block w-full text-left px-6 py-3 text-sm text-black hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-200 font-medium"
                        >
                          🏠 Home
                        </button>
                        
                        <button
                          onClick={() => {
                            const userType = session.user?.userType;
                            if (userType === 'recruiter') {
                              router.push('/recruiter/dashboard');
                            } else {
                              router.push('/candidate-dashboard');
                            }
                            setIsDropdownOpen(false);
                          }}
                          className="block w-full text-left px-6 py-3 text-sm text-black hover:bg-gradient-to-r hover:from-purple-50 hover:to-cyan-50 transition-all duration-200 font-medium"
                        >
                          📊 Dashboard
                        </button>
                        
                        {session?.user?.userType === 'recruiter' && (
                          <button
                            onClick={() => {
                              router.push('/recruiter/post-job');
                              setIsDropdownOpen(false);
                            }}
                            className="block w-full text-left px-6 py-3 text-sm text-black hover:bg-gradient-to-r hover:from-cyan-50 hover:to-teal-50 transition-all duration-200 font-medium"
                          >
                            ✏️ Post a Job
                          </button>
                        )}
                        
                        <div className="border-t border-white/30 mt-2">
                          <button
                            onClick={async () => {
                              await signOut({ callbackUrl: '/' });
                            }}
                            className="block w-full text-left px-6 py-3 text-sm text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 font-medium"
                          >
                            🚪 Sign Out
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                /* Enhanced Not logged in - show auth buttons */
                <>
                  <button
                    onClick={handleCandidate}
                    className="bg-gradient-to-r from-white/80 to-blue-100/80 backdrop-blur-md border border-white/60 rounded-xl px-6 py-3 text-black hover:text-blue-800 hover:bg-white/90 transition-all duration-300 font-bold shadow-lg hover:shadow-xl transform hover:scale-105"
                  >
                    I'm looking for a job
                  </button>
                  <button
                    onClick={handleRecruiter}
                    className="bg-gradient-to-r from-white/80 to-purple-100/80 backdrop-blur-md border border-white/60 rounded-xl px-6 py-3 text-black hover:text-purple-800 hover:bg-white/90 transition-all duration-300 font-bold shadow-lg hover:shadow-xl transform hover:scale-105"
                  >
                    I'm hiring talent
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative z-10 pt-8 pb-32 px-6">
        <div className="max-w-7xl mx-auto">
          {/* Main Content */}
          <div className="text-center">
            <div className="opacity-0 animate-fade-in-up">
              <h1 className="text-6xl md:text-8xl font-bold mb-8 leading-tight">
                <span className="bg-gradient-to-r from-blue-700 via-purple-600 to-cyan-600 bg-clip-text text-transparent">
                  Future of
                </span>
                <br />
                <span className="text-gray-900 font-black">Hiring is Here</span>
              </h1>
              
              <p className="text-xl md:text-2xl text-gray-800 mb-12 max-w-3xl mx-auto opacity-0 animate-fade-in-up font-medium" style={{animationDelay: '200ms'}}>
                Revolutionize your recruitment with AI-powered screening, intelligent matching, and predictive analytics. 
                <span className="text-cyan-700 font-bold bg-gradient-to-r from-cyan-600 to-blue-600 bg-clip-text text-transparent"> Hire faster, more efficiently.</span>
              </p>
            </div>
          </div>

          {/* Enhanced Animated Hiring Flow */}
          <div className="mt-20 relative opacity-0 animate-fade-in-up" style={{animationDelay: '600ms'}}>
            <div className="relative mx-auto max-w-6xl">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-400/40 via-blue-400/40 to-cyan-500/40 rounded-3xl blur-3xl"></div>
              <div 
                className="relative bg-gradient-to-br from-white/80 to-blue-50/80 backdrop-blur-lg rounded-3xl p-10 hover:bg-white/90 transition-all duration-500 shadow-2xl overflow-hidden transform hover:scale-[1.02]"
                style={{
                  backgroundImage: 'url(/ai-recruitment-brain.png)',
                  backgroundSize: 'contain',
                  backgroundPosition: 'center center',
                  backgroundRepeat: 'no-repeat',
                  backgroundOpacity: 0.02
                }}
              >
                {/* Enhanced overlay to ensure text readability */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/90 via-blue-50/85 to-purple-50/85 rounded-3xl"></div>
                
                {/* Flow Title */}
                <div className="text-center mb-10 relative z-10">
                  <h3 className="text-3xl font-bold text-black mb-3 bg-gradient-to-r from-blue-700 to-purple-700 bg-clip-text text-transparent">AI-Powered Hiring Revolution</h3>
                  <p className="text-gray-800 text-lg font-medium">Experience the future of recruitment with real-time analytics</p>
                  <div className="mt-4 flex justify-center space-x-8 text-sm">
                    <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full font-semibold">⚡ 3x Faster</span>
                    <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-semibold">💰 Significant Savings</span>
                    <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full font-semibold">🎯 Smart Matching</span>
                  </div>
                </div>

                {/* Animated Process Cards */}
                <div className="relative z-10 mb-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-3 w-full">
                    {flowSteps.map((step, index) => (
                      <div 
                        key={index} 
                        className={`relative transform transition-all duration-700 ${
                          index === currentFlowStep ? 'scale-110 z-20' : 'scale-100 z-10'
                        }`}
                      >
                        {/* Animated Card */}
                        <div className={`
                          relative overflow-hidden rounded-2xl p-4 text-center transition-all duration-500
                          ${index === currentFlowStep 
                            ? `bg-gradient-to-br ${step.color} text-white shadow-2xl` 
                            : 'bg-white/90 text-gray-700 shadow-md hover:shadow-lg'
                          }
                        `}>
                          {/* Animated Background Pulse */}
                          {index === currentFlowStep && (
                            <div className="absolute inset-0 bg-white/20 rounded-2xl animate-pulse"></div>
                          )}
                          
                          {/* Icon with Animation */}
                          <div className={`text-3xl mb-3 transition-transform duration-300 relative z-10 ${
                            index === currentFlowStep ? 'animate-bounce' : ''
                          }`}>
                            {step.icon}
                          </div>
                          
                          {/* Step Title */}
                          <h4 className="font-bold text-xs mb-2 relative z-10">{step.title}</h4>
                          
                          {/* Step Description */}
                          <p className="text-xs mb-2 relative z-10 opacity-90">{step.description}</p>
                          
                          {/* New: Performance Metrics */}
                          <div className="relative z-10">
                            {index === 0 && <div className="text-xs font-semibold">⏱️ 2 min setup</div>}
                            {index === 1 && <div className="text-xs font-semibold">📊 Real-time tracking</div>}
                            {index === 2 && <div className="text-xs font-semibold">🤖 Smart analysis</div>}
                            {index === 3 && <div className="text-xs font-semibold">⚡ 3 sec analysis</div>}
                            {index === 4 && <div className="text-xs font-semibold">🛡️ Advanced monitoring</div>}
                            {index === 5 && <div className="text-xs font-semibold">⭐ Streamlined process</div>}
                          </div>
                          
                          {/* Active Step Indicator */}
                          {index === currentFlowStep && (
                            <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center animate-pulse relative z-10">
                              <div className="w-3 h-3 bg-white rounded-full"></div>
                            </div>
                          )}
                        </div>
                        
                        {/* Animated Connection Lines */}
                        {index < flowSteps.length - 1 && (
                          <div className="hidden lg:block absolute top-1/2 -right-1.5 w-3 h-0.5 z-5">
                            <div className={`h-full transition-all duration-500 ${
                              index <= currentFlowStep 
                                ? 'bg-gradient-to-r from-cyan-500 to-blue-600 shadow-md' 
                                : 'bg-gray-300'
                            }`}></div>
                            {/* Moving Data Packet */}
                            {index === currentFlowStep && (
                              <div className="absolute top-0 w-2 h-2 bg-cyan-400 rounded-full animate-ping -translate-y-0.5"></div>
                            )}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Enhanced Analytics Dashboard */}
                <div className="relative z-10 bg-gradient-to-r from-white/80 to-blue-50/80 rounded-2xl p-6 mb-6">
                  <div className="text-center mb-4">
                    <h4 className="text-xl font-bold text-gray-800 mb-2">
                      Live Process Analytics
                    </h4>
                    <p className="text-gray-600">Current Step: <span className="font-bold text-blue-600">{flowSteps[currentFlowStep].title}</span></p>
                  </div>
                  
                  {/* Dynamic Progress Bar */}
                  <div className="mb-4">
                    <div className="flex justify-between text-xs text-gray-600 mb-1">
                      <span>Progress</span>
                      <span>{Math.round(((currentFlowStep + 1) / flowSteps.length) * 100)}% Complete</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                      <div 
                        className="bg-gradient-to-r from-cyan-500 via-blue-500 to-purple-500 h-3 rounded-full transition-all duration-1000 relative"
                        style={{ width: `${((currentFlowStep + 1) / flowSteps.length) * 100}%` }}
                      >
                        <div className="absolute inset-0 bg-white/30 animate-pulse rounded-full"></div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Real-time Metrics Grid */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                    <div className="bg-white/60 rounded-xl p-3">
                      <div className="text-2xl font-bold text-blue-600">
                        {currentFlowStep < 2 ? '0' : currentFlowStep < 4 ? '127' : currentFlowStep < 5 ? '89' : '23'}
                      </div>
                      <div className="text-xs text-gray-600">Candidates Processed</div>
                    </div>
                    <div className="bg-white/60 rounded-xl p-3">
                      <div className="text-2xl font-bold text-green-600">
                        {currentFlowStep < 3 ? '0ms' : currentFlowStep < 4 ? '2.3s' : '4.7s'}
                      </div>
                      <div className="text-xs text-gray-600">Avg Processing Time</div>
                    </div>
                    <div className="bg-white/60 rounded-xl p-3">
                      <div className="text-2xl font-bold text-purple-600">
                        {currentFlowStep < 2 ? '--' : currentFlowStep < 4 ? 'Good' : currentFlowStep < 5 ? 'High' : 'Optimized'}
                      </div>
                      <div className="text-xs text-gray-600">Quality Score</div>
                    </div>
                    <div className="bg-white/60 rounded-xl p-3">
                      <div className="text-2xl font-bold text-red-600">
                        {currentFlowStep < 4 ? '0' : currentFlowStep === 4 ? '3' : '0'}
                      </div>
                      <div className="text-xs text-gray-600">Violations Detected</div>
                    </div>
                  </div>
                </div>

                {/* Technology Stack Showcase */}
                <div className="relative z-10 grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="bg-gradient-to-br from-blue-50 to-cyan-50 rounded-xl p-4 text-center">
                    <div className="text-2xl mb-2">🧠</div>
                    <h5 className="font-bold text-gray-800 mb-1">Neural Networks</h5>
                    <p className="text-xs text-gray-600">Advanced ML algorithms for pattern recognition</p>
                  </div>
                  <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-4 text-center">
                    <div className="text-2xl mb-2">👁️</div>
                    <h5 className="font-bold text-gray-800 mb-1">Computer Vision</h5>
                    <p className="text-xs text-gray-600">Real-time facial and behavioral analysis</p>
                  </div>
                  <div className="bg-gradient-to-br from-green-50 to-teal-50 rounded-xl p-4 text-center">
                    <div className="text-2xl mb-2">🔗</div>
                    <h5 className="font-bold text-gray-800 mb-1">API Integration</h5>
                    <p className="text-xs text-gray-600">Connects with 50+ HR platforms seamlessly</p>
                  </div>
                </div>

                {/* ROI Calculator */}
                <div className="relative z-10 bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-200">
                  <div className="text-center">
                    <h4 className="text-lg font-bold text-green-800 mb-3">💰 Your Potential Savings</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                      <div>
                        <div className="text-xl font-bold text-green-700">$50K+</div>
                        <div className="text-xs text-green-600">Per Bad Hire Prevented</div>
                      </div>
                      <div>
                        <div className="text-xl font-bold text-green-700">50%+</div>
                        <div className="text-xs text-green-600">Manual Work Reduced</div>
                      </div>
                      <div>
                        <div className="text-xl font-bold text-green-700">2+ Weeks</div>
                        <div className="text-xs text-green-600">Time to Hire Reduction</div>
                      </div>
                      <div>
                        <div className="text-xl font-bold text-green-700">Significant</div>
                        <div className="text-xs text-green-600">Cost Savings</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Enhanced Flow Legend */}
                <div className="mt-6 flex justify-center space-x-6 text-sm text-black relative z-10 font-medium">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full shadow-sm animate-pulse"></div>
                    <span>Active Process</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-gray-400 rounded-full shadow-sm"></div>
                    <span>Pending</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-1 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full"></div>
                    <span>Data Pipeline</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-yellow-400 rounded-full shadow-sm animate-pulse"></div>
                    <span>Real-time Analytics</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Stats Section */}
      <section className="relative z-10 py-20 px-6 bg-gradient-to-r from-teal-200/60 via-cyan-200/60 via-blue-200/60 to-purple-200/60 backdrop-blur-lg">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div 
                key={index}
                className="text-center group opacity-0 animate-fade-in-up bg-gradient-to-br from-white/80 to-blue-50/80 rounded-2xl p-6 shadow-xl border border-white/60 hover:scale-110 transition-all duration-300"
                style={{animationDelay: `${index * 100}ms`}}
              >
                <div className="text-5xl mb-4 group-hover:scale-125 transition-transform duration-300 filter drop-shadow-lg">
                  {stat.icon}
                </div>
                <div className="text-4xl font-bold bg-gradient-to-r from-blue-700 to-purple-600 bg-clip-text text-transparent mb-2">
                  {stat.value}
                </div>
                <div className="text-black font-bold">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Industry Versatility Showcase */}
      <section className="relative z-10 py-20 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="mt-20 relative">
            <div className="text-center mb-12">
              <h3 className="text-3xl font-bold text-black mb-4 bg-gradient-to-r from-purple-700 to-blue-700 bg-clip-text text-transparent">
                One Platform, Every Industry
              </h3>
              <p className="text-lg text-gray-700 max-w-2xl mx-auto font-medium">
                From healthcare to tech, education to finance - ArisIQ adapts to any industry's unique hiring requirements
              </p>
            </div>

            {/* Animated Industry Cards */}
            <div className="relative bg-gradient-to-br from-white/70 to-purple-50/70 rounded-3xl p-8 shadow-xl">
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4 mb-8">
                {[
                  { icon: "💻", name: "Technology", color: "from-blue-500 to-cyan-500", delay: "0ms" },
                  { icon: "🏥", name: "Healthcare", color: "from-red-500 to-pink-500", delay: "100ms" },
                  { icon: "📚", name: "Education", color: "from-green-500 to-emerald-500", delay: "200ms" },
                  { icon: "🏦", name: "Finance", color: "from-yellow-500 to-orange-500", delay: "300ms" },
                  { icon: "🏭", name: "Manufacturing", color: "from-gray-500 to-slate-500", delay: "400ms" },
                  { icon: "🛒", name: "Retail", color: "from-purple-500 to-violet-500", delay: "500ms" },
                  { icon: "⚖️", name: "Legal", color: "from-indigo-500 to-blue-500", delay: "600ms" },
                  { icon: "🎨", name: "Creative", color: "from-pink-500 to-rose-500", delay: "700ms" }
                ].map((industry, index) => (
                  <div 
                    key={index}
                    className="group relative transform transition-all duration-500 hover:scale-110 opacity-0 animate-fade-in-up"
                    style={{animationDelay: industry.delay}}
                  >
                    <div className={`bg-gradient-to-br ${industry.color} rounded-2xl p-4 text-center text-white shadow-lg group-hover:shadow-2xl transition-all duration-300`}>
                      <div className="text-3xl mb-2 group-hover:animate-bounce">{industry.icon}</div>
                      <div className="text-xs font-semibold">{industry.name}</div>
                    </div>
                    
                    {/* Floating particles effect */}
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <div className="absolute -top-1 -left-1 w-2 h-2 bg-white rounded-full animate-ping"></div>
                      <div className="absolute -top-1 -right-1 w-1 h-1 bg-white rounded-full animate-ping" style={{animationDelay: '0.5s'}}></div>
                      <div className="absolute -bottom-1 -left-1 w-1 h-1 bg-white rounded-full animate-ping" style={{animationDelay: '1s'}}></div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Dynamic Capability Showcase */}
              <div className="text-center">
                <h4 className="text-xl font-bold text-gray-800 mb-6">Intelligent Adaptation in Action</h4>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* IT Example */}
                  <div className="bg-gradient-to-br from-blue-50 to-cyan-50 rounded-xl p-6 border border-blue-200">
                    <div className="text-3xl mb-3">💻</div>
                    <h5 className="font-bold text-gray-800 mb-2">Software Engineer</h5>
                    <div className="text-sm text-gray-600 space-y-1">
                      <div className="flex items-center">
                        <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                        <span>Code quality assessment</span>
                      </div>
                      <div className="flex items-center">
                        <span className="w-2 h-2 bg-cyan-500 rounded-full mr-2"></span>
                        <span>Technical problem solving</span>
                      </div>
                      <div className="flex items-center">
                        <span className="w-2 h-2 bg-teal-500 rounded-full mr-2"></span>
                        <span>Framework expertise</span>
                      </div>
                    </div>
                  </div>

                  {/* Healthcare Example */}
                  <div className="bg-gradient-to-br from-red-50 to-pink-50 rounded-xl p-6 border border-red-200">
                    <div className="text-3xl mb-3">🏥</div>
                    <h5 className="font-bold text-gray-800 mb-2">Registered Nurse</h5>
                    <div className="text-sm text-gray-600 space-y-1">
                      <div className="flex items-center">
                        <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                        <span>Patient care scenarios</span>
                      </div>
                      <div className="flex items-center">
                        <span className="w-2 h-2 bg-pink-500 rounded-full mr-2"></span>
                        <span>Medical knowledge</span>
                      </div>
                      <div className="flex items-center">
                        <span className="w-2 h-2 bg-rose-500 rounded-full mr-2"></span>
                        <span>Emergency response</span>
                      </div>
                    </div>
                  </div>

                  {/* Education Example */}
                  <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 border border-green-200">
                    <div className="text-3xl mb-3">📚</div>
                    <h5 className="font-bold text-gray-800 mb-2">Mathematics Teacher</h5>
                    <div className="text-sm text-gray-600 space-y-1">
                      <div className="flex items-center">
                        <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                        <span>Teaching methodology</span>
                      </div>
                      <div className="flex items-center">
                        <span className="w-2 h-2 bg-emerald-500 rounded-full mr-2"></span>
                        <span>Curriculum knowledge</span>
                      </div>
                      <div className="flex items-center">
                        <span className="w-2 h-2 bg-teal-500 rounded-full mr-2"></span>
                        <span>Student engagement</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Adaptive AI Feature Highlight */}
                <div className="mt-8 bg-gradient-to-r from-purple-100 to-blue-100 rounded-2xl p-6 border border-purple-200">
                  <div className="flex items-center justify-center mb-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center mr-4">
                      <span className="text-white text-xl">🧠</span>
                    </div>
                    <div>
                      <h5 className="font-bold text-gray-800">Smart Industry Adaptation</h5>
                      <p className="text-sm text-gray-600">AI automatically adjusts evaluation criteria based on role requirements</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                    <div className="bg-white/60 rounded-lg p-3">
                      <div className="text-lg font-bold text-purple-600">100+</div>
                      <div className="text-xs text-gray-600">Job Templates</div>
                    </div>
                    <div className="bg-white/60 rounded-lg p-3">
                      <div className="text-lg font-bold text-blue-600">15+</div>
                      <div className="text-xs text-gray-600">Industries</div>
                    </div>
                    <div className="bg-white/60 rounded-lg p-3">
                      <div className="text-lg font-bold text-cyan-600">200+</div>
                      <div className="text-xs text-gray-600">Skill Assessments</div>
                    </div>
                    <div className="bg-white/60 rounded-lg p-3">
                      <div className="text-lg font-bold text-teal-600">100%</div>
                      <div className="text-xs text-gray-600">Customizable</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Superhuman Recruitment Section */}
      <section id="features" className="relative z-10 py-20 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-5xl font-bold mb-6 bg-gradient-to-r from-blue-700 via-purple-600 to-cyan-700 bg-clip-text text-transparent opacity-0 animate-fade-in-up">
              Superhuman Recruitment
            </h2>
            <p className="text-xl text-gray-800 max-w-3xl mx-auto opacity-0 animate-fade-in-up font-medium" style={{animationDelay: '200ms'}}>
              Our AI doesn't just screen candidates—it predicts success, eliminates bias, and transforms how you discover talent.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* AI-Powered Analysis */}
            <div className="group relative overflow-hidden rounded-3xl bg-gradient-to-br from-blue-500 to-cyan-600 p-[2px] hover:scale-110 transition-all duration-500 opacity-0 animate-fade-in-up shadow-2xl" style={{animationDelay: '400ms'}}>
              <div className="relative h-full bg-gradient-to-br from-white/95 to-blue-50/95 backdrop-blur-sm rounded-3xl p-8 group-hover:bg-white transition-colors border border-white/50">
                <div className="text-5xl mb-6 group-hover:scale-125 transition-transform duration-300 filter drop-shadow-lg">
                  🧠
                </div>
                <h3 className="text-xl font-bold mb-4 text-black">Intelligent Analysis</h3>
                <p className="text-gray-700 group-hover:text-gray-800 transition-colors font-medium mb-4">
                  Advanced AI algorithms analyze resumes, skills, and interview responses to identify the best candidates.
                </p>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                    <span className="text-gray-600">Resume pattern recognition</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-cyan-500 rounded-full mr-2"></span>
                    <span className="text-gray-600">Skill matching algorithms</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-teal-500 rounded-full mr-2"></span>
                    <span className="text-gray-600">Behavioral assessment</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Bias Elimination */}
            <div className="group relative overflow-hidden rounded-3xl bg-gradient-to-br from-purple-500 to-pink-600 p-[2px] hover:scale-110 transition-all duration-500 opacity-0 animate-fade-in-up shadow-2xl" style={{animationDelay: '500ms'}}>
              <div className="relative h-full bg-gradient-to-br from-white/95 to-blue-50/95 backdrop-blur-sm rounded-3xl p-8 group-hover:bg-white transition-colors border border-white/50">
                <div className="text-5xl mb-6 group-hover:scale-125 transition-transform duration-300 filter drop-shadow-lg">
                  ⚖️
                </div>
                <h3 className="text-xl font-bold mb-4 text-black">Bias-Free Hiring</h3>
                <p className="text-gray-700 group-hover:text-gray-800 transition-colors font-medium mb-4">
                  AI-driven evaluation focuses purely on qualifications and merit, eliminating unconscious human bias.
                </p>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                    <span className="text-gray-600">Anonymous screening</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-pink-500 rounded-full mr-2"></span>
                    <span className="text-gray-600">Merit-based evaluation</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-rose-500 rounded-full mr-2"></span>
                    <span className="text-gray-600">Fair opportunity for all</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Success Prediction */}
            <div className="group relative overflow-hidden rounded-3xl bg-gradient-to-br from-green-500 to-emerald-600 p-[2px] hover:scale-110 transition-all duration-500 opacity-0 animate-fade-in-up shadow-2xl" style={{animationDelay: '600ms'}}>
              <div className="relative h-full bg-gradient-to-br from-white/95 to-blue-50/95 backdrop-blur-sm rounded-3xl p-8 group-hover:bg-white transition-colors border border-white/50">
                <div className="text-5xl mb-6 group-hover:scale-125 transition-transform duration-300 filter drop-shadow-lg">
                  🎯
                </div>
                <h3 className="text-xl font-bold mb-4 text-black">Success Prediction</h3>
                <p className="text-gray-700 group-hover:text-gray-800 transition-colors font-medium mb-4">
                  Predictive models analyze candidate potential and cultural fit to forecast job performance.
                </p>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    <span className="text-gray-600">Performance indicators</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-emerald-500 rounded-full mr-2"></span>
                    <span className="text-gray-600">Cultural fit analysis</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-teal-500 rounded-full mr-2"></span>
                    <span className="text-gray-600">Long-term potential</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Anti-Cheat Technology */}
            <div className="group relative overflow-hidden rounded-3xl bg-gradient-to-br from-red-500 to-orange-600 p-[2px] hover:scale-110 transition-all duration-500 opacity-0 animate-fade-in-up shadow-2xl" style={{animationDelay: '700ms'}}>
              <div className="relative h-full bg-gradient-to-br from-white/95 to-blue-50/95 backdrop-blur-sm rounded-3xl p-8 group-hover:bg-white transition-colors border border-white/50">
                <div className="text-5xl mb-6 group-hover:scale-125 transition-transform duration-300 filter drop-shadow-lg">
                  🛡️
                </div>
                <h3 className="text-xl font-bold mb-4 text-black">Secure Assessment</h3>
                <p className="text-gray-700 group-hover:text-gray-800 transition-colors font-medium mb-4">
                  Advanced monitoring ensures interview integrity with real-time cheat detection and prevention.
                </p>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                    <span className="text-gray-600">Real-time monitoring</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
                    <span className="text-gray-600">Behavior analysis</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                    <span className="text-gray-600">Integrity verification</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Automation & Efficiency */}
            <div className="group relative overflow-hidden rounded-3xl bg-gradient-to-br from-indigo-500 to-purple-600 p-[2px] hover:scale-110 transition-all duration-500 opacity-0 animate-fade-in-up shadow-2xl" style={{animationDelay: '800ms'}}>
              <div className="relative h-full bg-gradient-to-br from-white/95 to-blue-50/95 backdrop-blur-sm rounded-3xl p-8 group-hover:bg-white transition-colors border border-white/50">
                <div className="text-5xl mb-6 group-hover:scale-125 transition-transform duration-300 filter drop-shadow-lg">
                  ⚡
                </div>
                <h3 className="text-xl font-bold mb-4 text-black">Smart Automation</h3>
                <p className="text-gray-700 group-hover:text-gray-800 transition-colors font-medium mb-4">
                  Streamline your entire hiring workflow with intelligent automation and instant candidate insights.
                </p>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-indigo-500 rounded-full mr-2"></span>
                    <span className="text-gray-600">Automated screening</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                    <span className="text-gray-600">Instant candidate ranking</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-violet-500 rounded-full mr-2"></span>
                    <span className="text-gray-600">Workflow optimization</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Universal Compatibility */}
            <div className="group relative overflow-hidden rounded-3xl bg-gradient-to-br from-cyan-500 to-teal-600 p-[2px] hover:scale-110 transition-all duration-500 opacity-0 animate-fade-in-up shadow-2xl" style={{animationDelay: '900ms'}}>
              <div className="relative h-full bg-gradient-to-br from-white/95 to-blue-50/95 backdrop-blur-sm rounded-3xl p-8 group-hover:bg-white transition-colors border border-white/50">
                <div className="text-5xl mb-6 group-hover:scale-125 transition-transform duration-300 filter drop-shadow-lg">
                  🌐
                </div>
                <h3 className="text-xl font-bold mb-4 text-black">Universal Platform</h3>
                <p className="text-gray-700 group-hover:text-gray-800 transition-colors font-medium mb-4">
                  Adaptable to any industry, role, or company size with customizable evaluation criteria.
                </p>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-cyan-500 rounded-full mr-2"></span>
                    <span className="text-gray-600">Multi-industry support</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-teal-500 rounded-full mr-2"></span>
                    <span className="text-gray-600">Customizable criteria</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                    <span className="text-gray-600">Scalable solution</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Social Proof Section */}
      <section className="relative z-10 py-16 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h3 className="text-2xl font-bold text-gray-800 mb-4">Trusted by Forward-Thinking Companies</h3>
            <p className="text-gray-600">Join innovative organizations transforming their hiring process</p>
          </div>
          
          {/* Company Logos Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center opacity-70">
            {[
              { name: "TechCorp", color: "from-blue-500 to-cyan-500" },
              { name: "InnovateLab", color: "from-purple-500 to-pink-500" },
              { name: "GrowthCo", color: "from-green-500 to-emerald-500" },
              { name: "FutureTech", color: "from-orange-500 to-red-500" },
              { name: "ScaleUp", color: "from-indigo-500 to-purple-500" },
              { name: "NextGen", color: "from-teal-500 to-cyan-500" }
            ].map((company, index) => (
              <div key={index} className="flex items-center justify-center">
                <div className={`bg-gradient-to-r ${company.color} rounded-lg p-4 text-white font-bold text-center min-h-16 flex items-center justify-center w-full`}>
                  <span className="text-sm">{company.name}</span>
                </div>
              </div>
            ))}
          </div>

          {/* Early Adopter Testimonial */}
          <div className="mt-16 bg-gradient-to-r from-white/80 to-blue-50/80 rounded-2xl p-8 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-4xl mb-4">💬</div>
              <blockquote className="text-lg text-gray-700 italic mb-6">
                "ArisIQ has streamlined our hiring process significantly. The AI-powered screening saves us hours of manual review, and the interview monitoring gives us confidence in our remote assessments."
              </blockquote>
              <div className="flex items-center justify-center">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                  JS
                </div>
                <div className="text-left">
                  <div className="font-semibold text-gray-800">Jane Smith</div>
                  <div className="text-gray-600 text-sm">HR Director, TechCorp</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="relative z-10 py-20 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-6 bg-gradient-to-r from-blue-700 to-purple-700 bg-clip-text text-transparent">
              Simple, Transparent Pricing
            </h2>
            <p className="text-xl text-gray-700 max-w-2xl mx-auto">
              Choose the plan that fits your hiring needs. Start free, scale as you grow.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Starter Plan */}
            <div className="bg-gradient-to-br from-white/90 to-blue-50/90 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/50">
              <div className="text-center">
                <h3 className="text-2xl font-bold text-gray-800 mb-2">Starter</h3>
                <p className="text-gray-600 mb-6">Perfect for small teams</p>
                <div className="mb-6">
                  <span className="text-4xl font-bold text-blue-600">$99</span>
                  <span className="text-gray-600">/month</span>
                </div>
                <ul className="text-left space-y-3 mb-8">
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                    <span className="text-gray-700">Up to 50 candidates/month</span>
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                    <span className="text-gray-700">AI resume screening</span>
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                    <span className="text-gray-700">Basic interview monitoring</span>
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                    <span className="text-gray-700">Email support</span>
                  </li>
                </ul>
                <button className="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                  Start Free Trial
                </button>
              </div>
            </div>

            {/* Professional Plan - Featured */}
            <div className="bg-gradient-to-br from-white/90 to-purple-50/90 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 border-2 border-purple-400 relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-2 rounded-full text-sm font-semibold">
                  Most Popular
                </span>
              </div>
              <div className="text-center">
                <h3 className="text-2xl font-bold text-gray-800 mb-2">Professional</h3>
                <p className="text-gray-600 mb-6">For growing companies</p>
                <div className="mb-6">
                  <span className="text-4xl font-bold text-purple-600">$299</span>
                  <span className="text-gray-600">/month</span>
                </div>
                <ul className="text-left space-y-3 mb-8">
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-purple-500 rounded-full mr-3"></span>
                    <span className="text-gray-700">Up to 200 candidates/month</span>
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-purple-500 rounded-full mr-3"></span>
                    <span className="text-gray-700">Advanced AI analysis</span>
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-purple-500 rounded-full mr-3"></span>
                    <span className="text-gray-700">Full interview security</span>
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-purple-500 rounded-full mr-3"></span>
                    <span className="text-gray-700">Priority support</span>
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-purple-500 rounded-full mr-3"></span>
                    <span className="text-gray-700">Custom integrations</span>
                  </li>
                </ul>
                <button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-all">
                  Start Free Trial
                </button>
              </div>
            </div>

            {/* Enterprise Plan */}
            <div className="bg-gradient-to-br from-white/90 to-gray-50/90 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/50">
              <div className="text-center">
                <h3 className="text-2xl font-bold text-gray-800 mb-2">Enterprise</h3>
                <p className="text-gray-600 mb-6">For large organizations</p>
                <div className="mb-6">
                  <span className="text-4xl font-bold text-gray-700">Custom</span>
                </div>
                <ul className="text-left space-y-3 mb-8">
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-gray-500 rounded-full mr-3"></span>
                    <span className="text-gray-700">Unlimited candidates</span>
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-gray-500 rounded-full mr-3"></span>
                    <span className="text-gray-700">White-label solution</span>
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-gray-500 rounded-full mr-3"></span>
                    <span className="text-gray-700">Dedicated support</span>
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-gray-500 rounded-full mr-3"></span>
                    <span className="text-gray-700">SLA guarantees</span>
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-gray-500 rounded-full mr-3"></span>
                    <span className="text-gray-700">Custom AI training</span>
                  </li>
                </ul>
                <button className="w-full bg-gray-700 text-white py-3 rounded-lg font-semibold hover:bg-gray-800 transition-colors">
                  Contact Sales
                </button>
              </div>
            </div>
          </div>

          {/* Pricing Footer */}
          <div className="text-center mt-12">
            <p className="text-gray-600 mb-4">All plans include a 14-day free trial • No credit card required</p>
            <div className="flex justify-center space-x-8 text-sm text-gray-600">
              <div className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                <span>Cancel anytime</span>
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                <span>24/7 support</span>
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                <span>Setup assistance</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="relative z-10 py-20 px-6">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-6 bg-gradient-to-r from-blue-700 to-purple-700 bg-clip-text text-transparent">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-gray-700">
              Everything you need to know about ArisIQ
            </p>
          </div>

          <div className="space-y-6">
            {[
              {
                question: "How does the AI actually work?",
                answer: "ArisIQ uses advanced machine learning algorithms to analyze resumes, assess candidate responses, and monitor interview behavior. Our AI is trained on recruitment best practices and continuously learns to improve accuracy."
              },
              {
                question: "What industries can use ArisIQ?",
                answer: "ArisIQ is designed to work across all industries - from technology and healthcare to education and finance. Our AI adapts to industry-specific requirements and job roles automatically."
              },
              {
                question: "How long does implementation take?",
                answer: "Most companies are up and running within 24 hours. Our simple setup process takes just a few minutes, and our team provides onboarding support to ensure smooth integration with your existing workflow."
              },
              {
                question: "Is candidate data secure?",
                answer: "Absolutely. We use bank-level encryption and follow strict data privacy regulations including GDPR. All candidate information is stored securely and access is strictly controlled."
              },
              {
                question: "What's included in the free trial?",
                answer: "The 14-day free trial includes full access to all features: AI resume screening, interview monitoring, candidate analytics, and support. No credit card required to start."
              },
              {
                question: "Can I integrate ArisIQ with my existing ATS?",
                answer: "Yes! ArisIQ integrates with most popular Applicant Tracking Systems and HR platforms. Our API allows seamless data flow between systems."
              }
            ].map((faq, index) => (
              <div key={index} className="bg-gradient-to-r from-white/80 to-blue-50/80 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
                <h3 className="text-lg font-bold text-gray-800 mb-3 flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  {faq.question}
                </h3>
                <p className="text-gray-700 leading-relaxed pl-5">
                  {faq.answer}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Enhanced Coming Soon Section */}
      <section className="relative z-10 py-20 px-6">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-gradient-to-br from-white/80 to-purple-50/80 backdrop-blur-lg rounded-3xl p-12 shadow-2xl hover:scale-105 transition-all duration-500">
            <div className="text-7xl mb-8 filter drop-shadow-lg">🚀</div>
            <h2 className="text-4xl font-bold mb-6 text-black bg-gradient-to-r from-purple-700 to-blue-700 bg-clip-text text-transparent">
              More Features Coming Soon
            </h2>
            <p className="text-xl text-gray-800 mb-8 font-medium">
              We're constantly innovating to bring you the most advanced AI hiring solutions.
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm text-black font-semibold">
              <span className="px-4 py-2 bg-gradient-to-r from-white/80 to-blue-100/80 rounded-full shadow-lg border border-white/50">• Advanced Analytics</span>
              <span className="px-4 py-2 bg-gradient-to-r from-white/80 to-purple-100/80 rounded-full shadow-lg border border-white/50">• Team Collaboration</span>
              <span className="px-4 py-2 bg-gradient-to-r from-white/80 to-cyan-100/80 rounded-full shadow-lg border border-white/50">• API Integrations</span>
              <span className="px-4 py-2 bg-gradient-to-r from-white/80 to-teal-100/80 rounded-full shadow-lg border border-white/50">• Mobile Apps</span>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced CTA Section */}
      <section className="relative z-10 py-20 px-6">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-gradient-to-br from-white/80 to-blue-50/80 backdrop-blur-lg rounded-3xl p-12 hover:bg-white/90 transition-all duration-500 shadow-2xl hover:scale-105">
            <h2 className="text-5xl font-bold mb-6 text-black bg-gradient-to-r from-blue-700 to-purple-700 bg-clip-text text-transparent">
              Ready to Transform Your Hiring?
            </h2>
            <p className="text-xl text-gray-800 mb-8 font-medium">
              Join thousands of companies already using ArisIQ to find exceptional talent faster than ever before.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <button 
                onClick={handleRecruiter}
                className="group relative px-10 py-5 bg-gradient-to-r from-blue-600 via-purple-600 to-cyan-600 rounded-full font-bold text-xl overflow-hidden transition-all duration-300 transform hover:scale-110 hover:shadow-2xl text-white border-2 border-white/50"
              >
                <span className="relative z-10 flex items-center">
                  🚀 Start Hiring Now
                  <span className="ml-3 group-hover:translate-x-2 transition-transform">→</span>
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-cyan-600 via-blue-600 to-purple-600 opacity-0 group-hover:opacity-100 transition-opacity rounded-full"></div>
              </button>
              
              <button 
                onClick={handleCandidate}
                className="flex items-center px-10 py-5 border-2 border-purple-400 bg-gradient-to-r from-white/90 to-purple-50/90 rounded-full font-bold text-xl hover:bg-white transition-all duration-300 transform hover:scale-110 text-black shadow-xl hover:shadow-2xl"
              >
                <span className="mr-3">💼</span>
                Find Your Dream Job
              </button>
            </div>
            
            <div className="mt-8 text-sm text-gray-700 font-medium">
              No credit card required • Free to get started • Setup in 5 minutes
            </div>
          </div>
        </div>
      </section>

      {/* Modals (keeping the same modal code but with enhanced styling) */}
      {/* Employer Access Modal */}
      {showEmployerModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 backdrop-blur-sm">
          <div className="bg-gradient-to-br from-white/95 to-blue-50/95 backdrop-blur-xl rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto shadow-2xl border border-white/60">
            <div className="p-6">
              {/* Close button */}
              <div className="flex justify-end mb-4">
                <button
                  onClick={() => {
                    setShowEmployerModal(false);
                    setError('');
                    setEmployerData({
                      companyName: '',
                      location: '',
                      industry: '',
                      website: '',
                      description: '',
                      contactName: '',
                      businessEmail: '',
                      password: '',
                    });
                  }}
                  className="text-gray-500 hover:text-gray-700 text-2xl font-bold w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
                >
                  ×
                </button>
              </div>

              {/* Header */}
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                  <span className="text-white text-2xl">🏢</span>
                </div>
                <h2 className="text-3xl font-bold text-black">Employer Access</h2>
                <p className="mt-2 text-sm text-gray-700 font-medium">
                  Sign in to your organization account or create a new employer profile.
                </p>
              </div>

              {/* Sign In / Sign Up Toggle */}
              <div className="flex mb-6 bg-gradient-to-r from-gray-100 to-blue-100 rounded-xl p-1 shadow-inner">
                <button
                  type="button"
                  onClick={() => {
                    setIsEmployerLogin(true);
                    setError('');
                  }}
                  className={`flex-1 py-3 px-4 rounded-lg text-sm font-bold transition-all duration-300 ${
                    isEmployerLogin
                      ? 'bg-white text-black shadow-md transform scale-105'
                      : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  Sign In
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setIsEmployerLogin(false);
                    setError('');
                  }}
                  className={`flex-1 py-3 px-4 rounded-lg text-sm font-bold transition-all duration-300 ${
                    !isEmployerLogin
                      ? 'bg-white text-black shadow-md transform scale-105'
                      : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  Sign Up
                </button>
              </div>

              {/* Error Message */}
              {error && (
                <div className="mb-4 p-4 bg-gradient-to-r from-red-50 to-pink-50 border-2 border-red-200 rounded-xl shadow-lg">
                  <p className="text-sm text-red-700 font-medium">{error}</p>
                </div>
              )}

              {/* Form */}
              <form onSubmit={handleEmployerSubmit} className="space-y-4">
                {!isEmployerLogin && (
                  <>
                    {/* Company Name */}
                    <div>
                      <label className="block text-sm font-bold text-black mb-2">
                        Company Name
                      </label>
                      <input
                        type="text"
                        name="companyName"
                        value={employerData.companyName}
                        onChange={handleEmployerInputChange}
                        required
                        className="w-full px-4 py-3 border-2 border-blue-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/90 text-black font-medium shadow-sm"
                        placeholder="Enter company name"
                      />
                    </div>

                    {/* Location */}
                    <div>
                      <label className="block text-sm font-bold text-black mb-2">
                        Location
                      </label>
                      <input
                        type="text"
                        name="location"
                        value={employerData.location}
                        onChange={handleEmployerInputChange}
                        required
                        className="w-full px-4 py-3 border-2 border-blue-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/90 text-black font-medium shadow-sm"
                        placeholder="City, Country"
                      />
                    </div>

                    {/* Industry */}
                    <div>
                      <label className="block text-sm font-bold text-black mb-2">
                        Industry
                      </label>
                      <input
                        type="text"
                        name="industry"
                        value={employerData.industry}
                        onChange={handleEmployerInputChange}
                        required
                        className="w-full px-4 py-3 border-2 border-blue-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/90 text-black font-medium shadow-sm"
                        placeholder="e.g., Technology, Healthcare"
                      />
                    </div>

                    {/* Website */}
                    <div>
                      <label className="block text-sm font-bold text-black mb-2">
                        Company Website
                      </label>
                      <input
                        type="url"
                        name="website"
                        value={employerData.website}
                        onChange={handleEmployerInputChange}
                        className="w-full px-4 py-3 border-2 border-blue-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/90 text-black font-medium shadow-sm"
                        placeholder="https://example.com"
                      />
                    </div>

                    {/* Description */}
                    <div>
                      <label className="block text-sm font-bold text-black mb-2">
                        Company Description
                      </label>
                      <textarea
                        name="description"
                        value={employerData.description}
                        onChange={handleEmployerInputChange}
                        rows={3}
                        className="w-full px-4 py-3 border-2 border-blue-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none bg-white/90 text-black font-medium shadow-sm"
                        placeholder="Tell us about your company..."
                      />
                    </div>

                    {/* Contact Person */}
                    <div>
                      <label className="block text-sm font-bold text-black mb-2">
                        Contact Person
                      </label>
                      <input
                        type="text"
                        name="contactName"
                        value={employerData.contactName}
                        onChange={handleEmployerInputChange}
                        required
                        className="w-full px-4 py-3 border-2 border-blue-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/90 text-black font-medium shadow-sm"
                        placeholder="Full name"
                      />
                    </div>
                  </>
                )}

                {/* Business Email */}
                <div>
                  <label className="block text-sm font-bold text-black mb-2">
                    Business Email
                  </label>
                  <input
                    type="email"
                    name="businessEmail"
                    value={employerData.businessEmail}
                    onChange={handleEmployerInputChange}
                    required
                    className="w-full px-4 py-3 border-2 border-blue-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/90 text-black font-medium shadow-sm"
                    placeholder="<EMAIL>"
                  />
                </div>

                {/* Password */}
                <div>
                  <label className="block text-sm font-bold text-black mb-2">
                    Password
                  </label>
                  <input
                    type="password"
                    name="password"
                    value={employerData.password}
                    onChange={handleEmployerInputChange}
                    required
                    minLength={6}
                    className="w-full px-4 py-3 border-2 border-blue-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/90 text-black font-medium shadow-sm"
                    placeholder={isEmployerLogin ? "Enter your password" : "Create a password (min 6 chars)"}
                  />
                </div>

                {/* Submit Button */}
                <button
                  type="submit"
                  disabled={loading}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 px-4 rounded-xl hover:from-purple-600 hover:to-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-bold flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                >
                  {loading ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                      {isEmployerLogin ? 'Signing In...' : 'Creating Account...'}
                    </div>
                  ) : (
                    <>
                      <span className="mr-3">🏢</span>
                      {isEmployerLogin ? 'Sign In to Employer Account' : 'Create Employer Account'}
                    </>
                  )}
                </button>
              </form>

              {/* Divider */}
              <div className="my-6">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t-2 border-gray-300" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-3 bg-white text-gray-600 font-medium">Or continue with</span>
                  </div>
                </div>
              </div>

              {/* Google Sign In for Employers */}
              <button
                type="button"
                onClick={() => handleGoogleSignIn('recruiter')}
                disabled={loading}
                className="w-full bg-white border-2 border-gray-300 text-black py-3 px-4 rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-bold flex items-center justify-center mb-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
              >
                <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                Continue with Google
              </button>

              {/* Support Link */}
              <div className="text-center">
                <p className="text-sm text-gray-700 font-medium">
                  Need help with access?{' '}
                  <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-500 font-bold">
                    Contact support
                  </a>
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Regular Candidate Auth Modal */}
      {showAuthModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 backdrop-blur-sm">
          <div className="bg-gradient-to-br from-white/95 to-blue-50/95 backdrop-blur-xl rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto shadow-2xl border border-white/60">
            <div className="p-6">
              {/* Close button */}
              <div className="flex justify-end mb-4">
                <button
                  onClick={() => {
                    setShowAuthModal(false);
                    setError('');
                    setFormData({ name: '', email: '', password: '', company: '' });
                  }}
                  className="text-gray-500 hover:text-gray-700 text-2xl font-bold w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
                >
                  ×
                </button>
              </div>

              {/* Header - Updated for Candidate-only */}
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                  <span className="text-white text-2xl">👤</span>
                </div>
                <h2 className="text-3xl font-bold text-black">Candidate Login</h2>
                <p className="mt-2 text-sm text-gray-700 font-medium">
                  {isLogin ? "Access your candidate account" : "Create your candidate account to find your dream job"}
                </p>
              </div>

              {/* Login/Signup Toggle - Removed User Type Toggle */}
              <div className="text-center mb-6">
                <p className="mt-2 text-sm text-gray-700 font-medium">
                  {isLogin ? "Don't have an account? " : "Already have an account? "}
                  <button
                    type="button"
                    onClick={() => {
                      setIsLogin(!isLogin);
                      setError('');
                      setFormData({ name: '', email: '', password: '', company: '' });
                    }}
                    className="text-blue-600 hover:text-blue-500 font-bold"
                  >
                    {isLogin ? 'Create account' : 'Sign in'}
                  </button>
                </p>
              </div>

              {/* Error Message */}
              {error && (
                <div className="mb-4 p-4 bg-gradient-to-r from-red-50 to-pink-50 border-2 border-red-200 rounded-xl shadow-lg">
                  <p className="text-sm text-red-700 font-medium">{error}</p>
                </div>
              )}

              {/* Auth Form */}
              <form onSubmit={handleAuthSubmit} className="space-y-4">
                {/* Remove company field since this is candidate-only */}
                {!isLogin && (
                  <div>
                    <label className="block text-sm font-bold text-black mb-2">
                      Full Name
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border-2 border-blue-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/90 text-black font-medium shadow-sm"
                      placeholder="Enter your full name"
                    />
                  </div>
                )}

                {/* Email field */}
                <div>
                  <label className="block text-sm font-bold text-black mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border-2 border-blue-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/90 text-black font-medium shadow-sm"
                    placeholder="Enter your email"
                  />
                </div>

                {/* Password field */}
                <div>
                  <label className="block text-sm font-bold text-black mb-2">
                    Password
                  </label>
                  <div className="relative">
                    <input
                      type={showPassword ? 'text' : 'password'}
                      name="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      required
                      minLength={6}
                      className="w-full px-4 py-3 pr-12 border-2 border-blue-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/90 text-black font-medium shadow-sm"
                      placeholder={isLogin ? "Enter your password" : "Create a password (min 6 chars)"}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-4 top-4 text-gray-400 hover:text-gray-600"
                    >
                      {showPassword ? '👁️' : '👁️‍🗨️'}
                    </button>
                  </div>
                </div>

                {/* Submit Button */}
                <button
                  type="submit"
                  disabled={loading}
                  className="w-full bg-gradient-to-r from-blue-600 to-cyan-600 text-white py-4 px-4 rounded-xl hover:from-cyan-600 hover:to-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-bold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                >
                  {loading ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                      {isLogin ? 'Signing In...' : 'Creating Account...'}
                    </div>
                  ) : (
                    isLogin ? 'Sign In' : 'Create Candidate Account'
                  )}
                </button>
              </form>

              {/* Divider */}
              <div className="my-6">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t-2 border-gray-300" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-3 bg-white text-gray-600 font-medium">Or continue with</span>
                  </div>
                </div>
              </div>

              {/* Google Sign In */}
              <button
                type="button"
                onClick={() => handleGoogleSignIn('candidate')}
                disabled={loading}
                className="w-full bg-white border-2 border-gray-300 text-black py-3 px-4 rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-bold flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
              >
                <span className="mr-3">🔍</span>
                Continue with Google
              </button>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        @keyframes fade-in-up {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-fade-in-up {
          animation: fade-in-up 0.8s ease-out forwards;
        }
      `}</style>

      {/* Enhanced Footer */}
      <footer className="relative z-10 bg-gradient-to-r from-gray-900 to-blue-900 text-white py-16 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Company Info */}
            <div>
              <div className="flex items-center mb-6">
                <img 
                  src="/ArisIQ logo.png" 
                  alt="ArisIQ" 
                  className="h-12 w-auto mr-3 filter brightness-150"
                />
                <span className="text-2xl font-bold">ArisIQ</span>
              </div>
              <p className="text-gray-300 mb-4">
                Revolutionizing recruitment with AI-powered intelligence and seamless automation.
              </p>
              <div className="flex space-x-4">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center cursor-pointer hover:bg-blue-700 transition-colors">
                  <span className="text-sm font-bold">Li</span>
                </div>
                <div className="w-10 h-10 bg-blue-400 rounded-full flex items-center justify-center cursor-pointer hover:bg-blue-500 transition-colors">
                  <span className="text-sm font-bold">Tw</span>
                </div>
                <div className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center cursor-pointer hover:bg-gray-600 transition-colors">
                  <span className="text-sm font-bold">Gh</span>
                </div>
              </div>
            </div>

            {/* Product */}
            <div>
              <h4 className="text-lg font-bold mb-4">Product</h4>
              <ul className="space-y-2 text-gray-300">
                <li><a href="#" className="hover:text-white transition-colors">Features</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Pricing</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Integrations</a></li>
                <li><a href="#" className="hover:text-white transition-colors">API Documentation</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Changelog</a></li>
              </ul>
            </div>

            {/* Company */}
            <div>
              <h4 className="text-lg font-bold mb-4">Company</h4>
              <ul className="space-y-2 text-gray-300">
                <li><a href="#" className="hover:text-white transition-colors">About Us</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Careers</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Blog</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Press Kit</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h4 className="text-lg font-bold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-300">
                <li><a href="#" className="hover:text-white transition-colors">Help Center</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Community</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Status Page</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Security</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Privacy Policy</a></li>
              </ul>
            </div>
          </div>

          {/* Footer Bottom */}
          <div className="border-t border-gray-700 mt-12 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="text-gray-400 text-sm mb-4 md:mb-0">
                © 2025 ArisIQ. All rights reserved. Built with ❤️ for better hiring.
              </div>
              <div className="flex space-x-6 text-sm text-gray-400">
                <a href="#" className="hover:text-white transition-colors">Terms of Service</a>
                <a href="#" className="hover:text-white transition-colors">Privacy Policy</a>
                <a href="#" className="hover:text-white transition-colors">Cookie Policy</a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
