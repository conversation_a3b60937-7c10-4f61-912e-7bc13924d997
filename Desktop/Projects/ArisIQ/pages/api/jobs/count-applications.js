// File: pages/api/jobs/count-applications.js

import connectDB from "@/lib/mongodb";
import mongoose from "mongoose";

const applicationSchema = new mongoose.Schema({}, { strict: false });
const Application = mongoose.models.Application || mongoose.model("Application", applicationSchema);

export default async function handler(req, res) {
  await connectDB();

  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method Not Allowed" });
  }

  const { jobIds } = req.body;
  if (!jobIds || !Array.isArray(jobIds)) {
    return res.status(400).json({ error: "Invalid request body" });
  }

  try {
    const counts = await Application.aggregate([
      { $match: { jobId: { $in: jobIds.map(id => new mongoose.Types.ObjectId(id)) } } },
      { $group: { _id: "$jobId", count: { $sum: 1 } } }
    ]);

    const result = {};
    counts.forEach(({ _id, count }) => {
      result[_id.toString()] = count;
    });

    res.status(200).json({ counts: result });
  } catch (error) {
    console.error("Count aggregation error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
}

