// File: pages/api/jobs/[id].js

import { MongoClient, ObjectId } from "mongodb";
const uri = process.env.MONGODB_URI;

export default async function handler(req, res) {
  const { id } = req.query;

  if (req.method !== "GET") {
    return res.status(405).json({ message: "Method Not Allowed" });
  }

  try {
    // Validate the job ID
    let jobObjectId;
    try {
      jobObjectId = new ObjectId(id);
    } catch (e) {
      return res.status(400).json({ message: "Invalid Job ID format" });
    }

    const client = await MongoClient.connect(uri);
    const db = client.db();

    // Get the job details
    const job = await db.collection("jobs").findOne({ _id: jobObjectId });

    if (!job) {
      await client.close();
      return res.status(404).json({ message: "Job not found" });
    }

    // IMPORTANT: Log what we found to see the job structure
    console.log("Found job:", job);
    
    // Check for different possible jobId formats in applications
    // Try both ObjectId and string formats
    const applications = await db.collection("applications")
      .find({ 
        $or: [
          { jobId: id }, // String ID format
          { jobId: jobObjectId }, // ObjectId format
          { jobId: jobObjectId.toString() } // ObjectId as string
        ]
      })
      .toArray();
    
    // Log the query results for debugging
    console.log(`Found ${applications.length} applications for job ${id}`);
    
    // If no applications found, let's check the entire collection to see what's there
    if (applications.length === 0) {
      const sampleApps = await db.collection("applications")
        .find({})
        .limit(3)
        .toArray();
      
      console.log("Sample applications in DB:", sampleApps);
      
      // Also check what jobIds exist in the applications collection
      const uniqueJobIds = await db.collection("applications")
        .distinct("jobId");
      
      console.log("Unique jobIds in applications:", uniqueJobIds);
    }

    await client.close();

    return res.status(200).json({ 
      job, 
      applications,
      debug: {
        jobId: id,
        jobObjectIdString: jobObjectId.toString(),
        applicationCount: applications.length
      }
    });
  } catch (error) {
    console.error("❌ Error loading job and applications:", error);
    return res.status(500).json({ 
      message: "Internal Server Error", 
      error: error.message,
      stack: error.stack
    });
  }
}
