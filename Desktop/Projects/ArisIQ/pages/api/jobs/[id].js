// File: pages/api/jobs/[id].js

import { MongoClient, ObjectId } from "mongodb";
const uri = process.env.MONGODB_URI;

export default async function handler(req, res) {
  const { id } = req.query;

  // Validate the job ID
  let jobObjectId;
  try {
    jobObjectId = new ObjectId(id);
  } catch (e) {
    return res.status(400).json({ message: "Invalid Job ID format" });
  }

  if (req.method === "GET") {
    try {
      const client = await MongoClient.connect(uri);
      const db = client.db();

      // Get the job details
      const job = await db.collection("jobs").findOne({ _id: jobObjectId });

      if (!job) {
        await client.close();
        return res.status(404).json({ message: "Job not found" });
      }

      // IMPORTANT: Log what we found to see the job structure
      console.log("Found job:", job);
      
      // Check for different possible jobId formats in applications
      // Try both ObjectId and string formats
      const applications = await db.collection("applications")
        .find({ 
          $or: [
            { jobId: id }, // String ID format
            { jobId: jobObjectId }, // ObjectId format
            { jobId: jobObjectId.toString() } // ObjectId as string
          ]
        })
        .toArray();
      
      // Log the query results for debugging
      console.log(`Found ${applications.length} applications for job ${id}`);
      
      // If no applications found, let's check the entire collection to see what's there
      if (applications.length === 0) {
        const sampleApps = await db.collection("applications")
          .find({})
          .limit(3)
          .toArray();
        
        console.log("Sample applications in DB:", sampleApps);
        
        // Also check what jobIds exist in the applications collection
        const uniqueJobIds = await db.collection("applications")
          .distinct("jobId");
        
        console.log("Unique jobIds in applications:", uniqueJobIds);
      }

      await client.close();

      return res.status(200).json({ 
        job, 
        applications,
        debug: {
          jobId: id,
          jobObjectIdString: jobObjectId.toString(),
          applicationCount: applications.length
        }
      });
    } catch (error) {
      console.error("❌ Error loading job and applications:", error);
      return res.status(500).json({ 
        message: "Internal Server Error", 
        error: error.message,
        stack: error.stack
      });
    }
  } 
  else if (req.method === "PATCH") {
    try {
      const client = await MongoClient.connect(uri);
      const db = client.db();

      // Get the existing job to make sure it exists
      const existingJob = await db.collection("jobs").findOne({ _id: jobObjectId });

      if (!existingJob) {
        await client.close();
        return res.status(404).json({ message: "Job not found" });
      }

      // Extract the fields to update from request body
      const updateFields = {};
      const allowedFields = ['status', 'closedAt', 'title', 'company', 'industry', 'experience', 
                           'salary', 'country', 'city', 'requiredSkills', 'preferredSkills', 
                           'website', 'applicationDeadline', 'screeningDeadline', 'employmentType', 
                           'workMode', 'description', 'visaSponsorship'];

      // Only include fields that are provided in the request
      for (const field of allowedFields) {
        if (req.body.hasOwnProperty(field)) {
          updateFields[field] = req.body[field];
        }
      }

      // Add updatedAt timestamp
      updateFields.updatedAt = new Date();

      // Handle question archival/restoration based on status change
      if (req.body.status === 'closed' && existingJob.status !== 'closed') {
        // Job is being closed - archive questions
        updateFields.closedAt = new Date();
        
        try {
          const archiveResult = await db.collection("questions").updateMany(
            { 
              $or: [
                { jobId: id },
                { jobId: jobObjectId }
              ]
            },
            { 
              $set: { 
                status: 'archived',
                archivedAt: new Date(),
                archivedReason: 'job_closed',
                retentionDays: 30,
                willBeDeletedAt: new Date(new Date().getTime() + 30 * 24 * 60 * 60 * 1000)
              }
            }
          );
          
          console.log(`📦 Archived ${archiveResult.modifiedCount} questions for closed job: ${id} (30-day retention)`);
        } catch (questionError) {
          console.log("⚠️ Question archival failed (questions might not exist):", questionError.message);
        }
      } 
      else if (req.body.status === 'open' && existingJob.status === 'closed') {
        // Job is being reopened - restore questions
        updateFields.closedAt = null;
        
        try {
          const restoreResult = await db.collection("questions").updateMany(
            { 
              $or: [
                { jobId: id },
                { jobId: jobObjectId }
              ],
              status: 'archived',
              archivedReason: 'job_closed'
            },
            { 
              $unset: { 
                status: "",
                archivedAt: "",
                archivedReason: "",
                retentionDays: "",
                willBeDeletedAt: ""
              }
            }
          );
          
          console.log(`📂 Restored ${restoreResult.modifiedCount} questions for reopened job: ${id}`);
        } catch (questionError) {
          console.log("⚠️ Question restoration failed (questions might not exist):", questionError.message);
        }
      }

      // Update the job
      const result = await db.collection("jobs").updateOne(
        { _id: jobObjectId },
        { $set: updateFields }
      );

      if (result.matchedCount === 0) {
        await client.close();
        return res.status(404).json({ message: "Job not found" });
      }

      // Get the updated job to return
      const updatedJob = await db.collection("jobs").findOne({ _id: jobObjectId });

      await client.close();

      return res.status(200).json({ 
        success: true,
        message: "Job updated successfully",
        job: updatedJob
      });
    } catch (error) {
      console.error("❌ Error updating job:", error);
      return res.status(500).json({ 
        message: "Internal Server Error", 
        error: error.message
      });
    }
  }
  else if (req.method === "PUT") {
    // Handle complete job updates (for edit functionality)
    try {
      const client = await MongoClient.connect(uri);
      const db = client.db();

      // Get the existing job to make sure it exists
      const existingJob = await db.collection("jobs").findOne({ _id: jobObjectId });

      if (!existingJob) {
        await client.close();
        return res.status(404).json({ message: "Job not found" });
      }

      // Prepare the update object
      const updateData = {
        ...req.body,
        updatedAt: new Date(),
        // Preserve original creation data
        createdAt: existingJob.createdAt,
        postedBy: existingJob.postedBy,
        _id: existingJob._id
      };

      // Remove the _id from the update data as it shouldn't be updated
      delete updateData._id;

      // Update the job
      const result = await db.collection("jobs").replaceOne(
        { _id: jobObjectId },
        updateData
      );

      if (result.matchedCount === 0) {
        await client.close();
        return res.status(404).json({ message: "Job not found" });
      }

      // Get the updated job to return
      const updatedJob = await db.collection("jobs").findOne({ _id: jobObjectId });

      await client.close();

      return res.status(200).json({ 
        success: true,
        message: "Job updated successfully",
        job: updatedJob
      });
    } catch (error) {
      console.error("❌ Error updating job:", error);
      return res.status(500).json({ 
        message: "Internal Server Error", 
        error: error.message
      });
    }
  }
  else {
    return res.status(405).json({ message: "Method Not Allowed" });
  }
}
