// File: pages/api/jobs/post.js
// Simplified version - Job Posting Only (No Question Generation)

import connectToDatabase from "../../../lib/mongodb";
import mongoose from "mongoose";
import { getUserEmail } from "../../../lib/auth-helper";

export const config = {
  api: {
    bodyParser: true, // Re-enable bodyParser since we're not handling file uploads
  },
};

const jobSchema = new mongoose.Schema({
  title: String,
  company: String,
  industry: String,
  experience: String,
  employmentType: String,
  workMode: String,
  salary: String,
  country: String,
  city: String,
  location: String,
  description: String,
  requiredSkills: [String],
  preferredSkills: [String],
  applicationDeadline: String,
  screeningDeadline: String,
  website: String,
  postedBy: String,
  visaSponsorship: String,
  createdAt: { type: Date, default: Date.now },
  // Keep only essential fields
  isTechnicalRole: Boolean
});

const Job = mongoose.models.Job || mongoose.model("Job", jobSchema);

export default async function handler(req, res) {
  try {
    // Connect to MongoDB first to fail fast if connection issues
    await connectToDatabase();
    
    if (req.method !== "POST") {
      return res.status(405).json({ 
        success: false, 
        message: "Method Not Allowed",
        details: "This endpoint only accepts POST requests"
      });
    }

    // Get the authenticated user's email - NO DEFAULTS!
    const userEmail = await getUserEmail(req);
    
    if (!userEmail) {
      return res.status(401).json({
        success: false,
        message: "Authentication required to post jobs",
        details: "Please log in and try again"
      });
    }
    
    console.log(`👤 Authenticated user posting job: ${userEmail}`);

    // Extract form data directly from request body
    const {
      title,
      company,
      industry,
      experience,
      employmentType,
      workMode,
      salary,
      country,
      city,
      description,
      requiredSkills,
      preferredSkills,
      applicationDeadline,
      screeningDeadline,
      website,
      visaSponsorship,
      questionSource
    } = req.body;

    // Debug: Log the received data
    console.log("📋 Received job data:", {
      title,
      company,
      industry,
      questionSource,
      hasApplicationDeadline: !!applicationDeadline,
      hasScreeningDeadline: !!screeningDeadline
    });

    // Comprehensive validation
    const validationErrors = [];
    
    // Required fields
    if (!title) validationErrors.push("Job title is required");
    if (!company) validationErrors.push("Company name is required");
    if (!industry) validationErrors.push("Industry is required");
    
    // Date validations
    if (applicationDeadline) {
      const appDeadline = new Date(applicationDeadline);
      const currentDate = new Date();
      currentDate.setHours(0, 0, 0, 0); // Reset time to start of day for fair comparison

      console.log("📅 Application deadline validation:", {
        applicationDeadline,
        appDeadline: appDeadline.toISOString(),
        currentDate: currentDate.toISOString(),
        isValid: !isNaN(appDeadline.getTime()),
        isInFuture: appDeadline >= currentDate
      });

      if (isNaN(appDeadline.getTime())) {
        validationErrors.push("Application deadline is not a valid date");
      } else if (appDeadline < currentDate) {
        validationErrors.push("Application deadline cannot be in the past");
      }
    }

    if (screeningDeadline) {
      const scrDeadline = new Date(screeningDeadline);
      const appDeadline = applicationDeadline ? new Date(applicationDeadline) : null;

      console.log("📅 Screening deadline validation:", {
        screeningDeadline,
        scrDeadline: scrDeadline.toISOString(),
        appDeadline: appDeadline ? appDeadline.toISOString() : null,
        isValid: !isNaN(scrDeadline.getTime()),
        isAfterAppDeadline: !appDeadline || scrDeadline >= appDeadline
      });

      if (isNaN(scrDeadline.getTime())) {
        validationErrors.push("Screening deadline is not a valid date");
      } else if (appDeadline && scrDeadline < appDeadline) {
        validationErrors.push("Screening deadline cannot be before application deadline");
      }
    }
    
    // Check if we have validation errors
    if (validationErrors.length > 0) {
      console.log("❌ Validation failed with errors:", validationErrors);
      return res.status(400).json({
        success: false,
        message: "Validation failed",
        errors: validationErrors
      });
    }

    // Process location from autocomplete
    console.log('🌍 Processing location from autocomplete...');
    const location = [country, city].filter(Boolean).join(", ");
    
    console.log(`Location: ${location}`);
    
    // Process skills arrays
    const processedRequiredSkills = requiredSkills ? 
      (typeof requiredSkills === 'string' ? 
        requiredSkills.split(',').map(s => s.trim()).filter(s => s) : 
        requiredSkills) : 
      [];
      
    const processedPreferredSkills = preferredSkills ? 
      (typeof preferredSkills === 'string' ? 
        preferredSkills.split(',').map(s => s.trim()).filter(s => s) : 
        preferredSkills) : 
      [];

    // Determine if job is technical (for interview question generation later)
    const isTechnicalRole = checkIfTechnicalRole({
      title,
      description,
      requiredSkills: processedRequiredSkills,
      industry
    });

    // Create the job with location from autocomplete
    const job = new Job({
      title,
      company,
      industry,
      experience,
      employmentType,
      workMode,
      salary,
      country: country || '',
      city: city || '',
      location,
      description,
      requiredSkills: processedRequiredSkills,
      preferredSkills: processedPreferredSkills,
      applicationDeadline,
      screeningDeadline,
      website,
      postedBy: userEmail,
      visaSponsorship,
      questionSource,
      isTechnicalRole,
      createdAt: new Date()
    });

    // Log the job creation
    console.log("✅ Job being created with:");
    console.log("  - Country:", job.country);
    console.log("  - City:", job.city);
    console.log("  - Location:", job.location);
    console.log("  - Posted by:", job.postedBy);
    console.log("  - Technical role:", job.isTechnicalRole);

    // Save the job
    const savedJob = await job.save();
    const jobId = savedJob._id.toString();
    
    console.log("✅ Job created successfully with ID:", jobId);
    
    return res.status(201).json({ 
      success: true, 
      jobId: jobId,
      message: "Job created successfully",
      location: {
        country: country || '',
        city: city || '',
        combined: location
      },
      technical: isTechnicalRole
    });
      
  } catch (error) {
    console.error("❌ Job Posting Error:", error);
    return res.status(500).json({ 
      success: false, 
      message: "Server error while posting job",
      details: error.message
    });
  }
}

// Helper function to determine if a role is technical
function checkIfTechnicalRole(job) {
  const technicalKeywords = [
    'developer', 'engineer', 'programmer', 'software', 'data scientist', 
    'devops', 'sre', 'architect', 'coding', 'coder', 'technical', 'java', 
    'python', 'javascript', 'c#', 'c++', 'ruby', 'php', 'golang', 'react', 
    'angular', 'vue', 'node', 'aws', 'azure', 'cloud', 'database', 'sql', 
    'nosql', 'mongodb', 'postgres', 'mysql', 'oracle', 'fullstack', 'frontend', 
    'backend', 'qa', 'quality assurance', 'automation'
  ];
  
  const jobTitle = (job.title || '').toLowerCase();
  const jobDescription = (job.description || '').toLowerCase();
  const requiredSkills = Array.isArray(job.requiredSkills) 
    ? job.requiredSkills.join(' ').toLowerCase() 
    : (typeof job.requiredSkills === 'string' ? job.requiredSkills.toLowerCase() : '');
  
  const jobText = jobTitle + ' ' + jobDescription + ' ' + requiredSkills;
  
  return technicalKeywords.some(keyword => jobText.includes(keyword.toLowerCase()));
}
