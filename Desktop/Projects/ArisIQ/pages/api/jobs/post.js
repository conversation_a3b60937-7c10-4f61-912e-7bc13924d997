// File: pages/api/jobs/post.js
// Complete fix with Job ID generation

import connectToDatabase from "../../../lib/mongodb";
import mongoose from "mongoose";
import { getUserEmail } from "../../../lib/auth-helper";

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '10mb',
    },
  },
};

const jobSchema = new mongoose.Schema({
  jobId: { type: String, unique: true, required: true }, // Custom Job ID
  title: String,
  company: String,
  industry: String,
  experience: String,
  employmentType: String,
  workMode: String,
  salary: String,
  country: String,
  city: String,
  location: String,
  description: String,
  requiredSkills: [String],
  preferredSkills: [String],
  applicationDeadline: String,
  screeningDeadline: String,
  website: String,
  postedBy: String,
  visaSponsorship: String,
  createdAt: { type: Date, default: Date.now },
  isTechnicalRole: Boolean
});

const Job = mongoose.models.Job || mongoose.model("Job", jobSchema);

// Function to generate unique Job ID
function generateJobId() {
  const prefix = 'J';
  const random1 = Math.floor(Math.random() * 1000).toString().padStart(3, '0'); // 3 digits
  const random2 = Math.floor(Math.random() * 1000).toString().padStart(3, '0'); // 3 digits
  return `${prefix}${random1}${random2}`; // Format: J123456
}

// Function to ensure Job ID is unique
async function getUniqueJobId() {
  let jobId;
  let isUnique = false;
  let attempts = 0;
  
  while (!isUnique && attempts < 10) {
    jobId = generateJobId();
    const existingJob = await Job.findOne({ jobId });
    if (!existingJob) {
      isUnique = true;
    }
    attempts++;
  }
  
  if (!isUnique) {
    throw new Error('Failed to generate unique job ID');
  }
  
  return jobId;
}

export default async function handler(req, res) {
  try {
    console.log("🚀 Starting job posting process...");
    console.log("📨 Request method:", req.method);
    console.log("📋 Content-Type:", req.headers['content-type']);
    console.log("📋 Request body type:", typeof req.body);
    
    // Connect to MongoDB first
    await connectToDatabase();
    console.log("✅ Connected to MongoDB");
    
    if (req.method !== "POST") {
      return res.status(405).json({ 
        success: false, 
        message: "Method Not Allowed",
        details: "This endpoint only accepts POST requests"
      });
    }

    // Handle both FormData and JSON
    let formData;
    
    if (req.headers['content-type']?.includes('multipart/form-data')) {
      // If it's FormData, we need to parse it differently
      console.log("📦 Parsing FormData...");
      
      // For FormData without external libraries, we'll convert to JSON on frontend
      return res.status(400).json({
        success: false,
        message: "Please send data as JSON, not FormData",
        details: "FormData parsing not configured"
      });
    } else {
      // Regular JSON data
      formData = req.body;
      console.log("📝 Processing JSON data...");
    }

    console.log("📋 Form data keys:", Object.keys(formData || {}));

    // Get the authenticated user's email
    const userEmail = await getUserEmail(req);
    
    if (!userEmail) {
      console.log("❌ Authentication failed");
      return res.status(401).json({
        success: false,
        message: "Authentication required to post jobs",
        details: "Please log in and try again"
      });
    }
    
    console.log(`👤 Authenticated user posting job: ${userEmail}`);

    // Extract form data
    const {
      title,
      company,
      industry,
      experience,
      employmentType,
      workMode,
      salary,
      country,
      city,
      description,
      requiredSkills,
      preferredSkills,
      applicationDeadline,
      screeningDeadline,
      website,
      visaSponsorship,
      questionSource
    } = formData;

    console.log("🔍 Validation check:");
    console.log("  - title:", title);
    console.log("  - company:", company);
    console.log("  - industry:", industry);

    // Comprehensive validation
    const validationErrors = [];
    
    // Required fields
    if (!title?.trim()) validationErrors.push("Job title is required");
    if (!company?.trim()) validationErrors.push("Company name is required");
    if (!industry?.trim()) validationErrors.push("Industry is required");
    
    // Date validations
    if (applicationDeadline) {
      const appDeadline = new Date(applicationDeadline);
      const currentDate = new Date();
      currentDate.setHours(0, 0, 0, 0); // Reset to start of day for fair comparison
      
      if (isNaN(appDeadline.getTime())) {
        validationErrors.push("Application deadline is not a valid date");
      } else if (appDeadline < currentDate) {
        validationErrors.push("Application deadline cannot be in the past");
      }
    }
    
    if (screeningDeadline) {
      const scrDeadline = new Date(screeningDeadline);
      const appDeadline = applicationDeadline ? new Date(applicationDeadline) : null;
      
      if (isNaN(scrDeadline.getTime())) {
        validationErrors.push("Screening deadline is not a valid date");
      } else if (appDeadline && scrDeadline < appDeadline) {
        validationErrors.push("Screening deadline cannot be before application deadline");
      }
    }
    
    // Check if we have validation errors
    if (validationErrors.length > 0) {
      console.log("❌ Validation failed:", validationErrors);
      return res.status(400).json({
        success: false,
        message: "Validation failed",
        errors: validationErrors
      });
    }

    // Generate unique Job ID
    console.log("🆔 Generating unique Job ID...");
    const customJobId = await getUniqueJobId();
    console.log("✅ Generated Job ID:", customJobId);

    // Process location from autocomplete
    console.log('🌍 Processing location from autocomplete...');
    const location = [country, city].filter(Boolean).join(", ");
    console.log(`Location: ${location}`);
    
    // Process skills arrays
    const processedRequiredSkills = requiredSkills ? 
      (typeof requiredSkills === 'string' ? 
        requiredSkills.split(',').map(s => s.trim()).filter(s => s) : 
        requiredSkills) : 
      [];
      
    const processedPreferredSkills = preferredSkills ? 
      (typeof preferredSkills === 'string' ? 
        preferredSkills.split(',').map(s => s.trim()).filter(s => s) : 
        preferredSkills) : 
      [];

    // Determine if job is technical
    const isTechnicalRole = checkIfTechnicalRole({
      title,
      description,
      requiredSkills: processedRequiredSkills,
      industry
    });

    // Create the job with custom Job ID
    const job = new Job({
      jobId: customJobId, // Custom Job ID
      title: title?.trim(),
      company: company?.trim(),
      industry: industry?.trim(),
      experience: experience?.trim() || '',
      employmentType: employmentType || 'Full-time',
      workMode: workMode?.trim() || '',
      salary: salary?.trim() || '',
      country: country?.trim() || '',
      city: city?.trim() || '',
      location,
      description: description?.trim() || '',
      requiredSkills: processedRequiredSkills,
      preferredSkills: processedPreferredSkills,
      applicationDeadline: applicationDeadline || '',
      screeningDeadline: screeningDeadline || '',
      website: website?.trim() || '',
      postedBy: userEmail,
      visaSponsorship: visaSponsorship || 'no',
      isTechnicalRole,
      createdAt: new Date()
    });

    // Log the job creation
    console.log("✅ Job being created with:");
    console.log("  - Job ID:", job.jobId);
    console.log("  - Title:", job.title);
    console.log("  - Company:", job.company);
    console.log("  - Country:", job.country);
    console.log("  - City:", job.city);
    console.log("  - Location:", job.location);
    console.log("  - Posted by:", job.postedBy);
    console.log("  - Technical role:", job.isTechnicalRole);

    // Save the job
    const savedJob = await job.save();
    const mongoId = savedJob._id.toString();
    
    console.log("✅ Job created successfully!");
    console.log("  - Custom Job ID:", savedJob.jobId);
    console.log("  - MongoDB ID:", mongoId);
    
    return res.status(201).json({ 
      success: true, 
      jobId: savedJob.jobId, // Return custom Job ID
      mongoId: mongoId, // Also return MongoDB ID if needed
      message: "Job created successfully",
      location: {
        country: country?.trim() || '',
        city: city?.trim() || '',
        combined: location
      },
      technical: isTechnicalRole,
      questionSource: questionSource || ''
    });
      
  } catch (error) {
    console.error("❌ Job Posting Error:", error);
    
    // Handle specific mongoose errors
    if (error.code === 11000) {
      return res.status(400).json({ 
        success: false, 
        message: "Job ID conflict, please try again",
        details: "A job with this ID already exists"
      });
    }
    
    return res.status(500).json({ 
      success: false, 
      message: "Server error while posting job",
      details: error.message
    });
  }
}

// Helper function to determine if a role is technical
function checkIfTechnicalRole(job) {
  const technicalKeywords = [
    'developer', 'engineer', 'programmer', 'software', 'data scientist', 
    'devops', 'sre', 'architect', 'coding', 'coder', 'technical', 'java', 
    'python', 'javascript', 'c#', 'c++', 'ruby', 'php', 'golang', 'react', 
    'angular', 'vue', 'node', 'aws', 'azure', 'cloud', 'database', 'sql', 
    'nosql', 'mongodb', 'postgres', 'mysql', 'oracle', 'fullstack', 'frontend', 
    'backend', 'qa', 'quality assurance', 'automation', 'machine learning',
    'ai', 'artificial intelligence', 'cybersecurity', 'blockchain'
  ];
  
  const jobTitle = (job.title || '').toLowerCase();
  const jobDescription = (job.description || '').toLowerCase();
  const requiredSkills = Array.isArray(job.requiredSkills) 
    ? job.requiredSkills.join(' ').toLowerCase() 
    : (typeof job.requiredSkills === 'string' ? job.requiredSkills.toLowerCase() : '');
  
  const jobText = jobTitle + ' ' + jobDescription + ' ' + requiredSkills;
  
  return technicalKeywords.some(keyword => jobText.includes(keyword.toLowerCase()));
}
