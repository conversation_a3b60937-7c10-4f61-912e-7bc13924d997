// File: pages/api/jobs/index.js

import connectDB from "../../../lib/mongodb";
import mongoose from "mongoose";

// Job Schema
const jobSchema = new mongoose.Schema({}, { strict: false });
const Job = mongoose.models.Job || mongoose.model("Job", jobSchema);

// Application Schema
const appSchema = new mongoose.Schema({}, { strict: false });
const Application = mongoose.models.Application || mongoose.model("Application", appSchema);

export default async function handler(req, res) {
  await connectDB();

  if (req.method === "GET") {
    try {
      // Log request parameters for debugging
      console.log("GET /api/jobs query params:", req.query);
      
      // Get all jobs
      const jobs = await Job.find({}).lean();
      console.log(`Found ${jobs.length} total jobs in database`);
      
      // Log the keys of jobs for debugging
      if (jobs.length > 0) {
        console.log("Sample job fields:", Object.keys(jobs[0]));
        console.log("Sample job postedBy:", jobs[0].postedBy || "undefined");
      }

      // Get all applications
      const allApps = await Application.find({}).lean();
      console.log(`Found ${allApps.length} total applications in database`);

      // Count applications per job
      const appMap = {};
      for (const app of allApps) {
        const jobId = app.jobId?.toString();
        if (jobId) {
          appMap[jobId] = (appMap[jobId] || 0) + 1;
        }
      }

      // Add applicationCount to each job
      const jobsWithCount = jobs.map((job) => ({
        ...job,
        _id: job._id.toString(),
        applicationCount: appMap[job._id.toString()] || 0,
        // Ensure postedBy exists to avoid filtering issues
        postedBy: job.postedBy || req.query.email || null
      }));

      res.status(200).json({ success: true, jobs: jobsWithCount });
    } catch (error) {
      console.error("Job list fetch error:", error);
      res.status(500).json({ success: false, error: error.message });
    }
  } else {
    res.status(405).json({ message: "Method Not Allowed" });
  }
}
