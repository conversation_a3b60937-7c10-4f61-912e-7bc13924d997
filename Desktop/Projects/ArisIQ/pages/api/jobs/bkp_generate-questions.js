// File: pages/api/jobs/generate-questions.js

import { MongoClient, ObjectId } from 'mongodb';
import { OpenAI } from 'openai';
import axios from 'axios';

// Initialize API clients
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
const uri = process.env.MONGODB_URI;

// Number of questions to generate per job
const QUESTION_BANK_SIZE = 2000;
const BATCH_SIZE = 50; // Process in batches to avoid timeouts

// API configurations with response caching for faster repeat queries
const apiCalls = new Map(); // Cache for API calls
const apiConfigs = {
  openai: {
    enabled: true,
    name: 'OpenAI',
    // Using the existing openai client
    cacheTimeout: 3600000 // 1 hour cache
  },
  gemini: {
    enabled: !!process.env.GEMINI_API_KEY,
    name: 'Google Gemini',
    endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent',
    formatRequest: (prompt) => ({
      contents: [{ parts: [{ text: prompt }] }],
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 8192
      }
    }),
    parseResponse: (response) => {
      return response.data.candidates[0].content.parts[0].text;
    },
    cacheTimeout: 3600000 // 1 hour cache
  },
  claude: {
    enabled: !!process.env.CLAUDE_API_KEY,
    name: 'Anthropic Claude',
    endpoint: 'https://api.anthropic.com/v1/messages',
    formatRequest: (prompt) => ({
      model: 'claude-3-haiku-20240307',
      max_tokens: 4000,
      messages: [{ role: 'user', content: prompt }]
    }),
    parseResponse: (response) => {
      return response.data.content[0].text;
    },
    cacheTimeout: 3600000 // 1 hour cache
  },
  mistral: {
    enabled: !!process.env.MISTRAL_API_KEY,
    name: 'Mistral AI',
    endpoint: 'https://api.mistral.ai/v1/chat/completions',
    formatRequest: (prompt) => ({
      model: 'mistral-small',
      messages: [{ role: 'user', content: prompt }]
    }),
    parseResponse: (response) => {
      return response.data.choices[0].message.content;
    },
    cacheTimeout: 3600000 // 1 hour cache
  },
  grok: {
    enabled: !!process.env.GROK_API_KEY,
    name: 'Grok AI',
    endpoint: 'https://api.grok.ai/v1/chat/completions',
    formatRequest: (prompt) => ({
      model: 'grok-1',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7,
      max_tokens: 4000
    }),
    parseResponse: (response) => {
      return response.data.choices[0].message.content;
    },
    cacheTimeout: 3600000 // 1 hour cache
  }
};

// Helper to clean Markdown and repair minor formatting issues
function extractJson(text) {
  try {
    const match = text.match(/```json([\s\S]*?)```/i);
    const raw = match ? match[1].trim() : text.trim();

    // Fix common malformed keys (e.g., type: -> "type":)
    const fixed = raw.replace(/([{,])\s*(\w+)\s*:/g, '$1 "$2":');
    return JSON.parse(fixed);
  } catch (err) {
    console.warn("❌ Failed to parse JSON:", err.message);
    return [];
  }
}

// Function to call an API with retries and caching for faster responses
async function callAPI(provider, prompt, maxRetries = 3) {
  const config = apiConfigs[provider];
  let retries = 0;

  // Skip if not enabled
  if (!config.enabled) {
    console.log(`⏭️ Skipping ${config.name} - not enabled`);
    return [];
  }

  // Create a cache key based on provider and prompt
  const cacheKey = `${provider}-${prompt.substring(0, 100)}`;
  
  // Check if we have a cached response
  const cachedCall = apiCalls.get(cacheKey);
  if (cachedCall && (Date.now() - cachedCall.timestamp < config.cacheTimeout)) {
    console.log(`✅ Using cached response for ${config.name}`);
    return cachedCall.data;
  }

  while (retries < maxRetries) {
    try {
      console.log(`🔄 Calling ${config.name} API...`);
      
      let response;
      let content;
      let parsed;
      
      if (provider === 'openai') {
        // Use the OpenAI SDK
        response = await openai.chat.completions.create({
          model: 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.7,
        });
        
        content = response.choices?.[0]?.message?.content || '';
        parsed = extractJson(content);
        
        if (Array.isArray(parsed)) {
          const result = parsed.map(q => ({...q, source: provider}));
          
          // Cache the result
          apiCalls.set(cacheKey, {
            timestamp: Date.now(),
            data: result
          });
          
          return result;
        }
        return [];
      } 
      else if (provider === 'gemini') {
        // Google's Gemini API call with timeout
        const apiKey = process.env.GEMINI_API_KEY;
        const url = `${config.endpoint}?key=${apiKey}`;
        
        // Log the URL being used (without the API key)
        console.log(`🔍 Using Gemini endpoint: ${config.endpoint}`);
        
        // Add timeout to prevent hanging requests
        response = await axios.post(
          url,
          config.formatRequest(prompt),
          { 
            headers: { 'Content-Type': 'application/json' },
            timeout: 30000 // 30 second timeout
          }
        );
      } 
      else {
        // For all other APIs
        const headers = {};
        
        if (provider === 'claude') {
          headers['anthropic-version'] = '2023-06-01';
          headers['x-api-key'] = process.env.CLAUDE_API_KEY;
        } 
        else if (provider === 'mistral') {
          headers['Authorization'] = `Bearer ${process.env.MISTRAL_API_KEY}`;
        } 
        else if (provider === 'grok') {
          headers['Authorization'] = `Bearer ${process.env.GROK_API_KEY}`;
          headers['Content-Type'] = 'application/json';
        }
        
        headers['Content-Type'] = 'application/json';
        
        response = await axios.post(
          config.endpoint,
          config.formatRequest(prompt),
          { headers }
        );
      }
      
      // Parse the response for non-OpenAI providers
      if (provider !== 'openai') {
        const textContent = config.parseResponse(response);
        const parsed = extractJson(textContent);
        
        if (Array.isArray(parsed)) {
          const result = parsed.map(q => ({...q, source: provider}));
          
          // Cache the result
          apiCalls.set(cacheKey, {
            timestamp: Date.now(),
            data: result
          });
          
          return result;
        }
        return [];
      }
      
    } catch (error) {
      retries++;
      console.error(`❌ Error calling ${config.name} API (attempt ${retries}/${maxRetries}):`, error.message);
      
      if (retries >= maxRetries) {
        console.error(`❌ Failed to call ${config.name} API after ${maxRetries} attempts`);
        return [];
      }
      
      // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retries)));
    }
  }
  
  return [];
}

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { jobId } = req.body;
    
    if (!jobId) {
      return res.status(400).json({ success: false, message: 'Missing jobId parameter' });
    }

    // Connect to MongoDB
    const client = await MongoClient.connect(uri);
    const db = client.db();
    
    // Get job details
    const job = await db.collection('jobs').findOne({ _id: new ObjectId(jobId) });
    if (!job) {
      await client.close();
      return res.status(404).json({ success: false, message: 'Job not found' });
    }
    
    // Check if questions already exist for this job
    const existingQuestionsCount = await db.collection('question_bank').countDocuments({
      jobId: new ObjectId(jobId)
    });

    if (existingQuestionsCount > 0) {
      await client.close();
      return res.status(200).json({ 
        success: true,
        message: `Question bank already exists with ${existingQuestionsCount} questions`,
        count: existingQuestionsCount
      });
    }

    // Determine if job requires technical skills
    const isTechnicalRole = checkIfTechnicalRole(job);
    
    // Start the generation process asynchronously - don't wait for completion
    process.nextTick(() => {
      startQuestionGeneration(job, isTechnicalRole)
        .catch(err => console.error('Error in background question generation process:', err));
    });
    
    await client.close();
    
    // Return success immediately, don't wait for generation to complete
    return res.status(200).json({ 
      success: true,
      message: 'Question generation started. This process will continue in the background.',
      jobId: jobId,
      isTechnicalRole: isTechnicalRole
    });
    
  } catch (error) {
    console.error('Error initiating question generation:', error);
    return res.status(500).json({ success: false, message: 'Failed to initiate question generation' });
  }
}

// Helper function to determine if a role is technical
function checkIfTechnicalRole(job) {
  // Keywords that indicate technical role
  const technicalKeywords = [
    'developer', 'engineer', 'programmer', 'software', 'data scientist', 
    'devops', 'sre', 'architect', 'coding', 'coder', 'technical', 'java', 
    'python', 'javascript', 'c#', 'c++', 'ruby', 'php', 'golang', 'react', 
    'angular', 'vue', 'node', 'aws', 'azure', 'cloud', 'database', 'sql', 
    'nosql', 'mongodb', 'postgres', 'mysql', 'oracle', 'fullstack', 'frontend', 
    'backend', 'qa', 'quality assurance', 'automation'
  ];
  
  // Check if any technical keyword is in the job title or description
  const jobTitle = (job.title || '').toLowerCase();
  const jobDescription = (job.description || '').toLowerCase();
  const requiredSkills = Array.isArray(job.requiredSkills) 
    ? job.requiredSkills.join(' ').toLowerCase() 
    : (typeof job.requiredSkills === 'string' ? job.requiredSkills.toLowerCase() : '');
  
  const jobText = jobTitle + ' ' + jobDescription + ' ' + requiredSkills;
  
  return technicalKeywords.some(keyword => jobText.includes(keyword.toLowerCase()));
}

// Main function to start the question generation process
async function startQuestionGeneration(job, isTechnicalRole) {
  try {
    console.log(`🔄 Starting question generation for job ${job._id} (${job.title})`);
    
    const client = await MongoClient.connect(uri);
    const db = client.db();
    
    // Set up question distribution based on role type
    let questionDistribution;
    
    if (isTechnicalRole) {
      // Technical role: 10 MCQ, 4 Coding, 3 Technical, 3 Behavioral per 20
      questionDistribution = {
        mcq: Math.ceil(QUESTION_BANK_SIZE * (10/20)),        // 50% of questions
        coding: Math.ceil(QUESTION_BANK_SIZE * (4/20)),      // 20% of questions
        technical: Math.ceil(QUESTION_BANK_SIZE * (3/20)),   // 15% of questions
        behavioral: Math.ceil(QUESTION_BANK_SIZE * (3/20))   // 15% of questions
      };
    } else {
      // Non-technical: 15 MCQ, 5 Behavioral, 2-3 Scenario per 22.5
      // Using 2.5 as average for scenario questions
      questionDistribution = {
        mcq: Math.ceil(QUESTION_BANK_SIZE * (15/22.5)),        // ~67% of questions
        behavioral: Math.ceil(QUESTION_BANK_SIZE * (5/22.5)),   // ~22% of questions
        scenario: Math.ceil(QUESTION_BANK_SIZE * (2.5/22.5))    // ~11% of questions
      };
    }
    
    // Log the planned distribution
    console.log(`Question distribution for job ${job._id}:`, questionDistribution);
    
    // Update job status to indicate generation started
    await db.collection('jobs').updateOne(
      { _id: job._id },
      { $set: { 
        questionBankGenerating: true, 
        questionBankGenerationStartedAt: new Date(),
        isTechnicalRole: isTechnicalRole
      } }
    );
    
    // Process each question type in batches
    for (const [type, count] of Object.entries(questionDistribution)) {
      const batches = Math.ceil(count / BATCH_SIZE);
      
      for (let i = 0; i < batches; i++) {
        const batchCount = Math.min(BATCH_SIZE, count - (i * BATCH_SIZE));
        if (batchCount <= 0) continue;
        
        // Log progress to server console
        console.log(`Generating batch ${i+1}/${batches} of ${type} questions for job ${job._id}`);
        
        let questions = [];
        
        try {
          // Generate different prompts based on question type
          const prompt = generatePromptForQuestionType(job, type, batchCount);
          
          // Get enabled API providers
          const enabledProviders = Object.keys(apiConfigs).filter(key => apiConfigs[key].enabled);
          
          // Try multiple providers until we get enough questions
          for (const provider of enabledProviders) {
            if (questions.length >= batchCount) break;
            
            try {
              const providerQuestions = await callAPI(provider, prompt);
              if (providerQuestions && providerQuestions.length > 0) {
                // Take only what we need to fill the batch
                const remaining = batchCount - questions.length;
                const newQuestions = providerQuestions.slice(0, remaining);
                
                questions.push(...newQuestions);
                console.log(`Got ${newQuestions.length} ${type} questions from ${provider}`);
              }
            } catch (providerError) {
              console.error(`Error with provider ${provider}:`, providerError.message);
              // Continue with next provider
            }
          }
          
          // Process and enhance questions
          questions = processQuestions(questions, type, isTechnicalRole);
          
          // Add metadata to questions
          questions = questions.map(q => ({
            ...q,
            id: `${type}-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`,
            jobId: job._id,
            type: type, // Ensure type is set
            used: false,
            usedBy: [],
            createdAt: new Date()
          }));
          
          // Store questions in database
          if (questions.length > 0) {
            await db.collection('question_bank').insertMany(questions);
            console.log(`✅ Inserted ${questions.length} ${type} questions for job ${job._id}`);
          }
          
        } catch (error) {
          console.error(`Error generating ${type} questions batch ${i+1}:`, error);
          // Continue with next batch even if this one failed
        }
        
        // Small delay to avoid API rate limits
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    // Update job record to indicate question generation is complete
    await db.collection('jobs').updateOne(
      { _id: job._id },
      { 
        $set: { 
          questionBankGenerated: true, 
          questionBankGeneratedAt: new Date(),
          questionBankGenerating: false
        } 
      }
    );
    
    // Count the total generated questions
    const totalQuestions = await db.collection('question_bank').countDocuments({
      jobId: job._id
    });
    
    console.log(`✅ Completed question bank generation for job ${job._id}. Total questions: ${totalQuestions}`);
    await client.close();
    
  } catch (error) {
    console.error('Error in question generation process:', error);
    
    // Try to update job status to indicate failure
    try {
      const client = await MongoClient.connect(uri);
      const db = client.db();
      
      await db.collection('jobs').updateOne(
        { _id: job._id },
        { 
          $set: { 
            questionBankGenerating: false,
            questionBankGenerationFailed: true,
            questionBankGenerationError: error.message
          } 
        }
      );
      
      await client.close();
    } catch (dbError) {
      console.error('Error updating job status after generation failure:', dbError);
    }
  }
}

// Generate prompts specific to each question type
function generatePromptForQuestionType(job, type, count) {
  const basePrompt = `
    Job Title: ${job.title}
    Industry: ${job.industry || 'Not specified'}
    Description: ${job.description || 'Not specified'}
    Required Skills: ${Array.isArray(job.requiredSkills) ? job.requiredSkills.join(', ') : job.requiredSkills || 'Not specified'}
    
    I need ${count} high-quality interview questions of type "${type}" for this job role.
    
    Ensure that:
    - Questions are directly relevant to the job requirements
    - Questions have varying difficulty levels (mark each as "basic", "intermediate", or "advanced")
    - Questions are clear and concise
    - No questions are repeated
    
    Format your response as a JSON array of question objects:
  `;
  
  // Add type-specific instructions
  switch (type) {
    case 'mcq':
      return `${basePrompt}
        [
          {
            "type": "mcq",
            "question": "Your multiple choice question here",
            "difficulty": "basic" | "intermediate" | "advanced",
            "options": ["a) Option 1", "b) Option 2", "c) Option 3", "d) Option 4"],
            "answer": "b) Option 2",
            "explanation": "Brief explanation of why this is the correct answer"
          }
        ]
        
        Create ${count} diverse MCQ questions that test knowledge relevant to the position.
        Ensure options are plausible and not obviously incorrect.
        Include one clear correct answer per question.
      `;
      
    case 'coding':
      return `${basePrompt}
        [
          {
            "type": "coding",
            "question": "Your coding problem statement here",
            "difficulty": "basic" | "intermediate" | "advanced",
            "language": "javascript" | "python" | "java" | "cpp",
            "starterCode": "function example() {\\n  // Your code here\\n}",
            "testCases": ["input1 -> output1", "input2 -> output2"],
            "expectedOutput": "Expected output description"
          }
        ]
        
        Create ${count} coding questions that test practical programming skills relevant to the job.
        Focus on real-world problems the candidate might face in this role.
        Include appropriate starter code and test cases.
        Vary the language based on what's likely needed for the job.
      `;
      
    case 'technical':
      return `${basePrompt}
        [
          {
            "type": "technical",
            "question": "Your technical concept question here",
            "difficulty": "basic" | "intermediate" | "advanced",
            "keyPoints": ["Point 1", "Point 2", "Point 3"],
            "sampleAnswer": "A sample exemplary answer that addresses the key points"
          }
        ]
        
        Create ${count} technical concept questions that test understanding of relevant technologies, methodologies, or principles.
        These questions should require written explanations rather than code.
        Include key points that should be addressed in a good answer.
      `;
      
    case 'behavioral':
      return `${basePrompt}
        [
          {
            "type": "behavioral",
            "question": "Your behavioral question here (e.g., 'Tell me about a time when...')",
            "difficulty": "basic" | "intermediate" | "advanced",
            "keyPoints": ["Point 1", "Point 2", "Point 3"],
            "sampleAnswer": "A sample exemplary answer that demonstrates the desired soft skills"
          }
        ]
        
        Create ${count} behavioral questions that assess soft skills, communication, teamwork, and problem-solving.
        Focus on situations relevant to the job role and industry.
        Include what a strong answer should demonstrate.
      `;
      
    case 'scenario':
      return `${basePrompt}
        [
          {
            "type": "scenario",
            "question": "Your industry-specific scenario question here",
            "difficulty": "basic" | "intermediate" | "advanced",
            "scenario": "Detailed description of a realistic work scenario",
            "keyPoints": ["Point 1", "Point 2", "Point 3"],
            "sampleAnswer": "A sample exemplary answer that demonstrates good judgment and industry knowledge"
          }
        ]
        
        Create ${count} scenario-based questions specific to the industry (${job.industry || 'general'}).
        Present realistic workplace situations the candidate might encounter in this role.
        Ask how they would handle or approach the scenario.
        Include key elements that should be present in a strong response.
      `;
      
    default:
      return basePrompt + `
        [
          {
            "type": "${type}",
            "question": "Your question here",
            "difficulty": "basic" | "intermediate" | "advanced"
          }
        ]
        
        Create ${count} relevant questions for the job role.
      `;
  }
}

// Process and enhance questions
function processQuestions(questions, type, isTechnicalRole) {
  return questions.map(q => {
    // Ensure question has proper type
    q.type = type;
    
    // Ensure question has difficulty
    if (!q.difficulty) {
      q.difficulty = 'intermediate'; // Default to intermediate
    }
    
    // Type-specific processing
    if (type === 'mcq') {
      // Ensure MCQ has options and answer
      if (!q.options || !Array.isArray(q.options) || q.options.length < 2) {
        q.options = [
          'a) Option 1',
          'b) Option 2',
          'c) Option 3',
          'd) Option 4'
        ];
      }
      
      if (!q.answer) {
        q.answer = q.options[0];
      }
      
      if (!q.explanation) {
        q.explanation = 'The selected option is the correct answer.';
      }
    } 
    else if (type === 'coding' && isTechnicalRole) {
      // Ensure coding questions have language and starter code
      if (!q.language) {
        // Try to infer language from starterCode if available
        if (q.starterCode && q.starterCode.includes('function')) {
          q.language = 'javascript';
        } else if (q.starterCode && q.starterCode.includes('def ')) {
          q.language = 'python';
        } else if (q.starterCode && q.starterCode.includes('class ') && q.starterCode.includes('public static')) {
          q.language = 'java';
        } else {
          // Default to JavaScript if can't determine
          q.language = 'javascript';
        }
      }
      
      if (!q.starterCode) {
        // Add basic starter code based on language
        switch (q.language.toLowerCase()) {
          case 'python':
            q.starterCode = "def solution():\n    # Your code here\n    pass\n\n# Test your solution\nprint(solution())";
            break;
          case 'java':
            q.starterCode = "public class Solution {\n    public static void main(String[] args) {\n        // Test your solution here\n        System.out.println(solution());\n    }\n\n    public static String solution() {\n        // Your code here\n        return \"\";\n    }\n}";
            break;
          case 'cpp':
            q.starterCode = "#include <iostream>\n\nstring solution() {\n    // Your code here\n    return \"\";\n}\n\nint main() {\n    // Test your solution\n    std::cout << solution() << std::endl;\n    return 0;\n}";
            break;
          default: // JavaScript
            q.starterCode = "function solution() {\n  // Your code here\n  return;\n}\n\n// Test your solution\nconsole.log(solution());";
        }
      }
      
      if (!q.testCases) {
        q.testCases = ["Example input -> Expected output"];
      }
    }
    else if (type === 'technical' || type === 'behavioral' || type === 'scenario') {
      // Ensure these types have keyPoints
      if (!q.keyPoints || !Array.isArray(q.keyPoints)) {
        q.keyPoints = ['Clarity of explanation', 'Depth of knowledge', 'Practical application'];
      }
      
      if (!q.sampleAnswer) {
        q.sampleAnswer = 'A complete answer would demonstrate understanding of the key concepts and practical application.';
      }
    }
    
    return q;
  });
}

export const config = {
  api: {
    bodyParser: true,
  },
};
