// File: pages/api/jobs/cleanup-questions.js

import { MongoClient, ObjectId } from 'mongodb';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { jobId } = req.body;
    
    if (!jobId) {
      return res.status(400).json({ success: false, message: 'Missing jobId parameter' });
    }

    // Connect to MongoDB
    const client = await MongoClient.connect(process.env.MONGODB_URI);
    const db = client.db();
    
    // Delete questions from the bank
    const deleteResult = await db.collection('question_bank').deleteMany({
      jobId: new ObjectId(jobId)
    });
    
    // Update job to indicate question bank was cleaned up
    await db.collection('jobs').updateOne(
      { _id: new ObjectId(jobId) },
      { $set: { questionBankCleaned: true, questionBankCleanedAt: new Date() } }
    );
    
    await client.close();
    
    return res.status(200).json({ 
      success: true,
      message: `Deleted ${deleteResult.deletedCount} questions from the question bank`,
      deletedCount: deleteResult.deletedCount
    });
    
  } catch (error) {
    console.error('Error cleaning up questions:', error);
    return res.status(500).json({ success: false, message: 'Failed to clean up questions' });
  }
}

export const config = {
  api: {
    bodyParser: true,
  },
};
