// File: pages/api/jobs/delete.js

import connectToDatabase from "../../../lib/mongodb";
import mongoose from "mongoose";

const jobSchema = new mongoose.Schema({}, { strict: false });
const Job = mongoose.models.Job || mongoose.model("Job", jobSchema);

export default async function handler(req, res) {
  await connectToDatabase();

  if (req.method !== "DELETE") {
    return res.status(405).json({ message: "Method Not Allowed" });
  }

  const { jobId, recruiterEmail } = req.body;

  if (!jobId || !recruiterEmail) {
    return res.status(400).json({ success: false, message: "Missing jobId or recruiterEmail" });
  }

  try {
    const job = await Job.findById(jobId);

    if (!job) {
      return res.status(404).json({ success: false, message: "Job not found" });
    }

    if (job.postedBy !== recruiterEmail) {
      return res.status(403).json({ success: false, message: "Unauthorized to delete this job" });
    }

    await Job.findByIdAndDelete(jobId);
    return res.status(200).json({ success: true, message: "Job deleted successfully" });
  } catch (error) {
    console.error("❌ Delete Job Error:", error);
    return res.status(500).json({ success: false, error: error.message });
  }
}

