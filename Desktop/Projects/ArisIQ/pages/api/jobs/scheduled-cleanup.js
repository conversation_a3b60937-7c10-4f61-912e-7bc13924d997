// File: pages/api/jobs/scheduled-cleanup.js
// This API handles automatic deletion of archived questions after 30 days

import connectToDatabase from "../../../lib/mongodb";
import mongoose from "mongoose";

const questionSchema = new mongoose.Schema({}, { strict: false });
const Questions = mongoose.models.Questions || mongoose.model("Questions", questionSchema);

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    await connectToDatabase();
    
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    console.log(`🧹 Starting scheduled cleanup for questions archived before: ${thirtyDaysAgo}`);

    // Delete questions from jobs closed more than 30 days ago
    const expiredQuestions = await Questions.deleteMany({
      status: 'archived',
      archivedAt: { $lt: thirtyDaysAgo },
      archivedReason: 'job_closed'
    });

    // Also clean up any orphaned questions (jobs that no longer exist)
    const orphanedQuestions = await Questions.aggregate([
      {
        $lookup: {
          from: "jobs",
          localField: "jobId",
          foreignField: "_id", 
          as: "job"
        }
      },
      {
        $match: {
          job: { $size: 0 } // No matching job found
        }
      }
    ]);

    let orphanedCount = 0;
    if (orphanedQuestions.length > 0) {
      const orphanedIds = orphanedQuestions.map(q => q._id);
      const orphanResult = await Questions.deleteMany({
        _id: { $in: orphanedIds }
      });
      orphanedCount = orphanResult.deletedCount;
    }

    const totalDeleted = expiredQuestions.deletedCount + orphanedCount;

    console.log(`✅ Scheduled cleanup completed:
    - Deleted ${expiredQuestions.deletedCount} expired archived questions (30+ days)
    - Deleted ${orphanedCount} orphaned questions
    - Total deleted: ${totalDeleted}
    - Cutoff date: ${thirtyDaysAgo}`);

    return res.status(200).json({
      success: true,
      message: `Cleanup completed: ${totalDeleted} questions deleted`,
      cleanup: {
        expiredQuestions: expiredQuestions.deletedCount,
        orphanedQuestions: orphanedCount,
        totalDeleted: totalDeleted,
        cutoffDate: thirtyDaysAgo,
        cleanupDate: now,
        retentionDays: 30
      }
    });

  } catch (error) {
    console.error('❌ Scheduled cleanup error:', error);
    return res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
}
