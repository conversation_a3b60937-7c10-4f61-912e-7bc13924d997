// File: pages/api/jobs/reset-generation.js
// Quick fix to reset stuck job generation states

import { MongoClient, ObjectId } from 'mongodb';

const uri = process.env.MONGODB_URI;

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  let client;
  
  try {
    const { jobId } = req.body;
    
    if (!jobId) {
      return res.status(400).json({ success: false, message: 'Missing jobId' });
    }

    console.log('🔧 RESET: Resetting generation state for job:', jobId);

    client = await MongoClient.connect(uri);
    const db = client.db();
    
    // Reset the job generation state
    const result = await db.collection('jobs').updateOne(
      { _id: new ObjectId(jobId) },
      { 
        $unset: { 
          questionBankGenerating: 1,
          questionBankGenerationStartedAt: 1,
          questionBankGenerationFailed: 1,
          questionBankGenerationError: 1
        }
      }
    );

    if (result.modifiedCount > 0) {
      console.log('✅ RESET: Job state reset successfully');
      return res.status(200).json({ 
        success: true,
        message: 'Job generation state reset successfully',
        jobId: jobId
      });
    } else {
      console.log('⚠️ RESET: Job not found or already reset');
      return res.status(404).json({ 
        success: false,
        message: 'Job not found or already reset'
      });
    }
    
  } catch (error) {
    console.error('❌ RESET: Error:', error);
    
    return res.status(500).json({ 
      success: false,
      message: 'Failed to reset job state',
      error: error.message
    });
  } finally {
    if (client) {
      await client.close();
    }
  }
}
