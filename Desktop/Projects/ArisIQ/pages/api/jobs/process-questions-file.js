// File: pages/api/jobs/process-questions-file.js

import { MongoClient, ObjectId } from 'mongodb';
import formidable from 'formidable';
import fs from 'fs';
import csv from 'csv-parser';
import readline from 'readline';

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // Parse the form data
    const form = new formidable.IncomingForm();
    
    form.parse(req, async (err, fields, files) => {
      if (err) {
        return res.status(500).json({ success: false, message: 'Error parsing form data' });
      }
      
      const { jobId } = fields;
      const questionsFile = files.questionsFile;
      
      if (!jobId || !questionsFile) {
        return res.status(400).json({ success: false, message: 'Job ID and questions file are required' });
      }

      // Connect to MongoDB
      const client = await MongoClient.connect(process.env.MONGODB_URI);
      const db = client.db();
      
      // Check if job exists
      const job = await db.collection('jobs').findOne({ _id: new ObjectId(jobId) });
      if (!job) {
        await client.close();
        return res.status(404).json({ success: false, message: 'Job not found' });
      }
      
      // Determine file type (CSV or TXT)
      const fileType = questionsFile.originalFilename.toLowerCase().endsWith('.csv') ? 'csv' : 'txt';
      
      // Parse the file based on type
      const questions = await parseQuestionsFile(questionsFile.filepath, fileType, job);
      
      if (questions.length === 0) {
        await client.close();
        return res.status(400).json({ 
          success: false, 
          message: 'No valid questions found in the file. Please check the file format.' 
        });
      }
      
      // Add metadata to questions
      const questionsWithMeta = questions.map(q => ({
        ...q,
        id: `recruiter-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`,
        jobId: new ObjectId(jobId),
        used: false,
        usedBy: [],
        createdAt: new Date(),
        source: 'recruiter'
      }));
      
      // Store questions in the recruiter_questions collection
      await db.collection('recruiter_questions').insertMany(questionsWithMeta);
      
      // Update job to indicate recruiter questions exist
      await db.collection('jobs').updateOne(
        { _id: new ObjectId(jobId) },
        { $set: { 
          recruiterQuestionsUploaded: true, 
          recruiterQuestionsCount: questionsWithMeta.length,
          recruiterQuestionsUploadedAt: new Date()
        }}
      );
      
      await client.close();
      
      return res.status(200).json({ 
        success: true, 
        message: `Successfully processed ${questionsWithMeta.length} questions from the file`,
        count: questionsWithMeta.length
      });
    });
  } catch (error) {
    console.error('Error processing questions file:', error);
    return res.status(500).json({ success: false, message: error.message });
  }
}

// Helper function to parse different file types
async function parseQuestionsFile(filePath, fileType, job) {
  const questions = [];
  const isTechnicalRole = checkIfTechnicalRole(job);
  
  return new Promise((resolve, reject) => {
    if (fileType === 'csv') {
      // Parse CSV
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (row) => {
          // Process each row from CSV
          const question = processQuestionRow(row, isTechnicalRole);
          if (question) {
            questions.push(question);
          }
        })
        .on('end', () => {
          resolve(questions);
        })
        .on('error', (err) => {
          reject(err);
        });
    } else {
      // Parse TXT (assumes a specific format)
      const questionBlocks = [];
      let currentBlock = [];
      
      const lineReader = readline.createInterface({
        input: fs.createReadStream(filePath),
        crlfDelay: Infinity
      });
      
      lineReader.on('line', (line) => {
        if (line.trim() === '') {
          if (currentBlock.length > 0) {
            questionBlocks.push(currentBlock);
            currentBlock = [];
          }
        } else {
          currentBlock.push(line);
        }
      });
      
      lineReader.on('close', () => {
        if (currentBlock.length > 0) {
          questionBlocks.push(currentBlock);
        }
        
        // Process each question block
        questionBlocks.forEach(block => {
          const question = processQuestionBlock(block, isTechnicalRole);
          if (question) {
            questions.push(question);
          }
        });
        
        resolve(questions);
      });
      
      lineReader.on('error', (err) => {
        reject(err);
      });
    }
  });
}

// Process a CSV row into a question object
function processQuestionRow(row, isTechnicalRole) {
  try {
    // Example format - adjust based on your expected CSV structure
    const type = row.type?.toLowerCase() || 'mcq';
    
    // Basic validation
    if (!row.question || row.question.trim() === '') {
      return null;
    }
    
    // Create base question
    const question = {
      type: type,
      question: row.question.trim(),
      difficulty: row.difficulty?.toLowerCase() || 'intermediate'
    };
    
    // Add type-specific fields
    if (type === 'mcq') {
      // Parse options (expects A, B, C, D format)
      const options = [];
      if (row.optionA) options.push(`a) ${row.optionA.trim()}`);
      if (row.optionB) options.push(`b) ${row.optionB.trim()}`);
      if (row.optionC) options.push(`c) ${row.optionC.trim()}`);
      if (row.optionD) options.push(`d) ${row.optionD.trim()}`);
      
      question.options = options;
      question.answer = row.answer?.trim() || (options.length > 0 ? options[0] : '');
      question.explanation = row.explanation?.trim() || '';
    } 
    else if (type === 'coding' && isTechnicalRole) {
      question.language = row.language?.toLowerCase() || 'javascript';
      question.starterCode = row.starterCode || '';
      question.testCases = row.testCases ? row.testCases.split(';').map(tc => tc.trim()) : [];
      question.expectedOutput = row.expectedOutput || '';
    }
    else if (type === 'technical' || type === 'behavioral' || type === 'scenario') {
      question.keyPoints = row.keyPoints ? row.keyPoints.split(';').map(kp => kp.trim()) : [];
      question.sampleAnswer = row.sampleAnswer || '';
    }
    
    return question;
  } catch (error) {
    console.error('Error processing CSV row:', error);
    return null;
  }
}

// Process a text block into a question object
function processQuestionBlock(block, isTechnicalRole) {
  try {
    if (block.length === 0) return null;
    
    // First line should contain the question
    const questionText = block[0].trim();
    if (!questionText) return null;
    
    // Try to determine type from the content
    let type = 'mcq'; // Default
    
    if (block.some(line => line.includes('def ') || line.includes('function ') || 
                  line.includes('class ') || line.includes('code:'))) {
      type = 'coding';
    }
    else if (block.some(line => line.toLowerCase().includes('tell me about a time') || 
                       line.toLowerCase().includes('describe a situation'))) {
      type = 'behavioral';
    }
    else if (block.some(line => line.toLowerCase().includes('scenario:') || 
                       line.toLowerCase().includes('situation:'))) {
      type = 'scenario';
    }
    else if (!block.some(line => line.toLowerCase().startsWith('a)') || 
                        line.toLowerCase().startsWith('b)'))) {
      type = 'technical'; // If no MCQ options found, assume technical
    }
    
    // Create base question
    const question = {
      type: type,
      question: questionText,
      difficulty: 'intermediate' // Default
    };
    
    // Set difficulty if provided
    const difficultyLine = block.find(line => 
      line.toLowerCase().includes('difficulty:') || 
      line.toLowerCase().includes('level:')
    );
    
    if (difficultyLine) {
      const lower = difficultyLine.toLowerCase();
      if (lower.includes('basic') || lower.includes('easy')) {
        question.difficulty = 'basic';
      } else if (lower.includes('advanced') || lower.includes('hard')) {
        question.difficulty = 'advanced';
      }
    }
    
    // Add type-specific fields
    if (type === 'mcq') {
      // Look for option lines (a), b), etc.)
      const options = [];
      const optionLines = block.filter(line => 
        line.match(/^[a-d]\s*\)/)
      );
      
      optionLines.forEach(line => {
        options.push(line.trim());
      });
      
      question.options = options;
      
      // Look for the answer
      const answerLine = block.find(line => 
        line.toLowerCase().includes('answer:') || 
        line.toLowerCase().includes('correct:')
      );
      
      if (answerLine) {
        const answerMatch = answerLine.match(/[a-d]\s*\)/i);
        question.answer = answerMatch ? answerMatch[0].trim() : (options.length > 0 ? options[0] : '');
      } else {
        question.answer = options.length > 0 ? options[0] : '';
      }
      
      // Look for explanation
      const explanationLine = block.find(line => 
        line.toLowerCase().includes('explanation:')
      );
      
      question.explanation = explanationLine ? 
        explanationLine.replace(/explanation:\s*/i, '').trim() : '';
    } 
    else if (type === 'coding' && isTechnicalRole) {
      // Try to identify language
      const languageLine = block.find(line => 
        line.toLowerCase().includes('language:')
      );
      
      if (languageLine) {
        const langMatch = languageLine.match(/language:\s*([a-z\+\#]+)/i);
        question.language = langMatch ? langMatch[1].toLowerCase() : 'javascript';
      } else {
        // Infer from content
        if (block.some(line => line.includes('def '))) {
          question.language = 'python';
        } else if (block.some(line => line.includes('public class'))) {
          question.language = 'java';
        } else if (block.some(line => line.includes('#include'))) {
          question.language = 'cpp';
        } else {
          question.language = 'javascript';
        }
      }
      
      // Extract starter code (anything between code blocks or indented)
      let startCode = false;
      let starterCode = [];
      
      for (const line of block) {
        if (line.includes('```') || line.includes('code:')) {
          startCode = !startCode;
          continue;
        }
        
        if (startCode || line.startsWith('  ') || line.startsWith('\t')) {
          starterCode.push(line);
        }
      }
      
      question.starterCode = starterCode.join('\n');
      
      // Look for test cases
      const testCases = [];
      const testCaseLines = block.filter(line => 
        line.includes('->') || 
        line.toLowerCase().includes('input:') || 
        line.toLowerCase().includes('output:')
      );
      
      testCaseLines.forEach(line => {
        testCases.push(line.trim());
      });
      
      question.testCases = testCases;
    }
    else if (type === 'technical' || type === 'behavioral' || type === 'scenario') {
      // Look for key points
      const keyPoints = [];
      let inKeyPoints = false;
      
      for (const line of block) {
        const lower = line.toLowerCase();
        if (lower.includes('key points:') || lower.includes('important points:')) {
          inKeyPoints = true;
          continue;
        } 
        else if (inKeyPoints && (lower.includes('sample answer:') || lower.includes('example answer:'))) {
          inKeyPoints = false;
          break;
        }
        else if (inKeyPoints && (line.trim().startsWith('-') || line.trim().startsWith('•') || 
                               line.match(/^\d+\.\s/))) {
          keyPoints.push(line.replace(/^[-•\d\.]\s*/, '').trim());
        }
      }
      
      question.keyPoints = keyPoints;
      
      // Look for sample answer
      let sampleAnswer = [];
      let inSampleAnswer = false;
      
      for (const line of block) {
        const lower = line.toLowerCase();
        if (lower.includes('sample answer:') || lower.includes('example answer:')) {
          inSampleAnswer = true;
          continue;
        } 
        else if (inSampleAnswer && (lower.includes('note:') || lower.includes('difficulty:'))) {
          break;
        }
        else if (inSampleAnswer) {
          sampleAnswer.push(line.trim());
        }
      }
      
      question.sampleAnswer = sampleAnswer.join(' ');
    }
    
    return question;
  } catch (error) {
    console.error('Error processing text block:', error);
    return null;
  }
}

// Helper function to determine if a role is technical (same as in questions.js)
function checkIfTechnicalRole(job) {
  // [Same implementation as in questions.js]
}
