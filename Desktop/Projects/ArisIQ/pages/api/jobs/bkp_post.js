// File: pages/api/jobs/post.js
// Enhanced with Real-time Autocomplete (AI detection removed) - FIXED VERSION

import connectToDatabase from "../../../lib/mongodb";
import mongoose from "mongoose";
import formidable from "formidable";
import fs from "fs";
import path from "path";
import axios from "axios";
import { getUserEmail } from "../../../lib/auth-helper";

export const config = {
  api: {
    bodyParser: false, // Disable bodyParser to handle file uploads
  },
};

const jobSchema = new mongoose.Schema({
  title: String,
  company: String,
  industry: String,
  experience: String,
  employmentType: String,
  workMode: String,
  salary: String,
  country: String,
  city: String,
  description: String,
  requiredSkills: [String],
  preferredSkills: [String],
  applicationDeadline: String,
  screeningDeadline: String,
  website: String,
  postedBy: String,
  questionSource: String,
  visaSponsorship: String,
  createdAt: { type: Date, default: Date.now },
  // Add fields for question tracking
  questionBankGenerating: { type: Boolean, default: false },
  questionBankGenerated: { type: Boolean, default: false },
  questionBankGeneratedAt: Date,
  questionBankGenerationStartedAt: Date,
  questionBankGenerationError: String,
  questionBankGenerationFailed: Boolean,
  recruiterQuestionsUploaded: { type: Boolean, default: false },
  recruiterQuestionsCount: Number,
  recruiterQuestionsFilename: String,
  recruiterQuestionsErrors: [String],
  // Add fields for AI location detection
  aiDetectedLocation: {
    country: String,
    city: String,
    confidence: Number,
    source: String // 'ai_detected' or 'user_provided'
  }
});

const Job = mongoose.models.Job || mongoose.model("Job", jobSchema);

// AI-powered location detection function
async function detectLocationWithAI(jobData) {
  try {
    const { title, company, description, website, industry } = jobData;
    
    // Create context for AI analysis
    const locationContext = `
Analyze this job posting and determine the most likely country and city location:

Job Title: ${title || 'Not specified'}
Company: ${company || 'Not specified'}
Industry: ${industry || 'Not specified'}
Website: ${website || 'Not specified'}
Job Description: ${description || 'Not specified'}

Based on the company name, website domain, job description context, and any location clues, determine:
1. The most likely COUNTRY where this job is located
2. The most likely CITY where this job is located
3. Your confidence level (0-100%)

Consider factors like:
- Company website domain (.com, .co.uk, .de, etc.)
- Company name patterns (common in specific countries)
- Industry context and regional presence
- Job description language and terminology
- Remote work mentions

Return only a JSON object in this exact format:
{
  "country": "Country Name",
  "city": "City Name", 
  "confidence": 85,
  "reasoning": "Brief explanation of why you chose this location"
}

If you cannot determine a location with reasonable confidence, return:
{
  "country": "United States",
  "city": "Remote",
  "confidence": 30,
  "reasoning": "Insufficient information to determine specific location"
}
`;

    console.log('🔍 Detecting location with AI...');
    
    const response = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: 'You are a location detection expert. Analyze job postings to determine the most likely country and city. Always return valid JSON.'
        },
        {
          role: 'user',
          content: locationContext
        }
      ],
      temperature: 0.3, // Lower temperature for more consistent results
      max_tokens: 300
    });

    const aiResponse = response.choices?.[0]?.message?.content;
    
    if (!aiResponse) {
      throw new Error('No response from AI');
    }

    // Parse the AI response
    let locationData;
    try {
      // Extract JSON from response (in case AI adds extra text)
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        locationData = JSON.parse(jsonMatch[0]);
      } else {
        locationData = JSON.parse(aiResponse);
      }
    } catch (parseError) {
      console.error('Failed to parse AI location response:', parseError);
      // Fallback to default
      locationData = {
        country: 'United States',
        city: 'Remote',
        confidence: 20,
        reasoning: 'Failed to parse AI response'
      };
    }

    // Validate the response structure
    if (!locationData.country || !locationData.city) {
      locationData = {
        country: 'United States',
        city: 'Remote', 
        confidence: 20,
        reasoning: 'Invalid AI response structure'
      };
    }

    console.log('✅ AI detected location:', locationData);
    return locationData;

  } catch (error) {
    console.error('❌ AI location detection failed:', error);
    
    // Return default location on error
    return {
      country: 'United States',
      city: 'Remote',
      confidence: 10,
      reasoning: `AI detection failed: ${error.message}`
    };
  }
}

// Enhanced location processing
async function processLocation(country, city, jobData) {
  let finalCountry = country;
  let finalCity = city;
  let aiDetected = null;
  
  // If either country or city is missing, use AI detection
  if (!country || !city || country.trim() === '' || city.trim() === '') {
    console.log('🤖 Country or city missing, using AI detection...');
    
    const aiLocationData = await detectLocationWithAI(jobData);
    
    // Use AI-detected values for missing fields
    if (!country || country.trim() === '') {
      finalCountry = aiLocationData.country;
    }
    if (!city || city.trim() === '') {
      finalCity = aiLocationData.city;
    }
    
    aiDetected = {
      country: aiLocationData.country,
      city: aiLocationData.city,
      confidence: aiLocationData.confidence,
      source: 'ai_detected',
      reasoning: aiLocationData.reasoning
    };
    
    console.log(`✅ Location processed: ${finalCountry}, ${finalCity} (AI confidence: ${aiLocationData.confidence}%)`);
  } else {
    // Both provided by user
    aiDetected = {
      country: finalCountry,
      city: finalCity,
      confidence: 100,
      source: 'user_provided'
    };
    
    console.log(`✅ Location provided by user: ${finalCountry}, ${finalCity}`);
  }
  
  return {
    country: finalCountry,
    city: finalCity,
    aiDetectedLocation: aiDetected
  };
}

export default async function handler(req, res) {
  try {
    // Connect to MongoDB first to fail fast if connection issues
    await connectToDatabase();
    
    if (req.method !== "POST") {
      return res.status(405).json({ 
        success: false, 
        message: "Method Not Allowed",
        details: "This endpoint only accepts POST requests"
      });
    }

    // Get the authenticated user's email - NO DEFAULTS!
    const userEmail = await getUserEmail(req);
    
    if (!userEmail) {
      return res.status(401).json({
        success: false,
        message: "Authentication required to post jobs",
        details: "Please log in and try again"
      });
    }
    
    console.log(`👤 Authenticated user posting job: ${userEmail}`);

    // Use formidable to parse form data including files
    const form = new formidable.IncomingForm({
      maxFileSize: 10 * 1024 * 1024, // 10MB max file size
      multiples: false,
      keepExtensions: true
    });
    
    form.parse(req, async (err, fields, files) => {
      if (err) {
        console.error("📋 Form parsing error:", err);
        return res.status(500).json({ 
          success: false, 
          message: "Error parsing form data",
          details: err.message
        });
      }
      
      try {
        const {
          title,
          company,
          industry,
          experience,
          employmentType,
          workMode,
          salary,
          country,
          city,
          description,
          requiredSkills,
          preferredSkills,
          applicationDeadline,
          screeningDeadline,
          website,
          postedBy, // This might be provided in form or undefined
          questionSource,
          visaSponsorship
        } = fields;

        // Comprehensive validation
        const validationErrors = [];
        
        // Required fields
        if (!title) validationErrors.push("Job title is required");
        if (!company) validationErrors.push("Company name is required");
        if (!industry) validationErrors.push("Industry is required");
        
        // Date validations
        if (applicationDeadline) {
          const appDeadline = new Date(applicationDeadline);
          const currentDate = new Date();
          if (isNaN(appDeadline.getTime())) {
            validationErrors.push("Application deadline is not a valid date");
          } else if (appDeadline < currentDate) {
            validationErrors.push("Application deadline cannot be in the past");
          }
        }
        
        if (screeningDeadline) {
          const scrDeadline = new Date(screeningDeadline);
          const appDeadline = applicationDeadline ? new Date(applicationDeadline) : null;
          
          if (isNaN(scrDeadline.getTime())) {
            validationErrors.push("Screening deadline is not a valid date");
          } else if (appDeadline && scrDeadline < appDeadline) {
            validationErrors.push("Screening deadline cannot be before application deadline");
          }
        }
        
        // Question source validation
        if (questionSource) {
          const validSources = ["100% AI", "50% AI + 50% Recruiter", "100% Recruiter"];
          if (!validSources.includes(questionSource)) {
            validationErrors.push("Invalid question source option");
          } else if ((questionSource === "50% AI + 50% Recruiter" || questionSource === "100% Recruiter") && 
                    !files.questionsFile) {
            validationErrors.push("Question file is required for the selected question source");
          }
        }
        
        // Check if we have validation errors
        if (validationErrors.length > 0) {
          return res.status(400).json({
            success: false,
            message: "Validation failed",
            errors: validationErrors
          });
        }

        // ✨ Process location (now comes from autocomplete instead of AI)
        console.log('🌍 Processing location from autocomplete...');
        const location = [country, city].filter(Boolean).join(", ");
        
        console.log(`Location: ${location}`);
        
        // Process skills arrays
        const processedRequiredSkills = requiredSkills ? 
          (typeof requiredSkills === 'string' ? 
            requiredSkills.split(',').map(s => s.trim()).filter(s => s) : 
            requiredSkills) : 
          [];
          
        const processedPreferredSkills = preferredSkills ? 
          (typeof preferredSkills === 'string' ? 
            preferredSkills.split(',').map(s => s.trim()).filter(s => s) : 
            preferredSkills) : 
          [];

        // Create the job with location from autocomplete
        const job = new Job({
          title,
          company,
          industry,
          experience,
          employmentType,
          workMode,
          salary,
          country: country || '',             // From autocomplete
          city: city || '',                   // From autocomplete
          location,                           // Combined location string
          description,
          requiredSkills: processedRequiredSkills,
          preferredSkills: processedPreferredSkills,
          applicationDeadline,
          screeningDeadline,
          website,
          postedBy: userEmail, // Always use the authenticated user's email
          questionSource,
          visaSponsorship,
          createdAt: new Date()
        });

        // Log the job creation
        console.log("✅ Job being created with:");
        console.log("  - Country:", job.country);
        console.log("  - City:", job.city);
        console.log("  - Location:", job.location);
        console.log("  - Posted by:", job.postedBy);

        // Save the job to get an ID
        const savedJob = await job.save();
        const jobId = savedJob._id.toString();
        
        console.log("✅ Job created with ID:", jobId);
        
        // Array to track processing steps and errors
        let processingSteps = [];
        let processingErrors = [];
        
        // Add location processing step
        if (country && city) {
          processingSteps.push(`Location: ${country}, ${city}`);
        } else if (country || city) {
          processingSteps.push(`Partial location: ${country || city}`);
        }
        
        // Handle question file if provided for appropriate question sources
        if (questionSource && questionSource !== "100% AI" && files.questionsFile) {
          try {
            processingSteps.push("Processing recruiter questions file");
            
            const questionsFile = files.questionsFile;
            
            // Validate file format
            const filename = questionsFile.originalFilename || '';
            const fileExt = path.extname(filename).toLowerCase();
            
            if (fileExt !== '.txt' && fileExt !== '.csv') {
              throw new Error("Invalid file format. Only .txt and .csv files are supported");
            }
            
            // Create a FormData object for the file upload
            const fileFormData = new FormData();
            fileFormData.append('jobId', jobId);
            
            // Create a copy of the file to send to the API
            const tempDir = path.join(process.cwd(), 'temp');
            if (!fs.existsSync(tempDir)) {
              fs.mkdirSync(tempDir, { recursive: true });
            }
            
            const filePath = path.join(tempDir, questionsFile.originalFilename || `questions_${jobId}${fileExt}`);
            
            // Copy the file with error handling
            try {
              fs.copyFileSync(questionsFile.filepath, filePath);
            } catch (copyError) {
              throw new Error(`Failed to process file: ${copyError.message}`);
            }
            
            // Update job with file information
            await Job.findByIdAndUpdate(jobId, { 
              recruiterQuestionsFilename: questionsFile.originalFilename
            });
            
            // Process questions file asynchronously
            const baseUrl = process.env.NEXT_PUBLIC_API_URL || 
                           (process.env.NODE_ENV === 'development' ? 
                           'http://localhost:3001' : 
                           'https://symplihire.com'); // Replace with your production domain
            
            const apiUrl = `${baseUrl}/api/jobs/process-questions-file`;
            console.log(`🔄 Sending file to processing endpoint: ${apiUrl}`);
            
            // Instead of using FormData (which can be tricky in Node.js), use a different approach
            const fileBuffer = fs.readFileSync(filePath);
            
            // Create form data with proper boundary
            const formData = new FormData();
            formData.append('jobId', jobId);
            formData.append('questionsFile', new Blob([fileBuffer]), {
              filename: questionsFile.originalFilename,
              contentType: questionsFile.mimetype
            });
            
            // Process asynchronously
            axios.post(apiUrl, formData, {
              headers: { 'Content-Type': 'multipart/form-data' }
            })
            .then(async response => {
              console.log("✅ Recruiter questions processed:", response.data);
              
              // Update job with processing results
              await Job.findByIdAndUpdate(jobId, { 
                recruiterQuestionsUploaded: true,
                recruiterQuestionsCount: response.data.count || 0
              });
              
              // If 100% Recruiter was selected but we didn't get enough questions,
              // Automatically trigger AI questions generation as a fallback
              if (questionSource === "100% Recruiter" && 
                  (response.data.count || 0) < 20) {
                
                console.log("⚠️ Not enough recruiter questions, generating AI questions as fallback");
                
                // Update job to indicate fallback
                await Job.findByIdAndUpdate(jobId, { 
                  questionSource: "50% AI + 50% Recruiter" // Change to mixed mode
                });
                
                // FIXED: Make direct synchronous call instead of using triggerAIQuestionGeneration
                console.log("⚠️ Not enough recruiter questions, generating AI questions as fallback");
                
                // Update job to indicate fallback
                await Job.findByIdAndUpdate(jobId, { 
                  questionSource: "50% AI + 50% Recruiter" // Change to mixed mode
                });
                
                // Make direct API call
                try {
                  const apiUrl = process.env.NODE_ENV === 'development' 
                    ? 'http://localhost:3001/api/jobs/generate-questions'
                    : `${process.env.NEXT_PUBLIC_API_URL || 'https://symplihire.com'}/api/jobs/generate-questions`;
                  
                  const fallbackResponse = await axios.post(apiUrl, { jobId }, {
                    headers: { 'Content-Type': 'application/json' },
                    timeout: 30000
                  });
                  
                  console.log("✅ Fallback AI generation completed:", fallbackResponse.data);
                } catch (fallbackError) {
                  console.error("❌ Fallback AI generation failed:", fallbackError.message);
                }
              }
            })
            .catch(async error => {
              console.error("❌ Error processing recruiter questions:", error);
              
              // Store error in job record
              await Job.findByIdAndUpdate(jobId, { 
                recruiterQuestionsErrors: [error.message || "Unknown error processing questions file"]
              });
              
              // If we're in "100% Recruiter" mode, fall back to AI questions
              if (questionSource === "100% Recruiter") {
                console.log("⚠️ Failed to process recruiter questions, generating AI questions as fallback");
                
                // Update job to indicate fallback
                await Job.findByIdAndUpdate(jobId, { 
                  questionSource: "100% AI" // Fall back to AI only
                });
                
                // FIXED: Make direct synchronous call instead of using triggerAIQuestionGeneration
                console.log("⚠️ Failed to process recruiter questions, generating AI questions as fallback");
                
                // Update job to indicate fallback
                await Job.findByIdAndUpdate(jobId, { 
                  questionSource: "100% AI" // Fall back to AI only
                });
                
                // Make direct API call
                try {
                  const apiUrl = process.env.NODE_ENV === 'development' 
                    ? 'http://localhost:3001/api/jobs/generate-questions'
                    : `${process.env.NEXT_PUBLIC_API_URL || 'https://symplihire.com'}/api/jobs/generate-questions`;
                  
                  const fallbackResponse = await axios.post(apiUrl, { jobId }, {
                    headers: { 'Content-Type': 'application/json' },
                    timeout: 30000
                  });
                  
                  console.log("✅ Fallback AI generation completed:", fallbackResponse.data);
                } catch (fallbackError) {
                  console.error("❌ Fallback AI generation failed:", fallbackError.message);
                }
              }
            });
            
            // Clean up temp file after sending (don't wait for API response)
            try {
              setTimeout(() => fs.unlinkSync(filePath), 5000);
            } catch (cleanupErr) {
              console.warn("Warning: Failed to clean up temp file:", cleanupErr);
            }
            
            processingSteps.push("Recruiter questions file submitted for processing");
          } catch (fileError) {
            console.error("❌ Error with question file:", fileError);
            processingErrors.push(`Failed to process recruiter questions file: ${fileError.message}`);
            
            // Update job with error
            await Job.findByIdAndUpdate(jobId, { 
              recruiterQuestionsErrors: [fileError.message]
            });
            
            // If we're in "100% Recruiter" mode, fall back to AI questions
            if (questionSource === "100% Recruiter") {
              console.log("⚠️ Failed to process recruiter questions, generating AI questions as fallback");
              
              // Update job to indicate fallback to AI
              await Job.findByIdAndUpdate(jobId, { 
                questionSource: "100% AI"
              });
              
              processingSteps.push("Falling back to AI-generated questions due to file processing error");
              
              // FIXED: Make direct synchronous call instead of using triggerAIQuestionGeneration
              console.log("⚠️ Failed to process recruiter questions, generating AI questions as fallback");
              
              // Update job to indicate fallback to AI
              await Job.findByIdAndUpdate(jobId, { 
                questionSource: "100% AI"
              });
              
              processingSteps.push("Falling back to AI-generated questions due to file processing error");
              
              // Make direct API call
              try {
                const apiUrl = process.env.NODE_ENV === 'development' 
                  ? 'http://localhost:3001/api/jobs/generate-questions'
                  : `${process.env.NEXT_PUBLIC_API_URL || 'https://symplihire.com'}/api/jobs/generate-questions`;
                
                const fallbackResponse = await axios.post(apiUrl, { jobId }, {
                  headers: { 'Content-Type': 'application/json' },
                  timeout: 30000
                });
                
                console.log("✅ Fallback AI generation completed:", fallbackResponse.data);
              } catch (fallbackError) {
                console.error("❌ Fallback AI generation failed:", fallbackError.message);
                processingErrors.push(`Fallback AI generation failed: ${fallbackError.message}`);
              }
            }
          }
        }
        
        // Generate AI question bank if needed
        if (questionSource === "100% AI" || questionSource === "50% AI + 50% Recruiter") {
          try {
            processingSteps.push("Initiating AI question generation");
            
            // FIXED: Make this synchronous to prevent duplicate calls
            console.log(`🔄 Starting synchronous AI question generation for job: ${jobId}`);
            
            // Use axios with localhost URL for IMMEDIATE internal API call
            const apiUrl = process.env.NODE_ENV === 'development' 
              ? 'http://localhost:3001/api/jobs/generate-questions'
              : `${process.env.NEXT_PUBLIC_API_URL || 'https://symplihire.com'}/api/jobs/generate-questions`;
            
            // FIXED: Make synchronous call with await
            const response = await axios.post(apiUrl, { jobId }, {
              headers: { 'Content-Type': 'application/json' },
              timeout: 30000, // 30 second timeout for generation
              validateStatus: function (status) {
                return status < 500; // Accept all status codes under 500
              }
            });
            
            if (response.status === 200) {
              console.log("✅ AI question generation completed:", response.data);
              processingSteps.push(`AI question generation: ${response.data.status}`);
              
              if (response.data.totalGenerated) {
                processingSteps.push(`Generated ${response.data.totalGenerated} questions`);
              }
            } else {
              console.error("⚠️ AI question generation warning:", response.data);
              processingErrors.push(`AI generation warning: ${response.data.message || 'Unknown issue'}`);
            }
            
          } catch (genError) {
            console.error("❌ Error with AI question generation:", genError);
            processingErrors.push(`Failed AI question generation: ${genError.message}`);
            
            // Reset the generating flag on error
            try {
              await Job.findByIdAndUpdate(jobId, { 
                questionBankGenerating: false,
                questionBankGenerationError: genError.message,
                questionBankGenerationFailed: true
              });
            } catch (updateError) {
              console.error("Failed to update job after generation error:", updateError);
            }
          }
        }
        
        return res.status(201).json({ 
          success: true, 
          jobId: jobId,
          message: "Job created successfully",
          location: {
            country: country || '',
            city: city || '',
            combined: location
          },
          processing: {
            steps: processingSteps,
            errors: processingErrors
          }
        });
      } catch (error) {
        console.error("❌ Job Processing Error:", error);
        return res.status(500).json({ 
          success: false, 
          message: "Failed to process job",
          details: error.message
        });
      }
    });
  } catch (error) {
    console.error("❌ Job Posting Error:", error);
    return res.status(500).json({ 
      success: false, 
      message: "Server error while posting job",
      details: error.message
    });
  }
}

// Helper function to update job with error
async function updateJobWithError(jobId, errorMessage) {
  try {
    await connectToDatabase();
    await Job.findByIdAndUpdate(jobId, { 
      $push: { recruiterQuestionsErrors: errorMessage }
    });
  } catch (dbError) {
    console.error("Failed to update job with error:", dbError);
  }
}
