// File: pages/api/jobs/delete.js

import connectToDatabase from "../../../lib/mongodb";
import mongoose from "mongoose";

const jobSchema = new mongoose.Schema({}, { strict: false });
const Job = mongoose.models.Job || mongoose.model("Job", jobSchema);

const applicationSchema = new mongoose.Schema({}, { strict: false });
const Application = mongoose.models.Application || mongoose.model("Application", applicationSchema);

// Add Questions schema for cleanup
const questionSchema = new mongoose.Schema({}, { strict: false });
const Questions = mongoose.models.Questions || mongoose.model("Questions", questionSchema);

export default async function handler(req, res) {
  await connectToDatabase();

  if (req.method !== "DELETE") {
    return res.status(405).json({ message: "Method Not Allowed" });
  }

  const jobId = req.body?.jobId || req.query?.id;
  const recruiterEmail = req.body?.recruiterEmail || req.query?.recruiterEmail;

  console.log("Delete request:", { jobId, recruiterEmail, body: req.body, query: req.query });

  if (!jobId) {
    return res.status(400).json({ success: false, message: "Missing jobId" });
  }

  try {
    // Validate ObjectId format
    if (!mongoose.Types.ObjectId.isValid(jobId)) {
      return res.status(400).json({ success: false, message: "Invalid job ID format" });
    }

    const job = await Job.findById(jobId);

    if (!job) {
      return res.status(404).json({ success: false, message: "Job not found" });
    }

    // Optional: Check if recruiter owns the job (if recruiterEmail is provided)
    if (recruiterEmail && job.postedBy && job.postedBy !== recruiterEmail) {
      console.log(`Authorization check: job.postedBy=${job.postedBy}, recruiterEmail=${recruiterEmail}`);
      return res.status(403).json({ success: false, message: "Unauthorized to delete this job" });
    }

    // Count related data before deletion
    const applicationCount = await Application.countDocuments({ 
      $or: [
        { jobId: jobId },
        { jobId: new mongoose.Types.ObjectId(jobId) }
      ]
    });

    const questionCount = await Questions.countDocuments({ 
      $or: [
        { jobId: jobId },
        { jobId: new mongoose.Types.ObjectId(jobId) }
      ]
    });

    console.log(`📊 Job ${jobId} has ${applicationCount} applications and ${questionCount} questions`);

    // 1. Delete the job
    const deleteResult = await Job.findByIdAndDelete(jobId);
    
    if (!deleteResult) {
      return res.status(404).json({ success: false, message: "Job not found or already deleted" });
    }

    // 2. Delete related applications
    const deletedApplications = await Application.deleteMany({ 
      $or: [
        { jobId: jobId },
        { jobId: new mongoose.Types.ObjectId(jobId) }
      ]
    });

    // 3. DELETE GENERATED QUESTIONS (since job is permanently deleted)
    const deletedQuestions = await Questions.deleteMany({ 
      $or: [
        { jobId: jobId },
        { jobId: new mongoose.Types.ObjectId(jobId) }
      ]
    });

    // 4. Clean up question files if they exist (optional)
    // If questions are stored as files, add file cleanup here
    try {
      const fs = require('fs').promises;
      const path = require('path');
      const glob = require('glob');
      
      const questionFilesPattern = path.join(process.cwd(), 'uploads', 'questions', `job-${jobId}-*`);
      const questionFiles = glob.sync(questionFilesPattern);
      
      for (const file of questionFiles) {
        await fs.unlink(file);
        console.log(`🗑️ Deleted question file: ${file}`);
      }
    } catch (fileError) {
      console.log("📁 No question files to delete or file cleanup not needed:", fileError.message);
    }

    console.log(`✅ Successfully deleted job and cleaned up:
    - Job: ${jobId}
    - Applications: ${deletedApplications.deletedCount}
    - Questions: ${deletedQuestions.deletedCount}`);

    return res.status(200).json({ 
      success: true, 
      message: "Job and all related data deleted successfully",
      deletedJobId: jobId,
      cleanup: {
        applications: deletedApplications.deletedCount,
        questions: deletedQuestions.deletedCount,
        hadApplications: applicationCount > 0,
        hadQuestions: questionCount > 0
      }
    });
  } catch (error) {
    console.error("❌ Delete Job Error:", error);
    return res.status(500).json({ 
      success: false, 
      message: "Internal Server Error",
      error: error.message 
    });
  }
}
