import connectDB from "../../lib/mongodb";
import { GridFSBucket, ObjectId, MongoClient } from "mongodb";
import pdfParse from "pdf-parse";
import { spawn } from "child_process";
import path from "path";

const uri = process.env.MONGODB_URI;

function runPythonScript(scriptPath, input) {
  return new Promise((resolve, reject) => {
    const process = spawn("python3", [scriptPath]);

    let output = "";
    let error = "";

    process.stdout.on("data", (data) => {
      output += data.toString();
    });

    process.stderr.on("data", (data) => {
      error += data.toString();
    });

    process.on("close", (code) => {
      if (code !== 0 && !output.trim()) {
        console.error("Python stderr:", error);
        return reject(new Error("Python script failed"));
      }
      try {
        resolve(JSON.parse(output));
      } catch (e) {
        reject(new Error("Failed to parse Python output"));
      }
    });

    process.stdin.write(JSON.stringify(input));
    process.stdin.end();
  });
}

export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ success: false, message: "Method not allowed" });
  }

  const { jobId, applicationId } = req.body;

  if (!jobId || !applicationId) {
    return res.status(400).json({ success: false, message: "jobId and applicationId are required" });
  }

  try {
    const client = await MongoClient.connect(uri);
    const db = client.db();
    const bucket = new GridFSBucket(db, { bucketName: "resumes" });

    const job = await db.collection("jobs").findOne({ _id: new ObjectId(jobId) });
    if (!job) return res.status(404).json({ success: false, message: "Job not found" });

    const app = await db.collection("applications").findOne({ _id: new ObjectId(applicationId) });
    if (!app) return res.status(404).json({ success: false, message: "Application not found" });

    const fileId = new ObjectId(app.resumeId);
    const chunks = [];
    const stream = bucket.openDownloadStream(fileId);

    const buffer = await new Promise((resolve, reject) => {
      stream.on("data", (chunk) => chunks.push(chunk));
      stream.on("end", () => resolve(Buffer.concat(chunks)));
      stream.on("error", reject);
    });

    let resumeText = "";
    try {
      const parsed = await pdfParse(buffer);
      resumeText = parsed.text;
    } catch (err) {
      return res.status(400).json({
        success: false,
        message: "Corrupted or invalid PDF. Unable to scan resume.",
      });
    }

    const compareScript = path.join(process.cwd(), "scripts", "compare.py");

    const input = {
      resume: resumeText,
      job: `${job.title} ${job.description} ${job.requiredSkills?.join(" ") || ""}`,
    };

    const compareResult = await runPythonScript(compareScript, input);

    await db.collection("applications").updateOne(
      { _id: new ObjectId(applicationId) },
      {
        $set: {
          scanResult: { compare: compareResult },
          scannedAt: new Date(),
        },
      }
    );

    res.status(200).json({ success: true, result: compareResult });
  } catch (err) {
    console.error("❌ Scan error:", err);
    res.status(500).json({ success: false, error: err.message });
  }
}

export const config = {
  api: {
    bodyParser: true,
  },
};

