// File: pages/api/debug/check-user.js
// Simple API to check user type for debugging

import connectToDatabase from "../../../lib/mongodb";
import mongoose from "mongoose";

const UserSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String },
  userType: { type: String, enum: ['recruiter', 'candidate'], required: true },
  company: { type: String },
  provider: { type: String, default: 'credentials' },
  image: { type: String },
  location: { type: String },
  industry: { type: String },
  website: { type: String },
  description: { type: String },
  status: { 
    type: String, 
    enum: ['active', 'suspended'], 
    default: 'active'
  },
}, {
  timestamps: true
});

const User = mongoose.models.User || mongoose.model('User', UserSchema);

export default async function handler(req, res) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { email } = req.query;

  if (!email) {
    return res.status(400).json({ 
      error: "Email parameter required" 
    });
  }

  try {
    await connectToDatabase();
    
    const user = await User.findOne({ 
      email: email.toLowerCase() 
    });

    if (!user) {
      return res.status(404).json({ 
        error: "User not found" 
      });
    }

    res.status(200).json({
      user: {
        id: user._id,
        email: user.email,
        name: user.name,
        userType: user.userType,
        company: user.company,
        provider: user.provider,
        status: user.status,
        createdAt: user.createdAt
      }
    });

  } catch (error) {
    console.error("Check user error:", error);
    res.status(500).json({ 
      error: "Internal server error" 
    });
  }
}
