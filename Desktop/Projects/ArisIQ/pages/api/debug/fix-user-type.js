// File: pages/api/debug/fix-user-type.js
// Quick fix to update user type in database

import connectToDatabase from "../../../lib/mongodb";
import mongoose from "mongoose";

const UserSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String },
  userType: { type: String, enum: ['recruiter', 'candidate'], required: true },
  company: { type: String },
  provider: { type: String, default: 'credentials' },
  image: { type: String },
  location: { type: String },
  industry: { type: String },
  website: { type: String },
  description: { type: String },
  status: { 
    type: String, 
    enum: ['active', 'suspended'], 
    default: 'active'
  },
}, {
  timestamps: true
});

const User = mongoose.models.User || mongoose.model('User', UserSchema);

export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { email, userType, company } = req.body;

  if (!email || !userType) {
    return res.status(400).json({ 
      error: "Email and userType are required" 
    });
  }

  if (!['recruiter', 'candidate'].includes(userType)) {
    return res.status(400).json({ 
      error: "userType must be 'recruiter' or 'candidate'" 
    });
  }

  try {
    await connectToDatabase();
    
    const user = await User.findOneAndUpdate(
      { email: email.toLowerCase() },
      { 
        userType: userType,
        ...(userType === 'recruiter' && company && { company: company })
      },
      { new: true }
    );

    if (!user) {
      return res.status(404).json({ 
        error: "User not found" 
      });
    }

    res.status(200).json({ 
      message: "User type updated successfully",
      user: {
        email: user.email,
        userType: user.userType,
        company: user.company,
        provider: user.provider
      }
    });

  } catch (error) {
    console.error("Fix user type error:", error);
    res.status(500).json({ 
      error: "Internal server error",
      details: error.message
    });
  }
}
