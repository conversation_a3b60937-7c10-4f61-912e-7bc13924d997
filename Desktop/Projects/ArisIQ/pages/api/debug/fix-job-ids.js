// File: pages/api/debug/fix-job-ids.js

import { MongoClient, ObjectId } from "mongodb";
const uri = process.env.MONGODB_URI;

export default async function handler(req, res) {
  // This should only be accessible in development
  if (process.env.NODE_ENV === 'production') {
    return res.status(403).json({ message: "This endpoint is only available in development" });
  }

  try {
    const client = await MongoClient.connect(uri);
    const db = client.db();

    // Step 1: Get all jobs
    const jobs = await db.collection("jobs").find({}).toArray();
    console.log(`Found ${jobs.length} jobs`);

    // Step 2: Get all applications
    const allApplications = await db.collection("applications").find({}).toArray();
    console.log(`Found ${allApplications.length} applications`);

    // Step 3: Analyze how jobId is stored
    const jobIdTypes = {};
    allApplications.forEach(app => {
      const type = typeof app.jobId;
      const isObjectId = app.jobId instanceof ObjectId;
      const key = `${type}${isObjectId ? '-ObjectId' : ''}`;
      
      if (!jobIdTypes[key]) {
        jobIdTypes[key] = 1;
      } else {
        jobIdTypes[key]++;
      }
    });

    // Step 4: Find applications without matching jobs
    const applicationsByJob = {};
    let unmatchedApplications = 0;
    
    await Promise.all(allApplications.map(async (app) => {
      let jobId = app.jobId;
      
      // Try to convert to ObjectId if it's a string
      let jobObjectId;
      if (typeof jobId === 'string') {
        try {
          jobObjectId = new ObjectId(jobId);
        } catch (e) {
          // Not a valid ObjectId string
        }
      }
      
      // Check for job using different formats
      const job = await db.collection("jobs").findOne({
        $or: [
          { _id: jobId },
          { _id: jobObjectId },
          { _id: jobObjectId?.toString() }
        ].filter(Boolean) // Remove undefined entries
      });
      
      if (job) {
        if (!applicationsByJob[job._id]) {
          applicationsByJob[job._id] = [];
        }
        applicationsByJob[job._id].push(app._id);
      } else {
        unmatchedApplications++;
      }
    }));

    // Step 5: Check if there's job data for the specific job ID passed
    let specificJobData = null;
    let specificJobApplications = [];
    
    if (req.query.jobId) {
      let jobId = req.query.jobId;
      try {
        let jobObjectId = new ObjectId(jobId);
        
        // Get the specific job
        specificJobData = await db.collection("jobs").findOne({ _id: jobObjectId });
        
        if (specificJobData) {
          // Get applications, trying different jobId formats
          specificJobApplications = await db.collection("applications").find({
            $or: [
              { jobId: jobId },
              { jobId: jobObjectId }
            ]
          }).toArray();
        }
      } catch (e) {
        console.error("Error checking specific job:", e);
      }
    }

    await client.close();

    return res.status(200).json({
      totalJobs: jobs.length,
      totalApplications: allApplications.length,
      jobIdTypes,
      unmatchedApplications,
      applicationCountByJob: Object.keys(applicationsByJob).map(jobId => ({
        jobId,
        applicationCount: applicationsByJob[jobId].length
      })),
      specificJob: specificJobData 
        ? {
            id: specificJobData._id,
            title: specificJobData.title,
            applicationCount: specificJobApplications.length,
            applicationSample: specificJobApplications.slice(0, 2)
          }
        : null
    });
  } catch (error) {
    console.error("❌ Error in fix-job-ids API:", error);
    return res.status(500).json({ message: "Internal Server Error", error: error.message });
  }
}
