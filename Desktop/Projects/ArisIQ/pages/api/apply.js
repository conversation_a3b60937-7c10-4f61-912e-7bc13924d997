// File: pages/api/apply.js

import nextConnect from "next-connect";
import formidable from "formidable";
import { MongoClient, GridFSBucket, ObjectId } from "mongodb";
import { Readable } from "stream";
import connectToDatabase from "../../lib/mongodb";

export const config = {
  api: {
    bodyParser: false,
  },
};

// MongoDB connection pool with retry logic
let cachedClient = null;
async function getMongoClient() {
  if (cachedClient) {
    return cachedClient;
  }
  
  try {
    // Try using the connectToDatabase helper first
    await connectToDatabase();
    
    // Create a new client with timeout options
    const client = new MongoClient(process.env.MONGODB_URI, {
      serverSelectionTimeoutMS: 5000, // 5 seconds
      connectTimeoutMS: 10000, // 10 seconds
      socketTimeoutMS: 45000, // 45 seconds
    });
    
    await client.connect();
    cachedClient = client;
    return client;
  } catch (error) {
    console.error("MongoDB connection error:", error);
    throw error;
  }
}

const handler = nextConnect();

handler.post(async (req, res) => {
  let client = null;
  
  try {
    const form = formidable({ keepExtensions: true, maxFileSize: 10 * 1024 * 1024 });

    const parseForm = () => new Promise((resolve, reject) => {
      form.parse(req, (err, fields, files) => {
        if (err) reject(err);
        resolve({ fields, files });
      });
    });

    const { fields, files } = await parseForm();

    // Validate required fields
    const requiredFields = ["firstName", "lastName", "email"];
    const missingFields = requiredFields.filter(field => !fields[field]);
    
    if (missingFields.length > 0) {
      return res.status(400).json({ 
        success: false, 
        message: `Missing required fields: ${missingFields.join(", ")}` 
      });
    }

    if (!files.resume) {
      return res.status(400).json({ success: false, message: "Resume file is required" });
    }

    // Get MongoDB client with improved connection handling
    try {
      client = await getMongoClient();
    } catch (dbError) {
      console.error("Failed to connect to MongoDB:", dbError);
      return res.status(500).json({ 
        success: false, 
        message: "Database connection failed. Please try again later." 
      });
    }

    const db = client.db();
    const bucket = new GridFSBucket(db, { bucketName: "resumes" });

    const file = files.resume;
    const resumeData = file.buffer || (file.filepath && require("fs").readFileSync(file.filepath));
    if (!resumeData) {
      return res.status(400).json({ success: false, message: "Could not read resume file content" });
    }

    const readable = Readable.from(resumeData);
    const uploadStream = bucket.openUploadStream(file.originalFilename || file.newFilename);

    // Process LinkedIn PDF if provided
    let linkedinPdfId = null;
    if (files.linkedinPdf) {
      try {
        const linkedinPdfData = files.linkedinPdf.buffer || 
          (files.linkedinPdf.filepath && require("fs").readFileSync(files.linkedinPdf.filepath));
        
        if (linkedinPdfData) {
          const linkedinReadable = Readable.from(linkedinPdfData);
          const linkedinUploadStream = bucket.openUploadStream(
            files.linkedinPdf.originalFilename || files.linkedinPdf.newFilename
          );
          
          await new Promise((resolve, reject) => {
            linkedinReadable.pipe(linkedinUploadStream)
              .on('error', reject)
              .on('finish', () => {
                linkedinPdfId = linkedinUploadStream.id;
                resolve();
              });
          });
        }
      } catch (linkedinError) {
        console.error("LinkedIn PDF upload error (non-fatal):", linkedinError);
        // Continue with application even if LinkedIn PDF upload fails
      }
    }

    const uploadPromise = new Promise((resolve, reject) => {
      readable.pipe(uploadStream)
        .on("error", (e) => {
          reject(e);
        })
        .on("finish", async () => {
          try {
            const newApp = {
              jobId: new ObjectId(fields.jobId),
              jobTitle: fields.jobTitle,
              companyName: fields.companyName,
              location: fields.location,
              firstName: fields.firstName,
              lastName: fields.lastName,
              fullName: `${fields.firstName} ${fields.lastName}`,
              email: fields.email,
              phone: fields.phone,
              linkedIn: fields.linkedIn,
              linkedinPdfId: linkedinPdfId,
              portfolio: fields.portfolio,
              github: fields.github,
              experienceYears: fields.experienceYears,
              coverLetter: fields.coverLetter,
              expectedSalary: fields.expectedSalary,
              education: JSON.parse(fields.education || "[]"),
              certifications: JSON.parse(fields.certifications || "[]"),
              availability: fields.availability,
              visaStatus: fields.visaStatus,
              visaSponsorship: fields.visaSponsorship,
              resumeId: uploadStream.id,
              status: "Applied",
              createdAt: new Date(),
              updatedAt: new Date(),
            };

            const result = await db.collection("applications").insertOne(newApp);
            resolve(result.insertedId);
          } catch (dbError) {
            reject(dbError);
          }
        });
    });

    try {
      // Wait for upload and database insertion with a timeout
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error("Operation timed out")), 30000)
      );
      
      const insertedId = await Promise.race([uploadPromise, timeoutPromise]);
      
      // Return success response immediately
      res.status(200).json({ success: true, applicationId: insertedId });
      
      // Trigger resume scan in the background (after response is sent)
      try {
        // Don't await this call - let it run in the background
        fetch(`${process.env.NEXTAUTH_URL || "http://localhost:3001"}/api/scan-resume`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ jobId: fields.jobId, applicationId: insertedId })
        }).catch(scanError => {
          console.error("Resume scan error (background process):", scanError);
          // This error doesn't affect the user experience since response is already sent
        });
      } catch (scanError) {
        console.error("Resume scan initiation error:", scanError);
        // This error doesn't affect the user experience since response is already sent
      }
    } catch (timeoutError) {
      console.error("Operation timed out:", timeoutError);
      return res.status(500).json({ 
        success: false, 
        message: "Operation timed out. Please try again later." 
      });
    }
  } catch (error) {
    console.error("❌ Full Error in /api/apply:", error);
    return res.status(500).json({ 
      success: false, 
      message: "Application submission failed", 
      error: error.message 
    });
  }
});

export default handler;
