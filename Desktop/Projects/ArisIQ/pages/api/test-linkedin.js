// File: pages/api/test-linkedin.js

const puppeteer = require("puppeteer");
const { loadLinkedInCookies, verifyLinkedInCookies } = require("../../utils/linkedin-auth");

export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  const { linkedinUrl } = req.body;
  if (!linkedinUrl) {
    return res.status(400).json({ 
      success: false,
      message: "Missing LinkedIn URL" 
    });
  }

  let browser = null;
  console.log("Starting LinkedIn test with URL:", linkedinUrl);

  try {
    // Verify if cookies exist and are valid (optional)
    try {
      const cookiesValid = await verifyLinkedInCookies();
      if (!cookiesValid) {
        console.log("LinkedIn cookies are missing or invalid. Will attempt unauthenticated access.");
      } else {
        console.log("LinkedIn cookies are valid. Will use authenticated access.");
      }
    } catch (cookieErr) {
      console.warn("Error verifying LinkedIn cookies:", cookieErr.message);
    }
    
    // Ensure URL has proper format
    let fullUrl = linkedinUrl;
    if (!fullUrl.startsWith('http')) {
      fullUrl = `https://${fullUrl}`;
      console.log("Added https prefix, URL is now:", fullUrl);
    }
    
    // Launch browser with detailed logging
    console.log("Launching browser...");
    browser = await puppeteer.launch({
      headless: "new", 
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--disable-gpu'
      ]
    });
    console.log("Browser launched successfully");
    
    // Create a new page with detailed logging
    console.log("Creating new page...");
    const page = await browser.newPage();
    console.log("Page created successfully");
    
    // Load LinkedIn cookies for authenticated access
    console.log("Loading LinkedIn cookies...");
    try {
      await loadLinkedInCookies(page);
    } catch (err) {
      console.warn("Failed to load LinkedIn cookies:", err.message);
    }
    
    // Set user agent and viewport
    await page.setUserAgent(
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36'
    );
    await page.setViewport({ width: 1366, height: 768 });
    console.log("User agent and viewport set");
    
    // Navigate to profile with detailed logging
    console.log(`Navigating to: ${fullUrl}`);
    
    // Enable console log collection from the browser
    page.on('console', msg => console.log('Browser console:', msg.text()));
    
    try {
      await page.goto(fullUrl, { 
        waitUntil: 'networkidle2',
        timeout: 60000 // Increase timeout to 60 seconds
      });
      console.log("Navigation completed");
    } catch (navError) {
      console.error("Navigation error:", navError.message);
      // Take screenshot even if navigation fails
      const screenshot = await page.screenshot({ encoding: "base64" });
      
      // Clean up
      await browser.close();
      return res.json({ 
        success: false, 
        message: `Navigation failed: ${navError.message}`,
        currentUrl: await page.url(),
        screenshot: `data:image/png;base64,${screenshot}`
      });
    }
    
    // Take a screenshot to debug
    console.log("Taking screenshot...");
    const screenshot = await page.screenshot({ encoding: "base64" });
    console.log("Screenshot taken");
    
    // Get current URL
    const currentUrl = await page.url();
    console.log("Current URL:", currentUrl);
    
    // Check if we hit a login page
    console.log("Checking if login page...");
    const isLoginPage = await page.evaluate(() => {
      return window.location.href.includes('linkedin.com/login') || 
             document.querySelector('.login-form') !== null ||
             document.querySelector('[data-test-id="sign-in-form"]') !== null ||
             document.body.textContent.includes('Sign in to view');
    });
    
    if (isLoginPage) {
      console.log("Login page or sign-in wall detected");
      
      // Try to extract basic information even from login page
      const basicInfo = await page.evaluate(() => {
        const name = document.querySelector('h1')?.textContent.trim();
        const bodyText = document.body.textContent.trim().substring(0, 500);
        
        // Get all available elements on the page for debugging
        const availableElements = {};
        const checkSelectors = [
          'h1', 'h2', '.text-heading-xlarge', '.text-body-medium', '.pv-text-details__left-panel',
          '.experience-section', '.education-section', '.skills-section', '[data-section]',
          '.pv-top-card', '.top-card-layout', '.pvs-list', '.pv-entity'
        ];
        
        checkSelectors.forEach(selector => {
          const elements = document.querySelectorAll(selector);
          availableElements[selector] = elements.length;
        });
        
        return {
          name,
          headline: null,
          bodyText,
          availableElements
        };
      });
      
      // Clean up
      await browser.close();
      
      // Return the limited info we could get
      return res.json({ 
        success: true, 
        limitedAccess: true,
        profile: basicInfo,
        message: "LinkedIn requires authentication. Only limited profile information available.",
        currentUrl,
        screenshot: `data:image/png;base64,${screenshot}`
      });
    }
    
    // Check page HTML
    console.log("Getting page HTML...");
    const pageHtml = await page.content();
    const htmlPreview = pageHtml.substring(0, 200) + "..."; // First 200 chars
    console.log("Page HTML preview:", htmlPreview);
    
    // Extract profile information with updated selectors
    console.log("Extracting profile information...");
    const profileInfo = await page.evaluate(() => {
      // Helper function to get text content safely
      const getText = (selector, fallbackSelectors = []) => {
        // Try the primary selector first
        let element = document.querySelector(selector);
        
        // If not found, try fallback selectors
        if (!element && fallbackSelectors.length > 0) {
          for (const fallbackSelector of fallbackSelectors) {
            element = document.querySelector(fallbackSelector);
            if (element) break;
          }
        }
        
        return element ? element.textContent.trim() : null;
      };
      
      // Try multiple different approaches to find elements
      
      // 1. Name - try multiple possible selectors
      const name = getText('h1', [
        'h1.text-heading-xlarge',
        'h1.inline',
        '.top-card-layout__title',
        '.pv-top-card--list li:first-child'
      ]);
      
      // 2. Headline - try multiple possible selectors
      const headline = getText('.text-body-medium', [
        '.mt1.t-18',
        '.pv-text-details__left-panel',
        '.top-card-layout__headline',
        '.pv-top-card--list li.t-18',
        '.ph5.pb5 h2',
        '.pv-top-card--list div.text-body-medium'
      ]);
      
      // 3. Location - try multiple possible selectors
      const location = getText('[data-section="location"]', [
        '.top-card-layout__first-subline',
        '.pv-top-card--list li.t-16',
        '.pv-entity__location'
      ]);
      
      // 4. About/Summary - multiple approaches
      const about = getText('#about ~ div p', [
        '.pv-shared-text-with-see-more p',
        '[data-section="summary"] p',
        '.personal-info__about-text',
        '.about-section p'
      ]);
      
      // 5. Look for Experience sections more broadly
      let experience = '';
      
      // Try various container selectors for experience
      const expContainers = [
        '#experience ~ div ul > li',
        '.experience-section li',
        '.pv-entity__position-group',
        '[data-section="experience"] li',
        '.pvs-list__item--experience'
      ];
      
      for (const containerSelector of expContainers) {
        const containers = document.querySelectorAll(containerSelector);
        if (containers.length > 0) {
          experience = Array.from(containers)
            .map(container => {
              // Try different selectors for role, company, dates within each container
              const role = getText('.t-bold, .t-16, .pv-entity__summary-info h3', [], container);
              const company = getText('.t-normal, .pv-entity__secondary-title', [], container);
              const dates = getText('.t-normal.t-black--light, .pv-entity__date-range', [], container);
              
              return `${role || 'Role not found'} at ${company || 'Company not found'} (${dates || 'Dates not found'})`;
            })
            .join('\n');
          break; // If we found experience items, don't try other container types
        }
      }
      
      // 6. Try to get skills from various selectors
      let skills = '';
      const skillSelectors = [
        '.skill-category-entity__name',
        '.pv-skill-category-entity__name',
        '[data-section="skills"] .pv-skill-entity__skill-name',
        '.skills-section li',
        '.pvs-list__item--skill'
      ];
      
      for (const skillSelector of skillSelectors) {
        const skillElements = document.querySelectorAll(skillSelector);
        if (skillElements.length > 0) {
          skills = Array.from(skillElements)
            .map(el => el.textContent.trim())
            .filter(text => text.length > 0)
            .join(', ');
          break;
        }
      }
      
      // 7. Look for education sections with multiple approaches
      let education = '';
      const eduContainers = [
        '#education ~ div ul > li',
        '.education-section li',
        '.pv-entity__degree-info',
        '[data-section="education"] li',
        '.pvs-list__item--education'
      ];
      
      for (const containerSelector of eduContainers) {
        const containers = document.querySelectorAll(containerSelector);
        if (containers.length > 0) {
          education = Array.from(containers)
            .map(container => {
              const school = getText('.t-bold, .pv-entity__school-name', [], container);
              const degree = getText('.t-normal, .pv-entity__degree-name', [], container);
              const dates = getText('.t-normal.t-black--light, .pv-entity__dates', [], container);
              
              return `${school || 'School not found'}, ${degree || 'Degree not found'} (${dates || 'Dates not found'})`;
            })
            .join('\n');
          break;
        }
      }
      
      // 8. Get all the main text from the page as a fallback
      const mainText = document.querySelector('main') 
        ? document.querySelector('main').textContent.trim()
        : document.body.textContent.trim();
      const bodyPreview = mainText.substring(0, 300) + '...';
      
      // Get all available elements on the page for debugging
      const availableElements = {};
      const checkSelectors = [
        'h1', 'h2', '.text-heading-xlarge', '.text-body-medium', '.pv-text-details__left-panel',
        '.experience-section', '.education-section', '.skills-section', '[data-section]',
        '.pv-top-card', '.top-card-layout', '.pvs-list', '.pv-entity'
      ];
      
      checkSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        availableElements[selector] = elements.length;
      });
      
      // Return all the extracted profile data
      return {
        name,
        headline,
        location,
        about,
        experience,
        education,
        skills,
        url: window.location.href,
        bodyText: bodyPreview,
        availableElements
      };
    });
    
    console.log("Profile extraction result:", profileInfo);
    
    // Clean up
    await browser.close();
    
    return res.json({ 
      success: true, 
      profile: profileInfo,
      currentUrl,
      screenshot: `data:image/png;base64,${screenshot}`
    });
    
  } catch (error) {
    console.error("Test LinkedIn scraping failed:", error);
    
    // Make sure browser is closed if an error occurs
    if (browser) {
      try {
        await browser.close();
      } catch (closeErr) {
        console.error("Error closing browser:", closeErr.message);
      }
    }
    
    return res.status(500).json({ 
      success: false, 
      message: `Error occurred: ${error.message}`,
      stack: error.stack,
      error: error.toString()
    });
  }
}
