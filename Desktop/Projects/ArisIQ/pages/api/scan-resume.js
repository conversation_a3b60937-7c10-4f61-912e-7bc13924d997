// File: pages/api/scan-resume.js

import { Readable } from "stream";
import { MongoClient, ObjectId, GridFSBucket } from "mongodb";
import pdfParse from "pdf-parse";
import OpenAI from "openai";
import Anthropic from "@anthropic-ai/sdk";
import axios from "axios";
import cheerio from "cheerio";
import nodemailer from 'nodemailer';

const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
const claude = new Anthropic({ apiKey: process.env.CLAUDE_API_KEY });

// Configure email transport
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: parseInt(process.env.SMTP_PORT),
  secure: false,
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

// Function to detect potential AI manipulation attempts with improved context awareness
function detectAIManipulationAttempts(text, jobIndustry, jobTitle) {
  // Remove all formatting and normalize whitespace
  const plainText = text.replace(/\s+/g, ' ').trim();
  
  // List of suspicious phrases that might be attempts to manipulate AI
  const suspiciousPhrases = [
    "ignore everything", "give max", "maximum score", "perfect score",
    "select this candidate", "choose me", "give this application",
    "ignore previous", "ignore content", "disregard", "award points",
    "auto approve", "automatic approval", "bypass"
  ];
  
  // Check for suspicious phrases
  const foundPhrases = suspiciousPhrases.filter(phrase => 
    plainText.toLowerCase().includes(phrase.toLowerCase())
  );
  
  // Check for abnormal keyword repetition with context awareness
  const wordCounts = {};
  plainText.split(' ').forEach(word => {
    if (word.length > 3) { // Only count words with more than 3 characters
      const normalizedWord = word.toLowerCase().replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, "");
      wordCounts[normalizedWord] = (wordCounts[normalizedWord] || 0) + 1;
    }
  });
  
  // Common technical terms that might legitimately appear frequently
  const commonTechTerms = [
    "data", "database", "sql", "python", "java", "cloud", "api", 
    "software", "development", "engineer", "management", "project", 
    "analysis", "design", "testing", "application", "server", "client",
    "network", "security", "system", "infrastructure", "architecture",
    "information", "technology", "business", "solution", "experience",
    "implementation", "integration", "platform"
  ];
  
  // Domain-specific terms based on job industry
  let industryTerms = [];
  
  // Add industry-specific common terms
  if (jobIndustry) {
    const industry = jobIndustry.toLowerCase();
    
    if (industry.includes("data") || industry.includes("analytics")) {
      industryTerms = ["data", "analytics", "warehouse", "lake", "mining", "visualization", 
                       "pipeline", "etl", "reporting", "intelligence", "dashboard"];
    } else if (industry.includes("finance") || industry.includes("banking")) {
      industryTerms = ["finance", "banking", "transaction", "payment", "financial", 
                      "investment", "trading", "asset", "portfolio", "analysis"];
    } else if (industry.includes("healthcare")) {
      industryTerms = ["health", "patient", "medical", "clinical", "care", 
                      "hospital", "doctor", "treatment", "diagnosis"];
    }
  }
  
  // Get the list of terms to check with higher thresholds
  const contextualTerms = [...new Set([...commonTechTerms, ...industryTerms])];
  
  // Identify words with abnormally high frequencies, with context awareness
  const suspiciousWords = Object.entries(wordCounts)
    .filter(([word, count]) => {
      if (contextualTerms.includes(word)) {
        // Higher threshold (25) for common technical and industry-specific terms
        return count > 25;
      } else {
        // Standard threshold (15) for other words
        return count > 15;
      }
    })
    .map(([word, count]) => ({ word, count }));
  
  // Detect if text color manipulation might be present
  const possibleColorManipulation = text.includes("color:white") || 
                                   text.includes("color: white") ||
                                   text.includes("color:#fff") ||
                                   text.includes("color: #fff") ||
                                   text.includes("font-size:1px") ||
                                   text.includes("font-size: 1px");
  
  // Return evaluation results
  return {
    manipulationDetected: foundPhrases.length > 0 || possibleColorManipulation || suspiciousWords.length > 0,
    suspiciousPhrases: foundPhrases,
    abnormalRepetition: suspiciousWords,
    possibleColorManipulation: possibleColorManipulation,
    suspiciousScore: (foundPhrases.length * 10) + 
                    (suspiciousWords.length * 5) + 
                    (possibleColorManipulation ? 15 : 0)
  };
}

// Function to send interview invitation email to candidate
async function sendInterviewInvitation(application, job) {
  // Create interview link
  const interviewLink = `${process.env.NEXTAUTH_URL || "http://localhost:3000"}/candidate-dashboard`;
  
  // Email Content
  const mailOptions = {
    from: `"ArisIQ" <${process.env.EMAIL_USER}>`,
    to: application.email,
    subject: `Congratulations! You're Selected for an Interview for ${job.title} at ${job.company}`,
    html: `
      <p>Hello ${application.firstName || application.fullName},</p>

      <p>Congratulations! Your application for the <strong>${job.title}</strong> position at <strong>${job.company}</strong> has been reviewed and you have been automatically selected for an interview based on your excellent qualifications.</p>
      
      <h4>🎯 Interview Instructions:</h4>
      <ul>
        <li>Use a desktop or laptop with a webcam and microphone.</li>
        <li>Webcam and microphone access is mandatory.</li>
        <li>Switch to single screen — dual monitors are not allowed.</li>
        <li>Stay in fullscreen mode — pressing ESC will disqualify you.</li>
        <li>Do not switch tabs or minimize your window.</li>
        <li>Copy/paste functionality is disabled.</li>
        <li>Use of developer tools (F12) is monitored.</li>
        <li>Your webcam and microphone will be monitored throughout the session.</li>
        <li><strong>Recommended Browser:</strong> Google Chrome or Microsoft Edge.</li>
      </ul>

      <p><strong>To start the interview:</strong></p>
      <ol>
        <li>Allow camera and mic access when prompted by the browser.</li>
        <li>Review all instructions and check the acknowledgment box.</li>
        <li>Click "Start Interview" when you're ready.</li>
      </ol>

      <p><em>Click the ⓘ or 🔒 icon next to the browser URL and enable permissions for mic and camera.</em></p>

      <p style="margin-top: 20px;">
        <a href="${interviewLink}" 
          style="background-color: #2563eb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 6px; display: inline-block;">
          👉 Click here to begin your interview
        </a>
      </p>

      <p>Good luck!<br/>— ${job.company} Recruiting Team</p>
    `,
  };

  // Send the email
  return transporter.sendMail(mailOptions);
}

export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  const { jobId, applicationId } = req.body;
  if (!jobId || !applicationId) {
    return res.status(400).json({ message: "Missing jobId or applicationId" });
  }

  try {
    const mongoClient = new MongoClient(process.env.MONGODB_URI);
    await mongoClient.connect();
    const db = mongoClient.db();
    const applications = db.collection("applications");
    const jobs = db.collection("jobs");
    const suspiciousCandidates = db.collection("suspiciousCandidates");

    // Get the application details
    const application = await applications.findOne({ _id: new ObjectId(applicationId) });
    if (!application) {
      return res.status(404).json({ message: "Application not found" });
    }

    // Get the job details to use in the comparison
    const job = await jobs.findOne({ _id: new ObjectId(jobId) });
    if (!job) {
      return res.status(404).json({ message: "Job not found" });
    }

    const bucket = new GridFSBucket(db, { bucketName: "resumes" });
    const downloadStream = bucket.openDownloadStream(application.resumeId);

    const buffer = await new Promise((resolve, reject) => {
      const chunks = [];
      downloadStream.on("data", (chunk) => chunks.push(chunk));
      downloadStream.on("end", () => resolve(Buffer.concat(chunks)));
      downloadStream.on("error", reject);
    });

    const pdfData = await pdfParse(buffer);
    const resumeText = pdfData.text;

    // Extract cover letter from the application data
    const coverLetter = application.coverLetter || "";
    console.log(`Cover letter length: ${coverLetter.length} characters`);

    // Extract and format certifications
    let certificationsText = "";
    let certifications = [];
    if (application.certifications && Array.isArray(application.certifications)) {
      certifications = application.certifications
        .filter(cert => cert && cert.trim() !== "");
        
      if (certifications.length > 0) {
        certificationsText = "Certifications:\n- " + certifications.join("\n- ");
      }
    }
    console.log(`Certifications: ${certificationsText || "None provided"}`);

    // Extract education information
    let educationText = "";
    if (application.education && Array.isArray(application.education)) {
      const validEducation = application.education.filter(edu => 
        (edu.degree && edu.degree.trim() !== "") || 
        (edu.university && edu.university.trim() !== "")
      );
      
      if (validEducation.length > 0) {
        educationText = "Education:\n" + validEducation.map(edu => 
          `- ${edu.degree || ""} ${edu.major || ""}, ${edu.university || ""}, ${edu.city || ""}, ${edu.year || ""}`
        ).join("\n");
      }
    }

    // Process LinkedIn PDF if available
    let linkedInText = "";
    let hasLinkedIn = false;
    if (application.linkedinPdfId) {
      try {
        console.log("Processing LinkedIn PDF...");
        const linkedInDownloadStream = bucket.openDownloadStream(application.linkedinPdfId);
        
        const linkedInBuffer = await new Promise((resolve, reject) => {
          const chunks = [];
          linkedInDownloadStream.on("data", (chunk) => chunks.push(chunk));
          linkedInDownloadStream.on("end", () => resolve(Buffer.concat(chunks)));
          linkedInDownloadStream.on("error", reject);
        });
        
        const linkedInPdfData = await pdfParse(linkedInBuffer);
        linkedInText = linkedInPdfData.text;
        hasLinkedIn = true;
        console.log(`LinkedIn PDF parsed successfully. Length: ${linkedInText.length} characters`);
      } catch (err) {
        console.warn("LinkedIn PDF parse failed:", err.message);
      }
    }

    // ========== AI MANIPULATION DETECTION ==========
    const allText = resumeText + " " + coverLetter + " " + linkedInText;
    const manipulationCheck = detectAIManipulationAttempts(allText, job.industry, job.title);
    
    console.log("Manipulation check results:", JSON.stringify(manipulationCheck, null, 2));
    
    // Flag suspicious candidate but don't disqualify
    let isSuspicious = false;
    
    if (manipulationCheck.manipulationDetected && manipulationCheck.suspiciousScore > 10) {
      isSuspicious = true;
      
      // Save to suspicious candidates collection for recruiter review
      await suspiciousCandidates.insertOne({
        applicationId: application._id,
        candidateName: application.fullName,
        candidateEmail: application.email,
        jobId: job._id,
        jobTitle: job.title,
        manipulationDetails: manipulationCheck,
        flaggedAt: new Date(),
        reviewed: false
      });
      
      console.log(`Candidate ${application.fullName} flagged as suspicious for recruiter review.`);
    }
    // ================================================

    // Prepare job requirements data
    const requiredSkills = job.requiredSkills || [];
    const preferredSkills = job.preferredSkills || [];
    const jobDescription = job.description || "";
    
    // Check if certifications are relevant to required skills
    const relevantCertifications = certifications.filter(cert => 
      requiredSkills.some(skill => 
        cert.toLowerCase().includes(skill.toLowerCase())
      )
    );
    
    // Construct a comprehensive prompt with all available information and evaluation guidelines
    const prompt = `
Evaluate the candidate's resume for the following job. Make sure to consider all aspects of their application, including resume, cover letter, certifications, and LinkedIn profile if available.

JOB TITLE: ${job.title || ""}
COMPANY: ${job.company || ""}
DESCRIPTION: ${jobDescription}

REQUIRED SKILLS: ${requiredSkills.join(", ")}
PREFERRED SKILLS: ${preferredSkills.join(", ")}

CANDIDATE INFORMATION:
-----------------------
RESUME:
${resumeText}

COVER LETTER:
${coverLetter || "Not provided"}

${certificationsText}

${educationText}

LINKEDIN PROFILE DATA:
${linkedInText || "Not provided"}

YEARS OF EXPERIENCE: ${application.experienceYears || "Not specified"}

EVALUATION CRITERIA AND SCORING GUIDELINES:
-------------------------------------------
1. Required Skills Match (Weight: 40%): How well does the candidate match the required skills? 
   - For each skill, indicate whether the candidate has demonstrated practical experience with it, or if it's just mentioned without evidence
   - Evidence of practical experience with a skill is much more valuable than just mentioning it
   - Score this section out of 100

2. Experience Match (Weight: 25%): Does the candidate have relevant experience for this role?
   - Consider both the quantity (years) and quality (relevance) of experience
   - Score this section out of 100

3. Education & University (Weight: 15%): Is their educational background suitable?
   - Consider the relevance of their degree to the position
   - Score this section out of 100

4. Certifications (Weight: 10%): Do they have relevant certifications?
   - Give higher scores for certifications directly related to required skills
   - Give moderate scores for other technical certifications as evidence of continued learning
   - Do not give high scores just for having certifications that aren't relevant
   - Score this section out of 100

5. Cover Letter (Weight: 5%): Does the cover letter show genuine interest and understanding of the role?
   - This is optional and should not heavily impact the overall score if missing
   - Score this section out of 100

6. LinkedIn Profile/Professional Presence (Weight: 5%): 
   - This is optional and should not heavily impact the overall score if missing
   - Score this section out of 100

7. Preferred Skills Match (Bonus up to 10%): How well does the candidate match the preferred skills?
   - This can boost the final score but won't penalize candidates without these skills
   - Score this section out of 100

IMPORTANT: Be vigilant for inconsistencies or exaggerated claims. Focus on demonstrated skills rather than just mentioned skills.

Please provide a detailed evaluation with a match score out of 100 for each criterion, and an overall match score.
Also include a brief recommendation on whether to proceed with the candidate based on their qualifications.

Finally, provide a final structured summary in this exact format:
EVALUATION:
1. Required Skills Match: [Brief assessment]. Score: [X]/100
2. Experience Match: [Brief assessment]. Score: [X]/100
3. Education & University: [Brief assessment]. Score: [X]/100
4. Certifications: [Brief assessment]. Score: [X]/100
5. Cover Letter: [Brief assessment]. Score: [X]/100
6. LinkedIn Profile: [Brief assessment]. Score: [X]/100
7. Preferred Skills Match: [Brief assessment]. Score: [X]/100

Overall match score: [X]/100

Recommendation: [1-2 sentence recommendation]
`;

    let gptScore = "";
    try {
      console.log("Requesting GPT evaluation...");
      const gptResponse = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [{ role: "user", content: prompt }],
      });
      gptScore = gptResponse.choices[0]?.message?.content || "";
      console.log("GPT evaluation complete");
    } catch (err) {
      console.error("GPT scoring failed:", err);
    }

    let claudeScore = "";
    try {
      console.log("Requesting Claude evaluation...");
      const claudeResponse = await claude.messages.create({
        model: "claude-3-5-sonnet-20241022", // Correct model name
        max_tokens: 1000,
        messages: [
          {
            role: "user",
            content: prompt,
          },
        ],
      });
      claudeScore = claudeResponse?.content?.[0]?.text || "";
      console.log("Claude evaluation complete");
    } catch (err) {
      console.error("Claude scoring failed:", err?.response?.data || err.message);
    }

    // Helper function to extract overall score from AI evaluations with improved pattern matching
    const extractOverallScore = (text) => {
      // Log a portion of the text for debugging
      console.log("Analyzing AI response for score extraction:", text.substring(text.length - 300));
      
      // First try to match "Overall match score: X/100" format
      const scoreMatch = text.match(/Overall match score:\s*(\d+(?:\.\d+)?)\/100/i);
      if (scoreMatch && scoreMatch[1]) {
        console.log(`Found overall match score pattern: ${scoreMatch[1]}`);
        return parseFloat(scoreMatch[1]);
      }
      
      // Also try to match "final score: X/100" format as fallback
      const finalScoreMatch = text.match(/final score:\s*(\d+(?:\.\d+)?)\/100/i);
      if (finalScoreMatch && finalScoreMatch[1]) {
        console.log(`Found final score pattern: ${finalScoreMatch[1]}`);
        return parseFloat(finalScoreMatch[1]);
      }
      
      // As a last resort, try to find any number followed by "/100" near the end of the text
      // Look in the last 500 characters where the summary is likely to be
      const lastPortion = text.substring(Math.max(0, text.length - 500));
      const anyScoreMatch = lastPortion.match(/(\d+(?:\.\d+)?)\/100/i);
      if (anyScoreMatch && anyScoreMatch[1]) {
        console.log(`Found generic score pattern near the end: ${anyScoreMatch[1]}`);
        return parseFloat(anyScoreMatch[1]);
      }
      
      console.log("No score pattern found in AI response");
      return null;
    };

    // Extract overall scores from AI evaluations
    const gptOverallScore = extractOverallScore(gptScore) || 0;
    const claudeOverallScore = extractOverallScore(claudeScore) || 0;
    
    // Calculate AI average score
    const aiScore = (gptOverallScore + claudeOverallScore) / 2;
    console.log(`AI Scores - GPT: ${gptOverallScore}, Claude: ${claudeOverallScore}, Average: ${aiScore}`);

    // Simple skill matching analysis
    const combinedText = resumeText + " " + coverLetter + " " + linkedInText;
    
    // Check which required skills are mentioned in the text
    const requiredSkillsMatched = requiredSkills.filter(skill => 
      combinedText.toLowerCase().includes(skill.toLowerCase())
    );
    
    // Check which preferred skills are mentioned in the text
    const preferredSkillsMatched = preferredSkills.filter(skill => 
      combinedText.toLowerCase().includes(skill.toLowerCase())
    );
    
    // Calculate keyword match score
    const keywordMatchScore = requiredSkills.length > 0 
      ? Math.round((requiredSkillsMatched.length / requiredSkills.length) * 100) 
      : 50; // Default to 50 if no required skills
    
    console.log(`Keyword Match Score: ${keywordMatchScore}%`);
    console.log(`Required Skills Matched: ${requiredSkillsMatched.join(', ')}`);
    
    // Calculate combined score (70% AI evaluation, 30% keyword matching)
    let finalScore = Math.round((aiScore * 0.7) + (keywordMatchScore * 0.3));
    
    console.log(`Final Combined Score: ${finalScore}`);
    
    // Determine score-based tier (for recruiter dashboard)
    const scoreTier = 
      finalScore >= 85 ? "Excellent Match" :
      finalScore >= 70 ? "Good Match" :
      finalScore >= 55 ? "Potential Match" :
      "Low Match";
    
    // ========== AUTOMATIC INTERVIEW INVITATION LOGIC ==========
    // Determine if candidate can take interview based on score
    const canTakeInterview = finalScore >= 80;
    
    // Determine candidate status based on score
    let candidateStatus = 'applied';
    if (finalScore >= 80) {
      candidateStatus = 'screening'; // Automatically approved for interview
    }
    
    // Send automatic email if score >= 80
    if (canTakeInterview && !isSuspicious) {
      try {
        console.log(`Sending automatic interview invitation to ${application.email} (Score: ${finalScore})`);
        await sendInterviewInvitation(application, job);
        console.log("✅ Automatic interview invitation sent successfully");
      } catch (emailErr) {
        console.error("Failed to send automatic interview invitation:", emailErr);
        // Continue with the rest of the process even if email sending fails
      }
    }
    // ================================================
    
    // Calculate score for relevant certifications vs general learning
    const certificationRelevanceScore = certifications.length > 0
      ? Math.round((relevantCertifications.length / certifications.length) * 100)
      : 0;
    
    // Update the application with the scan results
    await applications.updateOne(
      { _id: new ObjectId(applicationId) },
      {
        $set: {
          scanResult: {
            scannedAt: new Date(),
            canTakeInterview: canTakeInterview,
            scoreTier: scoreTier,
            isSuspicious: isSuspicious,
            gptScore,
            claudeScore,
            compare: {
              success: true,
              match_score: finalScore,
              details: {
                required_skills_matched: requiredSkillsMatched,
                preferred_skills_matched: preferredSkillsMatched,
                gpt_evaluation_score: gptOverallScore,
                claude_evaluation_score: claudeOverallScore,
                keyword_match_score: keywordMatchScore,
                ai_average_score: Math.round(aiScore),
                certification_relevance: certificationRelevanceScore,
                has_linkedin: hasLinkedIn,
                has_cover_letter: coverLetter.length > 0,
                manipulation_check: isSuspicious ? manipulationCheck : null
              }
            }
          },
          score: finalScore,
          status: candidateStatus,
          // Add these fields if automatic interview was set up
          ...(canTakeInterview ? {
            screeningInviteSent: true,
            screeningInviteSentAt: new Date(),
          } : {})
        },
      }
    );

    return res.status(200).json({ 
      message: "Scan complete",
      match_score: finalScore,
      score_tier: scoreTier,
      can_take_interview: canTakeInterview,
      flagged_as_suspicious: isSuspicious,
      required_skills_matched: requiredSkillsMatched,
      preferred_skills_matched: preferredSkillsMatched,
      scores: {
        keyword_match: keywordMatchScore,
        gpt: gptOverallScore,
        claude: claudeOverallScore,
        ai_average: Math.round(aiScore),
        final: finalScore,
        certification_relevance: certificationRelevanceScore
      },
      manipulation_check: isSuspicious ? manipulationCheck : null
    });
  } catch (error) {
    console.error("❌ Resume scan failed:", error);
    return res.status(500).json({ message: "Internal Server Error", error: error.message });
  }
}
