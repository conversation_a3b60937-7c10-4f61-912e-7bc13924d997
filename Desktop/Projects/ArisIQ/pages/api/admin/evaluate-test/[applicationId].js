// pages/api/admin/evaluate-test/[applicationId].js
import TestEvaluationService from '../../../../services/testEvaluationService';
import connectToDatabase from '../../../../lib/mongodb'; // Use your existing connection

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    await connectToDatabase(); // Use your existing connection function
    
    const { applicationId } = req.query;
    
    if (!applicationId) {
      return res.status(400).json({ error: 'Application ID is required' });
    }

    console.log(`Starting AI evaluation for application: ${applicationId}`);

    // Initialize evaluation service
    const evaluationService = new TestEvaluationService();
    
    // Run the evaluation
    const result = await evaluationService.evaluateTest(applicationId);
    
    res.status(200).json({
      success: true,
      message: 'AI evaluation completed successfully',
      data: {
        applicationId,
        finalScore: result.finalScore,
        evaluationCount: Object.keys(result.evaluations).length,
        completedAt: new Date()
      }
    });

  } catch (error) {
    console.error('Evaluation API error:', error);
    
    res.status(500).json({
      success: false,
      error: 'AI evaluation failed',
      message: error.message,
      timestamp: new Date()
    });
  }
}

// pages/api/admin/evaluation-status/[applicationId].js
export async function getEvaluationStatus(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    await connectDB();
    
    const { applicationId } = req.query;
    const Application = require('../../../models/Application');
    
    const application = await Application.findById(applicationId).select(
      'evaluationStatus finalTestScore aiEvaluations evaluationStartedAt evaluationCompletedAt evaluationErrors'
    );
    
    if (!application) {
      return res.status(404).json({ error: 'Application not found' });
    }

    res.status(200).json({
      success: true,
      data: {
        status: application.evaluationStatus || 'pending',
        finalScore: application.finalTestScore,
        hasEvaluations: application.aiEvaluations && Object.keys(application.aiEvaluations).length > 0,
        startedAt: application.evaluationStartedAt,
        completedAt: application.evaluationCompletedAt,
        errors: application.evaluationErrors || []
      }
    });

  } catch (error) {
    console.error('Status check error:', error);
    res.status(500).json({ error: 'Failed to get evaluation status' });
  }
}

// pages/api/admin/retry-evaluation/[applicationId].js
export async function retryEvaluation(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    await connectDB();
    
    const { applicationId } = req.query;
    const Application = require('../../../models/Application');
    
    // Check current retry count
    const application = await Application.findById(applicationId);
    if (!application) {
      return res.status(404).json({ error: 'Application not found' });
    }

    const retryCount = application.evaluationRetries || 0;
    if (retryCount >= 3) {
      return res.status(400).json({ 
        error: 'Maximum retry attempts reached',
        retryCount 
      });
    }

    // Increment retry count
    await Application.findByIdAndUpdate(applicationId, {
      $set: {
        evaluationStatus: 'pending',
        evaluationRetries: retryCount + 1,
        evaluationErrors: []
      }
    });

    // Initialize evaluation service and retry
    const evaluationService = new TestEvaluationService();
    const result = await evaluationService.evaluateTest(applicationId);
    
    res.status(200).json({
      success: true,
      message: 'Evaluation retry completed successfully',
      data: {
        applicationId,
        retryCount: retryCount + 1,
        finalScore: result.finalScore
      }
    });

  } catch (error) {
    console.error('Retry evaluation error:', error);
    res.status(500).json({ error: 'Retry evaluation failed' });
  }
}
