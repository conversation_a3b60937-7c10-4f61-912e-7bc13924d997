// File: pages/api/force-fix-user.js
// Emergency API to fix user types

import connectToDatabase from "../../lib/mongodb";
import mongoose from "mongoose";

const UserSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String },
  userType: { type: String, enum: ['recruiter', 'candidate'], required: true },
  company: { type: String },
  provider: { type: String, default: 'credentials' },
  image: { type: String },
  location: { type: String },
  industry: { type: String },
  website: { type: String },
  description: { type: String },
  status: { 
    type: String, 
    enum: ['active', 'suspended'], 
    default: 'active'
  },
}, {
  timestamps: true
});

const User = mongoose.models.User || mongoose.model('User', UserSchema);

export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { email, company = "Default Company" } = req.body;

  if (!email) {
    return res.status(400).json({ 
      error: "Email is required" 
    });
  }

  try {
    await connectToDatabase();
    
    // FORCE update user to recruiter
    const updatedUser = await User.findOneAndUpdate(
      { email: email.toLowerCase() },
      { 
        userType: 'recruiter',
        company: company
      },
      { new: true }
    );

    if (!updatedUser) {
      return res.status(404).json({ error: "User not found" });
    }

    console.log('FORCE FIXED USER:', {
      email: updatedUser.email,
      userType: updatedUser.userType,
      company: updatedUser.company
    });

    res.status(200).json({ 
      message: "User FORCE FIXED to recruiter",
      user: {
        id: updatedUser._id,
        email: updatedUser.email,
        userType: updatedUser.userType,
        company: updatedUser.company,
      }
    });

  } catch (error) {
    console.error("Force fix error:", error);
    res.status(500).json({ 
      error: "Internal server error" 
    });
  }
}
