// File: pages/api/apply.js

import connectToDatabase from "../../lib/mongodb";
import multer from "multer";
import nextConnect from "next-connect";
import { GridFSBucket, MongoClient, ObjectId } from "mongodb";
import { Readable } from "stream";
import axios from "axios";
import pdfParse from "pdf-parse";

const uri = process.env.MONGODB_URI;
let gfsBucket;

const handler = nextConnect();
const upload = multer({ storage: multer.memoryStorage() });
handler.use(upload.single("resume"));

handler.post(async (req, res) => {
  try {
    const client = await MongoClient.connect(uri);
    const db = client.db();
    if (!gfsBucket) gfsBucket = new GridFSBucket(db, { bucketName: "resumes" });

    // Parse multipart form data
    const body = req.body;
    const firstName = body.firstName || "";
    const lastName = body.lastName || "";
    const email = body.email;
    const phone = body.phone;
    const linkedIn = body.linkedIn;
    const portfolio = body.portfolio;
    const experienceYears = body.experienceYears;
    const coverLetter = body.coverLetter;
    const expectedSalary = body.expectedSalary;
    const jobId = body.jobId;

    // ✅ Validate resume before upload
    try {
      await pdfParse(req.file.buffer); // Will throw if PDF is malformed
    } catch (pdfErr) {
      return res.status(400).json({
        success: false,
        message:
          "Invalid or corrupted resume file. Please upload a valid PDF (not scanned or image-based). If resume upload failed, try using 'Save as PDF' from a text editor or browser print option."
      });
    }

    // Check for duplicate
    const existing = await db.collection("applications").findOne({
      email,
      jobId
    });
    if (existing) {
      return res.status(409).json({ success: false, message: "You've already applied to this job." });
    }

    // Upload resume to GridFS
    const readableStream = Readable.from(req.file.buffer);
    const uploadStream = gfsBucket.openUploadStream(req.file.originalname);
    readableStream.pipe(uploadStream);

    uploadStream.on("finish", async () => {
      const application = {
        firstName,
        lastName,
        fullName: `${firstName} ${lastName}`.trim(),
        email,
        phone,
        linkedIn,
        portfolio,
        experienceYears,
        coverLetter,
        expectedSalary,
        resumeId: uploadStream.id,
        jobId,
        appliedAt: new Date(),
      };

      const result = await db.collection("applications").insertOne(application);
      const applicationId = result.insertedId;

      // ✅ Trigger resume scan API with jobId + applicationId
      try {
        await axios.post(
          `${process.env.NEXTAUTH_URL || "http://localhost:3000"}/api/scan-resume`,
          {
            jobId,
            applicationId: applicationId.toString(),
          }
        );
      } catch (scanErr) {
        console.error("❌ Resume Scan Trigger Failed:", scanErr);
      }

      res.status(200).json({ success: true, message: "Application submitted" });
    });
  } catch (err) {
    console.error("❌ API Error:", err);
    res.status(501).json({ success: false, message: err.message });
  }
});

export const config = {
  api: {
    bodyParser: false,
  },
};

export default handler;

