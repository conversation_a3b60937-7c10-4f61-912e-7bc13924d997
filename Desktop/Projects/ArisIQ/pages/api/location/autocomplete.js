// File: pages/api/location/autocomplete.js
// Real-time location autocomplete API

import axios from 'axios';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { type, query, country } = req.query;
    
    if (!type || !query) {
      return res.status(400).json({ 
        success: false, 
        message: 'Missing required parameters: type and query' 
      });
    }

    if (query.length < 2) {
      return res.status(200).json({ 
        success: true, 
        suggestions: [] 
      });
    }

    let suggestions = [];

    if (type === 'country') {
      suggestions = await getCountrySuggestions(query);
    } else if (type === 'city') {
      suggestions = await getCitySuggestions(query, country);
    }

    return res.status(200).json({
      success: true,
      suggestions: suggestions,
      count: suggestions.length
    });

  } catch (error) {
    console.error('Autocomplete error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch suggestions',
      error: error.message
    });
  }
}

// Get country suggestions from REST Countries API
async function getCountrySuggestions(query) {
  try {
    const response = await axios.get(
      `https://restcountries.com/v3.1/name/${encodeURIComponent(query)}?fields=name,flag,cca2`,
      { timeout: 5000 }
    );

    const countries = response.data || [];
    
    return countries
      .map(country => ({
        value: country.name.common,
        label: `${country.flag} ${country.name.common}`,
        code: country.cca2
      }))
      .sort((a, b) => a.value.localeCompare(b.value))
      .slice(0, 10); // Limit to 10 suggestions

  } catch (error) {
    console.error('Country API error:', error);
    
    // Fallback to popular countries if API fails
    const popularCountries = [
      { value: 'United States', label: '🇺🇸 United States', code: 'US' },
      { value: 'United Kingdom', label: '🇬🇧 United Kingdom', code: 'GB' },
      { value: 'Canada', label: '🇨🇦 Canada', code: 'CA' },
      { value: 'Australia', label: '🇦🇺 Australia', code: 'AU' },
      { value: 'Germany', label: '🇩🇪 Germany', code: 'DE' },
      { value: 'France', label: '🇫🇷 France', code: 'FR' },
      { value: 'India', label: '🇮🇳 India', code: 'IN' },
      { value: 'Japan', label: '🇯🇵 Japan', code: 'JP' },
      { value: 'Singapore', label: '🇸🇬 Singapore', code: 'SG' },
      { value: 'Netherlands', label: '🇳🇱 Netherlands', code: 'NL' }
    ];

    return popularCountries.filter(country => 
      country.value.toLowerCase().includes(query.toLowerCase())
    );
  }
}

// Get city suggestions from OpenStreetMap Nominatim API
async function getCitySuggestions(query, countryFilter = null) {
  try {
    // Build the search query
    let searchQuery = query;
    if (countryFilter) {
      searchQuery += `, ${countryFilter}`;
    }

    const response = await axios.get(
      `https://nominatim.openstreetmap.org/search`,
      {
        params: {
          q: searchQuery,
          format: 'json',
          addressdetails: 1,
          limit: 10,
          featuretype: 'city',
          'accept-language': 'en',
          countrycodes: countryFilter ? getCountryCode(countryFilter) : undefined
        },
        headers: {
          'User-Agent': 'SymplihireJobPlatform/1.0'
        },
        timeout: 5000
      }
    );

    const places = response.data || [];
    
    return places
      .filter(place => {
        // Only include cities, towns, villages
        const placeType = place.type;
        const acceptableTypes = [
          'city', 'town', 'village', 'municipality', 
          'administrative', 'suburb', 'hamlet'
        ];
        return acceptableTypes.includes(placeType) || 
               place.class === 'place';
      })
      .map(place => {
        const address = place.address || {};
        
        // Build city, state format
        let cityLabel = place.display_name.split(',')[0]; // Main city name
        
        // Add state/province if available
        const state = address.state || address.province || address.region;
        if (state) {
          cityLabel += `, ${state}`;
        }
        
        // Add country for context
        const country = address.country;
        if (country) {
          cityLabel += `, ${country}`;
        }

        return {
          value: `${place.display_name.split(',')[0]}${state ? `, ${state}` : ''}`,
          label: cityLabel,
          city: place.display_name.split(',')[0],
          state: state || '',
          country: country || '',
          lat: parseFloat(place.lat),
          lon: parseFloat(place.lon)
        };
      })
      .filter((place, index, array) => {
        // Remove duplicates based on city + state
        return array.findIndex(p => p.value === place.value) === index;
      })
      .slice(0, 8); // Limit to 8 suggestions

  } catch (error) {
    console.error('City API error:', error);
    
    // Fallback to popular cities if API fails
    const popularCities = [
      { value: 'New York, NY', label: 'New York, NY, United States', city: 'New York', state: 'NY', country: 'United States' },
      { value: 'San Francisco, CA', label: 'San Francisco, CA, United States', city: 'San Francisco', state: 'CA', country: 'United States' },
      { value: 'Los Angeles, CA', label: 'Los Angeles, CA, United States', city: 'Los Angeles', state: 'CA', country: 'United States' },
      { value: 'Chicago, IL', label: 'Chicago, IL, United States', city: 'Chicago', state: 'IL', country: 'United States' },
      { value: 'London', label: 'London, England, United Kingdom', city: 'London', state: 'England', country: 'United Kingdom' },
      { value: 'Toronto, ON', label: 'Toronto, ON, Canada', city: 'Toronto', state: 'ON', country: 'Canada' },
      { value: 'Sydney, NSW', label: 'Sydney, NSW, Australia', city: 'Sydney', state: 'NSW', country: 'Australia' },
      { value: 'Berlin', label: 'Berlin, Germany', city: 'Berlin', state: '', country: 'Germany' },
      { value: 'Remote', label: 'Remote Work', city: 'Remote', state: '', country: '' }
    ];

    return popularCities.filter(city => 
      city.value.toLowerCase().includes(query.toLowerCase()) ||
      city.city.toLowerCase().includes(query.toLowerCase())
    );
  }
}

// Helper function to get country code from country name
function getCountryCode(countryName) {
  const countryCodes = {
    'United States': 'us',
    'United Kingdom': 'gb', 
    'Canada': 'ca',
    'Australia': 'au',
    'Germany': 'de',
    'France': 'fr',
    'India': 'in',
    'Japan': 'jp',
    'Singapore': 'sg',
    'Netherlands': 'nl',
    'Spain': 'es',
    'Italy': 'it',
    'Brazil': 'br',
    'Mexico': 'mx',
    'China': 'cn',
    'South Korea': 'kr',
    'Taiwan': 'tw',
    'Hong Kong': 'hk',
    'Switzerland': 'ch',
    'Sweden': 'se',
    'Norway': 'no',
    'Denmark': 'dk',
    'Finland': 'fi',
    'Austria': 'at',
    'Belgium': 'be',
    'Ireland': 'ie',
    'New Zealand': 'nz',
    'South Africa': 'za',
    'Israel': 'il',
    'UAE': 'ae',
    'Saudi Arabia': 'sa'
  };

  return countryCodes[countryName] || null;
}
