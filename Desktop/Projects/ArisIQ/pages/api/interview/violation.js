// File: pages/api/interview/violation.js
/**
 * COMPLETELY FIXED - Handles MongoDB violations array properly
 */

import { MongoClient, ObjectId } from 'mongodb';

export default async function handler(req, res) {
  console.log('=== Interview Violation API ===');
  
  // Only allow POST method
  if (req.method !== 'POST') {
    console.log('Method not allowed:', req.method);
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }
  
  const { applicationId, type, message, timestamp, category } = req.body;
  
  console.log('Violation details:', {
    applicationId,
    type,
    message,
    timestamp,
    category,
    fullBody: req.body
  });
  
  // Validate required fields
  if (!applicationId) {
    console.log('Missing applicationId');
    return res.status(400).json({ success: false, message: 'Application ID is required' });
  }
  
  if (!type || !message) {
    console.log('Missing type or message:', { type, message });
    return res.status(400).json({ success: false, message: 'Violation type and message are required' });
  }
  
  // Get database connection string from environment variables
  const uri = process.env.MONGODB_URI;
  
  if (!uri) {
    console.error('MongoDB URI not found in environment variables');
    return res.status(500).json({ success: false, message: 'Database configuration error' });
  }
  
  let client;
  
  try {
    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    client = await MongoClient.connect(uri);
    const db = client.db();
    
    // Create ObjectId from application ID
    let appId;
    try {
      appId = new ObjectId(applicationId);
    } catch (error) {
      console.error('Invalid ObjectId format:', applicationId, error);
      return res.status(400).json({ success: false, message: 'Invalid application ID format' });
    }
    
    // Check if application exists
    console.log('Checking if application exists...');
    const application = await db.collection('applications').findOne({ _id: appId });
    
    if (!application) {
      console.error('Application not found:', applicationId);
      return res.status(404).json({ success: false, message: 'Application not found' });
    }
    
    console.log('Application found, recording violation...');
    
    // Create violation record
    const violationData = {
      applicationId: appId,
      type,
      message,
      timestamp: timestamp || new Date().toISOString(),
      recordedAt: new Date(),
      category: category || 'warning', // Default to warning if not specified
      metadata: {
        userAgent: req.headers['user-agent'],
        ip: req.headers['x-forwarded-for'] || req.socket.remoteAddress
      }
    };
    
    console.log('Saving violation data:', violationData);
    
    // Insert violation into separate collection for detailed tracking
    const insertResult = await db.collection('interview_violations').insertOne(violationData);
    console.log('Violation saved with ID:', insertResult.insertedId);
    
    // Get current violation count for this type
    const typeCount = await db.collection('interview_violations').countDocuments({
      applicationId: appId,
      type: type
    });
    
    console.log(`Total ${type} violations:`, typeCount);
    
    // FIXED: Handle violations array properly
    const currentViolations = application.violations || [];
    
    // Find existing violation of this type
    const existingViolationIndex = currentViolations.findIndex(v => v.type === type);
    
    let updatedViolations;
    if (existingViolationIndex >= 0) {
      // Update existing violation
      updatedViolations = [...currentViolations];
      updatedViolations[existingViolationIndex] = {
        type,
        count: typeCount,
        message: `${type}: ${typeCount} violation(s)`,
        timestamp: new Date(timestamp).toISOString()
      };
    } else {
      // Add new violation
      updatedViolations = [...currentViolations, {
        type,
        count: typeCount,
        message: `${type}: ${typeCount} violation(s)`,
        timestamp: new Date(timestamp).toISOString()
      }];
    }
    
    // Calculate total violations
    const totalViolations = updatedViolations.reduce((sum, v) => sum + v.count, 0);
    
    // Update application with corrected violations array
    const updateDoc = {
      $set: {
        violations: updatedViolations,
        lastViolation: {
          type,
          message,
          timestamp: new Date()
        }
      }
    };
    
    // Only add to violation history if it's a warning violation
    if (category === 'warning') {
      updateDoc.$push = { 
        violationHistory: {
          type,
          message,
          timestamp: violationData.timestamp,
          recordedAt: violationData.recordedAt
        }
      };
    }
    
    console.log('Updating application with:', updateDoc);
    
    const updateResult = await db.collection('applications').updateOne(
      { _id: appId },
      updateDoc
    );
    
    console.log('Application update result:', {
      matchedCount: updateResult.matchedCount,
      modifiedCount: updateResult.modifiedCount
    });
    
    console.log('✅ Violation recorded successfully');
    
    // Return success response
    return res.status(200).json({ 
      success: true, 
      message: 'Violation recorded successfully',
      violationId: insertResult.insertedId,
      typeCount: typeCount,
      totalViolations: totalViolations,
      type: type
    });
    
  } catch (error) {
    console.error('❌ Error recording violation:', error);
    return res.status(500).json({ 
      success: false, 
      message: `Server error: ${error.message}`,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  } finally {
    // Close database connection
    if (client) {
      console.log('Closing MongoDB connection...');
      await client.close();
    }
  }
}
