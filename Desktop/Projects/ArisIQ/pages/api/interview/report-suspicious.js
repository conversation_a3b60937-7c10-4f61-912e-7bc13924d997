// pages/api/interview/report-suspicious.js

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { applicationId, questionIndex, event, timestamp } = req.body;
    
    // Log the suspicious event (for development)
    console.log('Suspicious event reported:', {
      applicationId,
      questionIndex,
      event,
      timestamp
    });
    
    // TODO: In production, you would store this in your database
    // Example: await prisma.suspiciousEvent.create({ data: {...} })
    
    return res.status(200).json({ 
      success: true, 
      message: 'Suspicious event recorded' 
    });
  } catch (error) {
    console.error('Error recording suspicious event:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to record suspicious event' 
    });
  }
}
