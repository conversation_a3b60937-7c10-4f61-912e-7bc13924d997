// pages/api/interview/complete.js
import connectToDatabase from '../../../lib/mongodb';
import mongoose from 'mongoose';

// Define a schema for the interview results if not already defined
let InterviewResult;
try {
  InterviewResult = mongoose.model('InterviewResult');
} catch (e) {
  const InterviewResultSchema = new mongoose.Schema({
    applicationId: { 
      type: String, 
      required: true,
      index: true
    },
    completed: {
      type: Boolean,
      default: true
    },
    completedAt: {
      type: Date,
      default: Date.now
    },
    securityScore: {
      type: Number,
      default: 100
    },
    requiresReview: {
      type: Boolean,
      default: false
    },
    reviewReason: String,
    suspiciousEvents: [{
      event: String,
      timestamp: Date
    }],
    securityViolations: [{
      type: String,
      timestamp: Date,
      count: Number
    }],
    disqualified: {
      type: Boolean,
      default: false
    },
    disqualificationReason: String
  });
  
  InterviewResult = mongoose.model('InterviewResult', InterviewResultSchema);
}

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    await connectToDatabase();
    
    const { 
      applicationId, 
      suspiciousEvents = [], 
      violations = [],
      disqualified = false,
      disqualificationReason = null
    } = req.body;
    
    // Calculate security score based on violations
    const securityScore = calculateSecurityScore(suspiciousEvents, violations);
    const requiresReview = (suspiciousEvents?.length > 0 || violations?.length > 0 || disqualified);
    const reviewReason = getReviewReason(suspiciousEvents, violations, disqualified, disqualificationReason);
    
    // Log the data being saved
    console.log('Saving interview results:', {
      applicationId,
      securityScore,
      requiresReview,
      reviewReason,
      suspiciousEvents: suspiciousEvents.length,
      violations: violations.length,
      disqualified
    });
    
    // Save to MongoDB
    const result = await InterviewResult.findOneAndUpdate(
      { applicationId },
      {
        applicationId,
        completedAt: new Date(),
        securityScore,
        requiresReview,
        reviewReason,
        suspiciousEvents,
        securityViolations: violations,
        disqualified,
        disqualificationReason
      },
      { upsert: true, new: true }
    );
    
    // Also update the Application status if needed
    // This assumes you have an Application model
    try {
      const Application = mongoose.model('Application');
      await Application.findByIdAndUpdate(
        applicationId,
        { 
          interviewCompleted: true, 
          interviewCompletedAt: new Date(),
          interviewStatus: disqualified ? 'disqualified' : 'completed'
        }
      );
    } catch (err) {
      console.warn('Could not update Application status:', err.message);
    }
    
    return res.status(200).json({ 
      success: true, 
      message: 'Interview completed successfully',
      securityScore,
      requiresReview
    });
  } catch (error) {
    console.error('Error completing interview:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to complete interview' 
    });
  }
}

/**
 * Calculate a security score based on suspicious events and violations
 * @param {Array} suspiciousEvents - List of suspicious events detected
 * @param {Array} violations - List of security violations
 * @returns {number} - Score from 0-100 (higher is better)
 */
function calculateSecurityScore(suspiciousEvents = [], violations = []) {
  // Start with perfect score
  let score = 100;
  
  // Deduct points for suspicious events (less severe)
  if (suspiciousEvents && suspiciousEvents.length) {
    // Group by event type to avoid over-penalizing repeated events
    const eventTypes = new Set(suspiciousEvents.map(e => e.event || e.type));
    // Deduct 5 points per unique type of suspicious event
    score -= eventTypes.size * 5;
    // Additional small penalty for frequency
    score -= Math.min(10, suspiciousEvents.length * 0.5);
  }
  
  // Deduct more points for violations (more severe)
  if (violations && violations.length) {
    // Deduct 10 points per violation
    score -= violations.length * 10;
  }
  
  // Ensure score stays in 0-100 range
  return Math.max(0, Math.min(100, score));
}

/**
 * Get the reason why an interview requires review
 * @param {Array} suspiciousEvents - List of suspicious events
 * @param {Array} violations - List of security violations
 * @param {Boolean} disqualified - Whether the candidate was disqualified
 * @param {String} disqualificationReason - Reason for disqualification
 * @returns {string} - Reason for review
 */
function getReviewReason(suspiciousEvents = [], violations = [], disqualified = false, disqualificationReason = null) {
  const reasons = [];
  
  // Add disqualification reason if applicable
  if (disqualified && disqualificationReason) {
    reasons.push(`DISQUALIFIED: ${disqualificationReason}`);
  }
  
  if (suspiciousEvents && suspiciousEvents.length) {
    // Count event types
    const eventCounts = {};
    suspiciousEvents.forEach(event => {
      const eventType = event.event || event.type;
      eventCounts[eventType] = (eventCounts[eventType] || 0) + 1;
    });
    
    // Add each type to reasons
    Object.entries(eventCounts).forEach(([type, count]) => {
      reasons.push(`${count}x ${type}`);
    });
  }
  
  if (violations && violations.length) {
    // Count violation types
    const violationCounts = {};
    violations.forEach(violation => {
      const violationType = violation.type;
      violationCounts[violationType] = (violationCounts[violationType] || 0) + 1;
    });
    
    // Add each type to reasons
    Object.entries(violationCounts).forEach(([type, count]) => {
      reasons.push(`${count}x ${type} (violation)`);
    });
  }
  
  return reasons.join(', ');
}
