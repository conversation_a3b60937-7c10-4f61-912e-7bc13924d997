// File: pages/api/interview/launch.js
import { MongoClient, ObjectId } from 'mongodb';

const uri = process.env.MONGODB_URI;

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  const { applicationId } = req.body;
  
  if (!applicationId) {
    return res.status(400).json({ success: false, message: 'applicationId is required' });
  }

  console.log(`Validating interview eligibility for application: ${applicationId}`);

  try {
    // Connect to MongoDB to validate the application
    const client = await MongoClient.connect(uri);
    const db = client.db();
    
    // Create valid ObjectId from application ID
    let appObjectId;
    try {
      appObjectId = new ObjectId(applicationId);
    } catch (idErr) {
      console.error(`Invalid ObjectId format: ${applicationId}`);
      await client.close();
      return res.status(400).json({ 
        success: false, 
        message: 'Invalid application ID format' 
      });
    }

    // Fetch the application
    const app = await db.collection('applications').findOne({ 
      _id: appObjectId 
    });

    if (!app) {
      console.error(`Application not found with ID: ${applicationId}`);
      await client.close();
      return res.status(404).json({ success: false, message: 'Application not found' });
    }

    // Check interview eligibility
    if (!app.canTakeInterview && !(app.scanResult && app.scanResult.canTakeInterview) && app.status !== 'screening') {
      await client.close();
      return res.status(403).json({ 
        success: false, 
        message: 'Interview not available for this application'
      });
    }

    // Update application status for web-based interview
    await db.collection('applications').updateOne(
      { _id: appObjectId },
      { $set: { interviewStatus: 'starting', interviewStartedAt: new Date() }}
    );

    await client.close();

    // Return success for web-based interview
    return res.status(200).json({ 
      success: true, 
      message: 'Interview eligibility verified',
      webBased: true
    });

  } catch (err) {
    console.error('Error validating interview:', err);
    return res.status(500).json({ success: false, message: err.message });
  }
}

export const config = {
  api: {
    bodyParser: true,
  },
};
