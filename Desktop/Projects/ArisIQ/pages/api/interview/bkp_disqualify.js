// File: pages/api/interview/disqualify.js
import { MongoClient, ObjectId } from 'mongodb';

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  const { applicationId, reason, violations } = req.body;

  if (!applicationId) {
    return res.status(400).json({ success: false, message: 'Application ID is required' });
  }

  // MongoDB connection string
  const uri = process.env.MONGODB_URI;
  if (!uri) {
    return res.status(500).json({ success: false, message: 'Database connection string not configured' });
  }

  let client;
  try {
    client = await MongoClient.connect(uri);
    const db = client.db();

    // Convert application ID to MongoDB ObjectId
    let appObjectId;
    try {
      appObjectId = new ObjectId(applicationId);
    } catch (err) {
      return res.status(400).json({ success: false, message: 'Invalid application ID format' });
    }

    // Get current timestamp
    const timestamp = new Date();

    // Update application status to disqualified
    const updateResult = await db.collection('applications').updateOne(
      { _id: appObjectId },
      { 
        $set: { 
          disqualified: true,
          disqualificationReason: reason,
          disqualificationTimestamp: timestamp,
          interviewStatus: 'disqualified'
        },
        // Add violations to violations array
        $push: { 
          violations: { 
            $each: Array.isArray(violations) ? violations : [{ type: 'unknown', message: reason, timestamp }] 
          } 
        }
      }
    );

    if (updateResult.modifiedCount === 0) {
      return res.status(404).json({ success: false, message: 'Application not found or already disqualified' });
    }

    // Create a record in interviewResults collection
    await db.collection('interviewResults').insertOne({
      applicationId: appObjectId,
      result: 'disqualified',
      reason: reason,
      violations: violations || [],
      timestamp: timestamp
    });

    // Return success response
    return res.status(200).json({ 
      success: true, 
      message: 'Candidate disqualified successfully',
      timestamp: timestamp 
    });

  } catch (error) {
    console.error('Error in disqualify API:', error);
    return res.status(500).json({ success: false, message: `Server error: ${error.message}` });
  } finally {
    if (client) {
      client.close();
    }
  }
}
