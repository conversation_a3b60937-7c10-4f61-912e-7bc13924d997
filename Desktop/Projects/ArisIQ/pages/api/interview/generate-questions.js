// File: pages/api/interview/generate-questions.js  
// Pure AI-Driven Interview Question Generation System - Zero Hardcoding

import { MongoClient, ObjectId } from 'mongodb';
import { OpenAI } from 'openai';
import axios from 'axios';

// Initialize API clients
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
const uri = process.env.MONGODB_URI;

// Configuration
const DEFAULT_INTERVIEW_LENGTH = 22;
const RESUME_QUESTIONS_COUNT = 1;
const FOLLOWUP_QUESTIONS_COUNT = 1;

// AI Provider configurations with specializations
const aiProviders = {
  openai: {
    enabled: !!process.env.OPENAI_API_KEY,
    name: 'OpenAI',
    specializes: ['mcq'],
    model: 'gpt-4'
  },
  gemini: {
    enabled: !!process.env.GEMINI_API_KEY,
    name: 'Google Gemini',
    specializes: ['coding', 'technical', 'analysis'],
    endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent'
  },
  claude: {
    enabled: !!process.env.CLAUDE_API_KEY,
    name: 'Anthropic Claude',
    specializes: ['behavioral', 'scenario', 'case_study', 'analysis'],
    endpoint: 'https://api.anthropic.com/v1/messages',
    model: 'claude-3-haiku-20240307'
  },
  mistral: {
    enabled: !!process.env.MISTRAL_API_KEY,
    name: 'Mistral AI',
    specializes: ['mcq'],
    endpoint: 'https://api.mistral.ai/v1/chat/completions',
    model: 'mistral-small'
  }
};

// Enhanced JSON parser for AI responses
function parseAIResponse(content, providerName) {
  console.log(`🔍 ${providerName} response preview:`, content.substring(0, 300) + '...');
  
  let cleanContent = content.replace(/```json\n?|\n?```/g, '').trim();
  
  // Find JSON boundaries (object or array)
  let start = cleanContent.indexOf('{');
  let end = cleanContent.lastIndexOf('}');
  let isArray = false;
  
  const arrayStart = cleanContent.indexOf('[');
  if (arrayStart !== -1 && (start === -1 || arrayStart < start)) {
    start = arrayStart;
    end = cleanContent.lastIndexOf(']');
    isArray = true;
  }
  
  if (start === -1 || end === -1) {
    throw new Error(`No JSON found in ${providerName} response`);
  }
  
  const jsonOnly = cleanContent.substring(start, end + 1);
  
  try {
    const parsed = JSON.parse(jsonOnly);
    console.log(`✅ Parsed JSON ${isArray ? 'array' : 'object'} from ${providerName}`);
    return parsed;
  } catch (error) {
    // Try aggressive cleaning
    const cleaned = jsonOnly
      .replace(/\n/g, ' ')
      .replace(/\s+/g, ' ')
      .replace(/,\s*([}\]])/g, '$1')
      .replace(/([{,]\s*)(\w+):/g, '$1"$2":')
      .replace(/:\s*'([^']*)'/g, ': "$1"');
    
    try {
      const parsed = JSON.parse(cleaned);
      console.log(`✅ Parsed cleaned JSON from ${providerName}`);
      return parsed;
    } catch (finalError) {
      throw new Error(`Failed to parse JSON from ${providerName}: ${error.message}`);
    }
  }
}

// Universal AI caller
async function callAI(provider, prompt) {
  const config = aiProviders[provider];
  if (!config?.enabled) {
    throw new Error(`${provider} not available`);
  }

  console.log(`🤖 Calling ${config.name}...`);

  try {
    if (provider === 'openai') {
      const response = await openai.chat.completions.create({
        model: config.model,
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.7,
        max_tokens: 4000
      });
      return parseAIResponse(response.choices[0].message.content, config.name);
    }
    
    if (provider === 'gemini') {
      const response = await axios.post(
        `${config.endpoint}?key=${process.env.GEMINI_API_KEY}`,
        {
          contents: [{ parts: [{ text: prompt }] }],
          generationConfig: { temperature: 0.7, maxOutputTokens: 8192 }
        },
        { headers: { 'Content-Type': 'application/json' }, timeout: 30000 }
      );
      return parseAIResponse(response.data.candidates[0].content.parts[0].text, config.name);
    }
    
    if (provider === 'claude') {
      const response = await axios.post(
        config.endpoint,
        {
          model: config.model,
          max_tokens: 4000,
          messages: [{ role: 'user', content: prompt }]
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': process.env.CLAUDE_API_KEY,
            'anthropic-version': '2023-06-01'
          },
          timeout: 30000
        }
      );
      return parseAIResponse(response.data.content[0].text, config.name);
    }
    
    if (provider === 'mistral') {
      const response = await axios.post(
        config.endpoint,
        {
          model: config.model,
          messages: [{ role: 'user', content: prompt }]
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.MISTRAL_API_KEY}`
          },
          timeout: 30000
        }
      );
      return parseAIResponse(response.data.choices[0].message.content, config.name);
    }
    
  } catch (error) {
    console.error(`❌ ${config.name} failed:`, error.message);
    if (error.response) {
      console.error(`Status: ${error.response.status}`, error.response.data);
    }
    throw error;
  }
}

// AI-powered job analysis and question strategy
async function analyzeJobWithAI(job) {
  console.log('🧠 Starting AI job analysis...');
  
  const analysisPrompt = `
    You are an expert interview strategist. Analyze this job posting and determine what skills need to be tested.

    Job Posting Analysis:
    - Position: ${job.title} at ${job.company}
    - Industry: ${job.industry || 'Not specified'}
    - Job Description: ${job.description || 'Not specified'}  
    - Required Skills: ${Array.isArray(job.requiredSkills) ? job.requiredSkills.join(', ') : job.requiredSkills || 'Not specified'}
    - Preferred Skills: ${Array.isArray(job.preferredSkills) ? job.preferredSkills.join(', ') : job.preferredSkills || 'Not specified'}
    - Experience Level: ${job.experience || 'Not specified'}

    Your Task:
    1. Analyze what this person will actually DO in this job
    2. Look at the skills they list - do any involve writing code, scripts, queries, or programming?
    3. Determine what types of questions would best assess if someone can succeed in this role
    4. Design an interview strategy with exactly 20 questions total

    MANDATORY: Use exactly these question counts:
    - MCQ: 10 questions (always 10, regardless of role)
    - Main skill questions: 4 questions (coding for technical roles, or role-specific for others)
    - Technical/discussion: 3 questions 
    - Behavioral: 3 questions
    - Total: 20 questions (+ 1 resume + 1 followup = 22 total)

    For technical roles like ETL Developer, Software Developer, Data Engineer:
    - Include 4 coding questions (SQL, Python, scripting) that can be written in text editor
    - NO GUI tools like Informatica PowerCenter mappings, SSIS packages
    
    For non-technical roles like Nurse, Teacher, Sales:
    - Replace coding with role-specific questions (clinical, educational, sales)

    Return ONLY this JSON:
    {
      "roleAnalysis": {
        "category": "your analysis of what this job category is",
        "type": "technical|business|creative|operational|sales|support|healthcare|education", 
        "dailyResponsibilities": ["what they'll actually do day-to-day"],
        "keySkillsToTest": ["specific skills that need assessment"],
        "complexity": "basic|intermediate|advanced"
      },
      "questionStrategy": {
        "mcq": {"count": 10, "rationale": "fundamental knowledge assessment"},
        "coding": {"count": 4, "rationale": "hands-on coding skills"} OR "clinical": {"count": 4, "rationale": "clinical skills"} OR "educational": {"count": 4, "rationale": "teaching skills"},
        "technical": {"count": 3, "rationale": "technical discussion"},
        "behavioral": {"count": 3, "rationale": "soft skills assessment"}
      }
    }

    Total must be exactly 20 questions (10+4+3+3). Choose the main skill type based on the role.
  `;

  const analysisProviders = ['gemini', 'claude'];
  
  for (const provider of analysisProviders) {
    if (!aiProviders[provider]?.enabled) continue;
    
    try {
      console.log(`🧠 Analyzing with ${aiProviders[provider].name}...`);
      const analysis = await callAI(provider, analysisPrompt);
      
      if (analysis.roleAnalysis && analysis.questionStrategy) {
        // Simple validation - just check total count
        const totalQuestions = Object.values(analysis.questionStrategy)
          .reduce((sum, q) => sum + (q.count || 0), 0);
          
        if (totalQuestions >= 18 && totalQuestions <= 22) {
          console.log(`✅ Valid analysis from ${aiProviders[provider].name}`);
          console.log(`📊 Role: ${analysis.roleAnalysis.category} (${analysis.roleAnalysis.type})`);
          console.log(`🔍 Key Skills to Test: ${analysis.roleAnalysis.keySkillsToTest?.join(', ')}`);
          console.log(`📋 Daily Responsibilities: ${analysis.roleAnalysis.dailyResponsibilities?.join(', ')}`);
          
          // Log AI's reasoning for question distribution
          Object.entries(analysis.questionStrategy).forEach(([type, config]) => {
            if (config.count > 0) {
              console.log(`   ${type}: ${config.count} questions - ${config.rationale}`);
            }
          });
          
          return analysis;
        } else {
          console.warn(`⚠️ Question count ${totalQuestions} not valid, trying next provider...`);
        }
      }
    } catch (error) {
      console.error(`❌ Analysis failed with ${aiProviders[provider].name}:`, error.message);
    }
  }
  
  throw new Error('All AI providers failed for job analysis');
}

// Fallback coding questions generator
async function generateFallbackCodingQuestions(count, job) {
  console.log(`🔄 Creating ${count} fallback coding questions...`);
  
  const fallbackQuestions = [];
  for (let i = 0; i < count; i++) {
    fallbackQuestions.push({
      id: `coding_fallback_${i + 1}_${Date.now()}`,
      type: 'coding',
      question: `Write a SQL query to extract data from a table relevant to ${job.title} role. Include appropriate WHERE clauses and ORDER BY statements.`,
      language: 'sql',
      expectedOutput: 'A properly formatted SQL SELECT statement with relevant business logic',
      source: 'fallback_generated',
      provider: 'fallback',
      generatedAt: new Date(),
      difficulty: 'intermediate',
      points: 20,
      timeLimit: 12,
      isFallback: true
    });
  }
  
  return fallbackQuestions;
}

// Generate questions using specialized AI providers
async function generateQuestions(questionType, count, job, analysis) {
  console.log(`🎯 Generating ${count} ${questionType} questions...`);
  
  // Determine which providers to use based on question type
  let providers = [];
  if (questionType === 'mcq') {
    providers = ['openai', 'mistral'];
  } else if (questionType === 'coding') {
    providers = ['gemini', 'claude']; // Add Claude as backup for coding
  } else if (questionType === 'technical') {
    providers = ['gemini'];
  } else {
    providers = ['claude'];
  }
  
  // If no coding/technical needed, Gemini can help with other types
  if (!analysis.roleAnalysis.keySkillsToTest?.some(skill => 
    skill.toLowerCase().includes('sql') || 
    skill.toLowerCase().includes('python') || 
    skill.toLowerCase().includes('script')
  ) && questionType !== 'mcq') {
    providers.push('gemini');
  }
  
  // Create specific prompts based on question type
  let generationPrompt = '';
  
  if (questionType === 'coding') {
    generationPrompt = `
      Generate ${count} ACTUAL TEXT-BASED CODING questions for this ${job.title} role.

      Job Context:
      - Position: ${job.title} at ${job.company}
      - Required Skills: ${Array.isArray(job.requiredSkills) ? job.requiredSkills.join(', ') : job.requiredSkills || 'programming skills'}
      - Daily Tasks: ${analysis.roleAnalysis.dailyResponsibilities?.join(', ') || 'coding tasks'}

      CRITICAL REQUIREMENT - Only generate questions that can be answered by TYPING CODE in a text editor:

      ✅ VALID CODING QUESTIONS (can be typed as text):
      - Code in any programming language mentioned in job requirements
      - Database queries if role involves databases
      - Scripts in languages relevant to this role
      - Configuration files if role requires them
      - Command line instructions for relevant tools
      - Functions and algorithms in appropriate languages

      ❌ INVALID CODING QUESTIONS (require visual/GUI tools):
      - Any task that says "design", "create a workflow", "build a mapping"
      - Any task that requires drag-and-drop interfaces
      - Any task that needs visual designers or GUI tools
      - Any task that requires drawing diagrams or flowcharts
      - Any task that needs point-and-click configuration

      VALIDATION RULE: Ask yourself - "Can this be completed by typing text code in a simple text editor like Notepad?" 
      If NO → Don't generate this question
      If YES → This is a valid coding question

      Based on the job requirements, determine which programming languages are most relevant:
      - Look at required skills to identify languages (Python, Java, JavaScript, C#, etc.)
      - Check if role involves databases (SQL queries)
      - See if role mentions scripting (shell, PowerShell, etc.)
      - Consider configuration languages (JSON, YAML, XML)

      Focus on tasks where the candidate writes actual executable code, scripts, or queries that run in command line or code environments.

      Return ONLY a JSON array of ${count} questions that require writing actual code.
      
      CRITICAL: Ensure JSON is properly formatted with no escaped characters or syntax errors.
      Use simple double quotes, avoid complex characters that break JSON parsing.
      
      Format: [{"question": "Write a SQL query to find...", "language": "sql", "expectedOutput": "description of expected result"}]
    `;
  } else if (questionType === 'mcq') {
    generationPrompt = `
      Generate ${count} MULTIPLE CHOICE questions for this ${job.title} role.

      Job Context:
      - Position: ${job.title} at ${job.company}
      - Skills to Test: ${analysis.roleAnalysis.keySkillsToTest?.join(', ') || 'technical skills'}

      Each question MUST have:
      - Clear question text
      - Exactly 4 options in format: ["A) option1", "B) option2", "C) option3", "D) option4"]
      - One correct answer (A, B, C, or D)
      - Brief explanation

      Return ONLY a JSON array of ${count} MCQ questions.
      Format: [{"question": "What is...", "options": ["A) option1", "B) option2", "C) option3", "D) option4"], "correctAnswer": "B", "explanation": "why B is correct"}]
    `;
  } else if (questionType === 'technical') {
    generationPrompt = `
      Generate ${count} TECHNICAL DISCUSSION questions for this ${job.title} role.

      Job Context:
      - Position: ${job.title} at ${job.company}
      - Technical Areas: ${analysis.roleAnalysis.keySkillsToTest?.join(', ') || 'technical skills'}
      - Daily Responsibilities: ${analysis.roleAnalysis.dailyResponsibilities?.join(', ') || 'technical tasks'}

      These should be DISCUSSION questions about technical concepts, NOT coding:
      - System design and architecture questions
      - Troubleshooting and problem-solving scenarios
      - Best practices and methodology questions
      - Technology comparison and evaluation questions

      Focus on testing deep technical understanding through explanation and discussion.

      Return ONLY a JSON array of ${count} technical discussion questions.
      Format: [{"question": "Explain your approach to...", "skill": "relevant technical area"}]
    `;
  } else {
    generationPrompt = `
      Generate ${count} ${questionType} interview questions for this specific role:

      Position: ${job.title} at ${job.company}
      Role Analysis: ${JSON.stringify(analysis.roleAnalysis)}
      Question Focus: ${analysis.questionStrategy[questionType]?.rationale || 'general assessment'}
      Required Skills: ${Array.isArray(job.requiredSkills) ? job.requiredSkills.join(', ') : job.requiredSkills}
      Industry: ${job.industry || 'general'}

      Requirements:
      - Create ${count} questions specifically relevant to this ${job.title} role
      - Focus on skills actually needed for this position
      - Make questions practical and job-relevant
      - Consider the industry context and role requirements

      Return ONLY a JSON array of ${count} questions with appropriate structure for ${questionType} questions.
      Each question should be professionally crafted and role-specific.
    `;
  }

  for (const provider of providers) {
    if (!aiProviders[provider]?.enabled) continue;
    
    try {
      console.log(`🤖 Generating ${questionType} with ${aiProviders[provider].name}...`);
      const response = await callAI(provider, generationPrompt);
      
      let questions = Array.isArray(response) ? response : [response];
      if (!Array.isArray(response) && Array.isArray(response[0])) {
        questions = response[0];
      }
      
      const validQuestions = questions
        .filter(q => {
          // Check for question content in various possible field names
          const hasQuestion = q && (
            (q.question && typeof q.question === 'string') ||
            (q.title && typeof q.title === 'string') ||
            (q.description && typeof q.description === 'string')
          );
          
          if (!hasQuestion) {
            console.warn(`⚠️ Filtering out question without valid content:`, q);
          }

          // Smart validation for coding questions - check if it's actually codeable
          if (questionType === 'coding' && hasQuestion) {
            const questionText = q.question || q.title || q.description;
            const lowerQuestion = questionText.toLowerCase();
            
            // Check for design/visual keywords that indicate non-coding tasks
            const designIndicators = ['design', 'create a', 'build a', 'configure', 'setup', 'draw', 'diagram'];
            const hasDesignKeywords = designIndicators.some(indicator => 
              lowerQuestion.includes(indicator)
            );
            
            // Check for coding keywords that indicate actual coding
            const codingIndicators = ['write', 'query', 'function', 'script', 'code', 'select', 'insert', 'update'];
            const hasCodingKeywords = codingIndicators.some(indicator => 
              lowerQuestion.includes(indicator)
            );
            
            // Reject if it has design words but no coding words
            if (hasDesignKeywords && !hasCodingKeywords) {
              console.warn(`⚠️ Filtering out design-based question: ${questionText.substring(0, 100)}...`);
              return false;
            }
            
            // Also check if question mentions relevant programming languages from job
            const jobSkills = (Array.isArray(job.requiredSkills) ? job.requiredSkills.join(' ') : job.requiredSkills || '').toLowerCase();
            const jobDescription = (job.description || '').toLowerCase();
            const allJobText = `${jobSkills} ${jobDescription}`;
            
            // Extract programming languages mentioned in job
            const allLanguages = ['sql', 'python', 'javascript', 'java', 'c#', 'c++', 'php', 'ruby', 'go', 'rust', 'scala', 'r', 'matlab', 'shell', 'bash', 'powershell', 'json', 'xml', 'yaml'];
            const jobLanguages = allLanguages.filter(lang => allJobText.includes(lang));
            
            // Check if question mentions languages relevant to the job
            const hasRelevantLanguage = jobLanguages.length === 0 || // If no languages detected in job, allow any
              jobLanguages.some(lang => lowerQuestion.includes(lang)) ||
              allLanguages.some(lang => lowerQuestion.includes(lang)); // Or any programming language
            
            // If it's supposed to be coding but doesn't mention coding, languages, or relevant actions, it's likely invalid
            if (!hasCodingKeywords && !hasRelevantLanguage) {
              console.warn(`⚠️ Filtering out non-coding question: ${questionText.substring(0, 100)}...`);
              return false;
            }
          }

          // Special validation for MCQ questions
          if (questionType === 'mcq') {
            if (!q.options || !Array.isArray(q.options) || q.options.length !== 4) {
              console.warn(`⚠️ Filtering out MCQ without 4 options:`, q.question?.substring(0, 50));
              return false;
            }
            if (!q.correctAnswer) {
              console.warn(`⚠️ Filtering out MCQ without correct answer:`, q.question?.substring(0, 50));
              return false;
            }
          }
          
          return hasQuestion && 
                 (q.question?.length > 10 || q.title?.length > 10 || q.description?.length > 10);
        })
        .map(q => {
          // Normalize question content - use the best available field
          let questionText = q.question;
          if (!questionText && q.title) {
            questionText = q.description ? `${q.title}: ${q.description}` : q.title;
          } else if (!questionText && q.description) {
            questionText = q.description;
          }
          
          return {
            ...q,
            question: questionText, // Normalize to 'question' field
            id: q.id || q.questionId || `${questionType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type: questionType,
            source: 'ai_generated',
            provider: provider,
            generatedAt: new Date(),
            difficulty: q.difficulty || analysis.roleAnalysis.complexity || 'intermediate',
            points: q.points || (questionType === 'coding' ? 20 : questionType === 'technical' ? 15 : 12),
            timeLimit: q.timeLimit || (questionType === 'coding' ? 12 : questionType === 'technical' ? 6 : 5),
            // Preserve any additional AI-generated fields
            correctAnswer: q.correctAnswer || q.answer,
            options: q.options,
            language: q.language,
            starterCode: q.starterCode,
            testCases: q.testCases,
            expectedSolution: q.expectedSolution
          };
        })
        .slice(0, count);

      if (validQuestions.length > 0) {
        console.log(`✅ Generated ${validQuestions.length} ${questionType} questions with ${aiProviders[provider].name}`);
        
        // Log sample for verification
        if (questionType === 'coding') {
          console.log(`🔍 Sample coding question: ${validQuestions[0]?.question?.substring(0, 100)}...`);
        }
        
        return validQuestions;
      } else {
        console.warn(`⚠️ Generated 0 valid ${questionType} questions, trying next provider...`);
      }
      
    } catch (error) {
      console.error(`❌ ${aiProviders[provider].name} failed for ${questionType}:`, error.message);
    }
  }
  
  throw new Error(`All providers failed for ${questionType} questions`);
}

// Create resume-based question placeholders
function createResumeQuestions(app, count) {
  console.log(`📄 Creating ${count} resume-based question slots...`);
  
  const resumeQuestions = [];
  for (let i = 0; i < count; i++) {
    resumeQuestions.push({
      id: `resume_${i + 1}_${Date.now()}`,
      type: 'resume_based',
      question: `[RESUME QUESTION ${i + 1} - GENERATED DURING INTERVIEW BASED ON CANDIDATE'S EXPERIENCE]`,
      isResumeQuestion: true,
      generatedDuringInterview: true,
      resumeContext: {
        applicantId: app._id,
        basedOn: 'resume_analysis_and_responses'
      },
      points: 15,
      timeLimit: 6,
      placeholder: true
    });
  }
  
  return resumeQuestions;
}

// Create follow-up question placeholders
function createFollowupQuestions(count) {
  console.log(`🔗 Creating ${count} follow-up question slots...`);
  
  const followupQuestions = [];
  for (let i = 0; i < count; i++) {
    followupQuestions.push({
      id: `followup_${i + 1}_${Date.now()}`,
      type: 'followup',
      question: `[FOLLOW-UP QUESTION ${i + 1} - GENERATED DURING INTERVIEW BASED ON PREVIOUS ANSWERS]`,
      isFollowupQuestion: true,
      generatedDuringInterview: true,
      followupContext: {
        basedOn: 'previous_question_responses',
        questionNumber: null // Will be set during interview
      },
      points: 10,
      timeLimit: 5,
      placeholder: true
    });
  }
  
  return followupQuestions;
}

// Strategic question shuffling with followup questions
function shuffleQuestions(questions) {
  const regular = questions.filter(q => !q.isResumeQuestion && !q.isFollowupQuestion);
  const resume = questions.filter(q => q.isResumeQuestion);
  const followup = questions.filter(q => q.isFollowupQuestion);
  
  const shuffled = regular.sort(() => 0.5 - Math.random());
  const result = [];
  
  // Strategic positions for dynamic questions
  const resumePosition = Math.floor(shuffled.length * 0.4); // 40% through
  const followupPosition = Math.floor(shuffled.length * 0.8); // 80% through
  
  for (let i = 0; i < shuffled.length; i++) {
    result.push(shuffled[i]);
    
    // Insert resume question at strategic position
    if (i === resumePosition && resume.length > 0) {
      result.push(resume[0]);
    }
    
    // Insert follow-up question at strategic position  
    if (i === followupPosition && followup.length > 0) {
      result.push(followup[0]);
    }
  }
  
  return result;
}

// Calculate interview time
function calculateTime(questions) {
  const timeMap = {
    mcq: 2,
    coding: 12,
    technical: 6,
    behavioral: 8,
    scenario: 5,
    resume_based: 6,
    followup: 4
  };
  
  const totalTime = questions.reduce((sum, q) => {
    return sum + (timeMap[q.type] || 5);
  }, 0);
  
  return totalTime + 10; // Buffer time
}

// MongoDB connection with retry
async function connectMongoDB(uri, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`📡 MongoDB connection attempt ${attempt}/${maxRetries}...`);
      const client = await MongoClient.connect(uri, {
        serverSelectionTimeoutMS: 8000,
        connectTimeoutMS: 8000,
        socketTimeoutMS: 8000,
        maxPoolSize: 10,
        retryWrites: true,
        w: 'majority'
      });
      
      await client.db().admin().ping();
      console.log(`✅ MongoDB connected on attempt ${attempt}`);
      return client;
    } catch (error) {
      console.warn(`⚠️ Connection attempt ${attempt} failed:`, error.message);
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, attempt * 2000));
      }
    }
  }
  throw new Error('MongoDB connection failed after all attempts');
}

// Main handler
export default async function handler(req, res) {
  console.log('🚀 Starting pure AI-driven interview generation...');
  
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  const startTime = Date.now();
  const { applicationId, interviewLength = DEFAULT_INTERVIEW_LENGTH } = req.body;
  
  if (!applicationId) {
    return res.status(400).json({ success: false, message: 'applicationId required' });
  }

  let client;
  try {
    // Connect to database
    client = await connectMongoDB(uri);
    const db = client.db();

    // Get application and job data
    console.log('📋 Fetching application and job data...');
    const app = await db.collection('applications').findOne({ 
      _id: new ObjectId(applicationId) 
    });
    
    if (!app) throw new Error('Application not found');
    
    const job = await db.collection('jobs').findOne({ 
      _id: new ObjectId(app.jobId) 
    });

    if (!job) throw new Error('Job not found');
    
    console.log(`✅ Found: ${job.title} at ${job.company}`);

    // AI job analysis
    const analysis = await analyzeJobWithAI(job);
    
    // Generate questions using specialized AI providers
    const allQuestions = [];
    
    for (const [questionType, config] of Object.entries(analysis.questionStrategy)) {
      if (config.count > 0) {
        try {
          const questions = await generateQuestions(questionType, config.count, job, analysis);
          allQuestions.push(...questions);
          console.log(`✅ Added ${questions.length} ${questionType} questions`);
        } catch (error) {
          console.error(`❌ Failed to generate ${questionType} questions:`, error.message);
          
          // For critical question types, try to generate simpler fallback questions
          if (questionType === 'coding') {
            console.log(`🔄 Attempting fallback coding questions...`);
            try {
              const fallbackQuestions = await generateFallbackCodingQuestions(config.count, job);
              allQuestions.push(...fallbackQuestions);
              console.log(`✅ Added ${fallbackQuestions.length} fallback coding questions`);
            } catch (fallbackError) {
              console.error(`❌ Fallback also failed:`, fallbackError.message);
            }
          }
        }
      }
    }

    // Add resume-based questions
    const resumeQuestions = createResumeQuestions(app, RESUME_QUESTIONS_COUNT);
    allQuestions.push(...resumeQuestions);

    // Add follow-up questions
    const followupQuestions = createFollowupQuestions(FOLLOWUP_QUESTIONS_COUNT);
    allQuestions.push(...followupQuestions);

    // Shuffle questions strategically
    const finalQuestions = shuffleQuestions(allQuestions);
    const estimatedTime = calculateTime(finalQuestions);

    // Save to database
    console.log('💾 Saving questions...');
    await db.collection('applications').updateOne(
      { _id: new ObjectId(applicationId) },
      {
        $set: {
          questions: finalQuestions,
          questionsGeneratedAt: new Date(),
          canTakeInterview: true,
          interviewTime: estimatedTime,
          jobAnalysis: analysis,
          generationMethod: 'pure-ai-driven',
          resumeQuestionsEnabled: true
        }
      }
    );

    await client.close();
    
    const generationTime = Date.now() - startTime;
    console.log(`✅ Generated ${finalQuestions.length} questions in ${generationTime}ms`);

    // Log breakdown
    const breakdown = {};
    finalQuestions.forEach(q => {
      breakdown[q.type] = (breakdown[q.type] || 0) + 1;
    });
    console.log('📈 Question breakdown:', breakdown);

    return res.status(200).json({
      success: true,
      questions: finalQuestions,
      interviewTime: estimatedTime,
      jobAnalysis: analysis,
      questionBreakdown: breakdown,
      generationTime,
      method: 'pure-ai-driven',
      resumeQuestionsEnabled: true
    });

  } catch (error) {
    console.error('❌ Generation failed:', error.message);
    
    if (client) {
      try {
        await client.close();
      } catch (closeError) {
        console.error('Error closing connection:', closeError.message);
      }
    }
    
    return res.status(500).json({ 
      success: false, 
      message: error.message.includes('not found') ? error.message : 'Question generation failed',
      retryable: !error.message.includes('not found')
    });
  }
}

export const config = {
  api: {
    bodyParser: true,
  },
};
