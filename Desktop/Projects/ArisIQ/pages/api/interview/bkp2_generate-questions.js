// File: pages/api/interview/generate-questions.js  
// Pure AI-Driven Interview Question Generation System - Zero Hardcoding

import { MongoClient, ObjectId } from 'mongodb';
import { OpenAI } from 'openai';
import axios from 'axios';

// Initialize API clients
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
const uri = process.env.MONGODB_URI;

// Configuration
const DEFAULT_INTERVIEW_LENGTH = 20;
const RESUME_QUESTIONS_COUNT = 2;

// AI Provider configurations with specializations
const aiProviders = {
  openai: {
    enabled: !!process.env.OPENAI_API_KEY,
    name: 'OpenAI',
    specializes: ['mcq'],
    model: 'gpt-4'
  },
  gemini: {
    enabled: !!process.env.GEMINI_API_KEY,
    name: 'Google Gemini',
    specializes: ['coding', 'technical', 'analysis'],
    endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent'
  },
  claude: {
    enabled: !!process.env.CLAUDE_API_KEY,
    name: 'Anthrop<PERSON> <PERSON>',
    specializes: ['behavioral', 'scenario', 'case_study', 'analysis'],
    endpoint: 'https://api.anthropic.com/v1/messages',
    model: 'claude-3-haiku-20240307'
  },
  mistral: {
    enabled: !!process.env.MISTRAL_API_KEY,
    name: 'Mistral AI',
    specializes: ['mcq'],
    endpoint: 'https://api.mistral.ai/v1/chat/completions',
    model: 'mistral-small'
  }
};

// Enhanced JSON parser for AI responses
function parseAIResponse(content, providerName) {
  console.log(`🔍 ${providerName} response preview:`, content.substring(0, 300) + '...');
  
  let cleanContent = content.replace(/```json\n?|\n?```/g, '').trim();
  
  // Find JSON boundaries (object or array)
  let start = cleanContent.indexOf('{');
  let end = cleanContent.lastIndexOf('}');
  let isArray = false;
  
  const arrayStart = cleanContent.indexOf('[');
  if (arrayStart !== -1 && (start === -1 || arrayStart < start)) {
    start = arrayStart;
    end = cleanContent.lastIndexOf(']');
    isArray = true;
  }
  
  if (start === -1 || end === -1) {
    throw new Error(`No JSON found in ${providerName} response`);
  }
  
  const jsonOnly = cleanContent.substring(start, end + 1);
  
  try {
    const parsed = JSON.parse(jsonOnly);
    console.log(`✅ Parsed JSON ${isArray ? 'array' : 'object'} from ${providerName}`);
    return parsed;
  } catch (error) {
    // Try aggressive cleaning
    const cleaned = jsonOnly
      .replace(/\n/g, ' ')
      .replace(/\s+/g, ' ')
      .replace(/,\s*([}\]])/g, '$1')
      .replace(/([{,]\s*)(\w+):/g, '$1"$2":')
      .replace(/:\s*'([^']*)'/g, ': "$1"');
    
    try {
      const parsed = JSON.parse(cleaned);
      console.log(`✅ Parsed cleaned JSON from ${providerName}`);
      return parsed;
    } catch (finalError) {
      throw new Error(`Failed to parse JSON from ${providerName}: ${error.message}`);
    }
  }
}

// Universal AI caller
async function callAI(provider, prompt) {
  const config = aiProviders[provider];
  if (!config?.enabled) {
    throw new Error(`${provider} not available`);
  }

  console.log(`🤖 Calling ${config.name}...`);

  try {
    if (provider === 'openai') {
      const response = await openai.chat.completions.create({
        model: config.model,
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.7,
        max_tokens: 4000
      });
      return parseAIResponse(response.choices[0].message.content, config.name);
    }
    
    if (provider === 'gemini') {
      const response = await axios.post(
        `${config.endpoint}?key=${process.env.GEMINI_API_KEY}`,
        {
          contents: [{ parts: [{ text: prompt }] }],
          generationConfig: { temperature: 0.7, maxOutputTokens: 8192 }
        },
        { headers: { 'Content-Type': 'application/json' }, timeout: 30000 }
      );
      return parseAIResponse(response.data.candidates[0].content.parts[0].text, config.name);
    }
    
    if (provider === 'claude') {
      const response = await axios.post(
        config.endpoint,
        {
          model: config.model,
          max_tokens: 4000,
          messages: [{ role: 'user', content: prompt }]
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': process.env.CLAUDE_API_KEY,
            'anthropic-version': '2023-06-01'
          },
          timeout: 30000
        }
      );
      return parseAIResponse(response.data.content[0].text, config.name);
    }
    
    if (provider === 'mistral') {
      const response = await axios.post(
        config.endpoint,
        {
          model: config.model,
          messages: [{ role: 'user', content: prompt }]
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.MISTRAL_API_KEY}`
          },
          timeout: 30000
        }
      );
      return parseAIResponse(response.data.choices[0].message.content, config.name);
    }
    
  } catch (error) {
    console.error(`❌ ${config.name} failed:`, error.message);
    if (error.response) {
      console.error(`Status: ${error.response.status}`, error.response.data);
    }
    throw error;
  }
}

// AI-powered job analysis and question strategy
async function analyzeJobWithAI(job) {
  console.log('🧠 Starting AI job analysis...');
  
  const analysisPrompt = `
    Analyze this job posting and determine the optimal interview strategy:

    Job: ${job.title} at ${job.company}
    Industry: ${job.industry || 'Not specified'}
    Description: ${job.description || 'Not specified'}  
    Required Skills: ${Array.isArray(job.requiredSkills) ? job.requiredSkills.join(', ') : job.requiredSkills || 'Not specified'}
    Experience: ${job.experience || 'Not specified'}

    Analyze and determine:
    1. What type of role this is (technical, business, creative, etc.)
    2. Whether coding/programming is required
    3. What specific skills need assessment
    4. Optimal question distribution for 20 questions total

    Return ONLY this JSON structure:
    {
      "roleAnalysis": {
        "category": "specific job category",
        "type": "technical|business|creative|operational", 
        "programmingRequired": true/false,
        "primarySkills": ["skill1", "skill2", "skill3"],
        "complexity": "basic|intermediate|advanced"
      },
      "questionStrategy": {
        "mcq": {"count": 0-15, "focus": "what to assess"},
        "coding": {"count": 0-8, "focus": "what to assess", "languages": ["lang1", "lang2"]},
        "technical": {"count": 0-8, "focus": "what to assess"},
        "behavioral": {"count": 1-8, "focus": "what to assess"},
        "scenario": {"count": 0-5, "focus": "what to assess"}
      }
    }

    Requirements:
    - Total question count must equal 20
    - Include coding questions only if programming is actually required
    - Focus on skills that matter for this specific role
    - Consider experience level when determining complexity
  `;

  const analysisProviders = ['gemini', 'claude'];
  
  for (const provider of analysisProviders) {
    if (!aiProviders[provider]?.enabled) continue;
    
    try {
      console.log(`🧠 Analyzing with ${aiProviders[provider].name}...`);
      const analysis = await callAI(provider, analysisPrompt);
      
      if (analysis.roleAnalysis && analysis.questionStrategy) {
        // Validate question counts
        const totalQuestions = Object.values(analysis.questionStrategy)
          .reduce((sum, q) => sum + (q.count || 0), 0);
          
        if (totalQuestions >= 18 && totalQuestions <= 22) {
          console.log(`✅ Valid analysis from ${aiProviders[provider].name}`);
          console.log(`📊 Role: ${analysis.roleAnalysis.category} (${analysis.roleAnalysis.type})`);
          console.log(`🎯 Programming Required: ${analysis.roleAnalysis.programmingRequired}`);
          return analysis;
        }
      }
    } catch (error) {
      console.error(`❌ Analysis failed with ${aiProviders[provider].name}:`, error.message);
    }
  }
  
  throw new Error('All AI providers failed for job analysis');
}

// Generate questions using specialized AI providers
async function generateQuestions(questionType, count, job, analysis) {
  console.log(`🎯 Generating ${count} ${questionType} questions...`);
  
  // Determine which providers to use based on question type
  let providers = [];
  if (questionType === 'mcq') {
    providers = ['openai', 'mistral'];
  } else if (questionType === 'coding' || questionType === 'technical') {
    providers = ['gemini'];
  } else {
    providers = ['claude'];
  }
  
  // If no coding/technical needed, Gemini can help with other types
  if (!analysis.roleAnalysis.programmingRequired && questionType !== 'mcq') {
    providers.push('gemini');
  }
  
  const generationPrompt = `
    Generate ${count} ${questionType} interview questions for this specific role:

    Position: ${job.title} at ${job.company}
    Role Analysis: ${JSON.stringify(analysis.roleAnalysis)}
    Question Focus: ${analysis.questionStrategy[questionType]?.focus || 'general assessment'}
    Required Skills: ${Array.isArray(job.requiredSkills) ? job.requiredSkills.join(', ') : job.requiredSkills}
    Industry: ${job.industry || 'general'}

    Requirements:
    - Create ${count} questions specifically relevant to this ${job.title} role
    - Focus on skills actually needed for this position
    - Make questions practical and job-relevant
    - Consider the industry context and role requirements

    Return ONLY a JSON array of ${count} questions with appropriate structure for ${questionType} questions.
    Each question should be professionally crafted and role-specific.
  `;

  for (const provider of providers) {
    if (!aiProviders[provider]?.enabled) continue;
    
    try {
      console.log(`🤖 Generating ${questionType} with ${aiProviders[provider].name}...`);
      const response = await callAI(provider, generationPrompt);
      
      let questions = Array.isArray(response) ? response : [response];
      if (!Array.isArray(response) && Array.isArray(response[0])) {
        questions = response[0];
      }
      
      const validQuestions = questions
        .filter(q => {
          // Check for question content in various possible field names
          const hasQuestion = q && (
            (q.question && typeof q.question === 'string') ||
            (q.title && typeof q.title === 'string') ||
            (q.description && typeof q.description === 'string')
          );
          
          if (!hasQuestion) {
            console.warn(`⚠️ Filtering out question without valid content:`, q);
          }
          
          return hasQuestion && 
                 (q.question?.length > 10 || q.title?.length > 10 || q.description?.length > 10);
        })
        .map(q => {
          // Normalize question content - use the best available field
          let questionText = q.question;
          if (!questionText && q.title) {
            questionText = q.description ? `${q.title}: ${q.description}` : q.title;
          } else if (!questionText && q.description) {
            questionText = q.description;
          }
          
          return {
            ...q,
            question: questionText, // Normalize to 'question' field
            id: q.id || q.questionId || `${questionType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type: questionType,
            source: 'ai_generated',
            provider: provider,
            generatedAt: new Date(),
            difficulty: q.difficulty || analysis.roleAnalysis.complexity || 'intermediate',
            points: q.points || (questionType === 'coding' ? 20 : questionType === 'technical' ? 15 : 12),
            timeLimit: q.timeLimit || (questionType === 'coding' ? 12 : questionType === 'technical' ? 6 : 5),
            // Preserve any additional AI-generated fields
            correctAnswer: q.correctAnswer || q.answer,
            options: q.options,
            language: q.language,
            starterCode: q.starterCode,
            testCases: q.testCases,
            expectedSolution: q.expectedSolution
          };
        })
        .slice(0, count);

      if (validQuestions.length > 0) {
        console.log(`✅ Generated ${validQuestions.length} ${questionType} questions with ${aiProviders[provider].name}`);
        return validQuestions;
      }
      
    } catch (error) {
      console.error(`❌ ${aiProviders[provider].name} failed for ${questionType}:`, error.message);
    }
  }
  
  throw new Error(`All providers failed for ${questionType} questions`);
}

// Create resume-based question placeholders
function createResumeQuestions(app, count) {
  console.log(`📄 Creating ${count} resume-based question slots...`);
  
  const resumeQuestions = [];
  for (let i = 0; i < count; i++) {
    resumeQuestions.push({
      id: `resume_${i + 1}_${Date.now()}`,
      type: 'resume_based',
      question: `[RESUME QUESTION ${i + 1} - GENERATED DURING INTERVIEW]`,
      isResumeQuestion: true,
      generatedDuringInterview: true,
      resumeContext: {
        applicantId: app._id,
        basedOn: i === 0 ? 'resume_analysis' : 'previous_responses'
      },
      points: 15,
      timeLimit: 6,
      placeholder: true
    });
  }
  
  return resumeQuestions;
}

// Strategic question shuffling
function shuffleQuestions(questions) {
  const regular = questions.filter(q => !q.isResumeQuestion);
  const resume = questions.filter(q => q.isResumeQuestion);
  
  const shuffled = regular.sort(() => 0.5 - Math.random());
  const result = [];
  
  // Place resume questions at strategic positions
  const positions = [
    Math.floor(shuffled.length * 0.3),
    Math.floor(shuffled.length * 0.7)
  ];
  
  let resumeIndex = 0;
  for (let i = 0; i < shuffled.length; i++) {
    result.push(shuffled[i]);
    if (positions.includes(i) && resumeIndex < resume.length) {
      result.push(resume[resumeIndex++]);
    }
  }
  
  while (resumeIndex < resume.length) {
    result.push(resume[resumeIndex++]);
  }
  
  return result;
}

// Calculate interview time
function calculateTime(questions) {
  const timeMap = {
    mcq: 2,
    coding: 12,
    technical: 6,
    behavioral: 8,
    scenario: 5,
    resume_based: 4
  };
  
  const totalTime = questions.reduce((sum, q) => {
    return sum + (timeMap[q.type] || 5);
  }, 0);
  
  return totalTime + 10; // Buffer time
}

// MongoDB connection with retry
async function connectMongoDB(uri, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`📡 MongoDB connection attempt ${attempt}/${maxRetries}...`);
      const client = await MongoClient.connect(uri, {
        serverSelectionTimeoutMS: 8000,
        connectTimeoutMS: 8000,
        socketTimeoutMS: 8000,
        maxPoolSize: 10,
        retryWrites: true,
        w: 'majority'
      });
      
      await client.db().admin().ping();
      console.log(`✅ MongoDB connected on attempt ${attempt}`);
      return client;
    } catch (error) {
      console.warn(`⚠️ Connection attempt ${attempt} failed:`, error.message);
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, attempt * 2000));
      }
    }
  }
  throw new Error('MongoDB connection failed after all attempts');
}

// Main handler
export default async function handler(req, res) {
  console.log('🚀 Starting pure AI-driven interview generation...');
  
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  const startTime = Date.now();
  const { applicationId, interviewLength = DEFAULT_INTERVIEW_LENGTH } = req.body;
  
  if (!applicationId) {
    return res.status(400).json({ success: false, message: 'applicationId required' });
  }

  let client;
  try {
    // Connect to database
    client = await connectMongoDB(uri);
    const db = client.db();

    // Get application and job data
    console.log('📋 Fetching application and job data...');
    const app = await db.collection('applications').findOne({ 
      _id: new ObjectId(applicationId) 
    });
    
    if (!app) throw new Error('Application not found');
    
    const job = await db.collection('jobs').findOne({ 
      _id: new ObjectId(app.jobId) 
    });

    if (!job) throw new Error('Job not found');
    
    console.log(`✅ Found: ${job.title} at ${job.company}`);

    // AI job analysis
    const analysis = await analyzeJobWithAI(job);
    
    // Generate questions using specialized AI providers
    const allQuestions = [];
    
    for (const [questionType, config] of Object.entries(analysis.questionStrategy)) {
      if (config.count > 0) {
        try {
          const questions = await generateQuestions(questionType, config.count, job, analysis);
          allQuestions.push(...questions);
        } catch (error) {
          console.error(`❌ Failed to generate ${questionType} questions:`, error.message);
          // Continue with other question types even if one fails
        }
      }
    }

    // Add resume-based questions
    const resumeQuestions = createResumeQuestions(app, RESUME_QUESTIONS_COUNT);
    allQuestions.push(...resumeQuestions);

    // Shuffle questions strategically
    const finalQuestions = shuffleQuestions(allQuestions);
    const estimatedTime = calculateTime(finalQuestions);

    // Save to database
    console.log('💾 Saving questions...');
    await db.collection('applications').updateOne(
      { _id: new ObjectId(applicationId) },
      {
        $set: {
          questions: finalQuestions,
          questionsGeneratedAt: new Date(),
          canTakeInterview: true,
          interviewTime: estimatedTime,
          jobAnalysis: analysis,
          generationMethod: 'pure-ai-driven',
          resumeQuestionsEnabled: true
        }
      }
    );

    await client.close();
    
    const generationTime = Date.now() - startTime;
    console.log(`✅ Generated ${finalQuestions.length} questions in ${generationTime}ms`);

    // Log breakdown
    const breakdown = {};
    finalQuestions.forEach(q => {
      breakdown[q.type] = (breakdown[q.type] || 0) + 1;
    });
    console.log('📈 Question breakdown:', breakdown);

    return res.status(200).json({
      success: true,
      questions: finalQuestions,
      interviewTime: estimatedTime,
      jobAnalysis: analysis,
      questionBreakdown: breakdown,
      generationTime,
      method: 'pure-ai-driven',
      resumeQuestionsEnabled: true
    });

  } catch (error) {
    console.error('❌ Generation failed:', error.message);
    
    if (client) {
      try {
        await client.close();
      } catch (closeError) {
        console.error('Error closing connection:', closeError.message);
      }
    }
    
    return res.status(500).json({ 
      success: false, 
      message: error.message.includes('not found') ? error.message : 'Question generation failed',
      retryable: !error.message.includes('not found')
    });
  }
}

export const config = {
  api: {
    bodyParser: true,
  },
};
