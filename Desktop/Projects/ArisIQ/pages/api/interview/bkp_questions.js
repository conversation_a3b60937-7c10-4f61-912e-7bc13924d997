// File: pages/api/interview/questions.js

import { MongoClient, ObjectId } from 'mongodb';
import { OpenAI } from 'openai';
import axios from 'axios';

// Initialize API clients
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
const uri = process.env.MONGODB_URI;

// Add this debugging function near the top of the file
async function debugMongoConnection() {
  console.log('Testing MongoDB connection...');
  try {
    const client = await MongoClient.connect(uri);
    console.log('✅ MongoDB connection successful');
    
    const db = client.db();
    console.log('Database:', db.databaseName);
    
    const collections = await db.listCollections().toArray();
    console.log('Collections:', collections.map(c => c.name).join(', '));
    
    await client.close();
    return true;
  } catch (err) {
    console.error('❌ MongoDB connection error:', err.message);
    return false;
  }
}

// API configurations with response caching for faster repeat queries
const apiCalls = new Map(); // Cache for API calls
const apiConfigs = {
  openai: {
    enabled: true,
    name: 'OpenAI',
    // Using the existing openai client
    cacheTimeout: 3600000 // 1 hour cache
  },
  gemini: {
    enabled: !!process.env.GEMINI_API_KEY,
    name: 'Google Gemini',
    // UPDATED: endpoint to the latest version
    endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent',
    formatRequest: (prompt) => ({
      contents: [{ parts: [{ text: prompt }] }],
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 8192
      }
    }),
    parseResponse: (response) => {
      return response.data.candidates[0].content.parts[0].text;
    },
    cacheTimeout: 3600000 // 1 hour cache
  },
  claude: {
    enabled: !!process.env.CLAUDE_API_KEY,
    name: 'Anthropic Claude',
    endpoint: 'https://api.anthropic.com/v1/messages',
    formatRequest: (prompt) => ({
      model: 'claude-3-haiku-20240307',
      max_tokens: 4000,
      messages: [{ role: 'user', content: prompt }]
    }),
    parseResponse: (response) => {
      return response.data.content[0].text;
    },
    cacheTimeout: 3600000 // 1 hour cache
  },
  mistral: {
    enabled: !!process.env.MISTRAL_API_KEY,
    name: 'Mistral AI',
    endpoint: 'https://api.mistral.ai/v1/chat/completions',
    formatRequest: (prompt) => ({
      model: 'mistral-small',
      messages: [{ role: 'user', content: prompt }]
    }),
    parseResponse: (response) => {
      return response.data.choices[0].message.content;
    },
    cacheTimeout: 3600000 // 1 hour cache
  },
  // REPLACED: Cohere with Grok API
  grok: {
    enabled: !!process.env.GROK_API_KEY,
    name: 'Grok AI',
    endpoint: 'https://api.grok.ai/v1/chat/completions', // Replace with actual Grok API endpoint
    formatRequest: (prompt) => ({
      model: 'grok-1', // Replace with actual model name
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7,
      max_tokens: 4000
    }),
    parseResponse: (response) => {
      return response.data.choices[0].message.content;
    },
    cacheTimeout: 3600000 // 1 hour cache
  }
};

// ✅ Helper to clean Markdown and repair minor formatting issues
function extractJson(text) {
  try {
    const match = text.match(/```json([\s\S]*?)```/i);
    const raw = match ? match[1].trim() : text.trim();

    // Fix common malformed keys (e.g., type: -> "type":)
    const fixed = raw.replace(/([{,])\s*(\w+)\s*:/g, '$1 "$2":');
    return JSON.parse(fixed);
  } catch (err) {
    console.warn("❌ Failed to parse JSON:", err.message);
    return [];
  }
}

// Function to call an API with retries and caching for faster responses
async function callAPI(provider, prompt, maxRetries = 3) {
  const config = apiConfigs[provider];
  let retries = 0;

  // Skip if not enabled
  if (!config.enabled) {
    console.log(`⏭️ Skipping ${config.name} - not enabled`);
    return [];
  }

  // Create a cache key based on provider and prompt
  const cacheKey = `${provider}-${prompt.substring(0, 100)}`;
  
  // Check if we have a cached response
  const cachedCall = apiCalls.get(cacheKey);
  if (cachedCall && (Date.now() - cachedCall.timestamp < config.cacheTimeout)) {
    console.log(`✅ Using cached response for ${config.name}`);
    return cachedCall.data;
  }

  while (retries < maxRetries) {
    try {
      console.log(`🔄 Calling ${config.name} API...`);
      
      let response;
      let content;
      let parsed;
      
      if (provider === 'openai') {
        // Use the OpenAI SDK
        response = await openai.chat.completions.create({
          model: 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.7,
        });
        
        content = response.choices?.[0]?.message?.content || '';
        parsed = extractJson(content);
        
        if (Array.isArray(parsed)) {
          const result = parsed.map(q => ({...q, source: provider}));
          
          // Cache the result
          apiCalls.set(cacheKey, {
            timestamp: Date.now(),
            data: result
          });
          
          return result;
        }
        return [];
      } 
      else if (provider === 'gemini') {
        // UPDATED: Google's Gemini API call with timeout
        const apiKey = process.env.GEMINI_API_KEY;
        const url = `${config.endpoint}?key=${apiKey}`;
        
        // Log the URL being used (without the API key)
        console.log(`🔍 Using Gemini endpoint: ${config.endpoint}`);
        
        // Add timeout to prevent hanging requests
        response = await axios.post(
          url,
          config.formatRequest(prompt),
          { 
            headers: { 'Content-Type': 'application/json' },
            timeout: 30000 // 30 second timeout
          }
        );
      } 
      else {
        // For all other APIs
        const headers = {};
        
        if (provider === 'claude') {
          headers['anthropic-version'] = '2023-06-01';
          headers['x-api-key'] = process.env.CLAUDE_API_KEY;
        } 
        else if (provider === 'mistral') {
          headers['Authorization'] = `Bearer ${process.env.MISTRAL_API_KEY}`;
        } 
        else if (provider === 'grok') {
          headers['Authorization'] = `Bearer ${process.env.GROK_API_KEY}`;
          headers['Content-Type'] = 'application/json';
        }
        
        headers['Content-Type'] = 'application/json';
        
        response = await axios.post(
          config.endpoint,
          config.formatRequest(prompt),
          { headers }
        );
      }
      
      // Parse the response for non-OpenAI providers
      if (provider !== 'openai') {
        const textContent = config.parseResponse(response);
        const parsed = extractJson(textContent);
        
        if (Array.isArray(parsed)) {
          const result = parsed.map(q => ({...q, source: provider}));
          
          // Cache the result
          apiCalls.set(cacheKey, {
            timestamp: Date.now(),
            data: result
          });
          
          return result;
        }
        return [];
      }
      
    } catch (error) {
      retries++;
      console.error(`❌ Error calling ${config.name} API (attempt ${retries}/${maxRetries}):`, error.message);
      
      if (retries >= maxRetries) {
        console.error(`❌ Failed to call ${config.name} API after ${maxRetries} attempts`);
        return [];
      }
      
      // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retries)));
    }
  }
  
  return [];
}

// Function to evaluate an answer
export async function evaluateAnswer(question, candidateAnswer) {
  const prompt = `
    Evaluate the following candidate's answer to an interview question.
    
    QUESTION: ${question.question}
    
    EXPECTED ANSWER: ${question.answer || "N/A"}
    
    CANDIDATE'S ANSWER: ${candidateAnswer}
    
    Evaluate the answer on a scale of 1-10 based on:
    1. Correctness (accuracy of the solution)
    2. Completeness (addresses all parts of the question)
    3. Communication (clarity of explanation)
    
    Provide a JSON object with your evaluation, including:
    {
      "overallScore": [1-10],
      "correctnessScore": [1-10],
      "completenessScore": [1-10],
      "communicationScore": [1-10],
      "feedback": "detailed feedback with suggestions for improvement",
      "strengths": ["list", "of", "strengths"],
      "weaknesses": ["list", "of", "areas", "to", "improve"]
    }
  `;

  // Get enabled API providers
  const enabledProviders = Object.keys(apiConfigs).filter(key => apiConfigs[key].enabled);
  const evaluations = [];

  // Call each API for evaluation
  for (const provider of enabledProviders) {
    try {
      const results = await callAPI(provider, prompt, 2);
      if (results.length > 0 && typeof results[0] === 'object') {
        evaluations.push({
          ...results[0],
          evaluator: provider
        });
      }
    } catch (error) {
      console.error(`❌ Error evaluating with ${provider}:`, error.message);
    }
  }

  // Calculate cumulative scores if we have evaluations
  if (evaluations.length > 0) {
    const aggregateScores = {
      overallScore: 0,
      correctnessScore: 0,
      completenessScore: 0,
      communicationScore: 0
    };

    // Sum up all scores
    evaluations.forEach(evaluation => {
      aggregateScores.overallScore += evaluation.overallScore || 0;
      aggregateScores.correctnessScore += evaluation.correctnessScore || 0;
      aggregateScores.completenessScore += evaluation.completenessScore || 0;
      aggregateScores.communicationScore += evaluation.communicationScore || 0;
    });

    // Calculate averages
    const numEvaluations = evaluations.length;
    Object.keys(aggregateScores).forEach(key => {
      aggregateScores[key] = parseFloat((aggregateScores[key] / numEvaluations).toFixed(1));
    });

    // Combine feedback and strengths/weaknesses
    const allFeedback = evaluations.map(e => e.feedback).join('\n\n');
    const allStrengths = [...new Set(evaluations.flatMap(e => e.strengths || []))];
    const allWeaknesses = [...new Set(evaluations.flatMap(e => e.weaknesses || []))];

    return {
      cumulativeScores: aggregateScores,
      individualEvaluations: evaluations,
      combinedFeedback: allFeedback,
      strengths: allStrengths,
      weaknesses: allWeaknesses
    };
  }

  return {
    error: "Failed to get evaluations",
    individualEvaluations: evaluations
  };
}

// Main handler for question generation
export default async function handler(req, res) {
  console.log('📝 Questions API called with method:', req.method);
  
  if (req.method !== 'POST') {
    console.log('❌ Method not allowed:', req.method);
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  // MODIFIED: Added waitForAllProviders parameter
  const { applicationId, forceRegenerate, waitForAllProviders } = req.body;
  console.log('📝 Request body:', req.body);
  
  if (!applicationId) {
    console.log('❌ Missing applicationId');
    return res.status(400).json({ success: false, message: 'applicationId is required' });
  }

  // Debug MongoDB connection
  await debugMongoConnection();

  try {
    // Connect to MongoDB
    console.time('mongo-connect');
    console.log('Connecting to MongoDB...');
    const client = await MongoClient.connect(uri);
    const db = client.db();
    console.timeEnd('mongo-connect');
    console.log('✅ Connected to MongoDB');

    // MODIFIED: Skip cache check if forceRegenerate is true
    console.time('check-existing');
    if (!forceRegenerate) {
      console.log('Checking for existing questions for application:', applicationId);
      
      // Validate applicationId format for MongoDB ObjectId
      let appObjectId;
      try {
        appObjectId = new ObjectId(applicationId);
      } catch (err) {
        console.error('❌ Invalid ObjectId format:', applicationId);
        return res.status(400).json({ 
          success: false, 
          message: 'Invalid applicationId format' 
        });
      }
      
      const existingApp = await db.collection('applications').findOne(
        { _id: appObjectId },
        { projection: { questions: 1, questionsGeneratedAt: 1 } }
      );

      console.log('Existing application data:', existingApp ? 'Found' : 'Not found');
      
      // If questions exist and are less than 1 day old, return them directly
      if (existingApp?.questions?.length > 0 && existingApp.questionsGeneratedAt) {
        const questionsAge = Date.now() - new Date(existingApp.questionsGeneratedAt).getTime();
        if (questionsAge < 24 * 60 * 60 * 1000) { // 24 hours
          console.log('✅ Returning existing questions:', existingApp.questions.length);
          console.timeEnd('check-existing');
          
          // Set overall interview time
          const totalQuestions = existingApp.questions.length;
          const interviewTime = Math.max(45, Math.min(60, totalQuestions * 2.5)); // 2.5 minutes per question on average
          
          return res.status(200).json({ 
            success: true, 
            questions: existingApp.questions,
            interviewTime: interviewTime, // Return overall interview time in minutes
            cached: true
          });
        }
      }
    } else {
      console.log('Force regenerate is true - generating new questions');
    }
    console.timeEnd('check-existing');

    // Fetch application and job data
    console.time('fetch-data');
    console.log('Fetching application data...');
    
    // Validate applicationId format for MongoDB ObjectId
    let appObjectId;
    try {
      appObjectId = new ObjectId(applicationId);
    } catch (err) {
      console.error('❌ Invalid ObjectId format:', applicationId);
      return res.status(400).json({ 
        success: false, 
        message: 'Invalid applicationId format' 
      });
    }
    
    const app = await db.collection('applications').findOne({ _id: appObjectId });
    
    if (!app) {
      console.log('❌ Application not found');
      return res.status(404).json({ success: false, message: 'Application not found' });
    }
    
    console.log('Fetching job data...');
    const job = await db.collection('jobs').findOne({ _id: new ObjectId(app.jobId) });

    if (!job) {
      console.log('❌ Job not found');
      return res.status(404).json({ success: false, message: 'Job not found' });
    }
    console.timeEnd('fetch-data');
    console.log('✅ Application and job data fetched');

    // Check if coding/technical skills are required
    const codingKeywords = ['developer', 'engineer', 'programmer', 'coding', 'software', 'development', 'fullstack', 'frontend', 'backend', 'web', 'mobile', 'app', 'python', 'javascript', 'java', 'c#', 'c++', '.net', 'react', 'angular', 'vue', 'node'];
    
    // Check job title, industry, and required skills for coding keywords
    const jobTitle = (job.title || '').toLowerCase();
    const jobIndustry = (job.industry || '').toLowerCase();
    const requiredSkills = (job.requiredSkills || []).map(skill => skill.toLowerCase());
    
    const isTechnicalRole = 
      codingKeywords.some(keyword => jobTitle.includes(keyword)) ||
      codingKeywords.some(keyword => jobIndustry.includes(keyword)) ||
      codingKeywords.some(keyword => requiredSkills.some(skill => skill.includes(keyword)));
    
    console.log(`Job analysis - Technical role: ${isTechnicalRole}`);
    
    // Define question counts based on role type
    let questionCounts;
    let interviewTime;
    
    if (isTechnicalRole) {
      // Technical role - include coding questions
      questionCounts = {
        mcq: 10,
        coding: 4,
        technical: 3,
        behavioral: 3
      };
      interviewTime = 60; // 60 minutes for technical interviews
    } else {
      // Non-technical role - no coding questions
      questionCounts = {
        mcq: 15,
        behavioral: 5
      };
      interviewTime = 45; // 45 minutes for non-technical interviews
    }
    
    console.log('Question distribution:', questionCounts);
    
    // Generate the prompt based on role type
    const prompt = `You are an AI recruiter. Based on the following:

Resume:
Full Name: ${app.fullName}
Experience: ${app.experienceYears} years
Cover Letter: ${app.coverLetter || 'N/A'}

Job Title: ${job.title}
Industry: ${job.industry || 'N/A'}
Required Skills: ${job.requiredSkills?.join(', ') || 'N/A'}
Preferred Skills: ${job.preferredSkills?.join(', ') || 'N/A'}

${isTechnicalRole ? 
  `Generate a total of 20 interview questions for this technical role with the following specific distribution:
  - ${questionCounts.mcq} multiple-choice technical questions (MCQ) relevant to the skills required (provide options as an array with one correct answer)
  - ${questionCounts.coding} coding questions focusing on the technical skills required
  - ${questionCounts.technical} technical concept questions (can be answered in writing or speaking)
  - ${questionCounts.behavioral} behavioral questions focused on communication, teamwork, and problem-solving` :
  
  `Generate a total of 20 interview questions for this non-technical role with the following specific distribution:
  - ${questionCounts.mcq} multiple-choice questions (MCQ) relevant to the industry and required skills (provide options as an array with one correct answer)
  - ${questionCounts.behavioral} behavioral questions focused on communication, teamwork, and relevant industry scenarios`}

Difficulty distribution:
- Most questions (about 70%) should be basic to intermediate level
- Include 2-3 difficult/advanced questions based on the candidate's experience (${app.experienceYears} years) and the job's requirements
- Mark each question with its difficulty level: "basic", "intermediate", or "advanced"

${isTechnicalRole ? 
  `For coding questions:
  - Focus on languages relevant to the job requirements
  - Provide starter code that guides the candidate
  - Include test cases or expected output
  - Questions should be focused on practical skills needed for the role` : ''}

Avoid repetition and ensure a comprehensive assessment of skills that are DIRECTLY RELEVANT to the job requirements.

Output format:
[
  {
    "type": "mcq" | "technical" | "behavioral" | "coding",
    "question": "Your question here",
    "difficulty": "basic" | "intermediate" | "advanced",
    "options": ["a) ...", "b) ...", "c) ...", "d) ..."],  // Only for MCQ
    "answer": "b) ...",  // Only for MCQ
    "language": "javascript", // Only for coding questions (javascript, python, java, cpp)
    "starterCode": "function example() {\\n  // Your code here\\n}", // Only for coding questions
    "testCases": ["input1 -> output1", "input2 -> output2"], // Optional for coding questions
    "expectedOutput": "Expected output description" // Optional for coding questions
  }
]`;

    // Get enabled API providers
    console.time('get-providers');
    const enabledProviders = Object.keys(apiConfigs).filter(key => apiConfigs[key].enabled);
    console.log('Enabled providers:', enabledProviders);
    let allQuestions = [];
    console.timeEnd('get-providers');

    // Call each API for question generation with parallel processing
    console.time('api-calls');
    
    // MODIFIED: Determine timeout based on waitForAllProviders parameter
    const apiTimeout = waitForAllProviders ? 45000 : 15000; // Wait longer if requested
    
    const apiPromises = enabledProviders.map(async (provider) => {
      try {
        console.log(`🔄 Calling ${provider} for questions...`);
        const questions = await callAPI(provider, prompt);
        if (questions && questions.length > 0) {
          console.log(`✅ Got ${questions.length} questions from ${provider}`);
          return questions;
        }
        console.log(`❌ No valid questions returned from ${provider}`);
        return [];
      } catch (error) {
        console.error(`❌ Error generating questions with ${provider}:`, error.message);
        return [];
      }
    });
    
    // MODIFIED: Use Promise.race with a timeout to handle slow providers
    let results = [];
    if (waitForAllProviders) {
      // Wait for all providers with a timeout
      const timeoutPromise = new Promise((resolve) => {
        setTimeout(() => {
          console.log(`⏱️ Waiting for all providers - timeout after ${apiTimeout/1000} seconds`);
          resolve([]);
        }, apiTimeout);
      });
      
      // Gather all results, waiting up to the timeout
      const allResults = await Promise.all(
        apiPromises.map(promise => 
          Promise.race([promise, timeoutPromise.then(() => [])])
        )
      );
      
      results = allResults;
    } else {
      // Just get results as they come in
      results = await Promise.all(apiPromises);
    }
    
    // Collect all questions
    results.forEach(questions => {
      allQuestions.push(...questions);
    });
    
    console.log(`Total questions from all providers: ${allQuestions.length}`);
    
    // If we don't have enough questions, try again with primary providers
    if (allQuestions.length < 15) {
      const primaryProviders = ['openai', 'claude'].filter(p => apiConfigs[p].enabled);
      
      for (const provider of primaryProviders) {
        console.log(`⚠️ Not enough questions, trying ${provider} again...`);
        
        try {
          // Try to get more questions focusing on missing types
          const followupQuestions = await callAPI(provider, 
            `${prompt}\n\nNOTE: Please focus especially on generating different questions for the interview. We need varied questions that don't repeat.`);
          
          if (followupQuestions && followupQuestions.length > 0) {
            console.log(`✅ Got ${followupQuestions.length} more questions from ${provider}`);
            allQuestions.push(...followupQuestions);
          }
        } catch (error) {
          console.error(`❌ Error generating additional questions with ${provider}:`, error.message);
        }
      }
    }
    console.timeEnd('api-calls');

    // ❌ If still no valid questions, avoid setting canTakeInterview
    if (allQuestions.length === 0) {
      console.log('❌ No questions generated from any provider');
      return res.status(200).json({
        success: false,
        questions: [],
        message: 'No valid questions generated. Please try again.',
      });
    }

    // ✅ Deduplicate
    console.time('process-questions');
    console.log('Processing and deduplicating questions...');
    const seen = new Set();
    const questions = allQuestions.filter(q =>
      q?.question &&
      typeof q.question === 'string' &&
      !seen.has(q.question) &&
      seen.add(q.question)
    );

    console.log(`After deduplication: ${questions.length} questions`);

    // ✅ Categorize questions by type
    const questionsByType = {
      mcq: [],
      technical: [],
      behavioral: [],
      coding: []
    };

    // Group questions by type and ensure difficulty is set
    questions.forEach(q => {
      // Ensure difficulty is set
      if (!q.difficulty) {
        q.difficulty = 'intermediate'; // Default to intermediate
      }
      
      // Group by type
      if (questionsByType.hasOwnProperty(q.type)) {
        questionsByType[q.type].push(q);
      } else {
        // If type is missing or invalid, categorize based on content
        if (q.options && q.answer) {
          q.type = 'mcq';
          questionsByType.mcq.push(q);
        } else if (q.starterCode || q.language) {
          q.type = 'coding';
          questionsByType.coding.push(q);
        } else if (q.question.toLowerCase().includes('tell me about a time') || 
                  q.question.toLowerCase().includes('describe a situation') ||
                  q.question.toLowerCase().includes('have you ever')) {
          q.type = 'behavioral';
          questionsByType.behavioral.push(q);
        } else {
          q.type = 'technical'; // Default to technical
          questionsByType.technical.push(q);
        }
      }
    });

    console.log('Questions by type:', Object.keys(questionsByType).map(type => 
      `${type}: ${questionsByType[type].length}`).join(', '));

    // Validate and enhance coding questions if technical role
    if (isTechnicalRole) {
      questionsByType.coding.forEach(q => {
        // Ensure coding questions have required fields
        if (!q.language) {
          // Try to infer language from starterCode if available
          if (q.starterCode && q.starterCode.includes('function')) {
            q.language = 'javascript';
          } else if (q.starterCode && q.starterCode.includes('def ')) {
            q.language = 'python';
          } else if (q.starterCode && q.starterCode.includes('class ') && q.starterCode.includes('public static')) {
            q.language = 'java';
          } else {
            // Default to JavaScript if can't determine
            q.language = 'javascript';
          }
        }
        
        if (!q.starterCode) {
          // Add basic starter code based on language
          switch (q.language.toLowerCase()) {
            case 'python':
              q.starterCode = "def solution():\n    # Your code here\n    pass\n\n# Test your solution\nprint(solution())";
              break;
            case 'java':
              q.starterCode = "public class Solution {\n    public static void main(String[] args) {\n        // Test your solution here\n        System.out.println(solution());\n    }\n\n    public static String solution() {\n        // Your code here\n        return \"\";\n    }\n}";
              break;
            case 'cpp':
              q.starterCode = "#include <iostream>\n\nstring solution() {\n    // Your code here\n    return \"\";\n}\n\nint main() {\n    // Test your solution\n    std::cout << solution() << std::endl;\n    return 0;\n}";
              break;
            default: // JavaScript
              q.starterCode = "function solution() {\n  // Your code here\n  return;\n}\n\n// Test your solution\nconsole.log(solution());";
          }
        }
      });
    }

    // Select final questions with balanced distribution based on target counts
    const finalQuestions = [];
    
    // Track how many advanced questions we've included
    let advancedCount = 0;
    const targetAdvancedCount = 3; // 2-3 advanced questions
    
    // First, let's ensure we get the required number of advanced questions
    Object.keys(questionCounts).forEach(type => {
      // Skip if we have no target for this type
      if (!questionCounts[type] || questionCounts[type] <= 0) return;
      
      const advancedQuestions = questionsByType[type]
        .filter(q => q.difficulty === 'advanced')
        .sort(() => 0.5 - Math.random()); // Shuffle
        
      // Take up to the target number or what's available
      const count = Math.min(advancedQuestions.length, 
                            targetAdvancedCount - advancedCount,
                            questionCounts[type]); // Don't exceed type target
                            
      if (count > 0) {
        const selected = advancedQuestions.slice(0, count);
        finalQuestions.push(...selected);
        advancedCount += selected.length;
        
        // Remove these questions from the type pool
        selected.forEach(q => {
          const index = questionsByType[type].findIndex(item => item.question === q.question);
          if (index !== -1) {
            questionsByType[type].splice(index, 1);
          }
        });
      }
    });
    
    console.log(`Selected ${advancedCount} advanced questions`);
    
    // Now fill the rest with basic/intermediate questions
    Object.keys(questionCounts).forEach(type => {
      // Skip if we have no target for this type
      if (!questionCounts[type] || questionCounts[type] <= 0) return;
      
      const remainingTypeQuestions = questionsByType[type];
      const remainingTarget = questionCounts[type] - 
        finalQuestions.filter(q => q.type === type).length;
      
      if (remainingTarget > 0) {
        // Shuffle to randomize selection
        const shuffled = [...remainingTypeQuestions].sort(() => 0.5 - Math.random());
        
        // Take up to the remaining target number for this type
        const selected = shuffled.slice(0, remainingTarget);
        finalQuestions.push(...selected);
        
        console.log(`Added ${selected.length} more ${type} questions`);
      }
    });
    
    console.log(`Final question count: ${finalQuestions.length}`);
    console.timeEnd('process-questions');
    
    // Ensure we have a minimum number of questions
    if (finalQuestions.length < 5) {
      console.log('❌ Not enough valid questions after processing');
      return res.status(200).json({
        success: false,
        questions: [],
        message: 'Not enough valid questions generated. Please try again.',
      });
    }

    // ✅ Save to MongoDB
    console.time('save-to-db');
    console.log('Saving questions to database...');
    try {
      await db.collection('applications').updateOne(
        { _id: appObjectId },
        {
          $set: {
            questions: finalQuestions,
            canTakeInterview: true,
            questionsGeneratedAt: new Date(),
            interviewTime: interviewTime // Store overall interview time in minutes
          },
        }
      );
      console.log('✅ Questions saved to database');
    } catch (dbErr) {
      console.error('❌ Error saving to database:', dbErr);
      // Continue and return questions even if save fails
    }
    console.timeEnd('save-to-db');

    // ✅ Return success response with questions
    console.log(`✅ Returning ${finalQuestions.length} questions`);
    return res.status(200).json({ 
      success: true, 
      questions: finalQuestions,
      interviewTime: interviewTime, // Return overall interview time in minutes
      technicalRole: isTechnicalRole,
      providerStats: {
        enabled: enabledProviders,
        questionCounts: Object.keys(apiConfigs).reduce((acc, provider) => {
          acc[provider] = finalQuestions.filter(q => q.source === provider).length;
          return acc;
        }, {}),
        typeDistribution: Object.keys(questionsByType).reduce((acc, type) => {
          acc[type] = finalQuestions.filter(q => q.type === type).length;
          return acc;
        }, {}),
        difficultyDistribution: {
          basic: finalQuestions.filter(q => q.difficulty === 'basic').length,
          intermediate: finalQuestions.filter(q => q.difficulty === 'intermediate').length,
          advanced: finalQuestions.filter(q => q.difficulty === 'advanced').length
        }
      }
    });
  } catch (err) {
    console.error('❌ Question generation error:', err);
    return res.status(500).json({ success: false, message: err.message });
  }
}

export const config = {
  api: {
    bodyParser: true,
  },
};
