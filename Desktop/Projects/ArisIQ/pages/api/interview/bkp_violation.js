// File: pages/api/interview/violation.js

import { MongoClient, ObjectId } from 'mongodb';

export default async function handler(req, res) {
  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }
  
  const { applicationId, type, message, timestamp } = req.body;
  
  // Validate required fields
  if (!applicationId) {
    return res.status(400).json({ success: false, message: 'Application ID is required' });
  }
  
  if (!type || !message) {
    return res.status(400).json({ success: false, message: 'Violation details are required' });
  }
  
  // Get database connection string from environment variables
  const uri = process.env.MONGODB_URI;
  
  if (!uri) {
    console.error('MongoDB URI not found in environment variables');
    return res.status(500).json({ success: false, message: 'Database configuration error' });
  }
  
  let client;
  
  try {
    // Connect to MongoDB
    client = await MongoClient.connect(uri);
    const db = client.db();
    
    // Create ObjectId from application ID
    let appId;
    try {
      appId = new ObjectId(applicationId);
    } catch (error) {
      return res.status(400).json({ success: false, message: 'Invalid application ID format' });
    }
    
    // Check if application exists
    const application = await db.collection('applications').findOne({ _id: appId });
    
    if (!application) {
      return res.status(404).json({ success: false, message: 'Application not found' });
    }
    
    // Create violation record
    const violationData = {
      applicationId: appId,
      type,
      message,
      timestamp: timestamp || new Date().toISOString(),
      recordedAt: new Date()
    };
    
    // Insert violation into separate collection for detailed tracking
    await db.collection('interview_violations').insertOne(violationData);
    
    // Update application with violation count
    await db.collection('applications').updateOne(
      { _id: appId },
      { 
        $inc: { [`violations.${type}`]: 1, 'violations.total': 1 },
        $push: { violationHistory: violationData },
        $set: { lastViolation: new Date() }
      },
      { upsert: false }
    );
    
    // Return success response
    return res.status(200).json({ 
      success: true, 
      message: 'Violation recorded successfully'
    });
    
  } catch (error) {
    console.error('Error recording violation:', error);
    return res.status(500).json({ success: false, message: error.message });
  } finally {
    // Close database connection
    if (client) {
      await client.close();
    }
  }
}
