// File: pages/api/interview/evaluate.js

import { MongoClient, ObjectId } from 'mongodb';
import { evaluateAnswer } from './questions'; // Import the evaluation function from questions.js

const uri = process.env.MONGODB_URI;

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  const { questionId, applicationId, answer } = req.body;
  
  if (!questionId || !applicationId || !answer) {
    return res.status(400).json({ 
      success: false, 
      message: 'questionId, applicationId, and answer are required' 
    });
  }

  try {
    const client = await MongoClient.connect(uri);
    const db = client.db();

    // Get the application with its questions
    const application = await db.collection('applications').findOne({ 
      _id: new ObjectId(applicationId) 
    });

    if (!application) {
      return res.status(404).json({ 
        success: false, 
        message: 'Application not found' 
      });
    }

    // Find the specific question
    const question = application.questions.find(q => 
      q.id === questionId || 
      q._id?.toString() === questionId ||
      q._id === questionId
    );

    if (!question) {
      return res.status(404).json({ 
        success: false, 
        message: 'Question not found in this application' 
      });
    }

    // Evaluate the answer using multiple AI providers
    const evaluation = await evaluateAnswer(question, answer);

    // Store the evaluation result
    await db.collection('evaluations').insertOne({
      applicationId: new ObjectId(applicationId),
      questionId,
      question: question.question,
      candidateAnswer: answer,
      evaluation,
      createdAt: new Date()
    });

    // Update application with evaluation if needed
    // This is optional, but useful if you want to track progress
    const currentProgress = application.interviewProgress || {};
    currentProgress[questionId] = {
      answered: true,
      evaluation: evaluation.cumulativeScores
    };
    
    await db.collection('applications').updateOne(
      { _id: new ObjectId(applicationId) },
      { 
        $set: { 
          interviewProgress: currentProgress,
          lastActivityAt: new Date()
        } 
      }
    );

    // Return the evaluation results
    return res.status(200).json({
      success: true,
      evaluation,
      // Include a simplified version in the response for easy display
      scores: {
        overall: evaluation.cumulativeScores.overallScore,
        correctness: evaluation.cumulativeScores.correctnessScore,
        completeness: evaluation.cumulativeScores.completenessScore,
        communication: evaluation.cumulativeScores.communicationScore
      },
      feedback: evaluation.combinedFeedback,
      strengths: evaluation.strengths,
      weaknesses: evaluation.weaknesses
    });
    
  } catch (error) {
    console.error('❌ Evaluation error:', error);
    return res.status(500).json({ 
      success: false, 
      message: error.message 
    });
  }
}

export const config = {
  api: {
    bodyParser: true,
  },
};
