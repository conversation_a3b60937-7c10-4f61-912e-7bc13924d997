// File: pages/api/interview/generate-questions.js
// Unified API - Real-time Question Generation for Interviews

import { MongoClient, ObjectId } from 'mongodb';
import { OpenAI } from 'openai';
import axios from 'axios';

// Initialize API clients
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
const uri = process.env.MONGODB_URI;

// Interview configuration
const DEFAULT_INTERVIEW_LENGTH = 20; // Total questions
const MAX_GENERATION_TIME = 120000; // 2 minutes max

// API configurations for multiple providers
const apiConfigs = {
  openai: {
    enabled: !!process.env.OPENAI_API_KEY,
    name: 'OpenAI',
    priority: 1,
    questionsPerCall: 10
  },
  gemini: {
    enabled: !!process.env.GEMINI_API_KEY,
    name: 'Google Gemini',
    priority: 2,
    questionsPerCall: 8,
    endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent',
    formatRequest: (prompt) => ({
      contents: [{ parts: [{ text: prompt }] }],
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 8192
      }
    }),
    parseResponse: (response) => response.data.candidates[0].content.parts[0].text
  },
  claude: {
    enabled: !!process.env.CLAUDE_API_KEY,
    name: 'Anthropic Claude',
    priority: 3,
    questionsPerCall: 8,
    endpoint: 'https://api.anthropic.com/v1/messages',
    formatRequest: (prompt) => ({
      model: 'claude-3-haiku-20240307',
      max_tokens: 4000,
      messages: [{ role: 'user', content: prompt }]
    }),
    parseResponse: (response) => response.data.content[0].text
  },
  mistral: {
    enabled: !!process.env.MISTRAL_API_KEY,
    name: 'Mistral AI',
    priority: 4,
    questionsPerCall: 8,
    endpoint: 'https://api.mistral.ai/v1/chat/completions',
    formatRequest: (prompt) => ({
      model: 'mistral-small',
      messages: [{ role: 'user', content: prompt }]
    }),
    parseResponse: (response) => response.data.choices[0].message.content
  },
  grok: {
    enabled: !!process.env.GROK_API_KEY,
    name: 'Grok AI',
    priority: 5,
    questionsPerCall: 6,
    endpoint: 'https://api.grok.ai/v1/chat/completions',
    formatRequest: (prompt) => ({
      model: 'grok-1',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7,
      max_tokens: 4000
    }),
    parseResponse: (response) => response.data.choices[0].message.content
  }
};

// Main handler
export default async function handler(req, res) {
  console.log('🎯 Starting real-time interview question generation...');
  
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  const startTime = Date.now();
  const { applicationId, interviewLength = DEFAULT_INTERVIEW_LENGTH } = req.body;
  
  if (!applicationId) {
    return res.status(400).json({ success: false, message: 'applicationId is required' });
  }

  try {
    // Connect to MongoDB
    console.log('📡 Connecting to MongoDB...');
    const client = await MongoClient.connect(uri);
    const db = client.db();

    // Get application and job data
    console.log('📋 Fetching application and job data...');
    const app = await db.collection('applications').findOne({ 
      _id: new ObjectId(applicationId) 
    });
    
    if (!app) {
      await client.close();
      return res.status(404).json({ success: false, message: 'Application not found' });
    }
    
    const job = await db.collection('jobs').findOne({ 
      _id: new ObjectId(app.jobId) 
    });

    if (!job) {
      await client.close();
      return res.status(404).json({ success: false, message: 'Job not found' });
    }

    console.log(`✅ Generating questions for: ${job.title} at ${job.company}`);
    console.log(`🔍 Technical role: ${job.isTechnicalRole || checkIfTechnicalRole(job)}`);

    // Determine question distribution based on role type
    const isTechnical = job.isTechnicalRole || checkIfTechnicalRole(job);
    const questionDistribution = calculateQuestionDistribution(isTechnical, interviewLength);
    const estimatedTime = calculateInterviewTime(questionDistribution);

    console.log('📊 Question distribution:', questionDistribution);
    console.log(`⏱️ Estimated interview time: ${estimatedTime} minutes`);

    // Generate questions in parallel using multiple APIs
    console.log('🚀 Starting parallel question generation...');
    const generationPromises = [];
    
    // Create generation tasks for each question type
    Object.entries(questionDistribution).forEach(([type, count]) => {
      if (count > 0) {
        generationPromises.push(
          generateQuestionsForType(job, type, count, isTechnical)
        );
      }
    });

    // Wait for all questions with timeout
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Generation timeout')), MAX_GENERATION_TIME);
    });

    let allQuestions = [];
    try {
      const questionResults = await Promise.race([
        Promise.allSettled(generationPromises),
        timeoutPromise
      ]);

      // Process results
      questionResults.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value) {
          allQuestions.push(...result.value);
        } else {
          console.warn(`⚠️ Failed to generate some questions:`, result.reason?.message);
        }
      });

    } catch (timeoutError) {
      console.warn('⚠️ Generation timeout, using partial results');
    }

    console.log(`✅ Generated ${allQuestions.length} questions total`);

    // If we don't have enough questions, generate some basic fallbacks
    if (allQuestions.length < Math.floor(interviewLength * 0.6)) {
      console.log('⚠️ Insufficient questions generated, adding fallbacks...');
      const fallbackQuestions = generateFallbackQuestions(job, interviewLength - allQuestions.length);
      allQuestions.push(...fallbackQuestions);
    }

    // Shuffle and limit to requested length
    const finalQuestions = allQuestions
      .sort(() => 0.5 - Math.random())
      .slice(0, interviewLength)
      .map((q, index) => ({
        ...q,
        id: q.id || `q_${index}_${Date.now()}`,
        order: index + 1
      }));

    // Save questions to application
    await db.collection('applications').updateOne(
      { _id: new ObjectId(applicationId) },
      {
        $set: {
          questions: finalQuestions,
          questionsGeneratedAt: new Date(),
          canTakeInterview: true,
          interviewTime: estimatedTime,
          questionDistribution: questionDistribution,
          generationMethod: 'real-time'
        }
      }
    );

    await client.close();

    const generationTime = Date.now() - startTime;
    console.log(`✅ Question generation completed in ${generationTime}ms`);

    // Log final breakdown
    const breakdown = {};
    finalQuestions.forEach(q => {
      breakdown[q.type] = (breakdown[q.type] || 0) + 1;
    });
    
    console.log('📈 Final question breakdown:', breakdown);

    return res.status(200).json({
      success: true,
      questions: finalQuestions,
      interviewTime: estimatedTime,
      questionDistribution: questionDistribution,
      questionBreakdown: breakdown,
      generationTime: generationTime,
      technical: isTechnical
    });

  } catch (error) {
    console.error('❌ Question generation error:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to generate interview questions',
      details: error.message 
    });
  }
}

// Calculate question distribution based on role type
function calculateQuestionDistribution(isTechnical, totalQuestions) {
  if (isTechnical) {
    // Technical role distribution
    return {
      mcq: Math.ceil(totalQuestions * 0.5),        // 50% - Technical MCQs
      coding: Math.ceil(totalQuestions * 0.25),     // 25% - Coding problems
      technical: Math.ceil(totalQuestions * 0.15),  // 15% - Technical concepts
      behavioral: Math.ceil(totalQuestions * 0.1)   // 10% - Behavioral
    };
  } else {
    // Non-technical role distribution
    return {
      mcq: Math.ceil(totalQuestions * 0.4),         // 40% - Industry MCQs
      behavioral: Math.ceil(totalQuestions * 0.35),  // 35% - Behavioral
      scenario: Math.ceil(totalQuestions * 0.25)     // 25% - Scenario-based
    };
  }
}

// Calculate estimated interview time
function calculateInterviewTime(distribution) {
  const timePerType = {
    mcq: 2,        // 2 minutes per MCQ
    coding: 12,    // 12 minutes per coding question
    technical: 6,  // 6 minutes per technical question
    behavioral: 8, // 8 minutes per behavioral question
    scenario: 5    // 5 minutes per scenario question
  };
  
  let totalTime = 0;
  Object.entries(distribution).forEach(([type, count]) => {
    totalTime += count * (timePerType[type] || 5);
  });
  
  return totalTime + 10; // Add 10 minutes buffer
}

// Generate questions for a specific type
async function generateQuestionsForType(job, questionType, count, isTechnical) {
  console.log(`🎯 Generating ${count} ${questionType} questions...`);
  
  const prompt = createPromptForType(job, questionType, count, isTechnical);
  const enabledApis = Object.entries(apiConfigs)
    .filter(([_, config]) => config.enabled)
    .sort((a, b) => a[1].priority - b[1].priority);

  // Try APIs in priority order
  for (const [apiName, config] of enabledApis) {
    try {
      console.log(`🔄 Trying ${config.name} for ${questionType} questions...`);
      
      const questions = await callAPI(apiName, prompt, config);
      if (questions && questions.length > 0) {
        console.log(`✅ Got ${questions.length} ${questionType} questions from ${config.name}`);
        return questions.slice(0, count).map(q => ({
          ...q,
          type: questionType,
          source: apiName,
          difficulty: q.difficulty || 'intermediate'
        }));
      }
    } catch (error) {
      console.warn(`⚠️ ${config.name} failed for ${questionType}:`, error.message);
      continue;
    }
  }

  console.warn(`⚠️ All APIs failed for ${questionType}, generating fallbacks`);
  return generateFallbackQuestionsForType(job, questionType, count);
}

// Call specific API
async function callAPI(apiName, prompt, config) {
  try {
    if (apiName === 'openai') {
      const response = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.7,
        timeout: 30000
      });
      
      const content = response.choices?.[0]?.message?.content || '';
      return parseQuestionResponse(content);
    }
    
    else if (apiName === 'gemini') {
      const apiKey = process.env.GEMINI_API_KEY;
      const url = `${config.endpoint}?key=${apiKey}`;
      
      const response = await axios.post(
        url,
        config.formatRequest(prompt),
        { 
          headers: { 'Content-Type': 'application/json' },
          timeout: 30000
        }
      );
      
      const content = config.parseResponse(response);
      return parseQuestionResponse(content);
    }
    
    else if (apiName === 'claude') {
      const response = await axios.post(
        config.endpoint,
        config.formatRequest(prompt),
        {
          headers: {
            'anthropic-version': '2023-06-01',
            'x-api-key': process.env.CLAUDE_API_KEY,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );
      
      const content = config.parseResponse(response);
      return parseQuestionResponse(content);
    }
    
    else if (apiName === 'mistral') {
      const response = await axios.post(
        config.endpoint,
        config.formatRequest(prompt),
        {
          headers: {
            'Authorization': `Bearer ${process.env.MISTRAL_API_KEY}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );
      
      const content = config.parseResponse(response);
      return parseQuestionResponse(content);
    }
    
    else if (apiName === 'grok') {
      const response = await axios.post(
        config.endpoint,
        config.formatRequest(prompt),
        {
          headers: {
            'Authorization': `Bearer ${process.env.GROK_API_KEY}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );
      
      const content = config.parseResponse(response);
      return parseQuestionResponse(content);
    }
    
  } catch (error) {
    throw new Error(`${config.name} API call failed: ${error.message}`);
  }
  
  return [];
}

// Parse API response to extract questions
function parseQuestionResponse(text) {
  try {
    // Try to extract JSON from markdown code blocks
    const jsonMatch = text.match(/```json\s*([\s\S]*?)\s*```/i);
    const jsonText = jsonMatch ? jsonMatch[1] : text;
    
    // Clean up common JSON formatting issues
    const cleanedJson = jsonText
      .replace(/([{,]\s*)(\w+):/g, '$1"$2":') // Fix unquoted keys
      .trim();
    
    const parsed = JSON.parse(cleanedJson);
    return Array.isArray(parsed) ? parsed : [parsed];
  } catch (error) {
    console.warn('Failed to parse question response:', error.message);
    return [];
  }
}

// Create prompt for specific question type
function createPromptForType(job, type, count, isTechnical) {
  const baseContext = `
Job Title: ${job.title}
Company: ${job.company}
Industry: ${job.industry || 'Not specified'}
Description: ${job.description || 'Not specified'}
Required Skills: ${Array.isArray(job.requiredSkills) ? job.requiredSkills.join(', ') : job.requiredSkills || 'Not specified'}
Technical Role: ${isTechnical ? 'Yes' : 'No'}
`;

  const prompts = {
    mcq: `${baseContext}

Generate ${count} multiple choice questions relevant to this job position. 

Format as JSON array:
[
  {
    "question": "Your question here",
    "difficulty": "basic|intermediate|advanced",
    "options": ["a) Option 1", "b) Option 2", "c) Option 3", "d) Option 4"],
    "correctAnswer": "a) Option 1",
    "explanation": "Why this is correct",
    "points": 10,
    "timeLimit": 2
  }
]

Make questions specific to the role and industry. Ensure one clear correct answer per question.`,

    coding: `${baseContext}

Generate ${count} coding problems suitable for this technical position.

Format as JSON array:
[
  {
    "question": "Problem statement here",
    "difficulty": "basic|intermediate|advanced",
    "language": "javascript|python|java",
    "starterCode": "function solution() {\\n  // Your code here\\n}",
    "testCases": ["input1 -> output1", "input2 -> output2"],
    "expectedOutput": "Description of expected solution",
    "points": 20,
    "timeLimit": 12
  }
]

Focus on practical problems relevant to the job requirements.`,

    technical: `${baseContext}

Generate ${count} technical concept questions for this role.

Format as JSON array:
[
  {
    "question": "Technical question here",
    "difficulty": "basic|intermediate|advanced",
    "keyPoints": ["Point 1", "Point 2", "Point 3"],
    "sampleAnswer": "Example of a good answer",
    "points": 15,
    "timeLimit": 6
  }
]

Focus on technologies and concepts mentioned in the job requirements.`,

    behavioral: `${baseContext}

Generate ${count} behavioral interview questions.

Format as JSON array:
[
  {
    "question": "Tell me about a time when...",
    "difficulty": "basic|intermediate|advanced",
    "keyPoints": ["Leadership", "Problem-solving", "Communication"],
    "sampleAnswer": "Example STAR method answer",
    "points": 15,
    "timeLimit": 8
  }
]

Focus on soft skills relevant to the role and industry.`,

    scenario: `${baseContext}

Generate ${count} scenario-based questions for this industry.

Format as JSON array:
[
  {
    "question": "How would you handle this situation...",
    "difficulty": "basic|intermediate|advanced",
    "scenario": "Detailed realistic scenario",
    "keyPoints": ["Analysis", "Decision-making", "Communication"],
    "sampleAnswer": "Example approach to the scenario",
    "points": 12,
    "timeLimit": 5
  }
]

Create realistic workplace scenarios for this specific role.`
  };

  return prompts[type] || prompts.mcq;
}

// Generate fallback questions when APIs fail
function generateFallbackQuestions(job, count) {
  const fallbacks = [];
  const isTechnical = job.isTechnicalRole || checkIfTechnicalRole(job);
  
  for (let i = 0; i < count; i++) {
    if (isTechnical) {
      fallbacks.push({
        id: `fallback_tech_${i}`,
        question: `Describe your experience with ${job.requiredSkills?.[0] || 'the main technology'} mentioned in this role.`,
        type: 'technical',
        difficulty: 'intermediate',
        keyPoints: ['Experience level', 'Practical application', 'Problem solving'],
        points: 10,
        timeLimit: 5,
        source: 'fallback'
      });
    } else {
      fallbacks.push({
        id: `fallback_general_${i}`,
        question: `Why are you interested in working at ${job.company} in the ${job.industry} industry?`,
        type: 'behavioral',
        difficulty: 'basic',
        keyPoints: ['Company research', 'Industry knowledge', 'Motivation'],
        points: 10,
        timeLimit: 3,
        source: 'fallback'
      });
    }
  }
  
  return fallbacks;
}

// Generate fallback questions for specific type
function generateFallbackQuestionsForType(job, type, count) {
  // Implementation similar to generateFallbackQuestions but type-specific
  return generateFallbackQuestions(job, count).map(q => ({ ...q, type }));
}

// Helper to check if role is technical
function checkIfTechnicalRole(job) {
  const technicalKeywords = [
    'developer', 'engineer', 'programmer', 'software', 'data scientist', 
    'devops', 'sre', 'architect', 'coding', 'technical', 'java', 
    'python', 'javascript', 'react', 'angular', 'vue', 'node', 
    'aws', 'azure', 'cloud', 'database', 'sql', 'fullstack'
  ];
  
  const jobText = `${job.title} ${job.description} ${Array.isArray(job.requiredSkills) ? job.requiredSkills.join(' ') : job.requiredSkills || ''}`.toLowerCase();
  
  return technicalKeywords.some(keyword => jobText.includes(keyword));
}

export const config = {
  api: {
    bodyParser: true,
  },
};
