// File: pages/api/interview/questions.js

import { MongoClient, ObjectId } from 'mongodb';
import { OpenAI } from 'openai';
import axios from 'axios';

// Initialize API clients
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
const uri = process.env.MONGODB_URI;

// API configurations with response caching for faster repeat queries
const apiCalls = new Map(); // Cache for API calls
const apiConfigs = {
  openai: {
    enabled: true,
    name: 'OpenAI',
    cacheTimeout: 3600000 // 1 hour cache
  },
  gemini: {
    enabled: !!process.env.GEMINI_API_KEY,
    name: 'Google Gemini',
    endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent',
    formatRequest: (prompt) => ({
      contents: [{ parts: [{ text: prompt }] }],
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 8192
      }
    }),
    parseResponse: (response) => {
      return response.data.candidates[0].content.parts[0].text;
    },
    cacheTimeout: 3600000 // 1 hour cache
  }
};

// Helper to clean Markdown and repair minor formatting issues
function extractJson(text) {
  try {
    const match = text.match(/```json([\s\S]*?)```/i);
    const raw = match ? match[1].trim() : text.trim();

    // Fix common malformed keys (e.g., type: -> "type":)
    const fixed = raw.replace(/([{,])\s*(\w+)\s*:/g, '$1 "$2":');
    return JSON.parse(fixed);
  } catch (err) {
    console.warn("❌ Failed to parse JSON:", err.message);
    return [];
  }
}

// Function to call an API with retries and caching for faster responses
async function callAPI(provider, prompt, maxRetries = 3) {
  const config = apiConfigs[provider];
  let retries = 0;

  // Skip if not enabled
  if (!config.enabled) {
    console.log(`⏭️ Skipping ${config.name} - not enabled`);
    return [];
  }

  // Create a cache key based on provider and prompt
  const cacheKey = `${provider}-${prompt.substring(0, 100)}`;
  
  // Check if we have a cached response
  const cachedCall = apiCalls.get(cacheKey);
  if (cachedCall && (Date.now() - cachedCall.timestamp < config.cacheTimeout)) {
    console.log(`✅ Using cached response for ${config.name}`);
    return cachedCall.data;
  }

  while (retries < maxRetries) {
    try {
      console.log(`🔄 Calling ${config.name} API...`);
      
      let response;
      let content;
      let parsed;
      
      if (provider === 'openai') {
        // Use the OpenAI SDK
        response = await openai.chat.completions.create({
          model: 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.7,
        });
        
        content = response.choices?.[0]?.message?.content || '';
        parsed = extractJson(content);
        
        if (Array.isArray(parsed)) {
          const result = parsed.map(q => ({...q, source: provider}));
          
          // Cache the result
          apiCalls.set(cacheKey, {
            timestamp: Date.now(),
            data: result
          });
          
          return result;
        }
        return [];
      } 
      else if (provider === 'gemini') {
        // Google's Gemini API call with timeout
        const apiKey = process.env.GEMINI_API_KEY;
        const url = `${config.endpoint}?key=${apiKey}`;
        
        console.log(`🔍 Using Gemini endpoint: ${config.endpoint}`);
        
        // Add timeout to prevent hanging requests
        response = await axios.post(
          url,
          config.formatRequest(prompt),
          { 
            headers: { 'Content-Type': 'application/json' },
            timeout: 30000 // 30 second timeout
          }
        );
        
        // Parse the response for non-OpenAI providers
        const textContent = config.parseResponse(response);
        const parsed = extractJson(textContent);
        
        if (Array.isArray(parsed)) {
          const result = parsed.map(q => ({...q, source: provider}));
          
          // Cache the result
          apiCalls.set(cacheKey, {
            timestamp: Date.now(),
            data: result
          });
          
          return result;
        }
        return [];
      }
      
    } catch (error) {
      retries++;
      console.error(`❌ Error calling ${config.name} API (attempt ${retries}/${maxRetries}):`, error.message);
      
      if (retries >= maxRetries) {
        console.error(`❌ Failed to call ${config.name} API after ${maxRetries} attempts`);
        return [];
      }
      
      // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retries)));
    }
  }
  
  return [];
}

// Function to evaluate an answer
export async function evaluateAnswer(question, candidateAnswer) {
  const prompt = `
    Evaluate the following candidate's answer to an interview question.
    
    QUESTION: ${question.question}
    
    EXPECTED ANSWER: ${question.answer || "N/A"}
    
    CANDIDATE'S ANSWER: ${candidateAnswer}
    
    Evaluate the answer on a scale of 1-10 based on:
    1. Correctness (accuracy of the solution)
    2. Completeness (addresses all parts of the question)
    3. Communication (clarity of explanation)
    
    Provide a JSON object with your evaluation, including:
    {
      "overallScore": [1-10],
      "correctnessScore": [1-10],
      "completenessScore": [1-10],
      "communicationScore": [1-10],
      "feedback": "detailed feedback with suggestions for improvement",
      "strengths": ["list", "of", "strengths"],
      "weaknesses": ["list", "of", "areas", "to", "improve"]
    }
  `;

  // Get enabled API providers
  const enabledProviders = Object.keys(apiConfigs).filter(key => apiConfigs[key].enabled);
  const evaluations = [];

  // Call each API for evaluation
  for (const provider of enabledProviders) {
    try {
      const results = await callAPI(provider, prompt, 2);
      if (results.length > 0 && typeof results[0] === 'object') {
        evaluations.push({
          ...results[0],
          evaluator: provider
        });
      }
    } catch (error) {
      console.error(`❌ Error evaluating with ${provider}:`, error.message);
    }
  }

  // Calculate cumulative scores if we have evaluations
  if (evaluations.length > 0) {
    const aggregateScores = {
      overallScore: 0,
      correctnessScore: 0,
      completenessScore: 0,
      communicationScore: 0
    };

    // Sum up all scores
    evaluations.forEach(evaluation => {
      aggregateScores.overallScore += evaluation.overallScore || 0;
      aggregateScores.correctnessScore += evaluation.correctnessScore || 0;
      aggregateScores.completenessScore += evaluation.completenessScore || 0;
      aggregateScores.communicationScore += evaluation.communicationScore || 0;
    });

    // Calculate averages
    const numEvaluations = evaluations.length;
    Object.keys(aggregateScores).forEach(key => {
      aggregateScores[key] = parseFloat((aggregateScores[key] / numEvaluations).toFixed(1));
    });

    // Combine feedback and strengths/weaknesses
    const allFeedback = evaluations.map(e => e.feedback).join('\n\n');
    const allStrengths = [...new Set(evaluations.flatMap(e => e.strengths || []))];
    const allWeaknesses = [...new Set(evaluations.flatMap(e => e.weaknesses || []))];

    return {
      cumulativeScores: aggregateScores,
      individualEvaluations: evaluations,
      combinedFeedback: allFeedback,
      strengths: allStrengths,
      weaknesses: allWeaknesses
    };
  }

  return {
    error: "Failed to get evaluations",
    individualEvaluations: evaluations
  };
}
