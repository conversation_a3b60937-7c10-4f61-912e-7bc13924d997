// File: pages/api/interview/questions.js

import { OpenAI } from 'openai';

// Initialize API clients
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// Function to evaluate an answer
export async function evaluateAnswer(question, candidateAnswer) {
  const prompt = `
    Evaluate the following candidate's answer to an interview question.
    
    QUESTION: ${question.question}
    
    EXPECTED ANSWER: ${question.answer || "N/A"}
    
    CANDIDATE'S ANSWER: ${candidateAnswer}
    
    Evaluate the answer on a scale of 1-10 based on:
    1. Correctness (accuracy of the solution)
    2. Completeness (addresses all parts of the question)
    3. Communication (clarity of explanation)
    
    Provide a JSON object with your evaluation, including:
    {
      "overallScore": [1-10],
      "correctnessScore": [1-10],
      "completenessScore": [1-10],
      "communicationScore": [1-10],
      "feedback": "detailed feedback with suggestions for improvement",
      "strengths": ["list", "of", "strengths"],
      "weaknesses": ["list", "of", "areas", "to", "improve"]
    }
  `;

  try {
    const response = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7,
    });
    
    const content = response.choices?.[0]?.message?.content || '';
    
    // Try to parse JSON from the response
    try {
      const match = content.match(/```json([\s\S]*?)```/i);
      const raw = match ? match[1].trim() : content.trim();
      const parsed = JSON.parse(raw);
      
      return {
        cumulativeScores: {
          overallScore: parsed.overallScore || 0,
          correctnessScore: parsed.correctnessScore || 0,
          completenessScore: parsed.completenessScore || 0,
          communicationScore: parsed.communicationScore || 0
        },
        individualEvaluations: [parsed],
        combinedFeedback: parsed.feedback || '',
        strengths: parsed.strengths || [],
        weaknesses: parsed.weaknesses || []
      };
    } catch (parseError) {
      console.error('Failed to parse evaluation JSON:', parseError);
      return {
        error: "Failed to parse evaluation",
        rawResponse: content
      };
    }
  } catch (error) {
    console.error('Error evaluating answer:', error);
    return {
      error: "Failed to get evaluation",
      message: error.message
    };
  }
}
