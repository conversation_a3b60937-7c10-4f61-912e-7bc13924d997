// File: pages/api/interview/disqualify.js
import { MongoClient, ObjectId } from 'mongodb';

export default async function handler(req, res) {
  console.log('=== Interview Disqualification API ===');
  
  if (req.method !== 'POST') {
    console.log('Method not allowed:', req.method);
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  const { applicationId, reason, violations, timestamp } = req.body;
  
  console.log('Disqualification request:', {
    applicationId,
    reason,
    violations: violations ? Object.keys(violations) : 'none',
    timestamp
  });

  if (!applicationId) {
    return res.status(400).json({ success: false, message: 'Application ID is required' });
  }

  if (!reason) {
    return res.status(400).json({ success: false, message: 'Disqualification reason is required' });
  }

  // MongoDB connection string
  const uri = process.env.MONGODB_URI;
  if (!uri) {
    return res.status(500).json({ success: false, message: 'Database connection string not configured' });
  }

  let client;
  try {
    client = await MongoClient.connect(uri);
    const db = client.db();

    // Convert application ID to MongoDB ObjectId
    let appObjectId;
    try {
      appObjectId = new ObjectId(applicationId);
    } catch (err) {
      console.error('Invalid ObjectId:', applicationId);
      return res.status(400).json({ success: false, message: 'Invalid application ID format' });
    }

    // Process violations data - handle both object and array formats
    let violationsArray = [];
    let violationsObject = {};
    let totalViolations = 0;
    
    if (violations) {
      console.log('Processing violations:', typeof violations, violations);
      
      if (typeof violations === 'object' && violations.warningViolations) {
        // Handle SecurityManager format
        violationsObject = violations.warningViolations;
        
        // Convert to array format
        violationsArray = Object.entries(violations.warningViolations)
          .filter(([_, count]) => count > 0)
          .map(([type, count]) => ({
            type,
            count,
            message: `${type}: ${count} violation(s)`,
            timestamp: timestamp || new Date().toISOString()
          }));
          
        totalViolations = Object.values(violations.warningViolations).reduce((sum, count) => sum + count, 0);
      } else if (typeof violations === 'object' && !Array.isArray(violations)) {
        // Handle object format
        violationsObject = violations;
        
        violationsArray = Object.entries(violations)
          .filter(([_, count]) => count > 0)
          .map(([type, count]) => ({
            type,
            count,
            message: `${type}: ${count} violation(s)`,
            timestamp: timestamp || new Date().toISOString()
          }));
          
        totalViolations = Object.values(violations).reduce((sum, count) => sum + count, 0);
      } else if (Array.isArray(violations)) {
        // Handle array format
        violationsArray = violations;
        violationsObject = violations.reduce((acc, v) => {
          acc[v.type] = v.count || 1;
          return acc;
        }, {});
        totalViolations = violations.length;
      }
    }
    
    console.log('Processed violations:', {
      array: violationsArray,
      object: violationsObject,
      total: totalViolations
    });

    // Get current timestamp
    const disqualificationTime = new Date();

    // Update application status to disqualified
    const updateData = { 
      disqualified: true,
      disqualificationReason: reason,
      disqualificationTimestamp: disqualificationTime,
      interviewStatus: 'disqualified',
      violationsData: {
        summary: violationsObject,
        details: violationsArray,
        total: totalViolations
      }
    };
    
    // Only add violations field if we have violations data
    if (violationsArray.length > 0) {
      updateData.violations = violationsArray;
    }
    
    console.log('Updating application with:', updateData);
    
    const updateResult = await db.collection('applications').updateOne(
      { _id: appObjectId },
      { $set: updateData }
    );

    if (updateResult.matchedCount === 0) {
      console.error('Application not found:', applicationId);
      return res.status(404).json({ success: false, message: 'Application not found' });
    }

    if (updateResult.modifiedCount === 0) {
      console.log('Application already disqualified:', applicationId);
      return res.status(200).json({ 
        success: true, 
        message: 'Application already disqualified',
        alreadyDisqualified: true
      });
    }

    // Create a record in interviewResults collection
    const interviewResult = {
      applicationId: appObjectId,
      result: 'disqualified',
      reason: reason,
      violations: violationsArray,
      violationsSummary: violationsObject,
      totalViolations: totalViolations,
      timestamp: disqualificationTime
    };
    
    console.log('Creating interview result:', interviewResult);
    
    await db.collection('interviewResults').insertOne(interviewResult);

    console.log('✅ Disqualification completed successfully');

    // Return success response
    return res.status(200).json({ 
      success: true, 
      message: 'Candidate disqualified successfully',
      applicationId,
      reason,
      totalViolations,
      timestamp: disqualificationTime 
    });

  } catch (error) {
    console.error('❌ Error in disqualify API:', error);
    return res.status(500).json({ 
      success: false, 
      message: `Server error: ${error.message}`,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  } finally {
    if (client) {
      await client.close();
    }
  }
}
