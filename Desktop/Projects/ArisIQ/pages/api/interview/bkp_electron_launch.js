// File: pages/api/interview/launch.js
import { MongoClient, ObjectId } from 'mongodb';
import { spawn } from 'child_process';
import path from 'path';

const uri = process.env.MONGODB_URI;

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  const { applicationId } = req.body;
  
  if (!applicationId) {
    return res.status(400).json({ success: false, message: 'applicationId is required' });
  }

  // Add debug logging
  console.log(`Starting interview launch process for application: ${applicationId}`);

  // Detect browser info from user agent
  const userAgent = req.headers['user-agent'] || '';
  let browserName = 'unknown';

  if (userAgent.includes('Chrome')) browserName = 'chrome';
  else if (userAgent.includes('Firefox')) browserName = 'firefox';
  else if (userAgent.includes('Safari')) browserName = 'safari';
  else if (userAgent.includes('Edge')) browserName = 'edge';

  try {
    // Connect to MongoDB to validate the application exists and can take interview
    console.log(`Connecting to MongoDB with URI: ${uri ? 'URI exists' : 'URI missing'}`);
    const client = await MongoClient.connect(uri);
    const db = client.db();
    
    // Create valid ObjectId from application ID
    let appObjectId;
    try {
      appObjectId = new ObjectId(applicationId);
    } catch (idErr) {
      console.error(`Invalid ObjectId format: ${applicationId}`);
      await client.close();
      return res.status(400).json({ 
        success: false, 
        message: 'Invalid application ID format' 
      });
    }

    // Fetch the application with more details for debugging
    console.log(`Fetching application with ID: ${applicationId}`);
    const app = await db.collection('applications').findOne({ 
      _id: appObjectId 
    });

    // Enhanced error handling for application not found
    if (!app) {
      console.error(`Application not found with ID: ${applicationId}`);
      await client.close();
      return res.status(404).json({ success: false, message: 'Application not found' });
    }

    // Log all application properties for debugging
    console.log('Application details:');
    console.log(`- ID: ${app._id}`);
    console.log(`- Job ID: ${app.jobId}`);
    console.log(`- Can Take Interview: ${app.canTakeInterview}`);
    console.log(`- Interview Status: ${app.interviewStatus || 'Not set'}`);
    console.log(`- Score: ${app.score || 'Not set'}`);
    
    // If canTakeInterview is undefined, automatically set it to true for applications with score >= 80
    if (app.canTakeInterview === undefined && app.score >= 80) {
      console.log(`Setting canTakeInterview=true for high-scoring application (score: ${app.score})`);
      await db.collection('applications').updateOne(
        { _id: appObjectId },
        { $set: { canTakeInterview: true }}
      );
      app.canTakeInterview = true;
    }

    // More detailed error for interview not available
    if (!app.canTakeInterview) {
      let message = 'Interview not available for this application';
      
      // Add more context to the error message
      if (app.score !== undefined) {
        message += ` (score: ${app.score})`;
        if (app.score < 80) {
          message += ' - requires score of 80+ or recruiter approval';
        }
      }
      
      if (app.interviewStatus) {
        message += ` - current status: ${app.interviewStatus}`;
      }
      
      console.error(message);
      await client.close();
      return res.status(403).json({ 
        success: false, 
        message: message
      });
    }

    // Update application status
    console.log(`Updating application status to 'starting'`);
    await db.collection('applications').updateOne(
      { _id: appObjectId },
      { $set: { interviewStatus: 'starting', interviewStartedAt: new Date() }}
    );

    await client.close();

    // ENHANCED: First check if Electron app is already running via ping
    try {
      console.log('Checking if Electron app is already running...');
      const pingResponse = await fetch('http://localhost:45678/ping', { 
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      
      if (pingResponse.ok) {
        console.log('✅ ArisIQ app is already running, using protocol handler');
        // If app is running, use protocol handler
        return res.status(200).json({ 
          success: true, 
          message: 'ArisIQ app is running',
          useProtocol: true,
          protocol: `symplihire://interview?id=${applicationId}`
        });
      }
    } catch (pingError) {
      console.log('ArisIQ app is not running or ping failed, attempting to launch');
    }

    // Launch the Electron app if not already running
    const electronPath = process.env.ELECTRON_PATH || 'electron';
    const mainJsPath = path.join(process.cwd(), 'main.js');
    
    console.log(`Launching Electron app with path: ${mainJsPath}`);
    try {
      // Spawn the Electron process with browser info
      const proc = spawn(electronPath, [mainJsPath, applicationId, `browser=${browserName}`], {
        detached: true,
        stdio: 'ignore'
      });
      
      // Unref the process so it runs independently
      proc.unref();
      
      return res.status(200).json({ 
        success: true, 
        message: 'Interview launched successfully',
        launchMethod: 'direct'
      });
    } catch (spawnError) {
      console.error('Failed to spawn Electron process:', spawnError);
      
      // If direct launch fails, return information for protocol handler
      return res.status(200).json({ 
        success: true, 
        message: 'Please use the ArisIQ desktop app',
        useProtocol: true,
        protocol: `symplihire://interview?id=${applicationId}`
      });
    }
  } catch (err) {
    console.error('❌ Error launching interview:', err);
    return res.status(500).json({ success: false, message: err.message });
  }
}

export const config = {
  api: {
    bodyParser: true,
  },
};
