// File: pages/api/interview/abort.js
import { MongoClient, ObjectId } from 'mongodb';

const uri = process.env.MONGODB_URI;

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  const { applicationId } = req.body;
  
  if (!applicationId) {
    return res.status(400).json({ success: false, message: 'applicationId is required' });
  }

  try {
    const client = await MongoClient.connect(uri);
    const db = client.db();

    // Update application status to aborted
    await db.collection('applications').updateOne(
      { _id: new ObjectId(applicationId) },
      { 
        $set: { 
          interviewStatus: 'aborted', 
          interviewAbortedAt: new Date()
        }
      }
    );

    await client.close();

    return res.status(200).json({ 
      success: true, 
      message: 'Interview aborted successfully'
    });
  } catch (err) {
    console.error('Error aborting interview:', err);
    return res.status(500).json({ success: false, message: err.message });
  }
}

export const config = {
  api: {
    bodyParser: true,
  },
};
