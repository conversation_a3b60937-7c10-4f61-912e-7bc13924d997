// pages/api/interview/runcode.js
// This file handles code execution in a safer way without python-shell

import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid'; // You'll need to install this: npm install uuid

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  try {
    const { code, language = 'javascript', questionId } = req.body;

    if (!code) {
      return res.status(400).json({ success: false, error: 'No code provided' });
    }

    // For now, we only support JavaScript
    if (language.toLowerCase() !== 'javascript') {
      return res.status(200).json({ 
        success: true, 
        output: `Note: Only JavaScript execution is currently supported. Your ${language} code has been saved but not executed.` 
      });
    }

    // Create a temporary file with the code
    const fileName = `${uuidv4()}.js`;
    const filePath = path.join(process.cwd(), 'tmp', fileName);
    
    // Make sure tmp directory exists
    const tmpDir = path.join(process.cwd(), 'tmp');
    if (!fs.existsSync(tmpDir)) {
      fs.mkdirSync(tmpDir, { recursive: true });
    }

    // Write the code to a file
    fs.writeFileSync(filePath, code);

    // Set a timeout for execution (5 seconds)
    const timeoutMs = 5000;
    
    // Execute the code with a timeout
    return new Promise((resolve) => {
      const child = exec(`node ${filePath}`, { timeout: timeoutMs }, (error, stdout, stderr) => {
        try {
          // Delete the temporary file
          fs.unlinkSync(filePath);
        } catch (e) {
          console.error('Error deleting temp file:', e);
        }

        if (error) {
          // If the error is due to timeout
          if (error.killed && error.signal === 'SIGTERM') {
            resolve(res.status(200).json({ 
              success: false, 
              output: 'Execution timed out. Your code took too long to run.'
            }));
            return;
          }

          // Other execution errors
          resolve(res.status(200).json({ 
            success: false, 
            output: `Error: ${stderr || error.message}`
          }));
          return;
        }

        // Success case
        resolve(res.status(200).json({ 
          success: true, 
          output: stdout || 'Code executed successfully (no output)'
        }));
      });
    });
  } catch (error) {
    console.error('Error running code:', error);
    return res.status(500).json({ 
      success: false, 
      output: `Server error: ${error.message}`
    });
  }
}
