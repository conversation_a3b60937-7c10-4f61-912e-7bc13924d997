// pages/api/interview/upload-recording.js
import formidable from 'formidable';
import fs from 'fs';
import path from 'path';

// Disable the default body parser to handle form data
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // Parse form data
    const form = new formidable.IncomingForm();
    form.uploadDir = path.join(process.cwd(), 'public', 'uploads');
    form.keepExtensions = true;
    
    // Create upload directory if it doesn't exist
    if (!fs.existsSync(form.uploadDir)) {
      fs.mkdirSync(form.uploadDir, { recursive: true });
    }
    
    // Parse the form
    const [fields, files] = await new Promise((resolve, reject) => {
      form.parse(req, (err, fields, files) => {
        if (err) reject(err);
        resolve([fields, files]);
      });
    });
    
    const { applicationId, questionIndex } = fields;
    const videoFile = files.video;
    
    // For development, just log that we received the file
    console.log('Received recording:', {
      applicationId,
      questionIndex,
      fileName: videoFile.originalFilename,
      size: videoFile.size,
      path: videoFile.filepath
    });
    
    // TODO: In production, you would store file references in your database
    // and potentially upload to cloud storage
    
    return res.status(200).json({ 
      success: true, 
      message: 'Recording uploaded successfully' 
    });
  } catch (error) {
    console.error('Error uploading recording:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to upload recording' 
    });
  }
}
