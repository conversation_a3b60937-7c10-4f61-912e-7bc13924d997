// File: pages/api/recruiter/update-application-status.js

import { MongoClient, ObjectId } from "mongodb";
const uri = process.env.MONGODB_URI;

export default async function handler(req, res) {
  if (req.method !== "PUT") {
    return res.status(405).json({ success: false, message: "Method not allowed" });
  }

  // Get the application ID and new status from request body
  const { applicationId, status } = req.body;

  if (!applicationId || !status) {
    return res.status(400).json({
      success: false,
      message: "Application ID and status are required"
    });
  }

  try {
    const client = await MongoClient.connect(uri);
    const db = client.db();

    // Convert string ID to ObjectId if needed
    let appIdQuery;
    try {
      appIdQuery = new ObjectId(applicationId);
    } catch (e) {
      // If it's not a valid ObjectId, use as is
      appIdQuery = applicationId;
    }

    // Update the application status
    const result = await db.collection("applications").updateOne(
      { _id: appIdQuery },
      { $set: { status: status, updatedAt: new Date() } }
    );

    // Get the updated application for the response
    const updatedApplication = await db.collection("applications").findOne(
      { _id: appIdQuery }
    );

    await client.close();

    if (result.matchedCount === 0) {
      return res.status(404).json({
        success: false,
        message: "Application not found"
      });
    }

    return res.status(200).json({
      success: true,
      message: "Application status updated successfully",
      application: updatedApplication
    });
  } catch (error) {
    console.error("Error updating application status:", error);
    return res.status(500).json({
      success: false,
      message: error.message
    });
  }
}
