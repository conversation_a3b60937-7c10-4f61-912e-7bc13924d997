// File: pages/api/recruiter/schedule-final-interview.js

import nodemailer from 'nodemailer';
import { MongoClient, ObjectId } from 'mongodb';

const uri = process.env.MONGODB_URI;

const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: parseInt(process.env.SMTP_PORT),
  secure: false,
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  const { 
    applicationId, 
    interviewDate, 
    interviewTime, 
    interviewType, // 'remote', 'in-person', or 'phone'
    location, // for in-person interviews
    meetingLink, // for remote interviews
    additionalNotes
  } = req.body;
  
  if (!applicationId || !interviewDate || !interviewTime || !interviewType) {
    return res.status(400).json({ 
      success: false, 
      message: 'Required fields missing: applicationId, interviewDate, interviewTime, and interviewType are required' 
    });
  }

  try {
    const client = await MongoClient.connect(uri);
    const db = client.db();

    // Validate application exists and is eligible for final interview
    const app = await db.collection('applications').findOne({ 
      _id: new ObjectId(applicationId)
    });
    
    if (!app) {
      await client.close();
      return res.status(404).json({ success: false, message: 'Application not found' });
    }

    // Check if the candidate has completed the screening interview
    if (app.interviewStatus !== 'completed') {
      await client.close();
      return res.status(400).json({ 
        success: false, 
        message: 'Candidate has not completed the screening interview yet'
      });
    }

    // Fetch job details
    const job = await db.collection('jobs').findOne({ _id: new ObjectId(app.jobId) });
    if (!job) {
      await client.close();
      return res.status(404).json({ success: false, message: 'Job not found' });
    }

    // Format interview details
    const interviewDateTime = `${interviewDate} at ${interviewTime}`;
    
    // Location or meeting information based on interview type
    let locationInfo = '';
    if (interviewType === 'remote') {
      locationInfo = `This will be a remote interview. Meeting link: ${meetingLink}`;
    } else if (interviewType === 'in-person') {
      locationInfo = `This will be an in-person interview at: ${location}`;
    } else if (interviewType === 'phone') {
      locationInfo = `This will be a phone interview. We will call you at the number provided in your application.`;
    }

    // Update application status
    await db.collection('applications').updateOne(
      { _id: new ObjectId(applicationId) },
      { 
        $set: { 
          status: 'final_interview',
          finalInterviewScheduled: true,
          finalInterviewDetails: {
            date: interviewDate,
            time: interviewTime,
            type: interviewType,
            location: location || null,
            meetingLink: meetingLink || null,
            additionalNotes: additionalNotes || null,
            scheduledAt: new Date()
          }
        } 
      }
    );

    // Send email notification to candidate
    const mailOptions = {
      from: `"${job.company} Recruiting" <${process.env.EMAIL_USER}>`,
      to: app.email,
      subject: `Final Interview for ${job.title} at ${job.company}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Final Interview Invitation</h2>
          <p>Hello ${app.firstName || app.fullName},</p>
          
          <p>Congratulations! Based on your successful completion of the screening interview, we're pleased to invite you to a final interview for the <strong>${job.title}</strong> position at <strong>${job.company}</strong>.</p>
          
          <div style="background-color: #f8f9fa; border-left: 4px solid #4285f4; padding: 15px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Interview Details</h3>
            <p><strong>Date and Time:</strong> ${interviewDateTime}</p>
            <p><strong>Format:</strong> ${interviewType.charAt(0).toUpperCase() + interviewType.slice(1)}</p>
            <p>${locationInfo}</p>
            ${additionalNotes ? `<p><strong>Additional Notes:</strong> ${additionalNotes}</p>` : ''}
          </div>
          
          <p>Please confirm your attendance by replying to this email. If you need to reschedule, please let us know as soon as possible.</p>
          
          <h3>Preparation Tips</h3>
          <ul>
            <li>Research ${job.company} and our recent projects</li>
            <li>Review the job description and prepare examples that demonstrate your relevant experience</li>
            <li>Prepare questions to ask the interviewer</li>
            ${interviewType === 'remote' ? '<li>Test your camera, microphone, and internet connection before the interview</li>' : ''}
            ${interviewType === 'in-person' ? '<li>Plan your journey to arrive 10-15 minutes early</li>' : ''}
          </ul>
          
          <p>We look forward to speaking with you!</p>
          
          <p>Best regards,<br/>The Recruiting Team at ${job.company}</p>
        </div>
      `,
    };

    await transporter.sendMail(mailOptions);
    await client.close();

    return res.status(200).json({ 
      success: true, 
      message: 'Final interview scheduled and candidate notified successfully' 
    });
  } catch (err) {
    console.error("Error scheduling final interview:", err);
    return res.status(500).json({ 
      success: false, 
      message: err.message 
    });
  }
}

export const config = {
  api: {
    bodyParser: true,
  },
};
