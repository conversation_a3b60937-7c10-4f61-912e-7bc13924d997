// File: pages/api/recruiter/send-screening-invite.js

import nodemailer from 'nodemailer';
import { MongoClient, ObjectId } from 'mongodb';

const uri = process.env.MONGODB_URI;

const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: parseInt(process.env.SMTP_PORT),
  secure: false,
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  const { applicationId } = req.body;
  if (!applicationId) {
    return res.status(400).json({ success: false, message: 'applicationId is required' });
  }

  try {
    const client = await MongoClient.connect(uri);
    const db = client.db();

    const app = await db.collection('applications').findOne({ _id: new ObjectId(applicationId) });
    if (!app) {
      return res.status(404).json({ success: false, message: 'Application not found' });
    }

    const job = await db.collection('jobs').findOne({ _id: new ObjectId(app.jobId) });
    if (!job) {
      return res.status(404).json({ success: false, message: 'Job not found' });
    }

    // Update application to allow interview
    await db.collection('applications').updateOne(
      { _id: new ObjectId(applicationId) },
      { $set: { 
          canTakeInterview: true,
          screeningInviteSent: true,
          screeningInviteSentAt: new Date(),
          status: 'screening' // Update status to screening
        } 
      }
    );

    // Email Content
    const mailOptions = {
      from: `"ArisIQ" <${process.env.EMAIL_USER}>`,
      to: app.email,
      subject: `Screening Interview Invitation for ${job.title} at ${job.company}`,
      html: `
        <p>Hello ${app.firstName || app.fullName},</p>

        <p>Thank you for your application for the <strong>${job.title}</strong> position at <strong>${job.company}</strong>.</p>
        
        <p>We've reviewed your application and would like to invite you to take a screening interview as the next step in our selection process.</p>

        <h4>🎯 Interview Instructions:</h4>
        <ul>
          <li>Use a desktop or laptop with a webcam and microphone.</li>
          <li>Webcam and microphone access is mandatory.</li>
          <li>Switch to single screen — dual monitors are not allowed.</li>
          <li>Stay in fullscreen mode — pressing ESC will disqualify you.</li>
          <li>Do not switch tabs or minimize your window.</li>
          <li>Copy/paste functionality is disabled.</li>
          <li>Use of developer tools (F12) is monitored.</li>
          <li>Your webcam and microphone will be monitored throughout the session.</li>
          <li><strong>Recommended Browser:</strong> Google Chrome or Microsoft Edge.</li>
        </ul>

        <p><strong>To start the interview:</strong></p>
        <ol>
          <li>Allow camera and mic access when prompted by the browser.</li>
          <li>Review all instructions and check the acknowledgment box.</li>
          <li>Click "Start Interview" when you're ready.</li>
        </ol>

        <p><em>Click the ⓘ or 🔒 icon next to the browser URL and enable permissions for mic and camera.</em></p>

        <p style="margin-top: 20px;">
          <a href="${process.env.NEXTAUTH_URL}/candidate-dashboard" 
            style="background-color: #2563eb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 6px; display: inline-block;">
            👉 Click here to begin your screening interview
          </a>
        </p>

        <p>Good luck!<br/>— ${job.company} Recruiting Team</p>
      `,
    };

    await transporter.sendMail(mailOptions);

    res.status(200).json({ success: true, message: 'Screening invitation sent and interview access enabled' });
  } catch (err) {
    console.error("Email error:", err);
    res.status(500).json({ success: false, message: err.message });
  }
}

export const config = {
  api: {
    bodyParser: true,
  },
};
