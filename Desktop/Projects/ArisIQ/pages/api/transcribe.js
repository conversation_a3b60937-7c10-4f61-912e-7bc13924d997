// File: pages/api/transcribe.js

import fs from 'fs';
import path from 'path';
import formidable from 'formidable';
import FormData from 'form-data';
import fetch from 'node-fetch';

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  console.log('Transcribe API called');
  
  // Create upload directory if it doesn't exist
  const uploadDir = path.join(process.cwd(), '/tmp');
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }

  const form = new formidable.IncomingForm({
    keepExtensions: true,
    uploadDir: uploadDir,
    maxFileSize: 30 * 1024 * 1024, // 30MB limit
  });

  form.parse(req, async (err, fields, files) => {
    if (err) {
      console.error('Form parse error:', err);
      return res.status(500).json({ error: `File upload failed: ${err.message}` });
    }

    const file = files.file;

    if (!file || !file.filepath) {
      console.error('No file uploaded or filepath missing');
      return res.status(400).json({ error: 'No file uploaded or invalid file' });
    }
    
    console.log(`File received: ${file.originalFilename || 'unnamed'} (${file.size} bytes, ${file.mimetype})`);

    try {
      // Read the file into a buffer instead of using a stream
      const fileBuffer = fs.readFileSync(file.filepath);
      
      if (fileBuffer.length === 0) {
        throw new Error('Empty audio file received');
      }
      
      console.log(`File read into buffer: ${fileBuffer.length} bytes`);
      
      // Create a proper multipart form for the Whisper API
      const formData = new FormData();
      formData.append('file', fileBuffer, {
        filename: file.originalFilename || 'audio.webm',
        contentType: file.mimetype || 'audio/webm'
      });
      formData.append('model', 'whisper-1');
      
      // Add optional parameters if provided in fields
      if (fields.language) {
        formData.append('language', fields.language);
      }
      
      if (fields.prompt) {
        formData.append('prompt', fields.prompt);
      }
      
      // Add temperature parameter for better recognition
      formData.append('temperature', fields.temperature || '0.2');

      console.log('Sending request to OpenAI Whisper API');
      console.log(`API Key configured: ${process.env.OPENAI_API_KEY ? 'Yes' : 'No'}`);
      
      // Check if API key is available
      if (!process.env.OPENAI_API_KEY) {
        throw new Error('OpenAI API key is missing');
      }

      // Call the OpenAI API with timeout and error handling
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout
      
      try {
        const response = await fetch('https://api.openai.com/v1/audio/transcriptions', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`
          },
          body: formData,
          signal: controller.signal
        });
        
        clearTimeout(timeoutId);

        if (!response.ok) {
          // Try to get detailed error information
          let errorDetails = '';
          try {
            const errorJson = await response.json();
            errorDetails = JSON.stringify(errorJson);
          } catch (e) {
            errorDetails = await response.text();
          }
          
          console.error(`OpenAI API error (${response.status}):`, errorDetails);
          throw new Error(`OpenAI API error: ${response.status} - ${errorDetails}`);
        }

        const result = await response.json();
        console.log('Transcription successful');

        // Return the transcription result with additional metadata
        return res.json({
          text: result.text || '',
          applicationId: fields?.applicationId,
          questionId: fields?.questionId,
          timestamp: new Date().toISOString(),
        });
      } finally {
        // Always clean up the temporary file, whether successful or not
        if (file && file.filepath && fs.existsSync(file.filepath)) {
          try {
            fs.unlinkSync(file.filepath);
            console.log('Temporary file cleaned up');
          } catch (unlinkError) {
            console.error('Error deleting temp file:', unlinkError);
          }
        }
      }
    } catch (error) {
      console.error('Transcription error:', error);
      
      // Create a user-friendly error message
      let errorMessage = 'Transcription failed';
      
      if (error.name === 'AbortError') {
        errorMessage = 'Transcription request timed out. Please try with a shorter audio clip.';
      } else if (error.message.includes('413')) {
        errorMessage = 'Audio file too large. Please keep recordings under 25MB.';
      } else if (error.message.includes('429')) {
        errorMessage = 'OpenAI API rate limit exceeded. Please try again later.';
      } else {
        errorMessage = `Transcription failed: ${error.message}`;
      }
      
      return res.status(500).json({ error: errorMessage });
    }
  });
}
