// File: pages/api/fix-my-account.js
// Simple one-time fix for your specific account

import connectToDatabase from "../../lib/mongodb";
import mongoose from "mongoose";

const UserSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String },
  userType: { type: String, enum: ['recruiter', 'candidate'], required: true },
  company: { type: String },
  provider: { type: String, default: 'credentials' },
  image: { type: String },
  location: { type: String },
  industry: { type: String },
  website: { type: String },
  description: { type: String },
  status: { 
    type: String, 
    enum: ['active', 'suspended'], 
    default: 'active'
  },
}, {
  timestamps: true
});

const User = mongoose.models.User || mongoose.model('User', UserSchema);

export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    await connectToDatabase();
    
    // Update your specific account to recruiter
    const result = await User.findOneAndUpdate(
      { email: "<EMAIL>" },
      { 
        userType: "recruiter",
        company: "ArisIQ" // or whatever your company name should be
      },
      { new: true }
    );

    if (!result) {
      return res.status(404).json({ error: "User not found" });
    }

    res.status(200).json({ 
      message: "Account fixed successfully",
      user: {
        email: result.email,
        userType: result.userType,
        company: result.company
      }
    });

  } catch (error) {
    console.error("Fix account error:", error);
    res.status(500).json({ 
      error: "Internal server error",
      details: error.message
    });
  }
}
