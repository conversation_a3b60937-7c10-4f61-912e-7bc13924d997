// File: pages/api/openai-location.js

import OpenAI from "openai";

const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { query, country, type } = req.body;

  if (!query || !type) {
    return res.status(400).json({ error: "Missing required parameters" });
  }

  try {
    let prompt = "";

    if (type === "country") {
      prompt = `Return only a JSON array of 10 most relevant country names that match "${query}".`;
    } else if (type === "city") {
      if (!country) {
        return res.status(400).json({ error: "Missing 'country' for city suggestion" });
      }
      prompt = `Return only a JSON array of 10 cities in ${country} that closely match "${query}".`;
    } else {
      return res.status(400).json({ error: "Invalid type. Use 'country' or 'city'." });
    }

    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [{ role: "user", content: prompt }],
    });

    const content = completion.choices?.[0]?.message?.content || "[]";

    let suggestions;
    try {
      suggestions = JSON.parse(content);
    } catch (jsonErr) {
      console.error("Failed to parse suggestions:", jsonErr);
      return res.status(500).json({ error: "OpenAI response parsing error." });
    }

    res.status(200).json({ suggestions });
  } catch (error) {
    console.error("OpenAI API error:", error);
    res.status(500).json({ error: "Failed to get suggestions from OpenAI." });
  }
}

