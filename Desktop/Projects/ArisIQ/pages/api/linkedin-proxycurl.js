// File: pages/api/linkedin-proxycurl.js

import axios from 'axios';

// Get your API key from https://nubela.co/proxycurl/
const PROXYCURL_API_KEY = process.env.PROXYCURL_API_KEY;

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const { linkedinUrl } = req.body;
  if (!linkedinUrl) {
    return res.status(400).json({ message: 'Missing LinkedIn URL' });
  }

  try {
    // Ensure URL has proper format
    let fullUrl = linkedinUrl;
    if (!fullUrl.startsWith('http')) {
      fullUrl = `https://${fullUrl}`;
    }

    // Make request to Proxycurl API
    const response = await axios.get('https://nubela.co/proxycurl/api/v2/linkedin', {
      params: {
        url: fullUrl,
        use_cache: 'if-present',
        fallback_to_cache: 'on-error',
      },
      headers: {
        'Authorization': `Bearer ${PROXYCURL_API_KEY}`
      }
    });

    const profileData = response.data;

    // Format the profile data
    const formattedProfile = {
      name: `${profileData.first_name || ''} ${profileData.last_name || ''}`.trim(),
      headline: profileData.headline || 'Not available',
      location: profileData.location_name || 'Not available',
      about: profileData.summary || 'Not available',
      experience: profileData.experiences?.map(exp => ({
        title: exp.title || 'Not available',
        company: exp.company || 'Not available',
        dates: `${exp.starts_at?.year || ''} - ${exp.ends_at?.year || 'Present'}`,
        description: exp.description || 'Not available'
      })) || [],
      education: profileData.education?.map(edu => ({
        school: edu.school || 'Not available',
        degree: edu.degree_name || 'Not available',
        field: edu.field_of_study || 'Not available',
        dates: `${edu.starts_at?.year || ''} - ${edu.ends_at?.year || ''}`
      })) || [],
      skills: profileData.skills?.map(skill => skill.name) || []
    };

    return res.json({
      success: true,
      profile: formattedProfile
    });
  } catch (error) {
    console.error('Proxycurl API error:', error.response?.data || error.message);

    return res.status(500).json({
      success: false,
      message: 'Error fetching LinkedIn profile data',
      error: error.response?.data || error.message
    });
  }
}
