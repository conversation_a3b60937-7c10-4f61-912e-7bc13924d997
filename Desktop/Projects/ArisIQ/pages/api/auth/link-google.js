// File: pages/api/auth/link-google.js
// Link Google account to existing recruiter account

import connectToDatabase from "../../../lib/mongodb";
import mongoose from "mongoose";

const UserSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String },
  userType: { type: String, enum: ['recruiter', 'candidate'], required: true },
  company: { type: String },
  provider: { type: String, default: 'credentials' },
  image: { type: String },
  location: { type: String },
  industry: { type: String },
  website: { type: String },
  description: { type: String },
  status: { 
    type: String, 
    enum: ['active', 'suspended'], 
    default: 'active'
  },
}, {
  timestamps: true
});

const User = mongoose.models.User || mongoose.model('User', UserSchema);

export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { companyEmail, googleEmail } = req.body;

  if (!companyEmail || !googleEmail) {
    return res.status(400).json({ 
      error: "Both company email and Google email are required" 
    });
  }

  try {
    await connectToDatabase();
    
    // Find existing recruiter account with company email
    const existingRecruiter = await User.findOne({ 
      email: companyEmail.toLowerCase(),
      userType: 'recruiter'
    });

    if (!existingRecruiter) {
      return res.status(404).json({ 
        error: "Recruiter account not found with that company email" 
      });
    }

    // Check if Google email already exists
    const googleUser = await User.findOne({ 
      email: googleEmail.toLowerCase() 
    });

    if (googleUser && googleUser.userType === 'recruiter') {
      return res.status(400).json({ 
        error: "Google email already linked to a recruiter account" 
      });
    }

    if (googleUser && googleUser.userType === 'candidate') {
      // Update the Google user to be a recruiter with the company data
      await User.findByIdAndUpdate(googleUser._id, {
        userType: 'recruiter',
        company: existingRecruiter.company,
        location: existingRecruiter.location,
        industry: existingRecruiter.industry,
        website: existingRecruiter.website,
        description: existingRecruiter.description,
        provider: 'google'
      });

      return res.status(200).json({ 
        message: "Google account successfully linked to recruiter profile",
        user: {
          email: googleEmail,
          userType: 'recruiter',
          company: existingRecruiter.company
        }
      });
    }

    // If Google user doesn't exist, create new one with recruiter data
    const newGoogleRecruiter = await User.create({
      email: googleEmail.toLowerCase(),
      name: existingRecruiter.name,
      userType: 'recruiter',
      company: existingRecruiter.company,
      location: existingRecruiter.location,
      industry: existingRecruiter.industry,
      website: existingRecruiter.website,
      description: existingRecruiter.description,
      provider: 'google',
      status: 'active'
    });

    res.status(200).json({ 
      message: "New Google recruiter account created and linked",
      user: {
        email: googleEmail,
        userType: 'recruiter',
        company: existingRecruiter.company
      }
    });

  } catch (error) {
    console.error("Link Google account error:", error);
    res.status(500).json({ 
      error: "Internal server error",
      details: error.message
    });
  }
}
