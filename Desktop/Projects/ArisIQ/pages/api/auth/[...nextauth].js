// File: pages/api/auth/[...nextauth].js
// Complete NextAuth configuration with proper Google user type handling

import NextAuth from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import bcrypt from "bcryptjs";
import connectToDatabase from "../../../lib/mongodb";
import mongoose from "mongoose";

// User Schema
const UserSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String },
  userType: { type: String, enum: ['recruiter', 'candidate'], required: true },
  company: { type: String },
  provider: { type: String, default: 'credentials' },
  image: { type: String },
  location: { type: String },
  industry: { type: String },
  website: { type: String },
  description: { type: String },
  status: { 
    type: String, 
    enum: ['active', 'suspended'], 
    default: 'active'
  },
}, {
  timestamps: true
});

const User = mongoose.models.User || mongoose.model('User', UserSchema);

export const authOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      authorization: {
        params: {
          prompt: "select_account",
          access_type: "offline",
          response_type: "code"
        }
      }
    }),
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password required");
        }

        try {
          await connectToDatabase();
          
          const user = await User.findOne({ 
            email: credentials.email.toLowerCase() 
          });

          if (!user) {
            throw new Error("No user found with this email");
          }

          if (!user.password) {
            throw new Error("Please sign in with Google");
          }

          if (user.status === 'suspended') {
            throw new Error("Your account has been suspended. Please contact support or update your billing information.");
          }

          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            user.password
          );

          if (!isPasswordValid) {
            throw new Error("Invalid password");
          }

          return {
            id: user._id.toString(),
            email: user.email,
            name: user.name,
            userType: user.userType,
            company: user.company,
            image: user.image,
            status: user.status,
          };
        } catch (error) {
          console.error("Auth error:", error);
          throw new Error(error.message || "Authentication failed");
        }
      },
    }),
  ],
  secret: process.env.NEXTAUTH_SECRET,
  session: {
    strategy: "jwt",
  },
  jwt: {
    encryption: true,
  },
  pages: {
    signIn: "/",
  },
  callbacks: {
    async jwt({ token, user, account }) {
      if (user) {
        token.userType = user.userType;
        token.company = user.company;
        token.userId = user.id;
        token.status = user.status;
        token.isNewGoogleUser = user.isNewGoogleUser;
        token.needsTypeSelection = user.needsTypeSelection;
      }
      return token;
    },
    async session({ session, token }) {
      session.user.userType = token.userType;
      session.user.company = token.company;
      session.user.id = token.userId;
      session.user.status = token.status;
      session.user.isNewGoogleUser = token.isNewGoogleUser;
      session.user.needsTypeSelection = token.needsTypeSelection;
      return session;
    },
    async signIn({ user, account, profile }) {
      if (account?.provider === "google") {
        try {
          await connectToDatabase();
          
          const existingUser = await User.findOne({ 
            email: user.email.toLowerCase() 
          });

          console.log('=== GOOGLE SIGN IN PROCESSING ===');
          console.log('Email:', user.email);
          console.log('Existing user:', existingUser ? 'Found' : 'Not found');

          if (!existingUser) {
            // NEW USER: Create with candidate as default, will be updated by frontend
            console.log('Creating new Google user with default candidate type');
            
            const newUser = await User.create({
              email: user.email.toLowerCase(),
              name: user.name,
              image: user.image,
              provider: "google",
              userType: 'candidate', // Default - frontend will update this
              status: "active",
            });
            
            // Mark that this user needs type determination
            user.isNewGoogleUser = true;
            user.needsTypeSelection = true;
            user.userType = newUser.userType;
            user.id = newUser._id.toString();
            user.company = newUser.company;
            user.status = newUser.status;
            
            console.log('Created new user - will be updated by frontend');
          } else {
            // EXISTING USER: Use stored type
            console.log('Existing user found with type:', existingUser.userType);
            
            user.userType = existingUser.userType;
            user.id = existingUser._id.toString();
            user.company = existingUser.company;
            user.status = existingUser.status;
            user.isNewGoogleUser = false;
            user.needsTypeSelection = false;
          }
          
          console.log('Final user type:', user.userType);
          console.log('=== END GOOGLE SIGN IN PROCESSING ===');

          return true;
        } catch (error) {
          console.error("Google sign-in error:", error);
          return false;
        }
      }
      return true;
    },
    async redirect({ url, baseUrl }) {
      console.log("NextAuth redirect - url:", url, "baseUrl:", baseUrl);
      
      // Always return to home page, let frontend handle the routing
      return baseUrl;
    },
  },
};

export default NextAuth(authOptions);
