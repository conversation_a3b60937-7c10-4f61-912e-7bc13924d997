// File: pages/api/auth/[...nextauth].js

import NextAuth from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import bcrypt from "bcryptjs";
import connectToDatabase from "../../../lib/mongodb";
import mongoose from "mongoose";

// User Schema
const UserSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String }, // Optional for Google OAuth users
  userType: { type: String, enum: ['recruiter', 'candidate'], required: true },
  company: { type: String }, // For recruiters
  provider: { type: String, default: 'credentials' }, // 'credentials' or 'google'
  image: { type: String }, // For Google OAuth profile images
  
  // Additional employer fields
  location: { type: String },
  industry: { type: String },
  website: { type: String },
  description: { type: String },
  
  // Account status (for billing/suspension management)
  status: { 
    type: String, 
    enum: ['active', 'suspended'], 
    default: 'active'
  },
}, {
  timestamps: true
});

// Check if model already exists to avoid re-compilation error
const User = mongoose.models.User || mongoose.model('User', UserSchema);

export const authOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password required");
        }

        try {
          await connectToDatabase();
          
          const user = await User.findOne({ 
            email: credentials.email.toLowerCase() 
          });

          if (!user) {
            throw new Error("No user found with this email");
          }

          if (!user.password) {
            throw new Error("Please sign in with Google");
          }

          // Check if account is suspended (for billing issues)
          if (user.status === 'suspended') {
            throw new Error("Your account has been suspended. Please contact support or update your billing information.");
          }

          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            user.password
          );

          if (!isPasswordValid) {
            throw new Error("Invalid password");
          }

          return {
            id: user._id.toString(),
            email: user.email,
            name: user.name,
            userType: user.userType,
            company: user.company,
            image: user.image,
            status: user.status,
          };
        } catch (error) {
          console.error("Auth error:", error);
          throw new Error(error.message || "Authentication failed");
        }
      },
    }),
  ],
  secret: process.env.NEXTAUTH_SECRET,
  session: {
    strategy: "jwt",
  },
  jwt: {
    encryption: true,
  },
  pages: {
    signIn: "/", // Redirects failed auth to homepage or custom login page
  },
  callbacks: {
    async jwt({ token, user, account }) {
      if (user) {
        token.userType = user.userType;
        token.company = user.company;
        token.userId = user.id;
        token.status = user.status;
        
        // Handle Google OAuth users
        if (account?.provider === "google" && !user.userType) {
          // Default to candidate for new Google users
          token.userType = "candidate";
        }
      }
      return token;
    },
    async session({ session, token }) {
      session.user.userType = token.userType;
      session.user.company = token.company;
      session.user.id = token.userId;
      session.user.status = token.status;
      return session;
    },
    async signIn({ user, account, profile }) {
      if (account?.provider === "google") {
        try {
          await connectToDatabase();
          
          const existingUser = await User.findOne({ 
            email: user.email.toLowerCase() 
          });

          if (!existingUser) {
            // Create new Google user with default candidate type
            await User.create({
              email: user.email.toLowerCase(),
              name: user.name,
              image: user.image,
              provider: "google",
              userType: "candidate", // Default - can be changed later
              status: "active",
            });
          }

          return true;
        } catch (error) {
          console.error("Google sign-in error:", error);
          return false;
        }
      }
      return true;
    },
  },
};

export default NextAuth(authOptions);
