// File: pages/api/auth/register.js

import bcrypt from "bcryptjs";
import connectToDatabase from "../../../lib/mongodb";
import mongoose from "mongoose";

// Use the same User schema as in NextAuth config
const UserSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String },
  userType: { type: String, enum: ['recruiter', 'candidate'], required: true },
  company: { type: String },
  provider: { type: String, default: 'credentials' },
  image: { type: String },
  location: { type: String },
  industry: { type: String },
  website: { type: String },
  description: { type: String },
  status: { 
    type: String, 
    enum: ['active', 'suspended'], 
    default: 'active'
  },
}, {
  timestamps: true
});

const User = mongoose.models.User || mongoose.model('User', UserSchema);

export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { name, email, password, userType, company } = req.body;

  // Validation
  if (!name || !email || !password || !userType) {
    return res.status(400).json({ 
      error: "Name, email, password, and user type are required" 
    });
  }

  if (!["recruiter", "candidate"].includes(userType)) {
    return res.status(400).json({ 
      error: "User type must be 'recruiter' or 'candidate'" 
    });
  }

  if (userType === "recruiter" && !company) {
    return res.status(400).json({ 
      error: "Company name is required for recruiters" 
    });
  }

  if (password.length < 6) {
    return res.status(400).json({ 
      error: "Password must be at least 6 characters long" 
    });
  }

  // Email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return res.status(400).json({ 
      error: "Please enter a valid email address" 
    });
  }

  try {
    await connectToDatabase();
    
    // Check if user already exists
    const existingUser = await User.findOne({ 
      email: email.toLowerCase() 
    });

    if (existingUser) {
      return res.status(400).json({ 
        error: "User already exists with this email" 
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user object
    const userData = {
      name: name.trim(),
      email: email.toLowerCase().trim(),
      password: hashedPassword,
      userType,
      provider: "credentials",
      status: "active",
      ...(userType === "recruiter" && { company: company.trim() }),
    };

    // Create user
    const newUser = await User.create(userData);

    // Return success (don't send password)
    res.status(201).json({ 
      message: "User created successfully",
      user: {
        id: newUser._id,
        name: newUser.name,
        email: newUser.email,
        userType: newUser.userType,
        company: newUser.company,
        status: newUser.status,
      }
    });

  } catch (error) {
    console.error("Registration error:", error);
    
    // Handle MongoDB duplicate key error
    if (error.code === 11000) {
      return res.status(400).json({ 
        error: "User already exists with this email" 
      });
    }
    
    res.status(500).json({ 
      error: "Internal server error. Please try again." 
    });
  }
}
