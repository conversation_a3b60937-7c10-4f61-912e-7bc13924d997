// File: pages/api/auth/update-user-type.js

import connectToDatabase from "../../../lib/mongodb";
import mongoose from "mongoose";

const UserSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String },
  userType: { type: String, enum: ['recruiter', 'candidate'], required: true },
  company: { type: String },
  provider: { type: String, default: 'credentials' },
  image: { type: String },
  location: { type: String },
  industry: { type: String },
  website: { type: String },
  description: { type: String },
  status: { 
    type: String, 
    enum: ['active', 'suspended'], 
    default: 'active'
  },
}, {
  timestamps: true
});

const User = mongoose.models.User || mongoose.model('User', UserSchema);

export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { email, userType, company } = req.body;

  if (!email || !userType) {
    return res.status(400).json({ 
      error: "Email and user type are required" 
    });
  }

  if (!["recruiter", "candidate"].includes(userType)) {
    return res.status(400).json({ 
      error: "User type must be 'recruiter' or 'candidate'" 
    });
  }

  try {
    await connectToDatabase();
    
    const updateData = {
      userType,
      ...(userType === 'recruiter' && company && { company: company.trim() })
    };

    const updatedUser = await User.findOneAndUpdate(
      { email: email.toLowerCase() },
      updateData,
      { new: true }
    );

    if (!updatedUser) {
      return res.status(404).json({ error: "User not found" });
    }

    res.status(200).json({ 
      message: "User type updated successfully",
      user: {
        id: updatedUser._id,
        email: updatedUser.email,
        userType: updatedUser.userType,
        company: updatedUser.company,
      }
    });

  } catch (error) {
    console.error("Update user type error:", error);
    res.status(500).json({ 
      error: "Internal server error. Please try again." 
    });
  }
}
