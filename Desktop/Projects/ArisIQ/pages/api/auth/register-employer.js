// File: pages/api/auth/register-employer.js
// Fixed employer registration API

import bcrypt from "bcryptjs";
import connectToDatabase from "../../../lib/mongodb";
import mongoose from "mongoose";

const UserSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String },
  userType: { type: String, enum: ['recruiter', 'candidate'], required: true },
  company: { type: String },
  provider: { type: String, default: 'credentials' },
  image: { type: String },
  location: { type: String },
  industry: { type: String },
  website: { type: String },
  description: { type: String },
  status: { 
    type: String, 
    enum: ['active', 'suspended'], 
    default: 'active'
  },
}, {
  timestamps: true
});

const User = mongoose.models.User || mongoose.model('User', UserSchema);

export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { 
    name, 
    email, 
    password, 
    userType = 'recruiter',
    company,
    location,
    industry,
    website,
    description
  } = req.body;

  // Validation
  if (!name || !email || !password) {
    return res.status(400).json({ 
      error: "Name, email, and password are required" 
    });
  }

  if (!company) {
    return res.status(400).json({ 
      error: "Company name is required for employer accounts" 
    });
  }

  // Validate business email (simple check)
  const personalDomains = [
    'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 
    'aol.com', 'icloud.com', 'protonmail.com', 'live.com'
  ];
  
  const domain = email.split('@')[1]?.toLowerCase();
  if (personalDomains.includes(domain)) {
    return res.status(400).json({ 
      error: "Please use a business email address. Personal emails are not allowed for employer accounts." 
    });
  }

  try {
    await connectToDatabase();

    // Check if user already exists
    const existingUser = await User.findOne({ 
      email: email.toLowerCase() 
    });

    if (existingUser) {
      return res.status(400).json({ 
        error: "An account with this email already exists" 
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create new employer user
    const newUser = await User.create({
      name,
      email: email.toLowerCase(),
      password: hashedPassword,
      userType: 'recruiter', // Force recruiter type for employer registration
      company,
      location,
      industry,
      website,
      description,
      provider: 'credentials',
      status: 'active'
    });

    console.log('Created new employer user:', {
      id: newUser._id,
      email: newUser.email,
      userType: newUser.userType,
      company: newUser.company
    });

    res.status(201).json({
      message: "Employer account created successfully",
      user: {
        id: newUser._id,
        name: newUser.name,
        email: newUser.email,
        userType: newUser.userType,
        company: newUser.company
      }
    });

  } catch (error) {
    console.error("Employer registration error:", error);
    
    if (error.code === 11000) {
      return res.status(400).json({ 
        error: "An account with this email already exists" 
      });
    }

    res.status(500).json({ 
      error: "Internal server error. Please try again." 
    });
  }
}
