// File: pages/api/auth/register-employer.js

import bcrypt from "bcryptjs";
import connectToDatabase from "../../../lib/mongodb";
import mongoose from "mongoose";

// Enhanced User schema for employer data
const UserSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String },
  userType: { type: String, enum: ['recruiter', 'candidate'], required: true },
  company: { type: String },
  provider: { type: String, default: 'credentials' },
  image: { type: String },
  
  // Additional employer fields
  location: { type: String },
  industry: { type: String },
  website: { type: String },
  description: { type: String },
  
  // Account status (active by default, can be suspended for billing issues)
  status: { 
    type: String, 
    enum: ['active', 'suspended'], 
    default: 'active'
  },
}, {
  timestamps: true
});

const User = mongoose.models.User || mongoose.model('User', UserSchema);

export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { 
    name, 
    email, 
    password, 
    userType, 
    company,
    location,
    industry,
    website,
    description
  } = req.body;

  // Validation
  if (!name || !email || !password || !company || !location || !industry) {
    return res.status(400).json({ 
      error: "Name, email, password, company, location, and industry are required" 
    });
  }

  if (password.length < 6) {
    return res.status(400).json({ 
      error: "Password must be at least 6 characters long" 
    });
  }

  // Validate business email (no personal domains)
  const personalDomains = [
    'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 
    'aol.com', 'icloud.com', 'protonmail.com', 'live.com',
    'msn.com', 'yandex.com'
  ];
  
  const domain = email.split('@')[1]?.toLowerCase();
  if (personalDomains.includes(domain)) {
    return res.status(400).json({ 
      error: "Please use a business email address. Personal emails are not allowed for employer accounts." 
    });
  }

  // Email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return res.status(400).json({ 
      error: "Please enter a valid email address" 
    });
  }

  // Website validation (if provided)
  if (website && website.trim()) {
    const urlRegex = /^https?:\/\/.+\..+/;
    if (!urlRegex.test(website)) {
      return res.status(400).json({ 
        error: "Please enter a valid website URL (include http:// or https://)" 
      });
    }
  }

  try {
    await connectToDatabase();
    
    // Check if user already exists
    const existingUser = await User.findOne({ 
      email: email.toLowerCase() 
    });

    if (existingUser) {
      return res.status(400).json({ 
        error: "An account already exists with this email address" 
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create employer user
    const employerData = {
      name: name.trim(),
      email: email.toLowerCase().trim(),
      password: hashedPassword,
      userType: 'recruiter',
      provider: "credentials",
      company: company.trim(),
      location: location.trim(),
      industry: industry.trim(),
      website: website?.trim() || '',
      description: description?.trim() || '',
      status: 'active', // Active immediately
    };

    const newEmployer = await User.create(employerData);

    // TODO: Send welcome email to new employer
    // TODO: Set up billing/subscription for employer account

    res.status(201).json({ 
      message: "Employer account created successfully. Welcome to ArisIQ!",
      user: {
        id: newEmployer._id,
        name: newEmployer.name,
        email: newEmployer.email,
        company: newEmployer.company,
        status: newEmployer.status,
      }
    });

  } catch (error) {
    console.error("Employer registration error:", error);
    
    // Handle MongoDB duplicate key error
    if (error.code === 11000) {
      return res.status(400).json({ 
        error: "An account already exists with this email address" 
      });
    }
    
    res.status(500).json({ 
      error: "Internal server error. Please try again." 
    });
  }
}
