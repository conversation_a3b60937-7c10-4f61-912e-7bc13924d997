// pages/api/candidate/submit-test.js
// This is your existing endpoint - we'll modify it to trigger AI evaluation

import TestEvaluationService from '../../../services/testEvaluationService';
import connectToDatabase from '../../../lib/mongodb';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    await connectToDatabase();
    
    const { applicationId, answers, interviewCompleted } = req.body;
    
    if (!applicationId || !answers) {
      return res.status(400).json({ error: 'Missing required data' });
    }

    const Application = require('../../../models/Application');
    
    // 1. Save test answers to database
    const testAnswers = answers.map(answer => ({
      questionId: answer.questionId || `q_${Date.now()}_${Math.random()}`,
      questionText: answer.question,
      questionType: answer.type || 'technical',
      answerText: answer.answer,
      timeSpent: answer.timeSpent || 0,
      submittedAt: new Date(),
      confidence: answer.confidence || null
    }));

    // 2. Update application with test answers
    await Application.findByIdAndUpdate(applicationId, {
      $set: {
        testAnswers: testAnswers,
        interviewStatus: interviewCompleted ? 'completed' : 'in_progress',
        interviewCompletedAt: interviewCompleted ? new Date() : null,
        evaluationStatus: 'pending' // Set for AI evaluation
      }
    });

    // 3. If interview is completed, trigger AI evaluation
    if (interviewCompleted) {
      try {
        console.log(`Test completed for application ${applicationId}, starting AI evaluation...`);
        
        // Start AI evaluation asynchronously (don't wait for completion)
        const evaluationService = new TestEvaluationService();
        
        // Fire and forget - don't block the response
        evaluationService.evaluateTest(applicationId)
          .then(result => {
            console.log(`AI evaluation completed for ${applicationId}:`, {
              score: result.finalScore.overall,
              recommendation: result.finalScore.recommendation
            });
          })
          .catch(error => {
            console.error(`AI evaluation failed for ${applicationId}:`, error);
          });

        // Immediately return success to candidate
        res.status(200).json({
          success: true,
          message: 'Test submitted successfully',
          data: {
            applicationId,
            interviewCompleted: true,
            evaluationStarted: true,
            aiEvaluationNote: 'AI evaluation started in background'
          }
        });

      } catch (error) {
        // Even if AI evaluation setup fails, still save the test
        console.error('Failed to start AI evaluation:', error);
        
        res.status(200).json({
          success: true,
          message: 'Test submitted successfully',
          data: {
            applicationId,
            interviewCompleted: true,
            evaluationStarted: false,
            note: 'Test saved, AI evaluation will be triggered manually'
          }
        });
      }
    } else {
      // Interview not completed yet
      res.status(200).json({
        success: true,
        message: 'Test progress saved',
        data: {
          applicationId,
          interviewCompleted: false
        }
      });
    }

  } catch (error) {
    console.error('Submit test error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to submit test',
      message: error.message
    });
  }
}

// Alternative: Manual trigger for existing completed tests
// pages/api/admin/trigger-missing-evaluations.js
export async function triggerMissingEvaluations(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    await connectToDatabase();
    
    const Application = require('../../../models/Application');
    
    // Find applications that completed tests but don't have AI evaluations
    const applicationsNeedingEvaluation = await Application.find({
      interviewStatus: 'completed',
      testAnswers: { $exists: true, $ne: [] },
      $or: [
        { evaluationStatus: { $exists: false } },
        { evaluationStatus: 'pending' },
        { evaluationStatus: 'failed' }
      ]
    }).select('_id fullName email jobTitle');

    console.log(`Found ${applicationsNeedingEvaluation.length} applications needing AI evaluation`);

    if (applicationsNeedingEvaluation.length === 0) {
      return res.status(200).json({
        success: true,
        message: 'No applications need evaluation',
        count: 0
      });
    }

    const evaluationService = new TestEvaluationService();
    const results = [];

    // Process evaluations with delay to avoid rate limits
    for (let i = 0; i < applicationsNeedingEvaluation.length; i++) {
      const app = applicationsNeedingEvaluation[i];
      
      try {
        console.log(`Starting evaluation ${i + 1}/${applicationsNeedingEvaluation.length} for ${app.fullName}`);
        
        // Add delay between evaluations to avoid rate limiting
        if (i > 0) {
          await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
        }

        const result = await evaluationService.evaluateTest(app._id);
        
        results.push({
          applicationId: app._id,
          candidateName: app.fullName,
          status: 'success',
          score: result.finalScore.overall,
          recommendation: result.finalScore.recommendation
        });

      } catch (error) {
        console.error(`Evaluation failed for ${app.fullName}:`, error);
        
        results.push({
          applicationId: app._id,
          candidateName: app.fullName,
          status: 'failed',
          error: error.message
        });
      }
    }

    const successCount = results.filter(r => r.status === 'success').length;
    const failureCount = results.filter(r => r.status === 'failed').length;

    res.status(200).json({
      success: true,
      message: `Bulk evaluation completed: ${successCount} successful, ${failureCount} failed`,
      data: {
        total: applicationsNeedingEvaluation.length,
        successful: successCount,
        failed: failureCount,
        results: results
      }
    });

  } catch (error) {
    console.error('Bulk evaluation error:', error);
    res.status(500).json({ error: 'Bulk evaluation failed' });
  }
}
