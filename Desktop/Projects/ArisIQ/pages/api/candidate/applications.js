// File: pages/api/candidate/applications.js

import { MongoClient } from "mongodb";
const uri = process.env.MONGODB_URI;

export default async function handler(req, res) {
  if (req.method !== "GET") {
    return res.status(405).json({ success: false, message: "Method not allowed" });
  }

  // Get query parameters
  const { email, recruiter } = req.query;

  try {
    const client = await MongoClient.connect(uri);
    const db = client.db();
    
    let query = {};
    
    // If email is provided, filter applications by candidate email
    if (email) {
      query.email = email;
    }
    
    // If this is a recruiter request, we don't filter by email but might filter by jobs posted by recruiter
    // This would require joining with jobs collection to find jobs posted by this recruiter
    // For now, we'll return all applications since the frontend will handle filtering
    
    // Get applications with job details
    const applications = await db.collection("applications").aggregate([
      { $match: query },
      {
        $addFields: {
          jobObjectId: { 
            $cond: {
              if: { $eq: [{ $type: "$jobId" }, "objectId"] },
              then: "$jobId",
              else: { 
                $cond: {
                  if: { $eq: [{ $type: "$jobId" }, "string"] },
                  then: { $toObjectId: "$jobId" },
                  else: null
                }
              }
            }
          }
        },
      },
      {
        $lookup: {
          from: "jobs",
          localField: "jobObjectId",
          foreignField: "_id",
          as: "jobDetails",
        },
      },
      { 
        $unwind: { 
          path: "$jobDetails",
          preserveNullAndEmptyArrays: true
        } 
      },
      {
        $project: {
          _id: 1,
          email: 1,
          candidateEmail: 1,
          fullName: 1,
          firstName: 1,
          lastName: 1,
          candidateName: 1,
          jobId: 1,
          appliedAt: 1,
          createdAt: 1,
          status: 1,
          scanResult: 1,
          score: 1,
          jobTitle: { 
            $cond: { 
              if: { $ifNull: ["$jobDetails", false] }, 
              then: "$jobDetails.title", 
              else: "$jobTitle" 
            } 
          },
          companyName: { 
            $cond: { 
              if: { $ifNull: ["$jobDetails", false] }, 
              then: "$jobDetails.company", 
              else: "$companyName" 
            } 
          },
          location: { 
            $cond: { 
              if: { $ifNull: ["$jobDetails", false] }, 
              then: "$jobDetails.location", 
              else: "$location" 
            } 
          },
        },
      },
    ]).toArray();

    // If recruiter is specified, filter applications to only include those for jobs posted by this recruiter
    let filteredApplications = applications;
    if (recruiter) {
      // First, get all jobs posted by this recruiter
      const recruiterJobs = await db.collection("jobs").find({ postedBy: recruiter }).toArray();
      const recruiterJobIds = recruiterJobs.map(job => job._id.toString());
      
      // Then filter applications to only include those for these jobs
      filteredApplications = applications.filter(app => {
        const appJobId = app.jobId?.toString();
        return recruiterJobIds.includes(appJobId);
      });
    }

    await client.close();
    
    return res.status(200).json({ 
      success: true, 
      applications: filteredApplications
    });
  } catch (error) {
    console.error("Error fetching applications:", error);
    return res.status(500).json({ 
      success: false, 
      message: error.message 
    });
  }
}
