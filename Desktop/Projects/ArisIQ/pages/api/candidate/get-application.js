// File: pages/api/candidate/get-application.js

import { MongoClient, ObjectId } from "mongodb";
const uri = process.env.MONGODB_URI;

export default async function handler(req, res) {
  if (req.method !== "GET") {
    return res.status(405).json({ success: false, message: "Method not allowed" });
  }

  // Support both applicationId and jobId+email query patterns
  const { applicationId, jobId, email } = req.query;

  if (!applicationId && !(jobId && email)) {
    return res.status(400).json({ 
      success: false, 
      message: "Either applicationId or both jobId and email are required" 
    });
  }

  try {
    const client = await MongoClient.connect(uri);
    const db = client.db();

    let query = {};
    if (applicationId) {
      // Find by applicationId
      try {
        query = { _id: new ObjectId(applicationId) };
      } catch (e) {
        // If it's not a valid ObjectId format, try as a string
        query = { _id: applicationId };
      }
    } else {
      // Find by jobId and email
      try {
        // First try if jobId is an ObjectId
        const jobIdObj = new ObjectId(jobId);
        query = { jobId: jobIdObj, email: email };
      } catch (e) {
        // If jobId is not a valid ObjectId, try as string
        query = { jobId: jobId, email: email };
      }
    }

    const application = await db.collection("applications").aggregate([
      { $match: query },
      {
        $addFields: {
          jobObjectId: { 
            $cond: {
              if: { $eq: [{ $type: "$jobId" }, "objectId"] },
              then: "$jobId",
              else: { 
                $cond: {
                  if: { $eq: [{ $type: "$jobId" }, "string"] },
                  then: { $toObjectId: "$jobId" },
                  else: null
                }
              }
            }
          }
        },
      },
      {
        $lookup: {
          from: "jobs",
          localField: "jobObjectId",
          foreignField: "_id",
          as: "jobDetails",
        },
      },
      { 
        $unwind: { 
          path: "$jobDetails",
          preserveNullAndEmptyArrays: true
        } 
      },
      {
        $project: {
          _id: 1,
          email: 1,
          fullName: 1,
          firstName: 1,
          lastName: 1,
          jobId: 1,
          appliedAt: 1,
          createdAt: 1,
          status: 1,
          scanResult: 1,
          score: 1, // Make sure to include score field
          jobTitle: { 
            $cond: { 
              if: { $ifNull: ["$jobDetails", false] }, 
              then: "$jobDetails.title", 
              else: "$jobTitle" 
            } 
          },
          companyName: { 
            $cond: { 
              if: { $ifNull: ["$jobDetails", false] }, 
              then: "$jobDetails.company", 
              else: "$companyName" 
            } 
          },
          location: { 
            $cond: { 
              if: { $ifNull: ["$jobDetails", false] }, 
              then: "$jobDetails.location", 
              else: "$location" 
            } 
          },
        },
      },
    ]).toArray();

    if (!application.length) {
      return res.status(404).json({ success: false, message: "Application not found" });
    }

    res.status(200).json({ success: true, application: application[0] });
  } catch (err) {
    console.error("❌ Error fetching application:", err);
    res.status(500).json({ success: false, message: err.message });
  }
}
