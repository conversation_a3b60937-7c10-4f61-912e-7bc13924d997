// File: pages/api/resumes/[id].js

import mongoose from 'mongoose';
import { GridFSBucket } from 'mongodb';
import connectToDatabase from '../../../lib/mongodb';

const applicationSchema = new mongoose.Schema({}, { strict: false });
const Application = mongoose.models.Application || mongoose.model('Application', applicationSchema);

export default async function handler(req, res) {
  await connectToDatabase();

  const { id } = req.query;
  if (!id) return res.status(400).send('Resume ID is required');

  try {
    const db = mongoose.connection.db;
    const bucket = new GridFSBucket(db, { bucketName: 'resumes' });

    const _id = new mongoose.Types.ObjectId(id);
    const application = await Application.findOne({ resumeId: _id });

    const candidateName = application?.fullName?.replace(/\s+/g, '_') || 'resume';

    const downloadStream = bucket.openDownloadStream(_id);
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${candidateName}_resume.pdf"`);

    downloadStream.pipe(res).on('error', (err) => {
      console.error('Stream error:', err);
      res.status(500).send('Error downloading resume');
    });
  } catch (error) {
    console.error('Download error:', error);
    res.status(500).send('Internal Server Error');
  }
}

