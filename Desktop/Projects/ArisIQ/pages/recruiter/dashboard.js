// File: pages/recruiter/dashboard.js
// Clean rebuild with Job ID display and fixes

import { useEffect, useState } from 'react';
import axios from 'axios';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from 'recharts';

export default function RecruiterDashboard() {
  const { data: session, status } = useSession();
  const [jobs, setJobs] = useState([]);
  const [applicationCounts, setApplicationCounts] = useState({});
  const [searchTerm, setSearchTerm] = useState('');
  const [sortOption, setSortOption] = useState('date');
  const [employmentTypeFilter, setEmploymentTypeFilter] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [loading, setLoading] = useState(true);
  const [toastMessage, setToastMessage] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [jobToDelete, setJobToDelete] = useState(null);
  const [showCloseModal, setShowCloseModal] = useState(false);
  const [jobToClose, setJobToClose] = useState(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [hoveredCard, setHoveredCard] = useState(null);

  // Analytics data
  const [analyticsData, setAnalyticsData] = useState({
    totalApplications: 0,
    activeJobs: 0,
    avgTimeToHire: "12 days",
    responseRate: "76%",
    hiringTrends: [
      { month: 'Jan', applications: 45, hires: 8 },
      { month: 'Feb', applications: 52, hires: 12 },
      { month: 'Mar', applications: 68, hires: 15 },
      { month: 'Apr', applications: 71, hires: 18 },
      { month: 'May', applications: 84, hires: 22 },
      { month: 'Jun', applications: 95, hires: 28 }
    ],
    scoreDistribution: [
      { name: 'High Score (≥80)', value: 0, color: '#10B981' },
      { name: 'Medium Score (60-79)', value: 0, color: '#F59E0B' },
      { name: 'Low Score (<60)', value: 0, color: '#EF4444' }
    ]
  });

  useEffect(() => {
    if (!session?.user?.email) return;

    const fetchJobsAndCounts = async () => {
      try {
        setLoading(true);
        console.log("Fetching jobs for email:", session.user.email);
        
        const timestamp = new Date().getTime();
        const res = await axios.get(`/api/jobs?t=${timestamp}&email=${session.user.email}`);
        
        console.log(`Retrieved ${res.data.jobs.length} total jobs from API`);
        
        let userJobs = res.data.jobs;
        
        userJobs.forEach(job => {
          console.log(`Job ${job.title} (${job.jobId}): postedBy=${job.postedBy}, user=${session.user.email}, match=${job.postedBy === session.user.email}`);
        });
        
        if (userJobs.length > 0) {
          userJobs = res.data.jobs.filter(
            (job) => !job.postedBy || job.postedBy === session.user.email
          );
        }
        
        console.log(`Filtered to ${userJobs.length} jobs posted by current user`);
        setJobs(userJobs);

        const jobIds = userJobs.map((job) => job._id);
        if (jobIds.length > 0) {
          const countRes = await axios.post('/api/jobs/count-applications', { jobIds });
          setApplicationCounts(countRes.data.counts || {});
          
          // Calculate analytics
          const totalApps = Object.values(countRes.data.counts || {}).reduce((sum, count) => sum + count, 0);
          const activeJobsCount = userJobs.filter(job => job.status !== 'closed').length;
          
          setAnalyticsData(prev => ({
            ...prev,
            totalApplications: totalApps,
            activeJobs: activeJobsCount,
            scoreDistribution: [
              { name: 'High Score (≥80)', value: Math.floor(totalApps * 0.3), color: '#10B981' },
              { name: 'Medium Score (60-79)', value: Math.floor(totalApps * 0.5), color: '#F59E0B' },
              { name: 'Low Score (<60)', value: Math.floor(totalApps * 0.2), color: '#EF4444' }
            ]
          }));
        }
      } catch (err) {
        console.error('Failed to fetch jobs or counts:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchJobsAndCounts();
  }, [session, refreshTrigger]);

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? 'N/A' : date.toLocaleDateString(undefined, { year: 'numeric', month: 'short', day: 'numeric' });
  };

  const handleDelete = async () => {
    if (!jobToDelete) return;
    try {
      await axios.delete('/api/jobs/delete', {
        data: {
          jobId: jobToDelete,
          recruiterEmail: session?.user?.email
        }
      });
      setJobs(jobs.filter((job) => job._id !== jobToDelete));
      setToastMessage('✅ Job deleted successfully');
    } catch (error) {
      setToastMessage('❌ Failed to delete job: ' + (error.response?.data?.message || error.message));
      console.error('Delete error:', error.response?.data || error);
    } finally {
      setShowModal(false);
      setJobToDelete(null);
      setTimeout(() => setToastMessage(''), 3000);
    }
  };

  const handleClosePosition = async () => {
    if (!jobToClose) return;
    try {
      await axios.patch(`/api/jobs/${jobToClose}`, { 
        status: 'closed',
        closedAt: new Date().toISOString()
      });
      
      setJobs(jobs.map((job) => 
        job._id === jobToClose 
          ? { ...job, status: 'closed', closedAt: new Date().toISOString() }
          : job
      ));
      
      setToastMessage('✅ Position closed successfully');
    } catch (error) {
      setToastMessage('❌ Failed to close position');
      console.error(error);
    } finally {
      setShowCloseModal(false);
      setJobToClose(null);
      setTimeout(() => setToastMessage(''), 3000);
    }
  };

  const handleReopenPosition = async (jobId) => {
    try {
      await axios.patch(`/api/jobs/${jobId}`, { 
        status: 'open',
        closedAt: null
      });
      
      setJobs(jobs.map((job) => 
        job._id === jobId 
          ? { ...job, status: 'open', closedAt: null }
          : job
      ));
      
      setToastMessage('✅ Position reopened successfully');
      setTimeout(() => setToastMessage(''), 3000);
    } catch (error) {
      setToastMessage('❌ Failed to reopen position');
      console.error(error);
      setTimeout(() => setToastMessage(''), 3000);
    }
  };
  
  const refreshJobs = () => {
    setRefreshTrigger(prev => prev + 1);
    setToastMessage('🔄 Refreshing jobs...');
  };

  const getJobStatus = (job) => {
    return job.status || 'open';
  };

  const isJobClosed = (job) => {
    return getJobStatus(job) === 'closed';
  };

  const filteredJobs = jobs
    .filter((job) => {
      const term = searchTerm.toLowerCase();
      return (
        job.title?.toLowerCase().includes(term) ||
        job.location?.toLowerCase().includes(term) ||
        job.experience?.toLowerCase().includes(term) ||
        job.industry?.toLowerCase().includes(term) ||
        job.employmentType?.toLowerCase().includes(term)
      );
    })
    .filter((job) => employmentTypeFilter === '' || job.employmentType === employmentTypeFilter)
    .filter((job) => statusFilter === '' || getJobStatus(job) === statusFilter)
    .filter((job) => {
      if (!dateFilter) return true;
      const jobDate = new Date(job.createdAt);
      const now = new Date();
      const days = parseInt(dateFilter);
      return now - jobDate <= days * 24 * 60 * 60 * 1000;
    })
    .sort((a, b) => {
      if (sortOption === 'date') {
        return new Date(b.createdAt) - new Date(a.createdAt);
      } else if (sortOption === 'applications') {
        return (applicationCounts[b._id] || 0) - (applicationCounts[a._id] || 0);
      } else {
        return 0;
      }
    });

  const MetricCard = ({ title, value, subtitle, icon, gradient, delay = 0 }) => (
    <div 
      className={`relative overflow-hidden rounded-2xl bg-white/50 backdrop-blur-xl border border-white/60 p-6 transform transition-all duration-500 hover:scale-105 hover:shadow-2xl cursor-pointer animate-fade-in-up`}
      style={{ 
        animationDelay: `${delay}ms`,
        boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)'
      }}
      onMouseEnter={() => setHoveredCard(title)}
      onMouseLeave={() => setHoveredCard(null)}
    >
      <div 
        className="absolute top-0 left-0 right-0 h-2 rounded-t-xl"
        style={{ background: gradient }}
      ></div>
      
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-400/30 to-purple-400/30 transform rotate-12 translate-x-full hover:translate-x-0 transition-transform duration-1000"></div>
      </div>
      
      <div className="relative z-10">
        <div className="flex items-center justify-between mb-4">
          <div className="text-slate-700 text-sm font-medium">{title}</div>
          <div className="text-2xl">{icon}</div>
        </div>
        <div className="text-3xl font-bold text-slate-900 mb-2">{value}</div>
        {subtitle && <div className="text-slate-600 text-sm">{subtitle}</div>}
      </div>
      
      {hoveredCard === title && (
        <div className="absolute inset-0 bg-gradient-to-r from-blue-100/40 to-purple-100/40 rounded-2xl"></div>
      )}
    </div>
  );

  const JobCard = ({ job, index }) => (
    <div 
      className="relative overflow-hidden rounded-2xl bg-white/60 backdrop-blur-md border border-white/40 p-6 hover:shadow-xl transition-all duration-300 animate-fade-in-up group"
      style={{ 
        animationDelay: `${index * 100}ms`,
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)'
      }}
    >
      <div className="absolute inset-0 bg-gradient-to-r from-blue-50/20 via-purple-50/20 to-pink-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl pointer-events-none"></div>
      
      <div className="relative z-10">
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <h3 className="text-xl font-semibold text-slate-800">{job.title}</h3>
              {job.jobId && (
                <span className="text-sm text-blue-600 bg-blue-100/60 px-2 py-1 rounded-full font-medium">
                  {job.jobId}
                </span>
              )}
              <span className={`px-3 py-1 text-xs font-medium rounded-full ${
                isJobClosed(job) 
                  ? 'bg-red-100/80 text-red-700 backdrop-blur-sm' 
                  : 'bg-green-100/80 text-green-700 backdrop-blur-sm'
              }`}>
                {isJobClosed(job) ? 'CLOSED' : 'OPEN'}
              </span>
            </div>
            
            <div className="space-y-1 text-sm text-slate-600 mb-4">
              <p className="flex items-center">📍 {job.company} • {job.location}</p>
              <p className="flex items-center">💼 {job.experience} • {job.employmentType}</p>
              <p className="flex items-center">📅 Posted: {formatDate(job.createdAt)}</p>
              <p className="flex items-center">🏭 {job.industry || 'N/A'}</p>
              {isJobClosed(job) && job.closedAt && (
                <p className="flex items-center text-red-600">🚫 Closed: {formatDate(job.closedAt)}</p>
              )}
            </div>
          </div>
        </div>

        <div className="flex justify-between items-center pt-4 border-t border-white/30">
          <div className="flex items-center space-x-4">
            <span className="flex items-center text-blue-700 font-medium bg-blue-100/60 backdrop-blur-sm px-3 py-1 rounded-full">
              📊 {applicationCounts[job._id] || 0} applications
            </span>
          </div>
          
          <div className="flex space-x-2 relative z-20">
            <Link
              href={`/recruiter/jobs/${job._id}`}
              className="px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 text-sm font-medium shadow-lg cursor-pointer backdrop-blur-sm"
            >
              📋 View Applications
            </Link>
            
            <Link
              href={`/recruiter/jobs/edit/${job._id}`}
              className="px-4 py-2 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-lg hover:from-purple-700 hover:to-purple-800 transition-all duration-200 text-sm font-medium shadow-lg cursor-pointer backdrop-blur-sm"
            >
              ✏️ Edit
            </Link>
            
            {isJobClosed(job) ? (
              <button
                onClick={() => handleReopenPosition(job._id)}
                className="px-4 py-2 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-200 text-sm font-medium shadow-lg cursor-pointer backdrop-blur-sm"
              >
                🔄 Reopen
              </button>
            ) : (
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setShowCloseModal(true);
                  setJobToClose(job._id);
                }}
                className="px-4 py-2 bg-gradient-to-r from-orange-600 to-orange-700 text-white rounded-lg hover:from-orange-700 hover:to-orange-800 transition-all duration-200 text-sm font-medium shadow-lg cursor-pointer backdrop-blur-sm"
              >
                🚫 Close
              </button>
            )}
            
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setShowModal(true);
                setJobToDelete(job._id);
              }}
              className="px-4 py-2 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-lg hover:from-red-700 hover:to-red-800 transition-all duration-200 text-sm font-medium shadow-lg cursor-pointer backdrop-blur-sm"
            >
              🗑️ Delete
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  if (status === 'loading') return (
    <div className="min-h-screen bg-gradient-to-br from-slate-100 via-blue-50 to-purple-100 flex items-center justify-center">
      <div className="text-slate-800 text-xl">Authenticating...</div>
    </div>
  );
  
  if (!session) return (
    <div className="min-h-screen bg-gradient-to-br from-slate-100 via-blue-50 to-purple-100 flex items-center justify-center">
      <div className="text-slate-800 text-xl">Please sign in to view your dashboard.</div>
    </div>
  );
  
  if (loading) return (
    <div className="min-h-screen bg-gradient-to-br from-slate-100 via-blue-50 to-purple-100 flex items-center justify-center">
      <div className="text-slate-800 text-xl flex items-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
        Loading dashboard...
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-200 via-blue-100 to-purple-200">
      {/* Animated background elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-400/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-400/30 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/2 left-1/2 w-60 h-60 bg-pink-400/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '4s'}}></div>
      </div>

      {/* Header */}
      <header className="relative z-10 bg-white/40 backdrop-blur-xl border-b border-white/60 shadow-xl">
        <div className="max-w-7xl mx-auto p-6 flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-700 via-purple-600 to-pink-600 bg-clip-text text-transparent">
              Recruiter Command Center
            </h1>
            <p className="text-slate-700 mt-1">Welcome back, {session?.user?.name || 'Recruiter'}! Here's your hiring overview</p>
          </div>
          
          <div className="flex items-center space-x-4">
            <button 
              onClick={refreshJobs}
              className="p-3 rounded-xl bg-white/50 backdrop-blur-md border border-white/60 hover:bg-white/70 transition-all duration-300 transform hover:scale-110 shadow-xl"
            >
              🔄
            </button>
            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center font-bold text-white shadow-xl">
              {session?.user?.name?.[0] || 'R'}
            </div>
          </div>
        </div>
      </header>

      <div className="relative z-10 max-w-7xl mx-auto p-6">
        {/* Toast Message */}
        {toastMessage && (
          <div className="fixed top-20 right-6 z-50 bg-white/80 backdrop-blur-md border border-white/40 text-slate-800 px-6 py-3 rounded-xl shadow-xl animate-fade-in-up">
            {toastMessage}
          </div>
        )}

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <MetricCard
            title="Total Applications"
            value={analyticsData.totalApplications}
            subtitle="All time"
            icon="📊"
            gradient="linear-gradient(135deg, #3B82F6, #1D4ED8)"
            delay={0}
          />
          <MetricCard
            title="Active Jobs"
            value={analyticsData.activeJobs}
            subtitle="Currently hiring"
            icon="💼"
            gradient="linear-gradient(135deg, #10B981, #059669)"
            delay={100}
          />
          <MetricCard
            title="Avg. Time to Hire"
            value={analyticsData.avgTimeToHire}
            subtitle="Industry standard: 18 days"
            icon="⏱️"
            gradient="linear-gradient(135deg, #F59E0B, #D97706)"
            delay={200}
          />
          <MetricCard
            title="Response Rate"
            value={analyticsData.responseRate}
            subtitle="Above average"
            icon="📈"
            gradient="linear-gradient(135deg, #8B5CF6, #7C3AED)"
            delay={300}
          />
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Hiring Trends */}
          <div className="bg-white/50 backdrop-blur-xl border border-white/60 rounded-2xl p-6 hover:shadow-2xl transition-all duration-500 animate-fade-in-up shadow-xl" style={{animationDelay: '400ms'}}>
            <h3 className="text-xl font-semibold mb-4 flex items-center text-slate-900">
              📈 Hiring Trends
              <span className="ml-2 text-sm text-slate-600">(Last 6 months)</span>
            </h3>
            <ResponsiveContainer width="100%" height={250}>
              <LineChart data={analyticsData.hiringTrends}>
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(148, 163, 184, 0.4)" />
                <XAxis dataKey="month" stroke="#475569" />
                <YAxis stroke="#475569" />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: 'rgba(255, 255, 255, 0.95)', 
                    border: '1px solid rgba(255, 255, 255, 0.6)',
                    borderRadius: '12px',
                    backdropFilter: 'blur(20px)',
                    boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)'
                  }} 
                />
                <Line type="monotone" dataKey="applications" stroke="#3B82F6" strokeWidth={3} dot={{ r: 6, fill: '#3B82F6' }} />
                <Line type="monotone" dataKey="hires" stroke="#10B981" strokeWidth={3} dot={{ r: 6, fill: '#10B981' }} />
              </LineChart>
            </ResponsiveContainer>
          </div>

          {/* Score Distribution */}
          <div className="bg-white/50 backdrop-blur-xl border border-white/60 rounded-2xl p-6 hover:shadow-2xl transition-all duration-500 animate-fade-in-up shadow-xl" style={{animationDelay: '500ms'}}>
            <h3 className="text-xl font-semibold mb-4 flex items-center text-slate-900">
              🎯 AI Score Distribution
            </h3>
            <ResponsiveContainer width="100%" height={250}>
              <PieChart>
                <Pie
                  data={analyticsData.scoreDistribution}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}`}
                >
                  {analyticsData.scoreDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip contentStyle={{ 
                  backgroundColor: 'rgba(255, 255, 255, 0.95)', 
                  border: '1px solid rgba(255, 255, 255, 0.6)',
                  borderRadius: '12px',
                  backdropFilter: 'blur(20px)'
                }} />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex flex-wrap gap-4 justify-center mb-8 animate-fade-in-up" style={{animationDelay: '600ms'}}>
          <Link
            href="/recruiter/post-job"
            className="px-6 py-3 rounded-xl bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white transition-all duration-300 transform hover:scale-105 font-medium shadow-lg flex items-center backdrop-blur-sm"
          >
            ➕ Post New Job
          </Link>
          <button className="px-6 py-3 rounded-xl bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white transition-all duration-300 transform hover:scale-105 font-medium shadow-lg backdrop-blur-sm">
            📊 Analytics Report
          </button>
          <button className="px-6 py-3 rounded-xl bg-gradient-to-r from-pink-500 to-rose-600 hover:from-pink-600 hover:to-rose-700 text-white transition-all duration-300 transform hover:scale-105 font-medium shadow-lg backdrop-blur-sm">
            🤖 AI Insights
          </button>
        </div>

        {/* Filters Section */}
        <div className="bg-white/50 backdrop-blur-xl border border-white/60 rounded-2xl p-6 mb-8 animate-fade-in-up shadow-xl" style={{animationDelay: '700ms'}}>
          <h3 className="text-lg font-semibold mb-4 text-slate-900">🔍 Filter & Search Jobs</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-4">
            <input
              type="text"
              placeholder="Search jobs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="col-span-1 md:col-span-2 bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-2 text-slate-900 placeholder-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-lg"
            />

            <select
              value={sortOption}
              onChange={(e) => setSortOption(e.target.value)}
              className="bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-2 text-slate-900 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg"
            >
              <option value="date">Sort by Date</option>
              <option value="applications">Sort by Applications</option>
            </select>

            <select
              value={employmentTypeFilter}
              onChange={(e) => setEmploymentTypeFilter(e.target.value)}
              className="bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-2 text-slate-900 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg"
            >
              <option value="">All Types</option>
              <option value="Full-time">Full-time</option>
              <option value="Part-time">Part-time</option>
              <option value="Contract">Contract</option>
            </select>

            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-2 text-slate-900 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg"
            >
              <option value="">All Statuses</option>
              <option value="open">Open</option>
              <option value="closed">Closed</option>
            </select>

            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              className="bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-2 text-slate-900 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg"
            >
              <option value="">All Dates</option>
              <option value="7">Last 7 Days</option>
              <option value="30">Last 30 Days</option>
              <option value="90">Last 90 Days</option>
            </select>
          </div>
        </div>

        {/* Jobs List */}
        <div className="space-y-6">
          <h3 className="text-2xl font-bold text-slate-800 mb-6 animate-fade-in-up" style={{animationDelay: '800ms'}}>
            📋 Your Job Postings ({filteredJobs.length})
          </h3>
          
          {filteredJobs.length === 0 ? (
            <div className="text-center py-12 animate-fade-in-up bg-white/60 backdrop-blur-md border border-white/40 rounded-2xl shadow-lg" style={{animationDelay: '900ms'}}>
              <div className="text-6xl mb-4">📭</div>
              <p className="text-xl text-slate-600">No jobs match your criteria</p>
              <p className="text-slate-500 mt-2">Try adjusting your filters or create a new job posting</p>
            </div>
          ) : (
            <div className="space-y-6">
              {filteredJobs.map((job, index) => (
                <JobCard key={job._id} job={job} index={index} />
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Modals */}
      {showModal && (
        <div className="fixed inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm z-50">
          <div className="bg-white/90 backdrop-blur-md border border-white/40 p-6 rounded-2xl shadow-2xl text-center max-w-md mx-4">
            <h2 className="text-lg font-semibold mb-4 text-slate-800">Are you sure?</h2>
            <p className="mb-6 text-slate-600">Do you really want to delete this job? This action cannot be undone.</p>
            <div className="flex justify-center space-x-4">
              <button
                onClick={() => setShowModal(false)}
                className="px-4 py-2 rounded-xl bg-slate-200/80 hover:bg-slate-300/80 text-slate-700 transition-colors backdrop-blur-sm"
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                className="px-4 py-2 rounded-xl bg-red-600 text-white hover:bg-red-700 transition-colors shadow-lg"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {showCloseModal && (
        <div className="fixed inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm z-50">
          <div className="bg-white/90 backdrop-blur-md border border-white/40 p-6 rounded-2xl shadow-2xl text-center max-w-md mx-4">
            <h2 className="text-lg font-semibold mb-4 text-slate-800">Close Position?</h2>
            <p className="mb-6 text-slate-600">
              Are you sure you want to close this position? 
              <br />
              <span className="text-sm text-slate-500">
                This will prevent new applications but you can reopen it later.
              </span>
            </p>
            <div className="flex justify-center space-x-4">
              <button
                onClick={() => setShowCloseModal(false)}
                className="px-4 py-2 rounded-xl bg-slate-200/80 hover:bg-slate-300/80 text-slate-700 transition-colors backdrop-blur-sm"
              >
                Cancel
              </button>
              <button
                onClick={handleClosePosition}
                className="px-4 py-2 rounded-xl bg-orange-600 text-white hover:bg-orange-700 transition-colors shadow-lg"
              >
                Close Position
              </button>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        @keyframes fade-in-up {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-fade-in-up {
          animation: fade-in-up 0.6s ease-out forwards;
          opacity: 0;
        }
      `}</style>
    </div>
  );
}
