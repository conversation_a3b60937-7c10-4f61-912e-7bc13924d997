// File: pages/recruiter/dashboard.js

import { useEffect, useState } from 'react';
import axios from 'axios';
import { useSession } from 'next-auth/react';
import Link from 'next/link';

export default function RecruiterDashboard() {
  const { data: session, status } = useSession();
  const [jobs, setJobs] = useState([]);
  const [applicationCounts, setApplicationCounts] = useState({});
  const [searchTerm, setSearchTerm] = useState('');
  const [sortOption, setSortOption] = useState('date');
  const [employmentTypeFilter, setEmploymentTypeFilter] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [loading, setLoading] = useState(true);
  const [toastMessage, setToastMessage] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [jobToDelete, setJobToDelete] = useState(null);
  const [showCloseModal, setShowCloseModal] = useState(false);
  const [jobToClose, setJobToClose] = useState(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  useEffect(() => {
    if (!session?.user?.email) return;

    const fetchJobsAndCounts = async () => {
      try {
        setLoading(true);
        console.log("Fetching jobs for email:", session.user.email);
        
        // Add timestamp and email to query to avoid caching
        const timestamp = new Date().getTime();
        const res = await axios.get(`/api/jobs?t=${timestamp}&email=${session.user.email}`);
        
        console.log(`Retrieved ${res.data.jobs.length} total jobs from API`);
        
        // Modified: Show all jobs for now to debug the issue, will filter by email if needed
        let userJobs = res.data.jobs;
        
        // Check each job's postedBy field
        userJobs.forEach(job => {
          console.log(`Job ${job.title} <span className="ml-2 text-sm text-gray-500">({job.jobCode})</span>: postedBy=${job.postedBy}, user=${session.user.email}, match=${job.postedBy === session.user.email}`);
        });
        
        // Only filter by postedBy if we have more than one job (for debugging)
        // This allows all jobs to show up initially so we can see what's wrong
        if (userJobs.length > 0) {
          userJobs = res.data.jobs.filter(
            (job) => !job.postedBy || job.postedBy === session.user.email
          );
        }
        
        console.log(`Filtered to ${userJobs.length} jobs posted by current user`);
        setJobs(userJobs);

        const jobIds = userJobs.map((job) => job._id);
        if (jobIds.length > 0) {
          const countRes = await axios.post('/api/jobs/count-applications', { jobIds });
          setApplicationCounts(countRes.data.counts || {});
        }
      } catch (err) {
        console.error('Failed to fetch jobs or counts:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchJobsAndCounts();
  }, [session, refreshTrigger]);

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? 'N/A' : date.toLocaleDateString(undefined, { year: 'numeric', month: 'short', day: 'numeric' });
  };

  const handleDelete = async () => {
    if (!jobToDelete) return;
    try {
      // Use the correct API endpoint that matches your delete.js file
      await axios.delete('/api/jobs/delete', {
        data: {
          jobId: jobToDelete,
          recruiterEmail: session?.user?.email
        }
      });
      setJobs(jobs.filter((job) => job._id !== jobToDelete));
      setToastMessage('✅ Job deleted successfully');
    } catch (error) {
      setToastMessage('❌ Failed to delete job: ' + (error.response?.data?.message || error.message));
      console.error('Delete error:', error.response?.data || error);
    } finally {
      setShowModal(false);
      setJobToDelete(null);
      setTimeout(() => setToastMessage(''), 3000);
    }
  };

  const handleClosePosition = async () => {
    if (!jobToClose) return;
    try {
      await axios.patch(`/api/jobs/${jobToClose}`, { 
        status: 'closed',
        closedAt: new Date().toISOString()
      });
      
      // Update local state
      setJobs(jobs.map((job) => 
        job._id === jobToClose 
          ? { ...job, status: 'closed', closedAt: new Date().toISOString() }
          : job
      ));
      
      setToastMessage('✅ Position closed successfully');
    } catch (error) {
      setToastMessage('❌ Failed to close position');
      console.error(error);
    } finally {
      setShowCloseModal(false);
      setJobToClose(null);
      setTimeout(() => setToastMessage(''), 3000);
    }
  };

  const handleReopenPosition = async (jobId) => {
    try {
      await axios.patch(`/api/jobs/${jobId}`, { 
        status: 'open',
        closedAt: null
      });
      
      // Update local state
      setJobs(jobs.map((job) => 
        job._id === jobId 
          ? { ...job, status: 'open', closedAt: null }
          : job
      ));
      
      setToastMessage('✅ Position reopened successfully');
      setTimeout(() => setToastMessage(''), 3000);
    } catch (error) {
      setToastMessage('❌ Failed to reopen position');
      console.error(error);
      setTimeout(() => setToastMessage(''), 3000);
    }
  };
  
  const refreshJobs = () => {
    setRefreshTrigger(prev => prev + 1);
    setToastMessage('🔄 Refreshing jobs...');
  };

  const getJobStatus = (job) => {
    return job.status || 'open';
  };

  const isJobClosed = (job) => {
    return getJobStatus(job) === 'closed';
  };

  const filteredJobs = jobs
    .filter((job) => {
      const term = searchTerm.toLowerCase();
      return (
        job.title?.toLowerCase().includes(term) ||
        job.location?.toLowerCase().includes(term) ||
        job.experience?.toLowerCase().includes(term) ||
        job.industry?.toLowerCase().includes(term) ||
        job.employmentType?.toLowerCase().includes(term)
      );
    })
    .filter((job) => employmentTypeFilter === '' || job.employmentType === employmentTypeFilter)
    .filter((job) => statusFilter === '' || getJobStatus(job) === statusFilter)
    .filter((job) => {
      if (!dateFilter) return true;
      const jobDate = new Date(job.createdAt);
      const now = new Date();
      const days = parseInt(dateFilter);
      return now - jobDate <= days * 24 * 60 * 60 * 1000;
    })
    .sort((a, b) => {
      if (sortOption === 'date') {
        return new Date(b.createdAt) - new Date(a.createdAt);
      } else if (sortOption === 'applications') {
        return (applicationCounts[b._id] || 0) - (applicationCounts[a._id] || 0);
      } else {
        return 0;
      }
    });

  if (status === 'loading') return <p className="p-6">Authenticating...</p>;
  if (!session) return <p className="p-6">Please sign in to view your dashboard.</p>;
  if (loading) return <p className="p-6">Loading jobs...</p>;

  return (
    <div className="max-w-6xl mx-auto px-6 py-10">
      <h1 className="text-3xl font-bold text-blue-700 mb-4">Recruiter Dashboard</h1>

      <div className="flex justify-between items-center mb-6">
        <Link
          href="/recruiter/post-job"
          className="inline-block bg-green-600 text-white font-semibold px-5 py-2 rounded hover:bg-green-700 transition"
        >
          + Post a New Job
        </Link>
        
        <button 
          onClick={refreshJobs}
          className="inline-block bg-blue-500 text-white font-semibold px-5 py-2 rounded hover:bg-blue-600 transition"
        >
          🔄 Refresh Jobs
        </button>
      </div>

      {toastMessage && (
        <div className="bg-black text-white px-4 py-2 rounded mb-4 shadow-lg w-fit">
          {toastMessage}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        <input
          type="text"
          placeholder="Search by title, location, industry, etc."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="col-span-1 md:col-span-2 border border-gray-300 rounded px-4 py-2"
        />

        <select
          value={sortOption}
          onChange={(e) => setSortOption(e.target.value)}
          className="border border-gray-300 rounded px-4 py-2"
        >
          <option value="date">Sort by Date</option>
          <option value="applications">Sort by Applications</option>
        </select>

        <select
          value={employmentTypeFilter}
          onChange={(e) => setEmploymentTypeFilter(e.target.value)}
          className="border border-gray-300 rounded px-4 py-2"
        >
          <option value="">All Employment Types</option>
          <option value="Full-time">Full-time</option>
          <option value="Part-time">Part-time</option>
          <option value="Contract">Contract</option>
        </select>

        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="border border-gray-300 rounded px-4 py-2"
        >
          <option value="">All Statuses</option>
          <option value="open">Open</option>
          <option value="closed">Closed</option>
        </select>
      </div>

      <div className="mb-4">
        <label className="block mb-1 text-sm text-gray-600">Filter by Posting Date</label>
        <select
          value={dateFilter}
          onChange={(e) => setDateFilter(e.target.value)}
          className="border border-gray-300 rounded px-4 py-2"
        >
          <option value="">All Dates</option>
          <option value="7">Last 7 Days</option>
          <option value="30">Last 30 Days</option>
          <option value="90">Last 90 Days</option>
        </select>
      </div>

      {filteredJobs.length === 0 ? (
        <p className="text-gray-600">No jobs match your criteria.</p>
      ) : (
        <div className="space-y-6">
          {filteredJobs.map((job) => (
            <div key={job._id} className={`bg-white border rounded-lg shadow p-6 ${isJobClosed(job) ? 'opacity-75 bg-gray-50' : ''}`}>
              <div className="flex justify-between items-start mb-3">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-1">
                    <h2 className="text-xl font-semibold text-gray-800">{job.title} <span className="ml-2 text-sm text-gray-500">({job.jobCode})</span></h2>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      isJobClosed(job) 
                        ? 'bg-red-100 text-red-800' 
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {isJobClosed(job) ? 'CLOSED' : 'OPEN'}
                    </span>
                  </div>
                  <p className="text-sm text-gray-500 mb-1">{job.company} • {job.location}</p>
                  <p className="text-sm text-gray-500 mb-1">Experience: {job.experience} • Type: {job.employmentType}</p>
                  <p className="text-sm text-gray-500 mb-1">Posted on: {formatDate(job.createdAt)}</p>
                  {isJobClosed(job) && job.closedAt && (
                    <p className="text-sm text-red-600 mb-1">Closed on: {formatDate(job.closedAt)}</p>
                  )}
                  <p className="text-sm text-gray-500 mb-1">Application Deadline: {formatDate(job.applicationDeadline)}</p>
                  <p className="text-sm text-gray-500 mb-1">Screening Deadline: {formatDate(job.screeningDeadline)}</p>
                  <p className="text-sm text-gray-500 mb-1">Visa Sponsorship: {job.visaSponsorship === 'yes' ? 'Yes' : 'No'}</p>
                  <p className="text-sm text-gray-600 mb-3">Industry: {job.industry || 'N/A'}</p>
                </div>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">
                  {applicationCounts[job._id] || 0} applications
                </span>
                <div className="flex space-x-2">
                  <Link
                    href={`/recruiter/jobs/${job._id}`}
                    className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                  >
                    View Applications
                  </Link>
                  
                  <Link
                    href={`/recruiter/jobs/edit/${job._id}`}
                    className="px-3 py-1 text-sm bg-purple-100 text-purple-700 rounded hover:bg-purple-200 transition-colors"
                  >
                    Edit
                  </Link>
                  
                  {isJobClosed(job) ? (
                    <button
                      onClick={() => handleReopenPosition(job._id)}
                      className="px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
                    >
                      Reopen
                    </button>
                  ) : (
                    <button
                      onClick={() => {
                        setShowCloseModal(true);
                        setJobToClose(job._id);
                      }}
                      className="px-3 py-1 text-sm bg-orange-100 text-orange-700 rounded hover:bg-orange-200 transition-colors"
                    >
                      Close
                    </button>
                  )}
                  
                  <button
                    onClick={() => {
                      setShowModal(true);
                      setJobToDelete(job._id);
                    }}
                    className="px-3 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showModal && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
          <div className="bg-white p-6 rounded shadow-md text-center">
            <h2 className="text-lg font-semibold mb-4">Are you sure?</h2>
            <p className="mb-6">Do you really want to delete this job? This action cannot be undone.</p>
            <div className="flex justify-center space-x-4">
              <button
                onClick={() => setShowModal(false)}
                className="px-4 py-2 rounded bg-gray-300 hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                className="px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Close Position Confirmation Modal */}
      {showCloseModal && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
          <div className="bg-white p-6 rounded shadow-md text-center">
            <h2 className="text-lg font-semibold mb-4">Close Position?</h2>
            <p className="mb-6">
              Are you sure you want to close this position? 
              <br />
              <span className="text-sm text-gray-600">
                This will prevent new applications but you can reopen it later.
              </span>
            </p>
            <div className="flex justify-center space-x-4">
              <button
                onClick={() => setShowCloseModal(false)}
                className="px-4 py-2 rounded bg-gray-300 hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={handleClosePosition}
                className="px-4 py-2 rounded bg-orange-600 text-white hover:bg-orange-700"
              >
                Close Position
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
