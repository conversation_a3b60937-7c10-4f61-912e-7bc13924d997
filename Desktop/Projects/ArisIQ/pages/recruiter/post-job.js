// File: pages/recruiter/post-job.js
// Updated to send JSON data instead of FormData

import { useState, useRef, useEffect } from "react";
import { useRouter } from "next/router";
import { useSession } from "next-auth/react";
import axios from "axios";

export default function PostJob() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [jobData, setJobData] = useState({
    title: "",
    company: "",
    industry: "",
    experience: "",
    salary: "",
    country: "",
    city: "",
    requiredSkills: "",
    preferredSkills: "",
    website: "",
    applicationDeadline: "",
    screeningDeadline: "",
    employmentType: "Full-time",
    workMode: "", 
    description: "",
    questionSource: "",
    visaSponsorship: "no",
    questionsFile: null,
  });
  
  const [feedback, setFeedback] = useState({ message: '', type: '' });
  
  // Autocomplete states
  const [countrySuggestions, setCountrySuggestions] = useState([]);
  const [citySuggestions, setCitySuggestions] = useState([]);
  const [showCountrySuggestions, setShowCountrySuggestions] = useState(false);
  const [showCitySuggestions, setShowCitySuggestions] = useState(false);
  const [loadingCountries, setLoadingCountries] = useState(false);
  const [loadingCities, setLoadingCities] = useState(false);
  
  // Refs for autocomplete
  const countryInputRef = useRef(null);
  const cityInputRef = useRef(null);
  const countryTimeoutRef = useRef(null);
  const cityTimeoutRef = useRef(null);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setJobData((prev) => ({ ...prev, [name]: value }));

    // Handle autocomplete for country and city
    if (name === 'country') {
      handleCountryInput(value);
    } else if (name === 'city') {
      handleCityInput(value);
    }
  };

  const handleFileChange = (e) => {
    setJobData((prev) => ({ ...prev, questionsFile: e.target.files[0] }));
  };

  // Country autocomplete handler
  const handleCountryInput = (value) => {
    if (countryTimeoutRef.current) {
      clearTimeout(countryTimeoutRef.current);
    }

    if (value.length < 2) {
      setShowCountrySuggestions(false);
      setCountrySuggestions([]);
      return;
    }

    countryTimeoutRef.current = setTimeout(async () => {
      try {
        setLoadingCountries(true);
        const response = await axios.get(`/api/location/autocomplete?type=country&query=${encodeURIComponent(value)}`);
        
        if (response.data.success) {
          setCountrySuggestions(response.data.suggestions);
          setShowCountrySuggestions(true);
        }
      } catch (error) {
        console.error('Country autocomplete error:', error);
        setCountrySuggestions([]);
      } finally {
        setLoadingCountries(false);
      }
    }, 300);
  };

  // City autocomplete handler
  const handleCityInput = (value) => {
    if (cityTimeoutRef.current) {
      clearTimeout(cityTimeoutRef.current);
    }

    if (value.length < 2) {
      setShowCitySuggestions(false);
      setCitySuggestions([]);
      return;
    }

    cityTimeoutRef.current = setTimeout(async () => {
      try {
        setLoadingCities(true);
        const countryParam = jobData.country ? `&country=${encodeURIComponent(jobData.country)}` : '';
        const response = await axios.get(`/api/location/autocomplete?type=city&query=${encodeURIComponent(value)}${countryParam}`);
        
        if (response.data.success) {
          setCitySuggestions(response.data.suggestions);
          setShowCitySuggestions(true);
        }
      } catch (error) {
        console.error('City autocomplete error:', error);
        setCitySuggestions([]);
      } finally {
        setLoadingCities(false);
      }
    }, 300);
  };

  // Handle country selection
  const selectCountry = (country) => {
    setJobData(prev => ({ ...prev, country: country.value }));
    setShowCountrySuggestions(false);
    setCountrySuggestions([]);
    
    // Clear city when country changes
    if (jobData.city) {
      setJobData(prev => ({ ...prev, city: "" }));
    }
  };

  // Handle city selection
  const selectCity = (city) => {
    setJobData(prev => ({ ...prev, city: city.value }));
    setShowCitySuggestions(false);
    setCitySuggestions([]);
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (countryInputRef.current && !countryInputRef.current.contains(event.target)) {
        setShowCountrySuggestions(false);
      }
      if (cityInputRef.current && !cityInputRef.current.contains(event.target)) {
        setShowCitySuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (countryTimeoutRef.current) {
        clearTimeout(countryTimeoutRef.current);
      }
      if (cityTimeoutRef.current) {
        clearTimeout(cityTimeoutRef.current);
      }
    };
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Check if user is authenticated
    if (status === "loading") {
      setFeedback({ message: 'Checking authentication...', type: 'info' });
      return;
    }
    
    if (!session?.user?.email) {
      setFeedback({ 
        message: 'Please log in to post a job', 
        type: 'error' 
      });
      return;
    }
    
    setIsSubmitting(true);
    setFeedback({ message: 'Posting job...', type: 'info' });

    // Prepare JSON payload instead of FormData
    const payload = {
      userEmail: session.user.email,
      title: jobData.title,
      company: jobData.company,
      industry: jobData.industry,
      experience: jobData.experience,
      salary: jobData.salary,
      country: jobData.country,
      city: jobData.city,
      requiredSkills: jobData.requiredSkills,
      preferredSkills: jobData.preferredSkills,
      website: jobData.website,
      applicationDeadline: jobData.applicationDeadline,
      screeningDeadline: jobData.screeningDeadline,
      employmentType: jobData.employmentType,
      workMode: jobData.workMode,
      description: jobData.description,
      questionSource: jobData.questionSource,
      visaSponsorship: jobData.visaSponsorship
    };

    console.log('📤 Sending payload:', payload);

    try {
      const res = await axios.post("/api/jobs/post", payload, {
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      if (res.data.success) {
        const jobId = res.data.jobId;
        
        // Show simple success message with Job ID
        setFeedback({ 
          message: `Job posted successfully! Job ID: ${jobId}`, 
          type: 'success' 
        });
        
        console.log('✅ Job created with ID:', jobId);
        
        // Redirect after a longer delay to show the Job ID
        setTimeout(() => {
          router.push("/recruiter/dashboard");
        }, 4000);
      }
    } catch (err) {
      console.error('❌ Job posting error:', err);
      
      let errorMessage = 'Failed to post job';
      
      if (err.response?.data?.message) {
        errorMessage += ': ' + err.response.data.message;
      } else if (err.response?.data?.errors) {
        errorMessage += ': ' + err.response.data.errors.join(', ');
      } else if (err.message) {
        errorMessage += ': ' + err.message;
      }
      
      setFeedback({ 
        message: errorMessage, 
        type: 'error' 
      });
      setIsSubmitting(false);
    }
  };

  const showUploadField =
    jobData.questionSource === "50% AI + 50% Recruiter" ||
    jobData.questionSource === "100% Recruiter";

  return (
    <div className="max-w-xl mx-auto mt-6 p-4 bg-white shadow">
      <h2 className="text-xl font-bold text-blue-700 mb-4">Post a New Job</h2>
      
      {/* Authentication Check */}
      {status === "loading" && (
        <div className="p-3 mb-4 rounded bg-blue-100 text-blue-800">
          Checking authentication...
        </div>
      )}
      
      {status === "unauthenticated" && (
        <div className="p-3 mb-4 rounded bg-red-100 text-red-800">
          Please log in to post a job.
        </div>
      )}
      
      {session?.user && (
        <div className="p-3 mb-4 rounded bg-green-100 text-green-800">
          Posting as: {session.user.email}
        </div>
      )}
      
      {feedback.message && (
        <div className={`p-3 mb-4 rounded ${
          feedback.type === 'success' ? 'bg-green-100 text-green-800' : 
          feedback.type === 'error' ? 'bg-red-100 text-red-800' : 
          feedback.type === 'warning' ? 'bg-yellow-100 text-yellow-800' : 
          'bg-blue-100 text-blue-800'
        }`}>
          {feedback.message}
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-3">
        <input 
          name="title" 
          placeholder="Job Title" 
          className="border p-2 rounded w-full" 
          value={jobData.title}
          onChange={handleChange} 
          required 
        />
        <input 
          name="company" 
          placeholder="Company Name" 
          className="border p-2 rounded w-full" 
          value={jobData.company}
          onChange={handleChange} 
          required 
        />
        
        <select name="industry" value={jobData.industry} className="border p-2 rounded w-full" onChange={handleChange} required>
          <option value="">Select Industry</option>
          <option value="Information Technology">Information Technology</option>
          <option value="Healthcare">Healthcare</option>
          <option value="Finance">Finance</option>
          <option value="Education">Education</option>
          <option value="Retail">Retail</option>
          <option value="Transportation">Transportation</option>
          <option value="Manufacturing">Manufacturing</option>
          <option value="Marketing">Marketing</option>
          <option value="Construction">Construction</option>
          <option value="Legal">Legal</option>
          <option value="Energy">Energy</option>
          <option value="Insurance">Insurance</option>
          <option value="Government">Government</option>
          <option value="Non-profit">Non-profit</option>
          <option value="Real Estate">Real Estate</option>
          <option value="Hospitality">Hospitality</option>
          <option value="Telecommunications">Telecommunications</option>
          <option value="Aerospace">Aerospace</option>
          <option value="Entertainment">Entertainment</option>
          <option value="Biotechnology">Biotechnology</option>
          <option value="Other">Other</option>
        </select>
        
        <input 
          name="experience" 
          placeholder="Experience Level" 
          className="border p-2 rounded w-full" 
          value={jobData.experience}
          onChange={handleChange} 
        />
        <input 
          name="salary" 
          placeholder="Salary Range" 
          className="border p-2 rounded w-full" 
          value={jobData.salary}
          onChange={handleChange} 
        />
        
        {/* Enhanced Country Input with Autocomplete */}
        <div className="relative" ref={countryInputRef}>
          <input 
            name="country" 
            placeholder="Country (Start typing...)" 
            className="border p-2 rounded w-full" 
            value={jobData.country}
            onChange={handleChange}
            autoComplete="off"
          />
          {loadingCountries && (
            <div className="absolute right-2 top-2">
              <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
            </div>
          )}
          {showCountrySuggestions && countrySuggestions.length > 0 && (
            <div className="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
              {countrySuggestions.map((country, index) => (
                <div
                  key={index}
                  className="px-3 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0"
                  onClick={() => selectCountry(country)}
                >
                  <div className="font-medium">{country.label}</div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Enhanced City Input with Autocomplete */}
        <div className="relative" ref={cityInputRef}>
          <input 
            name="city" 
            placeholder="City, State (Start typing...)" 
            className="border p-2 rounded w-full" 
            value={jobData.city}
            onChange={handleChange}
            autoComplete="off"
          />
          {loadingCities && (
            <div className="absolute right-2 top-2">
              <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
            </div>
          )}
          {showCitySuggestions && citySuggestions.length > 0 && (
            <div className="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
              {citySuggestions.map((city, index) => (
                <div
                  key={index}
                  className="px-3 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0"
                  onClick={() => selectCity(city)}
                >
                  <div className="font-medium">{city.label}</div>
                  {city.state && (
                    <div className="text-sm text-gray-600">{city.city}, {city.state}</div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
        
        <input 
          name="requiredSkills" 
          placeholder="Required Skills (comma-separated)" 
          className="border p-2 rounded w-full" 
          value={jobData.requiredSkills}
          onChange={handleChange} 
        />
        <input 
          name="preferredSkills" 
          placeholder="Preferred Skills (comma-separated)" 
          className="border p-2 rounded w-full" 
          value={jobData.preferredSkills}
          onChange={handleChange} 
        />
        <input 
          name="website" 
          placeholder="Company Website" 
          className="border p-2 rounded w-full" 
          value={jobData.website}
          onChange={handleChange} 
        />

        <label className="text-sm font-medium">Application Deadline</label>
        <input 
          type="date" 
          name="applicationDeadline" 
          className="border p-2 rounded w-full" 
          value={jobData.applicationDeadline}
          onChange={handleChange} 
        />

        <label className="text-sm font-medium">Screening/Interview Deadline</label>
        <input 
          type="date" 
          name="screeningDeadline" 
          className="border p-2 rounded w-full" 
          value={jobData.screeningDeadline}
          onChange={handleChange} 
        />

        <select name="employmentType" value={jobData.employmentType} className="border p-2 rounded w-full" onChange={handleChange}>
          <option value="Full-time">Full-time</option>
          <option value="Part-time">Part-time</option>
          <option value="Contract">Contract</option>
        </select>

        <select name="workMode" value={jobData.workMode} className="border p-2 rounded w-full" onChange={handleChange}>
          <option value="">Select Work Mode</option>
          <option value="On-site">On-site</option>
          <option value="Remote">Remote</option>
          <option value="Hybrid">Hybrid</option>
        </select>

        <textarea 
          name="description" 
          placeholder="Job Description" 
          className="border p-2 rounded w-full" 
          rows={4} 
          value={jobData.description}
          onChange={handleChange} 
        />

        <select name="questionSource" value={jobData.questionSource} className="border p-2 rounded w-full" onChange={handleChange}>
          <option value="">Select Question Source</option>
          <option value="50% AI + 50% Recruiter">50% AI + 50% Recruiter</option>
          <option value="100% Recruiter">100% Recruiter</option>
          <option value="100% AI">100% AI</option>
        </select>

        {showUploadField && (
          <div className="p-3 bg-yellow-100 text-yellow-800 rounded">
            <p className="text-sm mb-2">File upload temporarily disabled. Questions will be generated based on job description.</p>
            <input 
              type="file" 
              accept=".txt,.csv" 
              onChange={handleFileChange} 
              className="border p-2 rounded w-full opacity-50" 
              disabled
            />
          </div>
        )}

        <select name="visaSponsorship" value={jobData.visaSponsorship} className="border p-2 rounded w-full" onChange={handleChange}>
          <option value="no">Visa Sponsorship Required? No</option>
          <option value="yes">Visa Sponsorship Required? Yes</option>
        </select>

        <button 
          type="submit" 
          className={`${isSubmitting ? 'bg-gray-500' : 'bg-green-600'} text-white px-4 py-2 rounded w-full hover:bg-green-700 transition-colors`}
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Posting Job...' : 'Post Job'}
        </button>
      </form>
    </div>
  );
}
