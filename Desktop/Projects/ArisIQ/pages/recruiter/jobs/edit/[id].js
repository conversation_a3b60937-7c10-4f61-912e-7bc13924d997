// File: pages/recruiter/jobs/edit/[id].js

import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import axios from "axios";
import Link from "next/link";

export default function EditJob() {
  const router = useRouter();
  const { id } = router.query;
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [jobData, setJobData] = useState({
    title: "",
    company: "",
    industry: "",
    experience: "",
    salary: "",
    country: "",
    city: "",
    requiredSkills: "",
    preferredSkills: "",
    website: "",
    applicationDeadline: "",
    screeningDeadline: "",
    employmentType: "Full-time",
    workMode: "", 
    description: "",
    questionSource: "",
    visaSponsorship: "no",
    questionsFile: null,
  });
  
  const [feedback, setFeedback] = useState({ message: '', type: '' });
  const [error, setError] = useState(null);

  // Fetch existing job data
  useEffect(() => {
    if (!id) return;
    
    const fetchJob = async () => {
      try {
        setLoading(true);
        const res = await axios.get(`/api/jobs/${id}`);
        const job = res.data.job;
        
        // Format dates for input fields
        const formatDateForInput = (dateString) => {
          if (!dateString) return "";
          const date = new Date(dateString);
          return date.toISOString().split('T')[0];
        };

        setJobData({
          title: job.title || "",
          company: job.company || "",
          industry: job.industry || "",
          experience: job.experience || "",
          salary: job.salary || "",
          country: job.country || "",
          city: job.city || "",
          requiredSkills: job.requiredSkills || "",
          preferredSkills: job.preferredSkills || "",
          website: job.website || "",
          applicationDeadline: formatDateForInput(job.applicationDeadline),
          screeningDeadline: formatDateForInput(job.screeningDeadline),
          employmentType: job.employmentType || "Full-time",
          workMode: job.workMode || "",
          description: job.description || "",
          questionSource: job.questionSource || "",
          visaSponsorship: job.visaSponsorship || "no",
          questionsFile: null,
        });
      } catch (err) {
        console.error('Error fetching job:', err);
        setError('Failed to load job data');
      } finally {
        setLoading(false);
      }
    };
    
    fetchJob();
  }, [id]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setJobData((prev) => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e) => {
    setJobData((prev) => ({ ...prev, questionsFile: e.target.files[0] }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setFeedback({ message: 'Updating job...', type: 'info' });

    try {
      // For job updates, we'll use PUT method with JSON data
      // File uploads for questions would need separate handling
      const updateData = { ...jobData };
      delete updateData.questionsFile; // Remove file from update data for now

      const res = await axios.put(`/api/jobs/${id}`, updateData);
      
      if (res.data.success) {
        setFeedback({ 
          message: 'Job updated successfully!', 
          type: 'success' 
        });
        
        // Redirect after a short delay to show the success message
        setTimeout(() => {
          router.push("/recruiter/dashboard");
        }, 2000);
      }
    } catch (err) {
      console.error(err);
      setFeedback({ 
        message: 'Failed to update job: ' + (err.response?.data?.message || err.message), 
        type: 'error' 
      });
      setIsSubmitting(false);
    }
  };

  const showUploadField =
    jobData.questionSource === "50% AI + 50% Recruiter" ||
    jobData.questionSource === "100% Recruiter";

  if (loading) {
    return (
      <div className="max-w-xl mx-auto mt-6 p-4 bg-white shadow">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="h-10 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-xl mx-auto mt-6 p-4 bg-white shadow">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <h2 className="text-lg font-semibold mb-2">Error</h2>
          <p>{error}</p>
          <Link href="/recruiter/dashboard" className="mt-3 inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
            Back to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-xl mx-auto mt-6 p-4 bg-white shadow">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-blue-700">Edit Job</h2>
        <Link 
          href="/recruiter/dashboard"
          className="text-sm text-gray-600 hover:text-gray-800"
        >
          ← Back to Dashboard
        </Link>
      </div>
      
      {feedback.message && (
        <div className={`p-3 mb-4 rounded ${
          feedback.type === 'success' ? 'bg-green-100 text-green-800' : 
          feedback.type === 'error' ? 'bg-red-100 text-red-800' : 
          feedback.type === 'warning' ? 'bg-yellow-100 text-yellow-800' : 
          'bg-blue-100 text-blue-800'
        }`}>
          {feedback.message}
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-3">
        <input 
          name="title" 
          placeholder="Job Title" 
          className="border p-2 rounded w-full" 
          value={jobData.title}
          onChange={handleChange} 
          required 
        />
        <input 
          name="company" 
          placeholder="Company Name" 
          className="border p-2 rounded w-full" 
          value={jobData.company}
          onChange={handleChange} 
          required 
        />
        <select 
          name="industry" 
          className="border p-2 rounded w-full" 
          value={jobData.industry}
          onChange={handleChange} 
          required
        >
          <option value="">Select Industry</option>
          <option value="Information Technology">Information Technology</option>
          <option value="Healthcare">Healthcare</option>
          <option value="Finance">Finance</option>
          <option value="Education">Education</option>
          <option value="Retail">Retail</option>
          <option value="Transportation">Transportation</option>
          <option value="Manufacturing">Manufacturing</option>
          <option value="Marketing">Marketing</option>
          <option value="Construction">Construction</option>
          <option value="Legal">Legal</option>
          <option value="Energy">Energy</option>
          <option value="Insurance">Insurance</option>
          <option value="Government">Government</option>
          <option value="Non-profit">Non-profit</option>
          <option value="Real Estate">Real Estate</option>
          <option value="Hospitality">Hospitality</option>
          <option value="Telecommunications">Telecommunications</option>
          <option value="Aerospace">Aerospace</option>
          <option value="Entertainment">Entertainment</option>
          <option value="Biotechnology">Biotechnology</option>
          <option value="Other">Other</option>
        </select>
        <input 
          name="experience" 
          placeholder="Experience Level" 
          className="border p-2 rounded w-full" 
          value={jobData.experience}
          onChange={handleChange} 
        />
        <input 
          name="salary" 
          placeholder="Salary Range" 
          className="border p-2 rounded w-full" 
          value={jobData.salary}
          onChange={handleChange} 
        />
        <input 
          name="country" 
          placeholder="Country (AI-powered)" 
          className="border p-2 rounded w-full" 
          value={jobData.country}
          onChange={handleChange} 
        />
        <input 
          name="city" 
          placeholder="City (AI-powered)" 
          className="border p-2 rounded w-full" 
          value={jobData.city}
          onChange={handleChange} 
        />
        <input 
          name="requiredSkills" 
          placeholder="Required Skills (comma-separated)" 
          className="border p-2 rounded w-full" 
          value={jobData.requiredSkills}
          onChange={handleChange} 
        />
        <input 
          name="preferredSkills" 
          placeholder="Preferred Skills (comma-separated)" 
          className="border p-2 rounded w-full" 
          value={jobData.preferredSkills}
          onChange={handleChange} 
        />
        <input 
          name="website" 
          placeholder="Company Website" 
          className="border p-2 rounded w-full" 
          value={jobData.website}
          onChange={handleChange} 
        />

        <label className="text-sm font-medium">Application Deadline</label>
        <input 
          type="date" 
          name="applicationDeadline" 
          className="border p-2 rounded w-full" 
          value={jobData.applicationDeadline}
          onChange={handleChange} 
        />

        <label className="text-sm font-medium">Screening/Interview Deadline</label>
        <input 
          type="date" 
          name="screeningDeadline" 
          className="border p-2 rounded w-full" 
          value={jobData.screeningDeadline}
          onChange={handleChange} 
        />

        <select 
          name="employmentType" 
          className="border p-2 rounded w-full" 
          value={jobData.employmentType}
          onChange={handleChange}
        >
          <option value="Full-time">Full-time</option>
          <option value="Part-time">Part-time</option>
          <option value="Contract">Contract</option>
        </select>

        <select 
          name="workMode" 
          className="border p-2 rounded w-full" 
          value={jobData.workMode}
          onChange={handleChange}
        >
          <option value="">Select Work Mode</option>
          <option value="On-site">On-site</option>
          <option value="Remote">Remote</option>
          <option value="Hybrid">Hybrid</option>
        </select>

        <textarea 
          name="description" 
          placeholder="Job Description" 
          className="border p-2 rounded w-full" 
          rows={4} 
          value={jobData.description}
          onChange={handleChange} 
        />

        <select 
          name="questionSource" 
          className="border p-2 rounded w-full" 
          value={jobData.questionSource}
          onChange={handleChange}
        >
          <option value="">Select Question Source</option>
          <option value="50% AI + 50% Recruiter">50% AI + 50% Recruiter</option>
          <option value="100% Recruiter">100% Recruiter</option>
          <option value="100% AI">100% AI</option>
        </select>

        {showUploadField && (
          <div>
            <input type="file" accept=".txt,.csv" onChange={handleFileChange} className="border p-2 rounded w-full" />
            <p className="text-xs text-gray-500 mt-1">Note: File uploads for question updates need separate implementation</p>
          </div>
        )}

        <select 
          name="visaSponsorship" 
          className="border p-2 rounded w-full" 
          value={jobData.visaSponsorship}
          onChange={handleChange}
        >
          <option value="no">Visa Sponsorship Required? No</option>
          <option value="yes">Visa Sponsorship Required? Yes</option>
        </select>

        <div className="flex space-x-3">
          <button 
            type="submit" 
            className={`flex-1 ${isSubmitting ? 'bg-gray-500' : 'bg-blue-600'} text-white px-4 py-2 rounded`}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Updating Job...' : 'Update Job'}
          </button>
          <Link 
            href="/recruiter/dashboard"
            className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-100 text-center"
          >
            Cancel
          </Link>
        </div>
      </form>
    </div>
  );
}
