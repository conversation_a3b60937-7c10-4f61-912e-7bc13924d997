// File: pages/recruiter/jobs/EnhancedResumeDetails.js
import React, { useState } from 'react';

const EnhancedResumeDetails = ({ application, onClose }) => {
  const [activeTab, setActiveTab] = useState('skills');
  
  // Safely get values from potentially undefined nested objects
  const safeGet = (obj, path, defaultVal = 'N/A') => {
    if (!obj) return defaultVal;
    
    const keys = path.split('.');
    let result = obj;
    
    for (const key of keys) {
      if (!result || typeof result !== 'object') return defaultVal;
      result = result[key];
      if (result === undefined || result === null) return defaultVal;
    }
    
    return result;
  };

  // Get scores with fallbacks
  const resumeScore = Number(safeGet(application, 'scanResult.compare.match_score', 0));
  const testScore = application.testScore ? Number(application.testScore) : 'N/A';
  const cumulative = resumeScore + (typeof testScore === 'number' ? testScore : 0);
  
  // Get matched skills arrays with fallbacks
  const matchedSkills = Array.isArray(safeGet(application, 'scanResult.compare.details.required_skills_matched')) 
    ? safeGet(application, 'scanResult.compare.details.required_skills_matched') 
    : [];
    
  const preferredSkills = Array.isArray(safeGet(application, 'scanResult.compare.details.preferred_skills_matched')) 
    ? safeGet(application, 'scanResult.compare.details.preferred_skills_matched') 
    : [];
  
  // Parse GPT score text to extract categories
  const parseGptScore = (text) => {
    if (!text || typeof text !== 'string') return {};
    
    try {
      // Try to extract categories from the text format:
      // 1. Required Skills Match: ...
      // 2. Experience Match: ...
      const categories = {};
      
      const lines = text.split('\n');
      let currentCategory = null;
      let currentContent = [];
      
      for (const line of lines) {
        const categoryMatch = line.match(/^\d+\.\s+(.*?):\s+(.*)$/);
        
        if (categoryMatch) {
          // Save previous category if exists
          if (currentCategory && currentContent.length > 0) {
            categories[currentCategory] = currentContent.join('\n');
            currentContent = [];
          }
          
          // Start new category
          currentCategory = categoryMatch[1];
          currentContent.push(categoryMatch[2]);
        } else if (currentCategory && line.trim()) {
          // Continue adding to current category
          currentContent.push(line);
        }
      }
      
      // Save the last category
      if (currentCategory && currentContent.length > 0) {
        categories[currentCategory] = currentContent.join('\n');
      }
      
      return categories;
    } catch (e) {
      console.error('Error parsing GPT score:', e);
      return {};
    }
  };
  
  // Get AI evaluations
  const gptEvalRaw = safeGet(application, 'scanResult.gptScore', '');
  const gptEvaluation = parseGptScore(gptEvalRaw);
  const claudeEvalRaw = safeGet(application, 'scanResult.claudeScore', '');
  
  // Get score tier
  const getScoreTier = (score) => {
    if (score >= 80) return { label: "Excellent Match", colorClass: "bg-green-100 text-green-800" };
    if (score >= 60) return { label: "Good Match", colorClass: "bg-blue-100 text-blue-800" };
    return { label: "Average Match", colorClass: "bg-yellow-100 text-yellow-800" };
  };
  
  const scoreTier = getScoreTier(cumulative);

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200 max-w-4xl mx-auto relative">
      {/* Fixed position header that stays visible */}
      <div className="sticky top-0 z-10 bg-white">
        {/* Header */}
        <div className="bg-gray-100 p-3 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-bold text-gray-800">Resume Evaluation Details</h2>
            <div className="flex items-center space-x-2">
              <div className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                {resumeScore}/100
              </div>
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${scoreTier.colorClass}`}>
                {scoreTier.label}
              </div>
            </div>
          </div>
          <div className="flex justify-between items-center mt-1">
            <div className="text-gray-600 text-sm">
              {application.fullName || application.candidateName || 'N/A'} • {application.email || 'N/A'}
            </div>
            <button 
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 p-1"
              aria-label="Close"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        </div>
        
        {/* Tabs - Always visible */}
        <div className="border-b border-gray-200 bg-white">
          <nav className="flex">
            <button
              onClick={() => setActiveTab('skills')}
              className={`px-4 py-2 font-medium text-sm border-b-2 ${
                activeTab === 'skills'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Matched Skills
            </button>
            <button
              onClick={() => setActiveTab('gpt')}
              className={`px-4 py-2 font-medium text-sm border-b-2 ${
                activeTab === 'gpt'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              GPT Evaluation
            </button>
            <button
              onClick={() => setActiveTab('claude')}
              className={`px-4 py-2 font-medium text-sm border-b-2 ${
                activeTab === 'claude'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Claude Evaluation
            </button>
            <button
              onClick={() => setActiveTab('summary')}
              className={`px-4 py-2 font-medium text-sm border-b-2 ${
                activeTab === 'summary'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Summary
            </button>
          </nav>
        </div>
      </div>
      
      {/* Tab Content - Scrollable */}
      <div className="p-4 max-h-[calc(100vh-180px)] overflow-y-auto">
        {activeTab === 'skills' && (
          <div>
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-3">Required Skills Match</h3>
              {matchedSkills.length > 0 ? (
                <div className="grid grid-cols-2 gap-4">
                  {matchedSkills.map((skill, index) => (
                    <div key={index} className="bg-green-50 border border-green-200 rounded-md p-3 flex items-center">
                      <div className="bg-green-100 p-1 rounded-full mr-3">
                        <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                      </div>
                      <span className="font-medium">{skill}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 italic">No matched skills information available.</p>
              )}
            </div>
            
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">Preferred Skills</h3>
              {preferredSkills.length > 0 ? (
                <div className="grid grid-cols-2 gap-4">
                  {preferredSkills.map((skill, index) => (
                    <div key={index} className="bg-blue-50 border border-blue-200 rounded-md p-3 flex items-center">
                      <div className="bg-blue-100 p-1 rounded-full mr-3">
                        <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                      </div>
                      <span className="font-medium">{skill}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 italic">No preferred skills information available.</p>
              )}
            </div>
          </div>
        )}
        
        {activeTab === 'gpt' && (
          <div className="space-y-4">
            {Object.keys(gptEvaluation).length > 0 ? (
              <>
                {Object.entries(gptEvaluation).map(([key, value], idx) => {
                  // Extract score if available (e.g. Score: 95/100)
                  const scoreMatch = value.match(/Score:\s*(\d+)\/100/);
                  const score = scoreMatch ? scoreMatch[1] : null;
                  
                  return (
                    <div key={idx} className="border border-gray-200 rounded-md overflow-hidden">
                      <div className="flex items-center justify-between bg-gray-50 px-4 py-2">
                        <h3 className="font-medium text-gray-800">{key}</h3>
                        {score !== null && (
                          <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                            {score}/100
                          </span>
                        )}
                      </div>
                      <div className="px-4 py-3 bg-white text-sm text-gray-700">
                        {value}
                      </div>
                    </div>
                  );
                })}
              </>
            ) : gptEvalRaw ? (
              <div className="bg-white p-4 rounded-md border border-gray-200">
                <p className="text-gray-700 whitespace-pre-line text-sm">
                  {gptEvalRaw}
                </p>
              </div>
            ) : (
              <div className="text-center py-8">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">No GPT evaluation available</h3>
                <p className="mt-1 text-sm text-gray-500">
                  GPT evaluation data is not available for this candidate.
                </p>
              </div>
            )}
          </div>
        )}
        
        {activeTab === 'claude' && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">Claude Evaluation</h3>
            {claudeEvalRaw ? (
              <div className="bg-white p-4 rounded-md border border-gray-200">
                <p className="text-gray-700 whitespace-pre-line text-sm">
                  {claudeEvalRaw}
                </p>
              </div>
            ) : (
              <div className="text-center py-8">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">No Claude evaluation available</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Claude evaluation data is not available for this candidate.
                </p>
              </div>
            )}
          </div>
        )}
        
        {activeTab === 'summary' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">Overall Evaluation</h3>
              <div className="bg-white p-4 rounded-md border border-gray-200">
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">Overall Score</span>
                    <span className="font-bold text-green-700">{cumulative}/100</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div 
                      className="bg-green-600 h-2.5 rounded-full" 
                      style={{ width: `${Math.min(cumulative, 100)}%` }}
                    ></div>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Resume Score</p>
                    <p className="font-medium">{resumeScore}/100</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Test Score</p>
                    <p className="font-medium">{testScore === 'N/A' ? 'N/A' : `${testScore}/100`}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Interview Status</p>
                    <p className="font-medium">{application.interviewStatus || 'Not Started'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Experience</p>
                    <p className="font-medium">{application.experienceYears || 'N/A'} years</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedResumeDetails;
