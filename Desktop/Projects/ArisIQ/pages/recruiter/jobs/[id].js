// File: pages/recruiter/jobs/[id].js

import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import axios from 'axios';
import Link from 'next/link';
import EnhancedResumeDetails from './EnhancedResumeDetails';

export default function JobApplicationsPage() {
  const router = useRouter();
  const { id } = router.query;
  const [job, setJob] = useState(null);
  const [applications, setApplications] = useState([]);
  const [statusFilter, setStatusFilter] = useState('');
  const [shortlistedOnly, setShortlistedOnly] = useState(false);
  const [sortBy, setSortBy] = useState('');
  const [activeView, setActiveView] = useState('category'); // Default to category view
  const [showModal, setShowModal] = useState(false);
  const [selectedCandidate, setSelectedCandidate] = useState(null);
  const [modalType, setModalType] = useState('details');
  const [showCloseModal, setShowCloseModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [toastMessage, setToastMessage] = useState('');
  const [selectedCandidates, setSelectedCandidates] = useState(new Set());
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [hoveredCard, setHoveredCard] = useState(null);

  useEffect(() => {
    if (!id) return;
    
    const fetchJobAndApplications = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const res = await axios.get(`/api/jobs/${id}`);
        setJob(res.data.job);
        
        if (res.data.applications && Array.isArray(res.data.applications)) {
          setApplications(res.data.applications);
        } else {
          console.error("Applications data is not an array:", res.data.applications);
          setApplications([]);
          setError("Applications data format is invalid");
        }
      } catch (err) {
        console.error('Error fetching job or applications:', err);
        setError(`Failed to load data: ${err.message || 'Unknown error'}`);
        setApplications([]);
      } finally {
        setLoading(false);
      }
    };
    
    fetchJobAndApplications();
  }, [id]);

  const getCumulativeScore = (app) => {
    const resumeScore = app.scanResult?.compare?.match_score || 0;
    const testScore = app.testScore || 0;
    return resumeScore + testScore;
  };

  const hasCompletedScreening = (app) => {
    return app.interviewStatus === 'completed' && app.status !== 'disqualified';
  };

  const isEligibleForInterview = (app) => {
    const hasTestScore = app.testScore && app.testScore > 0;
    const score = getCumulativeScore(app);
    return score >= 80 && hasCompletedScreening(app) && hasTestScore;
  };

  const isEligibleForScreening = (app) => {
    const score = getCumulativeScore(app);
    return score >= 60 && score < 80 && app.interviewStatus !== 'completed';
  };

  const getCurrentStatusLabel = (app) => {
    if (app.status === 'hired') return 'Hired';
    if (app.status === 'rejected') return 'Rejected';
    if (app.status === 'disqualified' || app.interviewStatus === 'disqualified') return 'Disqualified';
    if (hasCompletedScreening(app)) return 'Screening Complete';
    if (app.screeningInviteSent || app.interviewStatus === 'in_progress') return 'Screening Sent';
    if (app.interviewStatus === 'completed') return 'Interview Done';
    return 'Applied';
  };

  const getJobStatus = (job) => {
    return job?.status || 'open';
  };

  const isJobClosed = (job) => {
    return getJobStatus(job) === 'closed';
  };

  const handleClosePosition = async () => {
    try {
      await axios.patch(`/api/jobs/${id}`, { 
        status: 'closed',
        closedAt: new Date().toISOString()
      });
      
      setJob(prev => ({
        ...prev,
        status: 'closed',
        closedAt: new Date().toISOString()
      }));
      
      setToastMessage('✅ Position closed successfully');
    } catch (error) {
      setToastMessage('❌ Failed to close position');
      console.error(error);
    } finally {
      setShowCloseModal(false);
      setTimeout(() => setToastMessage(''), 3000);
    }
  };

  const handleReopenPosition = async () => {
    try {
      await axios.patch(`/api/jobs/${id}`, { 
        status: 'open',
        closedAt: null
      });
      
      setJob(prev => ({
        ...prev,
        status: 'open',
        closedAt: null
      }));
      
      setToastMessage('✅ Position reopened successfully');
      setTimeout(() => setToastMessage(''), 3000);
    } catch (error) {
      setToastMessage('❌ Failed to reopen position');
      console.error(error);
      setTimeout(() => setToastMessage(''), 3000);
    }
  };

  // Bulk actions
  const handleSelectCandidate = (candidateId) => {
    const newSelected = new Set(selectedCandidates);
    if (newSelected.has(candidateId)) {
      newSelected.delete(candidateId);
    } else {
      newSelected.add(candidateId);
    }
    setSelectedCandidates(newSelected);
    setShowBulkActions(newSelected.size > 0);
  };

  const handleSelectAll = (candidates) => {
    const candidateIds = candidates.map(app => app._id);
    setSelectedCandidates(new Set(candidateIds));
    setShowBulkActions(true);
  };

  const handleDeselectAll = () => {
    setSelectedCandidates(new Set());
    setShowBulkActions(false);
  };

  const handleBulkAction = async (action) => {
    const selectedIds = Array.from(selectedCandidates);
    try {
      // Implement bulk actions here
      console.log(`Performing ${action} on candidates:`, selectedIds);
      setToastMessage(`✅ ${action} applied to ${selectedIds.length} candidates`);
      setTimeout(() => setToastMessage(''), 3000);
    } catch (error) {
      setToastMessage(`❌ Failed to ${action} candidates`);
      setTimeout(() => setToastMessage(''), 3000);
    }
    handleDeselectAll();
  };

  // Filter applications
  const filteredApplications = applications
    .filter(app => {
      if (shortlistedOnly && !app.canTakeInterview) return false;
      if (statusFilter && app.interviewStatus !== statusFilter) return false;
      return true;
    })
    .sort((a, b) => {
      if (sortBy === 'highest_score') return getCumulativeScore(b) - getCumulativeScore(a);
      if (sortBy === 'lowest_score') return getCumulativeScore(a) - getCumulativeScore(b);
      if (sortBy === 'resume_score') return (b.scanResult?.compare?.match_score || 0) - (a.scanResult?.compare?.match_score || 0);
      if (sortBy === 'test_score') return (b.testScore || 0) - (a.testScore || 0);
      return 0;
    });

  // Categorize applications
  const highScoreApplications = filteredApplications.filter(app => getCumulativeScore(app) >= 80);
  const mediumScoreApplications = filteredApplications.filter(app => {
    const score = getCumulativeScore(app);
    return score >= 60 && score < 80;
  });
  const lowScoreApplications = filteredApplications.filter(app => getCumulativeScore(app) < 60);

  const showDetailsModal = (title, content) => {
    if (title && content) {
      const win = window.open('', '_blank', 'width=600,height=400');
      win.document.write(`<title>${title}</title><pre>${content}</pre>`);
      return;
    }
    setModalType('details');
    setShowModal(true);
  };

  const handleStatusChange = async (applicationId, newStatus) => {
    try {
      await axios.put('/api/recruiter/update-application-status', {
        applicationId,
        status: newStatus
      });
      
      setApplications(applications.map(app => 
        app._id === applicationId ? {...app, status: newStatus} : app
      ));
      
      setShowModal(false);
      setSelectedCandidate(null);
      setToastMessage('✅ Status updated successfully');
      setTimeout(() => setToastMessage(''), 3000);
    } catch (err) {
      console.error('Error updating status:', err);
      setToastMessage('❌ Failed to update status');
      setTimeout(() => setToastMessage(''), 3000);
    }
  };

  const handleSendScreeningInvite = async (applicationId) => {
    try {
      await axios.post('/api/recruiter/send-screening-invite', {
        applicationId
      });
      
      setApplications(applications.map(app => 
        app._id === applicationId ? {...app, screeningInviteSent: true} : app
      ));
      
      setToastMessage('✅ Screening invitation sent successfully');
      setShowModal(false);
      setSelectedCandidate(null);
      setTimeout(() => setToastMessage(''), 3000);
    } catch (err) {
      console.error('Error sending screening invite:', err);
      setToastMessage('❌ Failed to send screening invitation');
      setTimeout(() => setToastMessage(''), 3000);
    }
  };

  const handleScheduleInterview = async (applicationId) => {
    try {
      router.push(`/recruiter/schedule-interview/${applicationId}`);
    } catch (err) {
      console.error('Error scheduling interview:', err);
      setToastMessage('❌ Failed to schedule interview');
      setTimeout(() => setToastMessage(''), 3000);
    }
  };

  // Enhanced Card Components
  const ScoreCard = ({ title, count, color, gradient, icon, percentage, delay = 0 }) => (
    <div 
      className={`relative overflow-hidden rounded-2xl bg-white/50 backdrop-blur-xl border border-white/60 p-6 transform transition-all duration-500 hover:scale-105 hover:shadow-2xl cursor-pointer animate-fade-in-up`}
      style={{ 
        animationDelay: `${delay}ms`,
        boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)'
      }}
      onMouseEnter={() => setHoveredCard(title)}
      onMouseLeave={() => setHoveredCard(null)}
    >
      <div 
        className="absolute top-0 left-0 right-0 h-2 rounded-t-xl"
        style={{ background: gradient }}
      ></div>
      
      <div className="absolute inset-0 opacity-10">
        <div className={`absolute inset-0 bg-gradient-to-r ${color} transform rotate-12 translate-x-full hover:translate-x-0 transition-transform duration-1000`}></div>
      </div>
      
      <div className="relative z-10">
        <div className="flex items-center justify-between mb-4">
          <div className="text-slate-700 text-sm font-medium">{title}</div>
          <div className="text-2xl">{icon}</div>
        </div>
        <div className="text-3xl font-bold text-slate-900 mb-2">{count}</div>
        <div className="flex items-center justify-between">
          <div className="text-slate-600 text-sm">Candidates</div>
          <div className="text-slate-600 text-sm font-medium">{percentage}%</div>
        </div>
        
        {/* Progress bar */}
        <div className="mt-3 bg-white/40 rounded-full h-2">
          <div 
            className="h-2 rounded-full transition-all duration-1000"
            style={{ 
              width: `${percentage}%`,
              background: gradient
            }}
          ></div>
        </div>
      </div>
      
      {hoveredCard === title && (
        <div className={`absolute inset-0 bg-gradient-to-r ${color}/20 rounded-2xl`}></div>
      )}
    </div>
  );

  const CandidateCard = ({ app, index, category }) => {
    const resumeScore = app.scanResult?.compare?.match_score || 0;
    const testScore = app.testScore || 0;
    const cumulative = getCumulativeScore(app);
    const isSelected = selectedCandidates.has(app._id);
    
    const getScoreColor = (score) => {
      if (score >= 80) return 'from-green-500 to-emerald-600';
      if (score >= 60) return 'from-yellow-500 to-orange-500';
      return 'from-red-500 to-pink-600';
    };

    const getCategoryColors = () => {
      if (category === 'high') return {
        border: 'border-green-200/60',
        bg: 'bg-green-50/60',
        accent: 'from-green-500 to-emerald-600'
      };
      if (category === 'medium') return {
        border: 'border-yellow-200/60',
        bg: 'bg-yellow-50/60',
        accent: 'from-yellow-500 to-orange-500'
      };
      return {
        border: 'border-red-200/60',
        bg: 'bg-red-50/60',
        accent: 'from-red-500 to-pink-600'
      };
    };

    const colors = getCategoryColors();

    return (
      <div 
        className={`relative overflow-hidden rounded-2xl ${colors.bg} backdrop-blur-md border ${colors.border} p-6 hover:shadow-xl transition-all duration-500 animate-fade-in-up group ${isSelected ? 'ring-2 ring-blue-500' : ''}`}
        style={{ 
          animationDelay: `${index * 50}ms`,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)'
        }}
      >
        {/* Selection overlay */}
        {isSelected && (
          <div className="absolute inset-0 bg-blue-100/30 rounded-2xl pointer-events-none"></div>
        )}

        {/* Gradient accent bar */}
        <div 
          className="absolute top-0 left-0 right-0 h-1 rounded-t-xl"
          style={{ background: `linear-gradient(135deg, ${colors.accent})` }}
        ></div>
        
        {/* Animated background on hover */}
        <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
          <div className={`absolute inset-0 bg-gradient-to-r ${colors.accent.replace('from-', 'from-').replace(' to-', '/20 to-')}/20 rounded-2xl`}></div>
        </div>

        <div className="relative z-10">
          {/* Header with selection and score */}
          <div className="flex justify-between items-start mb-4">
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={isSelected}
                onChange={() => handleSelectCandidate(app._id)}
                className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
              />
              <div>
                <h3 className="text-lg font-semibold text-slate-800">{app.fullName || 'N/A'}</h3>
                <p className="text-sm text-slate-600">{app.email}</p>
              </div>
            </div>
            
            {/* Score circle */}
            <div className="relative">
              <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${getScoreColor(cumulative)} flex items-center justify-center text-white font-bold text-lg shadow-lg`}>
                {cumulative}
              </div>
              <div className="absolute -bottom-2 -right-2 bg-white rounded-full p-1 shadow-lg">
                <div className="text-xs text-slate-600 font-medium px-2 py-1">
                  {cumulative >= 80 ? '🎯' : cumulative >= 60 ? '⚡' : '📊'}
                </div>
              </div>
            </div>
          </div>

          {/* Details grid */}
          <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
            <div className="space-y-2">
              <div className="flex items-center text-slate-700">
                <span className="w-6">💼</span>
                <span>{app.experienceYears || 'N/A'} years exp</span>
              </div>
              <div className="flex items-center text-slate-700">
                <span className="w-6">💰</span>
                <span>{app.expectedSalary || 'N/A'}</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center text-slate-700">
                <span className="w-6">📄</span>
                <span>Resume: {resumeScore}</span>
              </div>
              <div className="flex items-center text-slate-700">
                <span className="w-6">🧪</span>
                <span>Test: {testScore || 'N/A'}</span>
              </div>
            </div>
          </div>

          {/* Status badges */}
          <div className="flex flex-wrap gap-2 mb-4">
            <span className={`px-3 py-1 text-xs font-medium rounded-full ${
              getCurrentStatusLabel(app) === 'Hired' ? 'bg-emerald-100/80 text-emerald-700' :
              getCurrentStatusLabel(app) === 'Rejected' ? 'bg-red-100/80 text-red-700' :
              getCurrentStatusLabel(app) === 'Disqualified' ? 'bg-red-100/80 text-red-700' :
              getCurrentStatusLabel(app) === 'Screening Complete' ? 'bg-blue-100/80 text-blue-700' :
              getCurrentStatusLabel(app) === 'Screening Sent' ? 'bg-yellow-100/80 text-yellow-700' :
              getCurrentStatusLabel(app) === 'Interview Done' ? 'bg-purple-100/80 text-purple-700' :
              'bg-gray-100/80 text-gray-700'
            }`}>
              {getCurrentStatusLabel(app)}
            </span>
            
            {app.canTakeInterview && (
              <span className="px-3 py-1 text-xs font-medium rounded-full bg-purple-100/80 text-purple-700">
                Shortlisted
              </span>
            )}
            
            {isEligibleForInterview(app) && (
              <span className="px-3 py-1 text-xs font-medium rounded-full bg-emerald-100/80 text-emerald-700">
                Interview Ready
              </span>
            )}
          </div>

          {/* Action buttons */}
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => {
                setSelectedCandidate(app);
                setModalType('resume-details');
                setShowModal(true);
              }}
              className="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 text-sm font-medium shadow-lg"
            >
              📄 Resume Score Details
            </button>

            <button
              onClick={() => {
                setSelectedCandidate(app);
                setModalType('test-details');
                setShowModal(true);
              }}
              className="px-4 py-2 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg hover:from-purple-600 hover:to-purple-700 transition-all duration-200 text-sm font-medium shadow-lg"
            >
              🧪 Test Score Details
            </button>

            {!isJobClosed(job) && isEligibleForInterview(app) && (
              <button 
                onClick={() => {
                  setSelectedCandidate(app);
                  setModalType('interview');
                  setShowModal(true);
                }}
                className="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-200 text-sm font-medium shadow-lg"
              >
                🎯 Schedule Interview
              </button>
            )}
            
            {!isJobClosed(job) && isEligibleForScreening(app) && (
              <button 
                onClick={() => {
                  setSelectedCandidate(app);
                  setModalType('screening');
                  setShowModal(true);
                }}
                className="px-4 py-2 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-lg hover:from-orange-600 hover:to-orange-700 transition-all duration-200 text-sm font-medium shadow-lg"
              >
                📧 Send Screening
              </button>
            )}
            
            <button 
              onClick={() => {
                setSelectedCandidate(app);
                setModalType('status');
                setShowModal(true);
              }}
              className="px-4 py-2 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-lg hover:from-gray-600 hover:to-gray-700 transition-all duration-200 text-sm font-medium shadow-lg"
            >
              ⚙️ Status: {getCurrentStatusLabel(app)}
            </button>

            {app.resumeId && (
              <a
                href={`/api/resumes/${app.resumeId}`}
                target="_blank"
                rel="noopener noreferrer"
                className="px-4 py-2 bg-gradient-to-r from-indigo-500 to-indigo-600 text-white rounded-lg hover:from-indigo-600 hover:to-indigo-700 transition-all duration-200 text-sm font-medium shadow-lg"
              >
                📥 Resume
              </a>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Loading and error states
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-200 via-blue-100 to-purple-200 flex items-center justify-center">
        <div className="text-slate-800 text-xl flex items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
          Loading applications...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-200 via-blue-100 to-purple-200 flex items-center justify-center">
        <div className="bg-white/80 backdrop-blur-md border border-white/40 rounded-2xl p-8 shadow-xl max-w-md">
          <div className="text-red-600 text-center">
            <div className="text-4xl mb-4">⚠️</div>
            <h2 className="text-lg font-semibold mb-2">Error Loading Data</h2>
            <p className="text-slate-600 mb-4">{error}</p>
            <button 
              onClick={() => router.reload()}
              className="px-6 py-2 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg hover:from-red-600 hover:to-red-700 transition-all duration-200 shadow-lg"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!job) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-200 via-blue-100 to-purple-200 flex items-center justify-center">
        <div className="bg-white/80 backdrop-blur-md border border-white/40 rounded-2xl p-8 shadow-xl max-w-md">
          <div className="text-center">
            <div className="text-4xl mb-4">🔍</div>
            <h2 className="text-lg font-semibold mb-2">Job Not Found</h2>
            <p className="text-slate-600 mb-4">The requested job could not be found.</p>
            <Link 
              href="/recruiter/dashboard"
              className="px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-lg inline-block"
            >
              Back to Dashboard
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const totalApplications = filteredApplications.length;
  const highPercentage = totalApplications > 0 ? Math.round((highScoreApplications.length / totalApplications) * 100) : 0;
  const mediumPercentage = totalApplications > 0 ? Math.round((mediumScoreApplications.length / totalApplications) * 100) : 0;
  const lowPercentage = totalApplications > 0 ? Math.round((lowScoreApplications.length / totalApplications) * 100) : 0;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-200 via-blue-100 to-purple-200">
      {/* Animated background elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-400/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-400/30 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/2 left-1/2 w-60 h-60 bg-pink-400/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '4s'}}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto p-6">
        {/* Toast Message */}
        {toastMessage && (
          <div className="fixed top-6 right-6 z-50 bg-white/80 backdrop-blur-md border border-white/40 text-slate-800 px-6 py-3 rounded-xl shadow-xl animate-fade-in-up">
            {toastMessage}
          </div>
        )}

        {/* Job Header */}
        <div className="bg-white/50 backdrop-blur-xl border border-white/60 rounded-2xl shadow-xl p-6 mb-8 animate-fade-in-up">
          <div className="flex justify-between items-start mb-4">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-700 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                  {job?.title}
                </h1>
                <span className={`px-4 py-2 text-sm font-medium rounded-full ${
                  isJobClosed(job) 
                    ? 'bg-red-100/80 text-red-700 backdrop-blur-sm' 
                    : 'bg-green-100/80 text-green-700 backdrop-blur-sm'
                }`}>
                  {isJobClosed(job) ? '🚫 CLOSED' : '✅ OPEN'}
                </span>
              </div>
              <p className="text-slate-700 mb-2 flex items-center">
                <span className="mr-2">🏢</span>
                Company: {job?.company}
              </p>
              {isJobClosed(job) && job.closedAt && (
                <p className="text-sm text-red-600 flex items-center">
                  <span className="mr-2">📅</span>
                  Position closed on: {new Date(job.closedAt).toLocaleDateString()}
                </p>
              )}
            </div>
            
            <div className="flex flex-col gap-3">
              <Link
                href={`/recruiter/jobs/edit/${job._id}`}
                className="px-6 py-2 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-200 text-center font-medium shadow-lg"
              >
                ✏️ Edit Job
              </Link>
              
              {isJobClosed(job) ? (
                <button
                  onClick={handleReopenPosition}
                  className="px-6 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 font-medium shadow-lg"
                >
                  🔄 Reopen Position
                </button>
              ) : (
                <button
                  onClick={() => setShowCloseModal(true)}
                  className="px-6 py-2 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl hover:from-orange-600 hover:to-orange-700 transition-all duration-200 font-medium shadow-lg"
                >
                  🚫 Close Position
                </button>
              )}
              
              <Link
                href="/recruiter/dashboard"
                className="px-6 py-2 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl hover:from-gray-600 hover:to-gray-700 transition-all duration-200 text-center font-medium shadow-lg"
              >
                ← Back to Dashboard
              </Link>
            </div>
          </div>
          
          {isJobClosed(job) && (
            <div className="bg-red-50/80 backdrop-blur-sm border border-red-200/60 rounded-xl p-4">
              <p className="text-red-700 text-sm flex items-center">
                <span className="mr-2">⚠️</span>
                This position is currently closed. New applications are not being accepted.
              </p>
            </div>
          )}
        </div>

        {/* Score Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <ScoreCard
            title="High Score Candidates"
            count={highScoreApplications.length}
            color="from-green-400/30 to-emerald-400/30"
            gradient="linear-gradient(135deg, #10B981, #059669)"
            icon="🎯"
            percentage={highPercentage}
            delay={0}
          />
          <ScoreCard
            title="Medium Score Candidates"
            count={mediumScoreApplications.length}
            color="from-yellow-400/30 to-orange-400/30"
            gradient="linear-gradient(135deg, #F59E0B, #D97706)"
            icon="⚡"
            percentage={mediumPercentage}
            delay={100}
          />
          <ScoreCard
            title="Low Score Candidates"
            count={lowScoreApplications.length}
            color="from-red-400/30 to-pink-400/30"
            gradient="linear-gradient(135deg, #EF4444, #DC2626)"
            icon="📊"
            percentage={lowPercentage}
            delay={200}
          />
        </div>

        {/* Screening Pipeline Analytics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <ScoreCard
            title="Screening Invites Sent"
            count={filteredApplications.filter(app => app.screeningInviteSent || app.interviewStatus === 'in_progress' || app.interviewStatus === 'completed').length}
            color="from-blue-400/30 to-cyan-400/30"
            gradient="linear-gradient(135deg, #3B82F6, #0EA5E9)"
            icon="📧"
            percentage={totalApplications > 0 ? Math.round((filteredApplications.filter(app => app.screeningInviteSent || app.interviewStatus === 'in_progress' || app.interviewStatus === 'completed').length / totalApplications) * 100) : 0}
            delay={300}
          />
          <ScoreCard
            title="Tests Completed"
            count={filteredApplications.filter(app => app.testScore && app.testScore > 0).length}
            color="from-purple-400/30 to-violet-400/30"
            gradient="linear-gradient(135deg, #8B5CF6, #7C3AED)"
            icon="🧪"
            percentage={totalApplications > 0 ? Math.round((filteredApplications.filter(app => app.testScore && app.testScore > 0).length / totalApplications) * 100) : 0}
            delay={400}
          />
          <ScoreCard
            title="Screening Complete"
            count={filteredApplications.filter(app => hasCompletedScreening(app)).length}
            color="from-teal-400/30 to-emerald-400/30"
            gradient="linear-gradient(135deg, #14B8A6, #10B981)"
            icon="✅"
            percentage={totalApplications > 0 ? Math.round((filteredApplications.filter(app => hasCompletedScreening(app)).length / totalApplications) * 100) : 0}
            delay={500}
          />
          <ScoreCard
            title="Disqualified"
            count={filteredApplications.filter(app => app.status === 'disqualified' || app.interviewStatus === 'disqualified').length}
            color="from-rose-400/30 to-red-400/30"
            gradient="linear-gradient(135deg, #F43F5E, #EF4444)"
            icon="❌"
            percentage={totalApplications > 0 ? Math.round((filteredApplications.filter(app => app.status === 'disqualified' || app.interviewStatus === 'disqualified').length / totalApplications) * 100) : 0}
            delay={600}
          />
        </div>

        {/* View Toggle and Filters */}
        <div className="bg-white/50 backdrop-blur-xl border border-white/60 rounded-2xl shadow-xl p-6 mb-8 animate-fade-in-up" style={{animationDelay: '300ms'}}>
          {/* View Toggle */}
          <div className="flex items-center space-x-1 bg-gray-100/60 backdrop-blur-sm rounded-xl p-1 mb-6 w-fit">
            <button 
              className={`px-6 py-2 rounded-lg font-medium transition-all duration-200 ${
                activeView === 'category' 
                  ? 'bg-white text-blue-600 shadow-lg' 
                  : 'text-slate-600 hover:bg-white/50'
              }`}
              onClick={() => setActiveView('category')}
            >
              📊 Category View
            </button>
            <button 
              className={`px-6 py-2 rounded-lg font-medium transition-all duration-200 ${
                activeView === 'list' 
                  ? 'bg-white text-blue-600 shadow-lg' 
                  : 'text-slate-600 hover:bg-white/50'
              }`}
              onClick={() => setActiveView('list')}
            >
              📋 List View
            </button>
          </div>

          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-2 text-slate-900 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg"
            >
              <option value="">🔍 All Interview Status</option>
              <option value="starting">🚀 Not Started</option>
              <option value="in_progress">⏳ In Progress</option>
              <option value="completed">✅ Completed</option>
              <option value="disqualified">❌ Disqualified</option>
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-2 text-slate-900 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg"
            >
              <option value="">📈 Sort by</option>
              <option value="highest_score">🎯 Top Scoring</option>
              <option value="lowest_score">📉 Lowest Scoring</option>
              <option value="resume_score">📄 Resume Score</option>
              <option value="test_score">🧪 Test Score</option>
            </select>

            <label className="flex items-center space-x-3 bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-2 cursor-pointer hover:bg-white/80 transition-all duration-200 shadow-lg">
              <input
                type="checkbox"
                checked={shortlistedOnly}
                onChange={() => setShortlistedOnly(!shortlistedOnly)}
                className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
              />
              <span className="text-slate-900 font-medium">⭐ Shortlisted Only</span>
            </label>

            <div className="text-slate-700 bg-white/70 backdrop-blur-sm border border-white/60 rounded-xl px-4 py-2 font-medium shadow-lg flex items-center justify-center">
              📊 {filteredApplications.length} Total Applications
            </div>
          </div>
        </div>

        {/* Bulk Actions Panel */}
        {showBulkActions && (
          <div className="bg-blue-100/80 backdrop-blur-md border border-blue-200/60 rounded-2xl p-4 mb-6 animate-fade-in-up shadow-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <span className="text-blue-800 font-medium">
                  ✅ {selectedCandidates.size} candidates selected
                </span>
                <button
                  onClick={handleDeselectAll}
                  className="text-blue-600 hover:text-blue-800 font-medium"
                >
                  Clear selection
                </button>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => handleBulkAction('screening')}
                  className="px-4 py-2 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg hover:from-purple-600 hover:to-purple-700 transition-all duration-200 text-sm font-medium shadow-lg"
                >
                  📧 Send Screening
                </button>
                <button
                  onClick={() => handleBulkAction('interview')}
                  className="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-200 text-sm font-medium shadow-lg"
                >
                  🎯 Schedule Interviews
                </button>
                <button
                  onClick={() => handleBulkAction('reject')}
                  className="px-4 py-2 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg hover:from-red-600 hover:to-red-700 transition-all duration-200 text-sm font-medium shadow-lg"
                >
                  ❌ Reject
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Applications Content */}
        {filteredApplications.length === 0 ? (
          <div className="text-center py-20 animate-fade-in-up bg-white/60 backdrop-blur-md border border-white/40 rounded-2xl shadow-lg" style={{animationDelay: '400ms'}}>
            <div className="text-8xl mb-6">📭</div>
            <h3 className="text-2xl font-bold text-slate-800 mb-4">No Applications Yet</h3>
            <p className="text-slate-600 text-lg">
              {applications.length === 0 
                ? "This position hasn't received any applications yet."
                : "No applications match your current filters."}
            </p>
            {applications.length > 0 && (
              <button
                onClick={() => {
                  setStatusFilter('');
                  setShortlistedOnly(false);
                  setSortBy('');
                }}
                className="mt-4 px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium shadow-lg"
              >
                Clear Filters
              </button>
            )}
          </div>
        ) : activeView === 'category' ? (
          // Enhanced Category View
          <div>
            {/* High Score Candidates */}
            {highScoreApplications.length > 0 && (
              <div className="mb-12">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold text-slate-800 flex items-center">
                    🎯 High Score Candidates ({highScoreApplications.length})
                  </h2>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleSelectAll(highScoreApplications)}
                      className="px-4 py-2 bg-green-100/80 text-green-700 rounded-lg hover:bg-green-200/80 transition-all duration-200 text-sm font-medium backdrop-blur-sm"
                    >
                      Select All
                    </button>
                  </div>
                </div>
                <div className="grid gap-6">
                  {highScoreApplications.map((app, index) => (
                    <CandidateCard key={app._id} app={app} index={index} category="high" />
                  ))}
                </div>
              </div>
            )}

            {/* Medium Score Candidates */}
            {mediumScoreApplications.length > 0 && (
              <div className="mb-12">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold text-slate-800 flex items-center">
                    ⚡ Medium Score Candidates ({mediumScoreApplications.length})
                  </h2>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleSelectAll(mediumScoreApplications)}
                      className="px-4 py-2 bg-yellow-100/80 text-yellow-700 rounded-lg hover:bg-yellow-200/80 transition-all duration-200 text-sm font-medium backdrop-blur-sm"
                    >
                      Select All
                    </button>
                  </div>
                </div>
                <div className="grid gap-6">
                  {mediumScoreApplications.map((app, index) => (
                    <CandidateCard key={app._id} app={app} index={index} category="medium" />
                  ))}
                </div>
              </div>
            )}

            {/* Low Score Candidates */}
            {lowScoreApplications.length > 0 && (
              <div className="mb-12">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold text-slate-800 flex items-center">
                    📊 Low Score Candidates ({lowScoreApplications.length})
                  </h2>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleSelectAll(lowScoreApplications)}
                      className="px-4 py-2 bg-red-100/80 text-red-700 rounded-lg hover:bg-red-200/80 transition-all duration-200 text-sm font-medium backdrop-blur-sm"
                    >
                      Select All
                    </button>
                  </div>
                </div>
                <div className="grid gap-6">
                  {lowScoreApplications.map((app, index) => (
                    <CandidateCard key={app._id} app={app} index={index} category="low" />
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          // Enhanced List View
          <div className="grid gap-6">
            {filteredApplications.map((app, index) => (
              <CandidateCard key={app._id} app={app} index={index} category={
                getCumulativeScore(app) >= 80 ? 'high' :
                getCumulativeScore(app) >= 60 ? 'medium' : 'low'
              } />
            ))}
          </div>
        )}
      </div>

      {/* Enhanced Modals */}
      {showModal && selectedCandidate && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 overflow-y-auto p-4">
          <div className={modalType === 'details' ? "w-full max-w-5xl" : "bg-white/90 backdrop-blur-md border border-white/40 p-8 rounded-2xl shadow-2xl max-w-md mx-4"}>
            {modalType === 'details' ? (
              <EnhancedResumeDetails
                application={selectedCandidate}
                onClose={() => {
                  setShowModal(false);
                  setSelectedCandidate(null);
                }}
              />
            ) : modalType === 'resume-details' ? (
              <div className="bg-white/90 backdrop-blur-md border border-white/40 p-8 rounded-2xl shadow-2xl max-w-2xl mx-4">
                <h3 className="text-xl font-semibold mb-6 flex items-center">
                  📄 Resume Score Details
                </h3>
                <div className="space-y-4">
                  <div className="bg-blue-50/80 p-4 rounded-xl">
                    <h4 className="font-semibold text-blue-800 mb-2">Candidate: {selectedCandidate.fullName}</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p><strong>Resume Score:</strong> {selectedCandidate.scanResult?.compare?.match_score || 0}</p>
                        <p><strong>Match Percentage:</strong> {selectedCandidate.scanResult?.compare?.match_score || 0}%</p>
                      </div>
                      <div>
                        <p><strong>Experience Match:</strong> {selectedCandidate.experienceYears || 'N/A'} years</p>
                        <p><strong>Skills Match:</strong> {selectedCandidate.scanResult?.skillsMatch || 'N/A'}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-gray-50/80 p-4 rounded-xl">
                    <h4 className="font-semibold text-gray-800 mb-2">AI Recommendation</h4>
                    <p className="text-sm text-gray-700">
                      {selectedCandidate.scanResult?.recommendation || 'No recommendation available'}
                    </p>
                  </div>

                  {selectedCandidate.scanResult?.compare?.strengths && (
                    <div className="bg-green-50/80 p-4 rounded-xl">
                      <h4 className="font-semibold text-green-800 mb-2">Strengths</h4>
                      <ul className="text-sm text-green-700 space-y-1">
                        {selectedCandidate.scanResult.compare.strengths.map((strength, index) => (
                          <li key={index}>• {strength}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {selectedCandidate.scanResult?.compare?.gaps && (
                    <div className="bg-orange-50/80 p-4 rounded-xl">
                      <h4 className="font-semibold text-orange-800 mb-2">Areas for Improvement</h4>
                      <ul className="text-sm text-orange-700 space-y-1">
                        {selectedCandidate.scanResult.compare.gaps.map((gap, index) => (
                          <li key={index}>• {gap}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
                
                <div className="flex justify-end mt-6">
                  <button 
                    onClick={() => {
                      setShowModal(false);
                      setSelectedCandidate(null);
                    }}
                    className="px-6 py-2 bg-gray-200/80 hover:bg-gray-300/80 text-gray-700 rounded-xl transition-all duration-200 font-medium"
                  >
                    Close
                  </button>
                </div>
              </div>
            ) : modalType === 'test-details' ? (
              <div className="bg-white/90 backdrop-blur-md border border-white/40 p-8 rounded-2xl shadow-2xl max-w-2xl mx-4">
                <h3 className="text-xl font-semibold mb-6 flex items-center">
                  🧪 Test Score Details
                </h3>
                <div className="space-y-4">
                  <div className="bg-purple-50/80 p-4 rounded-xl">
                    <h4 className="font-semibold text-purple-800 mb-2">Candidate: {selectedCandidate.fullName}</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p><strong>Test Score:</strong> {selectedCandidate.testScore || 'Not Taken'}</p>
                        <p><strong>Test Status:</strong> {selectedCandidate.testScore ? 'Completed' : 'Pending'}</p>
                      </div>
                      <div>
                        <p><strong>Questions Answered:</strong> {selectedCandidate.questionsAnswered || 'N/A'}</p>
                        <p><strong>Accuracy:</strong> {selectedCandidate.testScore ? `${selectedCandidate.testScore}%` : 'N/A'}</p>
                      </div>
                    </div>
                  </div>
                  
                  {selectedCandidate.testScore ? (
                    <div className="bg-green-50/80 p-4 rounded-xl">
                      <h4 className="font-semibold text-green-800 mb-2">Test Performance</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Score:</span>
                          <span className="font-medium">{selectedCandidate.testScore}/100</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Performance Level:</span>
                          <span className={`font-medium ${
                            selectedCandidate.testScore >= 80 ? 'text-green-600' :
                            selectedCandidate.testScore >= 60 ? 'text-yellow-600' : 'text-red-600'
                          }`}>
                            {selectedCandidate.testScore >= 80 ? 'Excellent' :
                             selectedCandidate.testScore >= 60 ? 'Good' : 'Needs Improvement'}
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                          <div 
                            className={`h-2 rounded-full ${
                              selectedCandidate.testScore >= 80 ? 'bg-green-500' :
                              selectedCandidate.testScore >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                            }`}
                            style={{ width: `${selectedCandidate.testScore}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="bg-gray-50/80 p-4 rounded-xl">
                      <h4 className="font-semibold text-gray-800 mb-2">Test Not Completed</h4>
                      <p className="text-sm text-gray-700">
                        This candidate has not yet completed the screening test.
                        {isEligibleForScreening(selectedCandidate) && ' You can send a screening invitation.'}
                      </p>
                    </div>
                  )}

                  <div className="bg-blue-50/80 p-4 rounded-xl">
                    <h4 className="font-semibold text-blue-800 mb-2">Combined Score</h4>
                    <div className="text-sm">
                      <p><strong>Resume Score:</strong> {selectedCandidate.scanResult?.compare?.match_score || 0}</p>
                      <p><strong>Test Score:</strong> {selectedCandidate.testScore || 0}</p>
                      <p><strong>Total Score:</strong> {getCumulativeScore(selectedCandidate)}</p>
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-end mt-6">
                  <button 
                    onClick={() => {
                      setShowModal(false);
                      setSelectedCandidate(null);
                    }}
                    className="px-6 py-2 bg-gray-200/80 hover:bg-gray-300/80 text-gray-700 rounded-xl transition-all duration-200 font-medium"
                  >
                    Close
                  </button>
                </div>
              </div>
            ) : modalType === 'status' ? (
              <>
                <h3 className="text-xl font-semibold mb-6 flex items-center">
                  ⚙️ Update Application Status
                </h3>
                <div className="bg-blue-50/80 p-4 rounded-xl mb-6">
                  <p className="text-blue-800 font-medium">
                    Current Status: <span className="font-bold">{getCurrentStatusLabel(selectedCandidate)}</span>
                  </p>
                  <p className="text-blue-600 text-sm mt-1">
                    Candidate: {selectedCandidate.fullName || selectedCandidate.candidateName}
                  </p>
                </div>
                
                <div className="space-y-3 mb-8">
                  <div className="text-sm font-medium text-gray-700 mb-2">Change status to:</div>
                  {[
                    { value: 'applied', label: '📝 Applied', color: 'bg-blue-50 hover:bg-blue-100 text-blue-700', description: 'Reset to initial application state' },
                    { value: 'screening', label: '🔍 Screening', color: 'bg-purple-50 hover:bg-purple-100 text-purple-700', description: 'Currently in screening process' },
                    { value: 'interview', label: '🎯 Interview', color: 'bg-green-50 hover:bg-green-100 text-green-700', description: 'Schedule or completed interview' },
                    { value: 'offered', label: '🎉 Offered', color: 'bg-yellow-50 hover:bg-yellow-100 text-yellow-700', description: 'Job offer extended' },
                    { value: 'hired', label: '✅ Hired', color: 'bg-emerald-50 hover:bg-emerald-100 text-emerald-700', description: 'Successfully hired' },
                    { value: 'rejected', label: '❌ Rejected', color: 'bg-red-50 hover:bg-red-100 text-red-700', description: 'Application rejected' }
                  ].map((status) => (
                    <button 
                      key={status.value}
                      onClick={() => handleStatusChange(selectedCandidate._id, status.value)}
                      className={`block w-full text-left px-6 py-4 rounded-xl transition-all duration-200 font-medium ${status.color} border border-transparent hover:border-current`}
                    >
                      <div className="flex justify-between items-center">
                        <span>{status.label}</span>
                        <span className="text-xs opacity-70">{status.description}</span>
                      </div>
                    </button>
                  ))}
                </div>
                
                <div className="flex justify-end">
                  <button 
                    onClick={() => {
                      setShowModal(false);
                      setSelectedCandidate(null);
                    }}
                    className="px-6 py-2 bg-gray-200/80 hover:bg-gray-300/80 text-gray-700 rounded-xl transition-all duration-200 font-medium"
                  >
                    Cancel
                  </button>
                </div>
              </>
            ) : modalType === 'screening' ? (
              <>
                <h3 className="text-xl font-semibold mb-6 flex items-center">
                  📧 Send Screening Interview
                </h3>
                <p className="mb-4 text-slate-700">
                  Send screening interview to <strong>{selectedCandidate.fullName || selectedCandidate.candidateName}</strong>?
                </p>
                <p className="mb-8 text-sm text-slate-600 bg-blue-50/80 p-4 rounded-xl">
                  💡 This will send an invitation email to the candidate with instructions to complete the screening interview.
                </p>
                <div className="flex justify-end space-x-4">
                  <button 
                    onClick={() => {
                      setShowModal(false);
                      setSelectedCandidate(null);
                    }}
                    className="px-6 py-2 bg-gray-200/80 hover:bg-gray-300/80 text-gray-700 rounded-xl transition-all duration-200 font-medium"
                  >
                    Cancel
                  </button>
                  <button 
                    onClick={() => handleSendScreeningInvite(selectedCandidate._id)}
                    className="px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium shadow-lg"
                  >
                    📧 Send Invitation
                  </button>
                </div>
              </>
            ) : modalType === 'interview' ? (
              <>
                <h3 className="text-xl font-semibold mb-6 flex items-center">
                  🎯 Schedule Interview
                </h3>
                <p className="mb-4 text-slate-700">
                  Schedule final interview with <strong>{selectedCandidate.fullName || selectedCandidate.candidateName}</strong>?
                </p>
                <p className="mb-8 text-sm text-slate-600 bg-green-50/80 p-4 rounded-xl">
                  ✅ This candidate has successfully completed the screening interview and is ready for the final interview.
                </p>
                <div className="flex justify-end space-x-4">
                  <button 
                    onClick={() => {
                      setShowModal(false);
                      setSelectedCandidate(null);
                    }}
                    className="px-6 py-2 bg-gray-200/80 hover:bg-gray-300/80 text-gray-700 rounded-xl transition-all duration-200 font-medium"
                  >
                    Cancel
                  </button>
                  <button 
                    onClick={() => handleScheduleInterview(selectedCandidate._id)}
                    className="px-6 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 font-medium shadow-lg"
                  >
                    🎯 Schedule Interview
                  </button>
                </div>
              </>
            ) : null}
          </div>
        </div>
      )}

      {/* Close Position Modal */}
      {showCloseModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-white/90 backdrop-blur-md border border-white/40 p-8 rounded-2xl shadow-2xl text-center max-w-md mx-4">
            <div className="text-4xl mb-4">🚫</div>
            <h2 className="text-xl font-semibold mb-4">Close Position?</h2>
            <p className="mb-6 text-slate-600">
              Are you sure you want to close this position? 
              <br />
              <span className="text-sm text-slate-500">
                This will prevent new applications but you can reopen it later.
              </span>
            </p>
            <div className="flex justify-center space-x-4">
              <button
                onClick={() => setShowCloseModal(false)}
                className="px-6 py-2 bg-gray-200/80 hover:bg-gray-300/80 text-gray-700 rounded-xl transition-all duration-200 font-medium"
              >
                Cancel
              </button>
              <button
                onClick={handleClosePosition}
                className="px-6 py-2 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl hover:from-orange-600 hover:to-orange-700 transition-all duration-200 font-medium shadow-lg"
              >
                🚫 Close Position
              </button>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        @keyframes fade-in-up {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-fade-in-up {
          animation: fade-in-up 0.6s ease-out forwards;
          opacity: 0;
        }
      `}</style>
    </div>
  );
}
