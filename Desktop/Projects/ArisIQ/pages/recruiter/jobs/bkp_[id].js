// File: pages/recruiter/jobs/[id].js

import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import axios from 'axios';
import Link from 'next/link';
import EnhancedResumeDetails from './EnhancedResumeDetails';

export default function JobApplicationsPage() {
  const router = useRouter();
  const { id } = router.query;
  const [job, setJob] = useState(null);
  const [applications, setApplications] = useState([]);
  const [statusFilter, setStatusFilter] = useState('');
  const [shortlistedOnly, setShortlistedOnly] = useState(false);
  const [sortBy, setSortBy] = useState('');
  const [activeView, setActiveView] = useState('list'); // 'list' or 'category'
  const [showModal, setShowModal] = useState(false);
  const [selectedCandidate, setSelectedCandidate] = useState(null);
  const [modalType, setModalType] = useState('details'); // 'details', 'status', 'screening', 'interview'
  const [showCloseModal, setShowCloseModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [toastMessage, setToastMessage] = useState('');

  useEffect(() => {
    if (!id) return;
    
    const fetchJobAndApplications = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const res = await axios.get(`/api/jobs/${id}`);
        setJob(res.data.job);
        
        // Check if applications exist and are an array
        if (res.data.applications && Array.isArray(res.data.applications)) {
          setApplications(res.data.applications);
        } else {
          console.error("Applications data is not an array:", res.data.applications);
          setApplications([]);
          setError("Applications data format is invalid");
        }
      } catch (err) {
        console.error('Error fetching job or applications:', err);
        setError(`Failed to load data: ${err.message || 'Unknown error'}`);
        setApplications([]);
      } finally {
        setLoading(false);
      }
    };
    
    fetchJobAndApplications();
  }, [id]);

  const getCumulativeScore = (app) => {
    const resumeScore = app.scanResult?.compare?.match_score || 0;
    const testScore = app.testScore || 0;
    return resumeScore + testScore;
  };

  // Check if candidate has completed screening
  const hasCompletedScreening = (app) => {
    return app.interviewStatus === 'completed' && app.status !== 'disqualified';
  };

  // Check if candidate is eligible for final interview
  const isEligibleForInterview = (app) => {
    // Only show Schedule Interview button if test score is available
    // and screening is completed
    const hasTestScore = app.testScore && app.testScore > 0;
    const score = getCumulativeScore(app);
    return score >= 80 && hasCompletedScreening(app) && hasTestScore;
  };

  // Check if candidate is eligible for screening
  const isEligibleForScreening = (app) => {
    const score = getCumulativeScore(app);
    return score >= 60 && score < 80 && app.interviewStatus !== 'completed';
  };

  const getJobStatus = (job) => {
    return job?.status || 'open';
  };

  const isJobClosed = (job) => {
    return getJobStatus(job) === 'closed';
  };

  const handleClosePosition = async () => {
    try {
      await axios.patch(`/api/jobs/${id}`, { 
        status: 'closed',
        closedAt: new Date().toISOString()
      });
      
      // Update local state
      setJob(prev => ({
        ...prev,
        status: 'closed',
        closedAt: new Date().toISOString()
      }));
      
      setToastMessage('✅ Position closed successfully');
    } catch (error) {
      setToastMessage('❌ Failed to close position');
      console.error(error);
    } finally {
      setShowCloseModal(false);
      setTimeout(() => setToastMessage(''), 3000);
    }
  };

  const handleReopenPosition = async () => {
    try {
      await axios.patch(`/api/jobs/${id}`, { 
        status: 'open',
        closedAt: null
      });
      
      // Update local state
      setJob(prev => ({
        ...prev,
        status: 'open',
        closedAt: null
      }));
      
      setToastMessage('✅ Position reopened successfully');
      setTimeout(() => setToastMessage(''), 3000);
    } catch (error) {
      setToastMessage('❌ Failed to reopen position');
      console.error(error);
      setTimeout(() => setToastMessage(''), 3000);
    }
  };

  // Filter applications based on current filters
  const filteredApplications = applications
    .filter(app => {
      if (shortlistedOnly && !app.canTakeInterview) return false;
      if (statusFilter && app.interviewStatus !== statusFilter) return false;
      return true;
    })
    .sort((a, b) => {
      if (sortBy === 'highest_score') return getCumulativeScore(b) - getCumulativeScore(a);
      if (sortBy === 'lowest_score') return getCumulativeScore(a) - getCumulativeScore(b);
      if (sortBy === 'resume_score') return (b.scanResult?.compare?.match_score || 0) - (a.scanResult?.compare?.match_score || 0);
      if (sortBy === 'test_score') return (b.testScore || 0) - (a.testScore || 0);
      return 0;
    });

  // Categorize applications by score
  const highScoreApplications = filteredApplications.filter(app => {
    const score = getCumulativeScore(app);
    return score >= 80;
  });
  
  const mediumScoreApplications = filteredApplications.filter(app => {
    const score = getCumulativeScore(app);
    return score >= 60 && score < 80;
  });
  
  const lowScoreApplications = filteredApplications.filter(app => {
    const score = getCumulativeScore(app);
    return score < 60;
  });

  // Updated showDetailsModal function to handle both popup windows and the enhanced modal
  const showDetailsModal = (title, content) => {
    // If title and content are provided, show a popup window (for simpler details)
    if (title && content) {
      const win = window.open('', '_blank', 'width=600,height=400');
      win.document.write(`<title>${title}</title><pre>${content}</pre>`);
      return;
    }
    
    // Otherwise, assume we're showing application details in the enhanced modal
    setModalType('details');
    setShowModal(true);
  };

  const handleStatusChange = async (applicationId, newStatus) => {
    try {
      await axios.put('/api/recruiter/update-application-status', {
        applicationId,
        status: newStatus
      });
      
      // Update local state
      setApplications(applications.map(app => 
        app._id === applicationId ? {...app, status: newStatus} : app
      ));
      
      setShowModal(false);
      setSelectedCandidate(null);
    } catch (err) {
      console.error('Error updating status:', err);
      alert('Failed to update application status');
    }
  };

  const handleSendScreeningInvite = async (applicationId) => {
    try {
      // API call to send screening invite
      await axios.post('/api/recruiter/send-screening-invite', {
        applicationId
      });
      
      // Update local state
      setApplications(applications.map(app => 
        app._id === applicationId ? {...app, screeningInviteSent: true} : app
      ));
      
      alert('Screening invitation sent successfully');
      setShowModal(false);
      setSelectedCandidate(null);
    } catch (err) {
      console.error('Error sending screening invite:', err);
      alert('Failed to send screening invitation');
    }
  };

  const handleScheduleInterview = async (applicationId) => {
    try {
      // Here you would typically navigate to an interview scheduling page
      // or show a modal with scheduling options
      router.push(`/recruiter/schedule-interview/${applicationId}`);
    } catch (err) {
      console.error('Error scheduling interview:', err);
      alert('Failed to schedule interview');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-5xl mx-auto p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <h2 className="text-lg font-semibold mb-2">Error Loading Data</h2>
          <p>{error}</p>
          <button 
            onClick={() => router.reload()}
            className="mt-3 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!job) {
    return (
      <div className="max-w-5xl mx-auto p-6">
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
          <h2 className="text-lg font-semibold mb-2">Job Not Found</h2>
          <p>The requested job could not be found.</p>
          <Link href="/recruiter/dashboard" className="mt-3 inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
            Back to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-5xl mx-auto p-6">
      {toastMessage && (
        <div className="bg-black text-white px-4 py-2 rounded mb-4 shadow-lg w-fit">
          {toastMessage}
        </div>
      )}

      {/* Job Header with Status and Actions */}
      <div className="bg-white border rounded-lg shadow p-6 mb-6">
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <h1 className="text-2xl font-bold">{job?.title}</h1>
              <span className={`px-3 py-1 text-sm font-medium rounded-full ${
                isJobClosed(job) 
                  ? 'bg-red-100 text-red-800' 
                  : 'bg-green-100 text-green-800'
              }`}>
                {isJobClosed(job) ? 'CLOSED' : 'OPEN'}
              </span>
            </div>
            <p className="text-gray-600 mb-2">Company: {job?.company}</p>
            {isJobClosed(job) && job.closedAt && (
              <p className="text-sm text-red-600">
                Position closed on: {new Date(job.closedAt).toLocaleDateString()}
              </p>
            )}
          </div>
          
          <div className="flex flex-col gap-2">
            <Link
              href={`/recruiter/jobs/edit/${job._id}`}
              className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 text-center"
            >
              Edit Job
            </Link>
            
            {isJobClosed(job) ? (
              <button
                onClick={handleReopenPosition}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                Reopen Position
              </button>
            ) : (
              <button
                onClick={() => setShowCloseModal(true)}
                className="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700"
              >
                Close Position
              </button>
            )}
            
            <Link
              href="/recruiter/dashboard"
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 text-center"
            >
              Back to Dashboard
            </Link>
          </div>
        </div>
        
        {isJobClosed(job) && (
          <div className="bg-red-50 border border-red-200 rounded p-3">
            <p className="text-red-700 text-sm">
              ⚠️ This position is currently closed. New applications are not being accepted.
            </p>
          </div>
        )}
      </div>

      {/* View Toggle */}
      <div className="flex border-b mb-6">
        <button 
          className={`px-4 py-2 font-medium ${activeView === 'list' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'}`}
          onClick={() => setActiveView('list')}
        >
          List View
        </button>
        <button 
          className={`px-4 py-2 font-medium ${activeView === 'category' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'}`}
          onClick={() => setActiveView('category')}
        >
          Category View
        </button>
      </div>

      {/* Filters and Sort */}
      <div className="flex flex-wrap gap-4 items-center mb-6">
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="border px-3 py-2 rounded text-sm w-56"
        >
          <option value="">All Interview Status</option>
          <option value="starting">Not Started</option>
          <option value="in_progress">In Progress</option>
          <option value="completed">Completed</option>
          <option value="disqualified">Disqualified</option>
        </select>

        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value)}
          className="border px-3 py-2 rounded text-sm w-56"
        >
          <option value="">Sort by</option>
          <option value="highest_score">Top Scoring</option>
          <option value="lowest_score">Lowest Scoring</option>
          <option value="resume_score">Resume Score</option>
          <option value="test_score">Test Score</option>
        </select>

        <label className="text-sm flex items-center space-x-2">
          <input
            type="checkbox"
            checked={shortlistedOnly}
            onChange={() => setShortlistedOnly(!shortlistedOnly)}
          />
          <span>Shortlisted Only</span>
        </label>
      </div>

      {filteredApplications.length === 0 ? (
        <p className="text-gray-500">No applications submitted yet.</p>
      ) : activeView === 'list' ? (
        // Original List View
        <div className="space-y-4">
          {filteredApplications.map((app) => {
            const resumeScore = app.scanResult?.compare?.match_score || 0;
            const testScore = app.testScore || 0;
            const cumulative = resumeScore + testScore;
            return (
              <div key={app._id} className="bg-white border rounded-md p-4 shadow-sm">
                <div className="flex justify-between items-start">
                  <div>
                    <p><strong>Name:</strong> {app.fullName || 'N/A'}</p>
                    <p><strong>Email:</strong> {app.email}</p>
                    <p><strong>Experience:</strong> {app.experienceYears || 'N/A'} years</p>
                    <p><strong>Expected Salary:</strong> {app.expectedSalary || 'N/A'}</p>
                    <p>
                      <strong>Resume Score:</strong> {resumeScore} {' '}
                      <button
                        onClick={() => {
                          setSelectedCandidate(app);
                          setModalType('details');
                          setShowModal(true);
                        }}
                        className="text-xs text-blue-600 underline ml-1"
                      >Details</button>
                    </p>
                    <p>
                      <strong>Test Score:</strong> {testScore || 'N/A'} {' '}
                      <button
                        onClick={() => showDetailsModal('Test Details', 'Test questions, correct/incorrect answers and score breakdown will appear here.')}
                        className="text-xs text-blue-600 underline ml-1"
                      >View</button>
                    </p>
                    <p>
                      <strong>Cumulative Score:</strong> {cumulative} {' '}
                      <button
                        onClick={() => showDetailsModal('Cumulative Score Breakdown', `Resume: ${resumeScore}, Test: ${testScore}`)}
                        className="text-xs text-blue-600 underline ml-1"
                      >Breakdown</button>
                    </p>
                    <p><strong>Interview Status:</strong> {app.interviewStatus || 'Not Started'}</p>
                    <p><strong>Recommendations:</strong> {app.scanResult?.recommendation || 'N/A'}</p>
                    {app.resumeId && (
                      <a
                        href={`/api/resumes/${app.resumeId}`}
                        className="text-blue-600 underline mt-2 inline-block"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Download Resume
                      </a>
                    )}
                    
                    {/* Action buttons based on score and screening status */}
                    <div className="mt-3 space-x-2">
                      {!isJobClosed(job) && isEligibleForInterview(app) && (
                        <button 
                          onClick={() => {
                            setSelectedCandidate(app);
                            setModalType('interview');
                            setShowModal(true);
                          }}
                          className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
                        >
                          Schedule Interview
                        </button>
                      )}
                      
                      {!isJobClosed(job) && isEligibleForScreening(app) && (
                        <button 
                          onClick={() => {
                            setSelectedCandidate(app);
                            setModalType('screening');
                            setShowModal(true);
                          }}
                          className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                        >
                          Send Screening
                        </button>
                      )}
                      
                      <button 
                        onClick={() => {
                          setSelectedCandidate(app);
                          setModalType('status');
                          setShowModal(true);
                        }}
                        className="px-3 py-1 bg-gray-200 text-gray-700 text-sm rounded hover:bg-gray-300"
                      >
                        Change Status
                      </button>
                    </div>
                  </div>

                  <div className="text-right space-y-2">
                    {app.canTakeInterview && (
                      <span className="inline-block bg-yellow-100 text-yellow-800 px-2 py-1 text-xs rounded-md">
                        Shortlisted
                      </span>
                    )}
                    {resumeScore > 85 && (
                      <span className="block bg-green-100 text-green-800 px-2 py-1 text-xs rounded-md">
                        Top Candidate
                      </span>
                    )}
                    {cumulative >= 80 && (
                      <span className="block bg-green-100 text-green-800 px-2 py-1 text-xs rounded-md">
                        High Score
                      </span>
                    )}
                    {cumulative >= 60 && cumulative < 80 && (
                      <span className="block bg-yellow-100 text-yellow-800 px-2 py-1 text-xs rounded-md">
                        Medium Score
                      </span>
                    )}
                    {cumulative < 60 && (
                      <span className="block bg-red-100 text-red-800 px-2 py-1 text-xs rounded-md">
                        Low Score
                      </span>
                    )}
                    {hasCompletedScreening(app) && (
                      <span className="block bg-blue-100 text-blue-800 px-2 py-1 text-xs rounded-md">
                        Screening Complete
                      </span>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        // Category View
        <div>
          {/* Score Category Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div className="bg-green-100 p-4 rounded shadow-md">
              <h3 className="font-bold text-green-800">High Score (≥80)</h3>
              <p className="text-2xl">{highScoreApplications.length}</p>
              <p className="text-sm text-gray-600">Candidates</p>
            </div>
            <div className="bg-yellow-100 p-4 rounded shadow-md">
              <h3 className="font-bold text-yellow-800">Medium Score (60-79)</h3>
              <p className="text-2xl">{mediumScoreApplications.length}</p>
              <p className="text-sm text-gray-600">Candidates</p>
            </div>
            <div className="bg-red-100 p-4 rounded shadow-md">
              <h3 className="font-bold text-red-800">Low Score (&lt;60)</h3>
              <p className="text-2xl">{lowScoreApplications.length}</p>
              <p className="text-sm text-gray-600">Candidates</p>
            </div>
          </div>
          
          {/* High Score Candidates */}
          <div className="mb-10">
            <h2 className="text-xl font-bold mb-4 bg-green-100 p-2 rounded">
              High Score Candidates (≥80)
            </h2>
            {highScoreApplications.length === 0 ? (
              <p className="text-gray-500 italic p-4">No candidates in this category.</p>
            ) : (
              <div className="space-y-4">
                {highScoreApplications.map((app) => renderApplicationCard(app))}
              </div>
            )}
          </div>
          
          {/* Medium Score Candidates */}
          <div className="mb-10">
            <h2 className="text-xl font-bold mb-4 bg-yellow-100 p-2 rounded">
              Medium Score Candidates (60-79)
            </h2>
            {mediumScoreApplications.length === 0 ? (
              <p className="text-gray-500 italic p-4">No candidates in this category.</p>
            ) : (
              <div className="space-y-4">
                {mediumScoreApplications.map((app) => renderApplicationCard(app))}
              </div>
            )}
          </div>
          
          {/* Low Score Candidates */}
          <div className="mb-10">
            <h2 className="text-xl font-bold mb-4 bg-red-100 p-2 rounded">
              Low Score (&lt;60)
            </h2>
            {lowScoreApplications.length === 0 ? (
              <p className="text-gray-500 italic p-4">No candidates in this category.</p>
            ) : (
              <div className="space-y-4">
                {lowScoreApplications.map((app) => renderApplicationCard(app))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Modals */}
      {showModal && selectedCandidate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 overflow-y-auto p-4">
          <div className={modalType === 'details' ? "w-full max-w-4xl" : "bg-white p-6 rounded-lg shadow-lg w-96"}>
            {modalType === 'details' ? (
              <EnhancedResumeDetails
                application={selectedCandidate}
                onClose={() => {
                  setShowModal(false);
                  setSelectedCandidate(null);
                }}
              />
            ) : modalType === 'status' ? (
              <>
                <h3 className="text-lg font-semibold mb-4">Update Application Status</h3>
                <p className="mb-4">
                  Change status for {selectedCandidate.fullName || selectedCandidate.candidateName}
                </p>
                
                <div className="space-y-2 mb-6">
                  <button 
                    onClick={() => handleStatusChange(selectedCandidate._id, 'applied')}
                    className="block w-full text-left px-4 py-2 hover:bg-gray-100 rounded"
                  >
                    Applied
                  </button>
                  <button 
                    onClick={() => handleStatusChange(selectedCandidate._id, 'screening')}
                    className="block w-full text-left px-4 py-2 hover:bg-gray-100 rounded"
                  >
                    Screening
                  </button>
                  <button 
                    onClick={() => handleStatusChange(selectedCandidate._id, 'interview')}
                    className="block w-full text-left px-4 py-2 hover:bg-gray-100 rounded"
                  >
                    Interview
                  </button>
                  <button 
                    onClick={() => handleStatusChange(selectedCandidate._id, 'offered')}
                    className="block w-full text-left px-4 py-2 hover:bg-gray-100 rounded"
                  >
                    Offered
                  </button>
                  <button 
                    onClick={() => handleStatusChange(selectedCandidate._id, 'hired')}
                    className="block w-full text-left px-4 py-2 hover:bg-gray-100 rounded"
                  >
                    Hired
                  </button>
                  <button 
                    onClick={() => handleStatusChange(selectedCandidate._id, 'rejected')}
                    className="block w-full text-left px-4 py-2 hover:bg-gray-100 rounded text-red-600"
                  >
                    Rejected
                  </button>
                </div>
                
                <div className="flex justify-end">
                  <button 
                    onClick={() => {
                      setShowModal(false);
                      setSelectedCandidate(null);
                    }}
                    className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-100"
                  >
                    Cancel
                  </button>
                </div>
              </>
            ) : modalType === 'screening' ? (
              <>
                <h3 className="text-lg font-semibold mb-4">Send Screening Interview</h3>
                <p className="mb-4">
                  Send screening interview to {selectedCandidate.fullName || selectedCandidate.candidateName}?
                </p>
                <p className="mb-6 text-sm text-gray-600">
                  This will send an invitation email to the candidate with instructions to complete the screening interview.
                </p>
                <div className="flex justify-end space-x-4">
                  <button 
                    onClick={() => {
                      setShowModal(false);
                      setSelectedCandidate(null);
                    }}
                    className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-100"
                  >
                    Cancel
                  </button>
                  <button 
                    onClick={() => handleSendScreeningInvite(selectedCandidate._id)}
                    className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    Send Invitation
                  </button>
                </div>
              </>
            ) : modalType === 'interview' ? (
              <>
                <h3 className="text-lg font-semibold mb-4">Schedule Interview</h3>
                <p className="mb-4">
                  Schedule final interview with {selectedCandidate.fullName || selectedCandidate.candidateName}?
                </p>
                <p className="mb-6 text-sm text-gray-600">
                  This candidate has successfully completed the screening interview.
                </p>
                <div className="flex justify-end space-x-4">
                  <button 
                    onClick={() => {
                      setShowModal(false);
                      setSelectedCandidate(null);
                    }}
                    className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-100"
                  >
                    Cancel
                  </button>
                  <button 
                    onClick={() => handleScheduleInterview(selectedCandidate._id)}
                    className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                  >
                    Schedule Interview
                  </button>
                </div>
              </>
            ) : null}
          </div>
        </div>
      )}

      {/* Close Position Confirmation Modal */}
      {showCloseModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded shadow-md text-center">
            <h2 className="text-lg font-semibold mb-4">Close Position?</h2>
            <p className="mb-6">
              Are you sure you want to close this position? 
              <br />
              <span className="text-sm text-gray-600">
                This will prevent new applications but you can reopen it later.
              </span>
            </p>
            <div className="flex justify-center space-x-4">
              <button
                onClick={() => setShowCloseModal(false)}
                className="px-4 py-2 rounded bg-gray-300 hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={handleClosePosition}
                className="px-4 py-2 rounded bg-orange-600 text-white hover:bg-orange-700"
              >
                Close Position
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  // Helper function to render application cards consistently
  function renderApplicationCard(app) {
    const resumeScore = app.scanResult?.compare?.match_score || 0;
    const testScore = app.testScore || 0;
    const cumulative = resumeScore + testScore;
    
    return (
      <div key={app._id} className="bg-white border rounded-md p-4 shadow-sm">
        <div className="flex justify-between items-start">
          <div>
            <p><strong>Name:</strong> {app.fullName || 'N/A'}</p>
            <p><strong>Email:</strong> {app.email}</p>
            <p><strong>Experience:</strong> {app.experienceYears || 'N/A'} years</p>
            <p><strong>Expected Salary:</strong> {app.expectedSalary || 'N/A'}</p>
            <p>
              <strong>Resume Score:</strong> {resumeScore} {' '}
              <button
                onClick={() => {
                  setSelectedCandidate(app);
                  setModalType('details');
                  setShowModal(true);
                }}
                className="text-xs text-blue-600 underline ml-1"
              >Details</button>
            </p>
            <p>
              <strong>Test Score:</strong> {testScore || 'N/A'} {' '}
              <button
                onClick={() => showDetailsModal('Test Details', 'Test questions, correct/incorrect answers and score breakdown will appear here.')}
                className="text-xs text-blue-600 underline ml-1"
              >View</button>
            </p>
            <p>
              <strong>Cumulative Score:</strong> {cumulative} {' '}
              <button
                onClick={() => showDetailsModal('Cumulative Score Breakdown', `Resume: ${resumeScore}, Test: ${testScore}`)}
                className="text-xs text-blue-600 underline ml-1"
              >Breakdown</button>
            </p>
            <p><strong>Interview Status:</strong> {app.interviewStatus || 'Not Started'}</p>
            
            {app.resumeId && (
              <a
                href={`/api/resumes/${app.resumeId}`}
                className="text-blue-600 underline mt-2 inline-block"
                target="_blank"
                rel="noopener noreferrer"
              >
                Download Resume
              </a>
            )}
            
            {/* Action buttons based on score and screening status */}
            <div className="mt-3 space-x-2">
              {!isJobClosed(job) && isEligibleForInterview(app) && (
                <button 
                  onClick={() => {
                    setSelectedCandidate(app);
                    setModalType('interview');
                    setShowModal(true);
                  }}
                  className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
                >
                  Schedule Interview
                </button>
              )}
              
              {!isJobClosed(job) && isEligibleForScreening(app) && (
                <button 
                  onClick={() => {
                    setSelectedCandidate(app);
                    setModalType('screening');
                    setShowModal(true);
                  }}
                  className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                >
                  Send Screening
                </button>
              )}
              
              <button 
                onClick={() => {
                  setSelectedCandidate(app);
                  setModalType('status');
                  setShowModal(true);
                }}
                className="px-3 py-1 bg-gray-200 text-gray-700 text-sm rounded hover:bg-gray-300"
              >
                Change Status
              </button>
            </div>
          </div>

          <div className="text-right space-y-2">
            {app.canTakeInterview && (
              <span className="inline-block bg-yellow-100 text-yellow-800 px-2 py-1 text-xs rounded-md">
                Shortlisted
              </span>
            )}
            {hasCompletedScreening(app) && (
              <span className="block bg-blue-100 text-blue-800 px-2 py-1 text-xs rounded-md">
                Screening Complete
              </span>
            )}
            {isEligibleForInterview(app) && (
              <span className="block bg-green-100 text-green-800 px-2 py-1 text-xs rounded-md">
                Interview Ready
              </span>
            )}
          </div>
        </div>
      </div>
    );
  }
}
