// File: pages/_app.js
// Simple fix - back to your original with just a small enhancement

import { useEffect } from "react";
import { SessionProvider } from "next-auth/react";
import "../styles/globals.css";
import Navbar from "../components/Navbar";

function MyApp({ Component, pageProps }) {
  useEffect(() => {
    const style = document.createElement('style');
    style.innerHTML = 'body { display: block !important; }';
    document.head.appendChild(style);
  }, []);

  return (
    <SessionProvider session={pageProps.session}>
      <div>
        {/* Re-enabled Navbar component */}
        <Navbar />
        <main>
          <Component {...pageProps} />
        </main>
      </div>
    </SessionProvider>
  );
}

export default MyApp;
