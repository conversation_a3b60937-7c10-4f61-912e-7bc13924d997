import connectDB from "../../../lib/mongodb";
import Application from "../../../models/Application"; 

export default async function handler(req, res) {
  await connectDB();
  const { email, jobId } = req.query;
  
  if (!email || !jobId) return res.status(400).json({ message: "Missing parameters" });

  try {
    const application = await Application.findOne({ candidateEmail: email, jobId });
    res.status(200).json({ status: application?.status || null });
  } catch (error) {
    res.status(500).json({ message: "Error fetching application status", error });
  }
}

