// File: pages/candidate/interview-live.js

import { useEffect, useState, useMemo, useRef } from 'react';
import { useRouter } from 'next/router';
import { FaSignOutAlt, FaVideo, FaMicrophone, FaLock, FaVolumeUp, FaStop, FaCircle, FaCode, FaExclamationTriangle } from 'react-icons/fa';
import dynamic from 'next/dynamic';
import axios from 'axios';
import TextToSpeech from '@/components/TextToSpeech';

// Dynamically import CodeEditor to prevent render issues
const CodeEditor = dynamic(() => import('@/components/CodeEditor'), {
  ssr: false,
  loading: () => <div className="p-4 bg-gray-100 rounded">Loading code editor...</div>
});

// Dynamically import WebcamFaceDetection with no SSR
const WebcamFaceDetection = dynamic(() => import('@/components/WebcamFaceDetection'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-64 bg-gray-800 rounded-lg flex items-center justify-center">
      <div className="animate-pulse flex space-x-4 w-full">
        <div className="flex-1 space-y-4 py-1">
          <div className="h-40 bg-gray-400 rounded w-full"></div>
          <div className="h-4 bg-gray-400 rounded w-3/4 mx-auto"></div>
        </div>
      </div>
    </div>
  )
});

export default function InterviewLive() {
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      nav, header, .navbar-auth, .user-menu,
      a[href="/browse-jobs"],
      a[href*="/signin"],
      a[href*="/login"],
      .signin-button {
        display: none !important;
        pointer-events: none !important;
        opacity: 0 !important;
      }
    `;
    document.head.appendChild(style);

    const observer = new MutationObserver(() => {
      document.querySelectorAll('button').forEach(btn => {
        if (btn.innerText.toLowerCase().includes('close interview')) {
          btn.style.display = 'none';
        }
      });
    });
    observer.observe(document.body, { childList: true, subtree: true });
    return () => observer.disconnect();
  }, []);

  // Hide top UI elements only inside Electron during interview
  useEffect(() => {
    if (typeof window !== 'undefined' && window.electronAPI?.isElectron()) {
      const style = document.createElement('style');
      style.textContent = `
        a[href="/browse-jobs"],
        a[href*="signin"],
        .auth-button,
        .signin-button,
        .user-menu,
        .navbar-auth,
        header nav,
        .navbar,
        #app-header {
          display: none !important;
          visibility: hidden !important;
          opacity: 0 !important;
          pointer-events: none !important;
        }
      `;
      document.head.appendChild(style);
    }
  }, []);


  // Injected fix: Hide UI and Close Interview button
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      a[href="/browse-jobs"],
      a[href*="signin"],
      a[href*="login"],
      .auth-button,
      .signin-button,
      .user-menu,
      .navbar-auth,
      header nav,
      .navbar,
      #app-header {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        pointer-events: none !important;
      }
    `;
    document.head.appendChild(style);

    document.querySelectorAll('button').forEach(btn => {
      if (btn.innerText.toLowerCase().includes('close interview')) {
        btn.style.display = 'none';
      }
    });
  }, []);

  const router = useRouter();
  const { id } = router.query;

  // Core states
  const [questions, setQuestions] = useState([]);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);
  const [answers, setAnswers] = useState({});
  const [showWebcam, setShowWebcam] = useState(false);
  const [cameraStatus, setCameraStatus] = useState({
    isDetecting: false,
    facesDetected: 0,
    lookingAway: false
  });
  
  // UPDATED: Security feature states
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [securityViolations, setSecurityViolations] = useState({
    fullscreenExit: 0,
    tabSwitch: 0,
    keyboardShortcut: 0,
    devTools: 0,
    screenCapture: 0,
    multipleFaces: 0,
    copyPaste: 0,
    externalBrowsing: 0,
  });
  const [totalViolations, setTotalViolations] = useState(0);
  const [showWarning, setShowWarning] = useState(false);
  const [warningMessage, setWarningMessage] = useState('');
  const [warningCount, setWarningCount] = useState(0);
  const [maxWarnings, setMaxWarnings] = useState(3); // Updated to 3 warnings, disqualify on 4th
  const [disqualified, setDisqualified] = useState(false);
  const [disqualificationReason, setDisqualificationReason] = useState('');
  const [suspiciousEvents, setSuspiciousEvents] = useState([]);
  
  // NEW: Add flags for one-time events
  const [dualScreenDetected, setDualScreenDetected] = useState(false);
  const [dualScreenReported, setDualScreenReported] = useState(false);
  
  // IMPROVED: Track external browsing violations separately with stricter limits
  const [externalBrowsingCount, setExternalBrowsingCount] = useState(0);
  const [maxExternalViolations, setMaxExternalViolations] = useState(3);
  
  // NEW: Add refs for security checks
  const fullscreenCheckIntervalRef = useRef(null);
  const visibilityCheckIntervalRef = useRef(null);
  const warningTimeoutRef = useRef(null);
  const securityViolationTimeoutRef = useRef(null);
  
  // Loading status
  const [loadingStatus, setLoadingStatus] = useState('Initializing...');
  
  // Provider statistics
  const [providerStats, setProviderStats] = useState(null);
  
  // Store the application ID in a ref to ensure it's available later
  const appIdRef = useRef(null);
  const safetyTimeoutRef = useRef(null);
  const loadingTimeoutRef = useRef(null);
  
  // Speech-to-text states
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [interimResult, setInterimResult] = useState('');
  const [useOfflineMode, setUseOfflineMode] = useState(false);
  const [webSpeechFailCount, setWebSpeechFailCount] = useState(0);
  
  // Code editor states
  const [code, setCode] = useState('');
  const [codeLanguage, setCodeLanguage] = useState('javascript');
  const [codeOutput, setCodeOutput] = useState('');
  const [isRunningCode, setIsRunningCode] = useState(false);
  
  // Text-to-speech state
  const [isSpeaking, setIsSpeaking] = useState(false);
  
  // Refs for speech-to-text
  const audioStreamRef = useRef(null);
  const audioMediaRecorderRef = useRef(null);
  const recordedAudioChunksRef = useRef([]);
  const recordingTimerRef = useRef(null);
  const speechRecognitionTimeoutRef = useRef(null);
  
  // Store the application ID in a ref when available
  useEffect(() => {
    if (id) {
      appIdRef.current = id;
      console.log('Application ID stored in ref:', id);
    }
  }, [id]);

  // FIXED: Add CSS to hide authentication elements and attach window blur handler
  useEffect(() => {
    if (!loading) {
      // Add custom style to hide authentication UI elements with safer selectors
      const style = document.createElement('style');
      style.textContent = `
        /* Hide authentication UI elements and app header completely */
        .navbar-auth,
        .auth-button,
        .signin-button, 
        .login-form,
        a[href*="/auth"],
        a[href*="/signin"],
        a[href*="/login"],
        button[onclick*="auth"],
        button[onclick*="login"],
        .user-menu,
        .user-dropdown,
        .app-logo,
        .app-name,
        /* Target specific elements */
        a[href="/browse-jobs"],
        a.navbar-brand,
        header nav,
        /* Target by ID if available */
        #navbar-top,
        #app-header,
        /* Less aggressive approach with standard selectors */
        [class*="header"]:not([class*="interview"]),
        [class*="navbar"]:not([class*="interview"]),
        [id*="header"]:not([id*="interview"]),
        [id*="navbar"]:not([id*="interview"]) {
          display: none !important;
          visibility: hidden !important;
          opacity: 0 !important;
          pointer-events: none !important;
          height: 0 !important;
          width: 0 !important;
          position: absolute !important;
          top: -9999px !important;
          left: -9999px !important;
          z-index: -9999 !important;
        }
        
        /* Ensure our interview UI is positioned at the top instead */
        .proctored-interview-container {
          margin-top: 0 !important;
          padding-top: 0 !important;
        }
      `;
      
      // Add a unique attribute to identify this style
      style.setAttribute('data-interview-styles', 'true');
      
      // Check if already exists before appending
      if (!document.head.querySelector('style[data-interview-styles="true"]')) {
        document.head.appendChild(style);
      }
      
      // Also disable external links with safer approach
      const disableExternalLinks = () => {
        try {
          const links = document.querySelectorAll('a');
          links.forEach(link => {
            const href = link.getAttribute('href');
            if (href && 
                !href.startsWith('#') && 
                !href.includes('/candidate/interview')) {
              
              // Add click handler to prevent navigation
              link.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                
                // Report as violation
                handleSecurityViolation('externalNavigation', 'Attempted to navigate away from interview');
                
                return false;
              });
              
              // Visually disable the link
              link.style.pointerEvents = 'none';
              link.style.opacity = '0.5';
              link.style.textDecoration = 'none';
            }
          });
        } catch (err) {
          console.error('Error in disableExternalLinks:', err);
        }
      };
      
      // Run immediately
      disableExternalLinks();
      
      // Use a safer interval mechanism
      const linkCheckInterval = setInterval(() => {
        try {
          disableExternalLinks();
        } catch (err) {
          console.error('Error in link check interval:', err);
        }
      }, 2000);
      
      // Expose global handler for external blur events with error handling
      window.handleWindowBlur = () => {
        try {
          handleExternalBrowsingViolation('Window focus lost');
        } catch (err) {
          console.error('Error in handleWindowBlur:', err);
        }
      };
      
      // Add listener for external browsing events with error handling
      const handleExternalBrowsingEvent = (e) => {
        try {
          handleExternalBrowsingViolation('External application detected');
        } catch (err) {
          console.error('Error in external browsing event handler:', err);
        }
      };
      
      window.addEventListener('external-browsing-violation', handleExternalBrowsingEvent);
      
      return () => {
        clearInterval(linkCheckInterval);
        window.removeEventListener('external-browsing-violation', handleExternalBrowsingEvent);
      };
    }
  }, [loading]);

  // UPDATED: Set up fullscreen and other security checks
  useEffect(() => {
    if (id && !loading) {
      // Check for dual screen once at startup
      const screenCount = window.screen && window.screen.availWidth && window.screen.availHeight 
        ? Math.round(window.outerWidth / window.screen.availWidth) 
        : 1;
        
      if (screenCount > 1 || (window.screen.width !== window.outerWidth)) {
        console.log('⚠️ Multiple displays detected');
        setDualScreenDetected(true);
        
        // Only report once
        if (!dualScreenReported) {
          logSuspiciousEvent('Multiple displays detected');
          setDualScreenReported(true);
        }
      }
      
      // Set up fullscreen check interval - but don't count the same violation repeatedly
      let lastFullscreenState = isDocumentFullScreen();
      fullscreenCheckIntervalRef.current = setInterval(() => {
        const currentFullscreenState = isDocumentFullScreen();
        setIsFullScreen(currentFullscreenState);
        
        // Only report a violation if the state actually changed from fullscreen to not fullscreen
        if (lastFullscreenState && !currentFullscreenState) {
          handleSecurityViolation('fullscreenExit', 'Exited fullscreen mode');
          requestFullScreen();
        }
        
        lastFullscreenState = currentFullscreenState;
      }, 2000);
      
      // Set up visibility check interval to detect tab switching
      visibilityCheckIntervalRef.current = setInterval(() => {
        if (document.visibilityState === 'hidden') {
          handleExternalBrowsingViolation('Switched to another tab/window');
        }
      }, 1000); // Reduced to 1 second to ensure we catch quick tab switches
      
      // Add event listeners for security
      document.addEventListener('visibilitychange', handleVisibilityChange);
      document.addEventListener('fullscreenchange', handleFullScreenChange);
      document.addEventListener('webkitfullscreenchange', handleFullScreenChange);
      document.addEventListener('mozfullscreenchange', handleFullScreenChange);
      document.addEventListener('MSFullscreenChange', handleFullScreenChange);
      document.addEventListener('contextmenu', handleContextMenu);
      document.addEventListener('selectstart', handleSelectStart);
      document.addEventListener('copy', handleCopy);
      document.addEventListener('paste', handlePaste);
      document.addEventListener('cut', handleCut);
      document.addEventListener('keydown', handleKeyDown);
      
      // Add blur event listener to catch window focus loss
      window.addEventListener('blur', handleWindowBlur);
      
      // Setup anti-console and dev tools detection
      setupDevToolsDetection();
      
      // Request fullscreen when interview starts
      requestFullScreen();
      
      // Clean up on unmount
      return () => {
        clearInterval(fullscreenCheckIntervalRef.current);
        clearInterval(visibilityCheckIntervalRef.current);
        clearTimeout(warningTimeoutRef.current);
        clearTimeout(securityViolationTimeoutRef.current);
        
        document.removeEventListener('visibilitychange', handleVisibilityChange);
        document.removeEventListener('fullscreenchange', handleFullScreenChange);
        document.removeEventListener('webkitfullscreenchange', handleFullScreenChange);
        document.removeEventListener('mozfullscreenchange', handleFullScreenChange);
        document.removeEventListener('MSFullscreenChange', handleFullScreenChange);
        document.removeEventListener('contextmenu', handleContextMenu);
        document.removeEventListener('selectstart', handleSelectStart);
        document.removeEventListener('copy', handleCopy);
        document.removeEventListener('paste', handlePaste);
        document.removeEventListener('cut', handleCut);
        document.removeEventListener('keydown', handleKeyDown);
        window.removeEventListener('blur', handleWindowBlur);
      };
    }
  }, [id, loading, dualScreenReported]);

  // IMPROVED: Handle window blur as a major violation - this will trigger for each blur
  const handleWindowBlur = () => {
    if (!loading) {
      console.log('⚠️ Window focus lost - possible external browsing');
      // This will trigger on every window blur without checking previous count
      handleExternalBrowsingViolation('Lost window focus - possible external browsing');
    }
  };

  // IMPROVED: Track external browsing as a separate and more serious violation
  const handleExternalBrowsingViolation = (message) => {
    // Track separate count for external browsing violations - always increment by 1
    const newCount = externalBrowsingCount + 1;
    setExternalBrowsingCount(newCount);
    
    console.warn(`⚠️ External browsing violation #${newCount}: ${message}`);
    
    // Also increment security violations
    setSecurityViolations(prev => {
      const updated = { ...prev };
      updated.externalBrowsing = newCount;
      return updated;
    });
    
    // Update total violations counter as well
    setTotalViolations(prev => prev + 1);
    
    // Add to suspicious events log
    const timestamp = new Date().toISOString();
    setSuspiciousEvents(prev => [...prev, { timestamp, event: message }]);
    
    // Report to server
    reportViolation('externalBrowsing', message, timestamp);
    
    // Show warning up to max allowed
    if (newCount < maxExternalViolations) {
      showSecurityWarning(
        `${message}. This is a major violation. Warning ${newCount} of ${maxExternalViolations}.`, 
        'externalBrowsing', 
        newCount
      );
    } else {
      // Disqualify after too many violations
      disqualifyCandidate(`Multiple external browsing violations: ${message}`);
    }
    
    // Force refocus on window
    window.focus();
  };

  // Setup console and dev tools detection
  const setupDevToolsDetection = () => {
    // Method 1: Override console methods to detect usage
    const originalConsole = { ...console };
    let consoleOpened = false;
    
    const methods = ['log', 'debug', 'info', 'warn', 'error'];
    methods.forEach(method => {
      console[method] = (...args) => {
        if (!consoleOpened && !args[0]?.toString().includes('React DevTools')) {
          consoleOpened = true;
          handleSecurityViolation('devTools', 'Console usage detected');
        }
        // Call original to maintain functionality
        originalConsole[method](...args);
      };
    });
    
    // Method 2: Window size detection - only report once per session
    let devToolsOpen = false;
    const checkDevTools = () => {
      // Use higher thresholds to avoid false positives
      const widthThreshold = window.outerWidth - window.innerWidth > 250;
      const heightThreshold = window.outerHeight - window.innerHeight > 250;
      
      if ((widthThreshold || heightThreshold) && !devToolsOpen) {
        devToolsOpen = true;
        handleSecurityViolation('devTools', 'Developer tools detected');
      }
    };
    
    // Check periodically
    setInterval(checkDevTools, 5000);
  };
  
  // Check if document is in fullscreen mode
  const isDocumentFullScreen = () => {
    return !!(
      document.fullscreenElement || 
      document.mozFullScreenElement ||
      document.webkitFullscreenElement || 
      document.msFullscreenElement
    );
  };
  
  // Request fullscreen mode
  const requestFullScreen = () => {
    try {
      const elem = document.documentElement;
      
      if (elem.requestFullscreen) {
        elem.requestFullscreen();
      } else if (elem.mozRequestFullScreen) {
        elem.mozRequestFullScreen();
      } else if (elem.webkitRequestFullscreen) {
        elem.webkitRequestFullscreen();
      } else if (elem.msRequestFullscreen) {
        elem.msRequestFullscreen();
      }
    } catch (error) {
      console.error('Error requesting fullscreen:', error);
      logSuspiciousEvent('Failed to enter fullscreen mode');
    }
  };
  
  // Exit fullscreen mode
  const exitFullScreen = () => {
    try {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }
    } catch (error) {
      console.error('Error exiting fullscreen:', error);
    }
  };
  
  // Handle fullscreen change events
  const handleFullScreenChange = () => {
    const isInFullScreen = isDocumentFullScreen();
    setIsFullScreen(isInFullScreen);
    
    if (!isInFullScreen && !loading) {
      handleSecurityViolation('fullscreenExit', 'Exited fullscreen mode');
      
      // Attempt to re-enter fullscreen after a short delay
      setTimeout(() => {
        requestFullScreen();
      }, 500);
    }
  };
  
  // Handle visibility change events
  const handleVisibilityChange = () => {
    if (document.visibilityState === 'hidden' && !loading) {
      handleExternalBrowsingViolation('Switched to another tab/window');
    }
  };
  
  // UPDATED: Handle security violations
  const handleSecurityViolation = (type, message) => {
    // Log the violation
    console.warn(`⚠️ Security violation: ${type} - ${message}`);
    
    // Increment violation count for this specific type
    setSecurityViolations(prev => {
      const updated = { ...prev };
      updated[type] = (updated[type] || 0) + 1;
      return updated;
    });
    
    // Track total violations
    setTotalViolations(prev => prev + 1);
    
    // Add to suspicious events log
    const timestamp = new Date().toISOString();
    setSuspiciousEvents(prev => [...prev, { timestamp, event: message }]);
    
    // Report to server
    reportViolation(type, message, timestamp);
    
    // Get updated count for warnings
    const newCount = totalViolations + 1;
    
    // Major violations trigger warnings immediately (tab switch, fullscreen exit)
    const isMajorViolation = ['tabSwitch', 'fullscreenExit', 'devTools'].includes(type);
    
    if (isMajorViolation) {
      // Show warning based on total major violations
      // Calculate warning count
      const newWarningCount = Math.min(Math.floor(newCount / 1), maxWarnings);
      
      if (newWarningCount > warningCount) {
        // Only show new warnings
        showSecurityWarning(message, type, newWarningCount);
      }
      
      // Check for disqualification
      if (newCount > maxWarnings) {
        // Disqualify after max violations
        disqualifyCandidate(`Security violations (${newCount}): ${message}`);
      }
    }
  };
  
  // UPDATED: Show security warning with smaller size
  const showSecurityWarning = (message, type, count) => {
    setWarningMessage(`${message}. Warning ${count} of ${maxWarnings}.`);
    setWarningCount(count);
    setShowWarning(true);
    
    // Hide warning after set time
    if (warningTimeoutRef.current) {
      clearTimeout(warningTimeoutRef.current);
    }
    
    warningTimeoutRef.current = setTimeout(() => {
      setShowWarning(false);
    }, 5000);
  };
  
  // Disqualify candidate
  const disqualifyCandidate = async (reason) => {
    setDisqualified(true);
    setDisqualificationReason(reason);
    
    try {
      // Report disqualification to server
      await axios.post('/api/interview/disqualify', {
        applicationId: id,
        reason,
        violations: suspiciousEvents
      });
      
      // Redirect after a short delay
      setTimeout(() => {
        exitInterview();
      }, 5000);
    } catch (error) {
      console.error('Error reporting disqualification:', error);
      exitInterview();
    }
  };
  
  // Report violation to server
  const reportViolation = async (type, message, timestamp) => {
    try {
      await axios.post('/api/interview/violation', {
        applicationId: id,
        type,
        message,
        timestamp: timestamp || new Date().toISOString()
      });
    } catch (error) {
      console.error('Error reporting violation:', error);
    }
  };
  
  // Log suspicious events
  const logSuspiciousEvent = (event) => {
    const timestamp = new Date().toISOString();
    setSuspiciousEvents(prev => [...prev, { timestamp, event }]);
    
    // Report to server
    reportSuspiciousEvent(event, timestamp);
    console.log(`⚠️ Suspicious event: ${event}`);
  };
  
  // Report suspicious event to server
  const reportSuspiciousEvent = async (event, timestamp) => {
    try {
      await axios.post('/api/interview/report-suspicious', {
        applicationId: id,
        questionIndex: currentQuestion,
        event,
        timestamp: timestamp || new Date().toISOString()
      });
    } catch (error) {
      console.error('Error reporting suspicious event:', error);
    }
  };
  
  // UPDATED: Event handlers for security measures
  const handleContextMenu = (e) => {
    // Allow context menu in code editor only
    if (e.target.closest('.monaco-editor')) {
      return true;
    }
    
    e.preventDefault();
    handleSecurityViolation('contextMenu', 'Context menu attempt');
    return false;
  };
  
  const handleSelectStart = (e) => {
    // Allow text selection in code editor ONLY
    if (
      e.target.closest('.monaco-editor')
    ) {
      return true;
    }
    
    e.preventDefault();
    return false;
  };
  
  // UPDATED: Copy/paste handling - only allowed in code editor
  const handleCopy = (e) => {
    // Allow copy in code editor ONLY
    if (e.target.closest('.monaco-editor')) {
      return true;
    }
    
    e.preventDefault();
    handleSecurityViolation('copyPaste', 'Copy attempt outside code editor');
    return false;
  };
  
  const handlePaste = (e) => {
    // Allow paste in code editor ONLY
    if (e.target.closest('.monaco-editor')) {
      return true;
    }
    
    e.preventDefault();
    handleSecurityViolation('copyPaste', 'Paste attempt outside code editor');
    return false;
  };
  
  const handleCut = (e) => {
    // Allow cut in code editor ONLY
    if (e.target.closest('.monaco-editor')) {
      return true;
    }
    
    e.preventDefault();
    handleSecurityViolation('copyPaste', 'Cut attempt outside code editor');
    return false;
  };
  
  // UPDATED: Handle key down events
  const handleKeyDown = (e) => {
    // Block Tab key to prevent focus navigation except in code editor
    if (e.key === 'Tab' && !e.target.closest('.monaco-editor')) {
      e.preventDefault();
      return false;
    }
    
    // UPDATED: Handle Escape key - report it but prevent default behavior
    if (e.key === 'Escape' && isFullScreen) {
      e.preventDefault();
      handleSecurityViolation('keyboardShortcut', 'Attempted to exit fullscreen with Escape key');
      return false;
    }
    
    // Block other dangerous key combinations
    if (
      (e.key === 'F11') ||
      (e.key === 'F12') ||
      (e.altKey && e.key === 'Tab') ||
      (e.ctrlKey && e.key === 'r') ||
      (e.ctrlKey && e.key === 'w') ||
      (e.ctrlKey && e.key === 'n') ||
      (e.ctrlKey && e.key === 't') ||
      (e.ctrlKey && e.key === 'p') ||  // Block print
      (e.altKey && e.key === 'F4')
    ) {
      e.preventDefault();
      
      // Specific reason for F12
      if (e.key === 'F12') {
        handleSecurityViolation('devTools', 'Attempted to open developer tools with F12');
      } else {
        handleSecurityViolation('keyboardShortcut', `Blocked keyboard shortcut: ${e.key}`);
      }
      
      return false;
    }
  };
  
  // Screen capture detection
  useEffect(() => {
    // Only run if navigator.mediaDevices exists
    if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
      // Save original method
      const originalGetDisplayMedia = navigator.mediaDevices.getDisplayMedia;
      
      // Override method to detect screen sharing attempts
      navigator.mediaDevices.getDisplayMedia = function(constraints) {
        // Report violation
        handleSecurityViolation('screenCapture', 'Screen sharing attempt detected');
        
        // Either block completely or allow but report
        throw new Error('Screen sharing is not allowed during this interview');
      };
      
      // Clean up on unmount
      return () => {
        navigator.mediaDevices.getDisplayMedia = originalGetDisplayMedia;
      };
    }
  }, []);

  // Network connectivity check function
  const checkNetworkConnection = () => {
    return navigator.onLine;
  };

  // Create demo questions if needed
  const createDemoQuestions = () => {
    console.log('Creating demo questions');
    return [
      // MCQ Questions (10)
      { 
        id: 'mcq1', 
        question: 'Which programming language is primarily used for iOS development?', 
        type: 'mcq',
        options: ['Swift', 'Java', 'Kotlin', 'C#'] 
      },
      { 
        id: 'mcq2', 
        question: 'Which of the following is NOT a NoSQL database?', 
        type: 'mcq',
        options: ['PostgreSQL', 'MongoDB', 'Cassandra', 'Firebase'] 
      },
      // ... more questions from original file ...
    ];
  };

  // Fetch questions when component loads
  useEffect(() => {
    if (!router.isReady || !id) return;
    
    console.log('Starting interview with ID:', id);
    
    // MODIFIED: Increase the safety timeout to wait for all providers
    setLoadingStatus('Generating interview questions from multiple AI providers...');
    
    safetyTimeoutRef.current = setTimeout(() => {
      console.log('Safety timeout triggered - forcing interview to continue');
      
      // Instead of using demo questions, check if we already have some real questions
      if (questions.length > 0) {
        // We already have some questions, proceed with those
        setLoading(false);
      } else {
        // Last resort: use demo questions
        setQuestions(createDemoQuestions());
        setError('Using demo questions - server connection timeout');
        setLoading(false);
      }
    }, 60000); // Increased to 60 seconds to wait for all providers
    
    // Fetch questions
    fetchQuestions();
    
    return () => {
      if (safetyTimeoutRef.current) {
        clearTimeout(safetyTimeoutRef.current);
      }
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
      if (speechRecognitionTimeoutRef.current) {
        clearTimeout(speechRecognitionTimeoutRef.current);
      }
      
      // Stop any ongoing recording
      stopRecording();
      
      // Cancel any speech
      if (window.speechSynthesis) {
        window.speechSynthesis.cancel();
      }
    };
  }, [router.isReady, id]);

  // The fetchQuestions function with better error handling and debugging
  const fetchQuestions = async () => {
    try {
      console.log('Starting fetchQuestions for application ID:', id);
      
      // Validate ID before making request
      if (!id) {
        console.error('Missing application ID');
        throw new Error('Missing application ID');
      }
      
      // MODIFIED: Add waitForAllProviders parameter to get questions from multiple APIs
      setLoadingStatus('Requesting questions from multiple AI providers...');
      console.log('Making API request with payload:', { 
        applicationId: id,
        forceRegenerate: true,
        waitForAllProviders: true // Wait for multiple AI providers to respond
      });
      
      const response = await axios.post('/api/interview/questions', { 
        applicationId: id,
        forceRegenerate: true,
        waitForAllProviders: true // Wait for multiple AI providers to respond
      });
      
      console.log('API Response received successfully:', {
        status: response.status,
        hasData: !!response.data,
        dataKeys: response.data ? Object.keys(response.data) : [],
        hasQuestions: response.data && response.data.questions,
        questionsLength: response.data && response.data.questions ? response.data.questions.length : 0
      });
      
      // NEW: Store provider statistics if available
      if (response.data && response.data.providerStats) {
        console.log('Provider statistics:', response.data.providerStats);
        setProviderStats(response.data.providerStats);
      }
      
      // First check if the response has the expected structure
      if (response.data && response.data.success === true) {
        // Check if questions are directly in the 'questions' property
        if (response.data.questions && Array.isArray(response.data.questions) && response.data.questions.length > 0) {
          const fetchedQuestions = response.data.questions;
          console.log(`✅ Successfully loaded ${fetchedQuestions.length} questions from API`);
          setLoadingStatus(`Processing ${fetchedQuestions.length} questions...`);
          
          // Explicitly clear the safety timeout
          if (safetyTimeoutRef.current) {
            clearTimeout(safetyTimeoutRef.current);
            safetyTimeoutRef.current = null;
          }
          
          // Validate all questions have the required fields
          const validQuestions = fetchedQuestions.filter(q => 
            q && typeof q === 'object' && q.question && 
            (q.type === 'mcq' || q.type === 'technical' || q.type === 'behavioral' || q.type === 'coding')
          );
          
          if (validQuestions.length > 0) {
            console.log(`✅ ${validQuestions.length}/${fetchedQuestions.length} questions are valid`);
            
            // Add missing properties if needed
            const processedQuestions = validQuestions.map(q => {
              // Ensure MCQ questions have options
              if (q.type === 'mcq' && (!q.options || !Array.isArray(q.options) || q.options.length === 0)) {
                console.warn(`⚠️ MCQ question missing options: "${q.question}"`);
                q.options = ['Option A', 'Option B', 'Option C', 'Option D'];
              }
              
              // Ensure coding questions have starter code and language
              if (q.type === 'coding') {
                if (!q.language) {
                  console.warn(`⚠️ Coding question missing language: "${q.question}"`);
                  q.language = 'javascript';
                }
                
                if (!q.starterCode) {
                  console.warn(`⚠️ Coding question missing starter code: "${q.question}"`);
                  q.starterCode = '// Write your solution here\n\n';
                }
              }
              
              // Ensure all questions have an ID
              if (!q.id) {
                q.id = `q-${Math.random().toString(36).substring(2, 9)}`;
              }
              
              return q;
            });
            
            setQuestions(processedQuestions);
            // Clear any previous error message when questions are loaded successfully
            setError(null);
            
            // Initialize camera after loading questions
            setLoadingStatus('Setting up camera...');
            setTimeout(() => {
              setShowWebcam(true);
              
              loadingTimeoutRef.current = setTimeout(() => {
                setLoading(false);
                
                // Request fullscreen after fully loaded
                requestFullScreen();
              }, 2000);
            }, 1000);
            return;
          } else {
            console.error('❌ No valid questions found in API response');
            throw new Error('No valid questions in API response');
          }
        } else {
          console.warn('⚠️ Response structure not as expected:', response.data);
          
          // Try to find questions in any property
          for (const key in response.data) {
            if (Array.isArray(response.data[key]) && 
                response.data[key].length > 0 && 
                response.data[key][0] && 
                typeof response.data[key][0] === 'object' && 
                response.data[key][0].question) {
              
              console.log(`✅ Found questions array in response.data.${key}`);
              const fetchedQuestions = response.data[key];
              
              // Explicitly clear the safety timeout
              if (safetyTimeoutRef.current) {
                clearTimeout(safetyTimeoutRef.current);
                safetyTimeoutRef.current = null;
              }
              
              setQuestions(fetchedQuestions);
              // Clear any previous error message when questions are loaded successfully
              setError(null);
              
              // Initialize camera after loading questions
              setLoadingStatus('Setting up camera...');
              setTimeout(() => {
                setShowWebcam(true);
                
                loadingTimeoutRef.current = setTimeout(() => {
                  setLoading(false);
                  
                  // Request fullscreen after fully loaded
                  requestFullScreen();
                }, 2000);
              }, 1000);
              return;
            }
          }
          
          throw new Error('Questions not found in API response');
        }
      } else {
        console.error('❌ API response indicates failure:', response.data);
        throw new Error(response.data?.message || 'API request failed');
      }
    } catch (error) {
      console.error('Error fetching questions:', error);
      
      // Detailed error logging
      if (error.response) {
        // The request was made and the server responded with a status code
        // outside the 2xx range
        console.error('API error response:', error.response.status, error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        console.error('No response received from server');
      }
      
      // MODIFIED: Don't immediately fall back to demo questions, wait for safety timeout
      console.log('Error occurred but waiting for safety timeout');
      setLoadingStatus('Error occurred while loading questions. Retrying...');
    }
  };

  // Update code language and starter code when a coding question loads
  useEffect(() => {
    if (!questions[currentQuestion]) return;
    
    const currentQ = questions[currentQuestion];
    
    if (currentQ.type === 'coding') {
      // Always set to JavaScript for now
      setCodeLanguage('javascript');
      
      if (currentQ.starterCode) {
        setCode(currentQ.starterCode);
      } else {
        // Default starter code if none provided
        setCode(`// Write your solution here\n\n`);
      }
      // Reset output
      setCodeOutput('');
    }
    
    // Reset transcript when moving to a new question
    setTranscript('');
    setInterimResult('');
    
    // Stop recording if active
    if (isListening) {
      stopRecording();
    }
    
  }, [currentQuestion, questions]);

  // Handle webcam status updates
  const handleCameraUpdate = (status) => {
    if (!status) return;
    
    setCameraStatus(prevStatus => {
      const needsUpdate = 
        prevStatus.isDetecting !== status.isDetecting ||
        prevStatus.facesDetected !== status.facesDetected ||
        prevStatus.lookingAway !== status.lookingAway;
      
      return needsUpdate ? { ...status } : prevStatus;
    });
    
    // NEW: Check for security violations based on camera status
    if (status.facesDetected > 1) {
      handleSecurityViolation('multipleFaces', 'Multiple faces detected');
    }
    
    if (status.facesDetected === 0 && !loading) {
      // Log this as suspicious but don't count as violation yet
      // Can become a violation if persists too long
      logSuspiciousEvent('No face detected');
    }
    
    if (status.lookingAway && !loading) {
      // Log looking away as suspicious
      logSuspiciousEvent('Looking away from screen');
    }
  };

  // Use useMemo for the webcam component to prevent re-renders
  const webcamComponent = useMemo(() => {
    if (!showWebcam) return null;
    
    return (
      <div className="bg-gray-100 p-3 rounded-lg shadow">
        <h3 className="text-lg font-medium mb-2 text-gray-700">Webcam Monitor</h3>
        
        {/* Static key ensures the component is never remounted */}
        <div key="permanent-camera">
          <WebcamFaceDetection onStatusUpdate={handleCameraUpdate} />
        </div>
        
        {/* Camera status indicators */}
        <div className="mt-4 grid grid-cols-2 gap-2">
          <div className={`p-2 rounded ${cameraStatus.isDetecting ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'} text-sm`}>
            Camera: {cameraStatus.isDetecting ? 'Active' : 'Inactive'}
          </div>
          
          <div className={`p-2 rounded ${cameraStatus.facesDetected === 1 ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'} text-sm`}>
            Faces: {cameraStatus.facesDetected}
          </div>
          
          <div className={`p-2 rounded ${!cameraStatus.lookingAway ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'} text-sm`}>
            Focus: {!cameraStatus.lookingAway ? 'Good' : 'Looking Away'}
          </div>
        </div>
        
        {/* UPDATED: Security violations summary */}
        {Object.values(securityViolations).some(count => count > 0) && (
          <div className="mt-4 p-3 bg-yellow-50 rounded border border-yellow-200">
            <h4 className="font-medium text-yellow-800 mb-2">Security Violations:</h4>
            <ul className="text-sm text-yellow-700">
              {Object.entries(securityViolations).map(([type, count]) => 
                count > 0 ? (
                  <li key={type} className="flex justify-between">
                    <span className="capitalize">{type.replace(/([A-Z])/g, ' $1').toLowerCase()}:</span>
                    <span>{count}</span>
                  </li>
                ) : null
              )}
              <li className="flex justify-between font-semibold border-t mt-1 pt-1">
                <span>Total:</span>
                <span>{totalViolations} / {maxWarnings}</span>
              </li>
            </ul>
          </div>
        )}
        
        {/* ADDED: Display dual screen status */}
        {dualScreenDetected && (
          <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-700">
            <p>Multiple displays detected. This is permitted but has been noted.</p>
          </div>
        )}
      </div>
    );
  }, [showWebcam, cameraStatus, securityViolations, totalViolations, dualScreenDetected]);

  // Handle leaving the interview
  const exitInterview = () => {
    if (window.confirm('Are you sure you want to exit the interview? All progress will be lost.')) {
      // Get the application ID from our ref
      const applicationId = appIdRef.current || id;
      
      // Notify backend
      try {
        axios.post('/api/interview/abort', { applicationId })
          .catch(error => console.error('Error aborting interview:', error));
      } catch (e) {
        console.error('Error aborting interview:', e);
      }
      
      // Stop recording if active
      stopRecording();
      
      // Cancel any speech
      if (window.speechSynthesis) {
        window.speechSynthesis.cancel();
      }
      
      // Exit fullscreen
      exitFullScreen();
      
      // Try to use Electron's exit function
      try {
        if (window.electronAPI && window.electronAPI.exitInterview) {
          console.log('Exiting via Electron API');
          window.electronAPI.exitInterview();
          
          // Set a fallback timeout in case Electron API fails
          setTimeout(() => {
            router.push('/candidate/dashboard');
          }, 1000);
        } else {
          router.push('/candidate/dashboard');
        }
      } catch (e) {
        console.error('Error with Electron exit:', e);
        router.push('/candidate/dashboard');
      }
    }
  };

  // Handle answer changes for various question types
  const handleAnswerChange = (answer) => {
    setAnswers(prev => ({
      ...prev,
      [currentQuestion]: answer
    }));
  };

  // Format recording time in MM:SS format
  const formatRecordingTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Start recording audio for speech-to-text
  const startRecording = async () => {
    try {
      // Clear any error messages
      setError(null);
      
      // Stop any existing recording
      stopRecording();
      
      // Check network connectivity first
      if (!checkNetworkConnection()) {
        setError('No internet connection detected. Using offline recording mode.');
        setUseOfflineMode(true);
        startBrowserRecording();
        return;
      }
      
      // Use the selected mode or fallback based on availability
      if (!useOfflineMode && (window.SpeechRecognition || window.webkitSpeechRecognition)) {
        startBrowserTranscription();
      } else {
        startBrowserRecording();
      }
      
    } catch (error) {
      console.error('Error starting recording:', error);
      setError(`Failed to start recording: ${error.message}. Please check your microphone permissions.`);
      setIsListening(false);
    }
  };

  // Direct browser recording with Whisper API transcription
  const startBrowserRecording = async () => {
    try {
      // Get audio stream
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      // Store stream reference for cleanup
      audioStreamRef.current = stream;
      
      // Create MediaRecorder for the stream
      const mediaRecorder = new MediaRecorder(stream);
      audioMediaRecorderRef.current = mediaRecorder;
      recordedAudioChunksRef.current = [];
      
      // Handle data available event
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          recordedAudioChunksRef.current.push(event.data);
        }
      };
      
      // Handle recording stop event
      mediaRecorder.onstop = async () => {
        if (recordedAudioChunksRef.current.length === 0) return;
        
        setIsProcessing(true);
        setInterimResult('Processing your recording...');
        
        try {
          // Create a blob from the recorded audio chunks
          const audioBlob = new Blob(recordedAudioChunksRef.current, { type: 'audio/webm' });
          
          // Create form data to send to the server
          const formData = new FormData();
          formData.append('file', audioBlob, 'recording.webm');
          
          // Add application and question IDs if available
          if (appIdRef.current) {
            formData.append('applicationId', appIdRef.current);
          }
          
          if (questions[currentQuestion]?.id) {
            formData.append('questionId', questions[currentQuestion].id);
          }
          
          // Send to your backend for processing with Whisper API
          const response = await fetch('/api/transcribe', {
            method: 'POST',
            body: formData
          });
          
          if (response.ok) {
            const result = await response.json();
            if (result.text) {
              // Add the transcribed text to the existing transcript
              setTranscript(prev => prev ? `${prev} ${result.text}` : result.text);
              setInterimResult('');
            } else {
              throw new Error('No transcription returned');
            }
          } else {
            // Try to get error details from response
            let errorMsg = 'Failed to transcribe audio';
            try {
              const errorData = await response.json();
              if (errorData.error) {
                errorMsg = errorData.error;
              }
            } catch (e) {
              // If response isn't JSON, just use status text
              errorMsg = `Server error: ${response.status} ${response.statusText}`;
            }
            throw new Error(errorMsg);
          }
        } catch (error) {
          console.error('Error processing recorded audio:', error);
          setError(`Failed to process recording: ${error.message}. Please try again or type manually.`);
        } finally {
          setIsProcessing(false);
          recordedAudioChunksRef.current = [];
        }
      };
      
      // Start recording UI state
      setIsListening(true);
      setRecordingTime(0);
      setInterimResult('Recording in progress... (Offline mode - will process when stopped)');
      
      // Start recording
      mediaRecorder.start(1000); // Collect data in 1-second chunks
      
      // Start timer
      recordingTimerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
      
    } catch (error) {
      console.error('Error starting browser recording:', error);
      setError(`Browser recording failed: ${error.message}. Please type manually.`);
      setIsListening(false);
    }
  };

  // Browser-based speech recognition
  const startBrowserTranscription = () => {
    // Check if the browser supports speech recognition
    if ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognition = new SpeechRecognition();
      
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US'; // Set appropriate language
      
      recognition.onstart = () => {
        console.log('🎤 Web Speech API recognition started');
        setIsListening(true);
        setRecordingTime(0);
        recordingTimerRef.current = setInterval(() => {
          setRecordingTime(prev => prev + 1);
        }, 1000);
        
        // Set a timeout to detect if recognition hangs
        speechRecognitionTimeoutRef.current = setTimeout(() => {
          console.log('Speech recognition timeout - switching to offline mode');
          if (window.currentRecognition) {
            window.currentRecognition.abort(); // Force abort
          }
          setError('Speech recognition timeout. Switching to offline mode.');
          setUseOfflineMode(true);
          startBrowserRecording();
        }, 10000); // 10 second timeout
      };
      
      recognition.onresult = (event) => {
        // Clear the timeout on successful results
        if (speechRecognitionTimeoutRef.current) {
          clearTimeout(speechRecognitionTimeoutRef.current);
          speechRecognitionTimeoutRef.current = null;
        }
        
        let interimTranscript = '';
        let finalTranscript = '';
        
        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscript += transcript + ' ';
            console.log('Final transcript:', finalTranscript);
          } else {
            interimTranscript += transcript;
          }
        }
        
        if (finalTranscript) {
          setTranscript(prev => (prev ? prev + ' ' + finalTranscript : finalTranscript));
        }
        
        setInterimResult(interimTranscript);
      };
      
      recognition.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        
        // Clear the timeout if there's an error
        if (speechRecognitionTimeoutRef.current) {
          clearTimeout(speechRecognitionTimeoutRef.current);
          speechRecognitionTimeoutRef.current = null;
        }
        
        // Handle network errors with special logic
        if (event.error === 'network') {
          setWebSpeechFailCount(prev => prev + 1);
          
          // After 2 failures, automatically switch to offline mode
          if (webSpeechFailCount >= 1) {
            setError('Speech recognition network error. Switching to offline mode.');
            setUseOfflineMode(true);
          } else {
            setError('Speech recognition error: network. Please check your internet connection.');
          }
          
          // Fall back to browser recording
          startBrowserRecording();
          return;
        }
        
        // Provide more specific error messages based on error type
        switch(event.error) {
          case 'not-allowed':
          case 'permission-denied':
            setError('Microphone access denied. Please check your browser permissions.');
            break;
          case 'no-speech':
            setError('No speech detected. Please try again or type manually.');
            break;
          case 'audio-capture':
            setError('No microphone detected. Please check your device and permissions.');
            break;
          case 'aborted':
            setError('Speech recognition was aborted. Please try again.');
            break;
          case 'language-not-supported':
            setError('Speech recognition language not supported. Using manual input mode.');
            break;
          case 'service-not-allowed':
            setError('Speech recognition service not allowed. Using manual input mode.');
            break;
          default:
            setError(`Speech recognition error: ${event.error}. Please try again or type manually.`);
        }
        
        // Fall back to browser recording for all errors
        startBrowserRecording();
      };
      
      recognition.onend = () => {
        console.log('Speech recognition service disconnected');
        
        // Clear timeout if recognition ends
        if (speechRecognitionTimeoutRef.current) {
          clearTimeout(speechRecognitionTimeoutRef.current);
          speechRecognitionTimeoutRef.current = null;
        }
        
        // Try to restart if we're still supposed to be listening and not in offline mode
        if (isListening && !useOfflineMode) {
          try {
            recognition.start();
            console.log('Restarted speech recognition');
          } catch (e) {
            console.warn('Could not restart recognition:', e);
            // Fall back to browser recording
            startBrowserRecording();
          }
        }
      };
      
      // Store reference to recognition
      window.currentRecognition = recognition;
      
      // Start recognition
      try {
        recognition.start();
        console.log('Started speech recognition');
        setInterimResult('Recording in progress... (Web Speech API)');
      } catch (error) {
        console.error('Error starting speech recognition:', error);
        setError('Could not start speech recognition. Using alternative method.');
        startBrowserRecording();
      }
    } else {
      console.log('Speech recognition not supported, using browser recording');
      startBrowserRecording();
    }
  };

  // Stop recording
  const stopRecording = () => {
    // Clear speech recognition timeout
    if (speechRecognitionTimeoutRef.current) {
      clearTimeout(speechRecognitionTimeoutRef.current);
      speechRecognitionTimeoutRef.current = null;
    }
    
    // Stop browser-based speech recognition if active
    if (window.currentRecognition) {
      try {
        window.currentRecognition.stop();
        window.currentRecognition = null;
      } catch (e) {
        console.warn('Error stopping current recognition:', e);
      }
    }
    
    // Stop media recorder if active
    if (audioMediaRecorderRef.current && audioMediaRecorderRef.current.state !== 'inactive') {
      try {
        audioMediaRecorderRef.current.stop(); // This will trigger the onstop event
      } catch (e) {
        console.warn('Error stopping media recorder:', e);
      }
    }
    
    // Stop and release audio stream
    if (audioStreamRef.current) {
      audioStreamRef.current.getTracks().forEach(track => track.stop());
      audioStreamRef.current = null;
    }
    
    // Stop recording timer
    if (recordingTimerRef.current) {
      clearInterval(recordingTimerRef.current);
      recordingTimerRef.current = null;
    }
    
    // Update UI state
    setIsListening(false);
    setInterimResult('');
  };

  // Function to run code - JavaScript only
  const runCode = async () => {
    if (!code.trim() || isRunningCode) return;
    
    setIsRunningCode(true);
    setCodeOutput('Running code...');
    
    try {
      // For languages other than JavaScript, show a notification
      if (codeLanguage.toLowerCase() !== 'javascript') {
        setCodeOutput(`Note: Only JavaScript execution is currently supported. Your ${codeLanguage} code has been saved but not executed.`);
        setIsRunningCode(false);
        return;
      }
      
      const response = await axios.post('/api/interview/runcode', {
        code,
        language: 'javascript', // Force JavaScript
        questionId: questions[currentQuestion]?.id || `question-${currentQuestion}`
      });
      
      if (response.data.success) {
        setCodeOutput(response.data.output);
      } else {
        setCodeOutput(`Error: ${response.data.output || response.data.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error running code:', error);
      setCodeOutput(`Error running code: ${error.message || 'Unknown error'}`);
    } finally {
      setIsRunningCode(false);
    }
  };
  
  // Text-to-speech function for reading questions
  const speakQuestion = () => {
    if (!questions[currentQuestion] || !window.speechSynthesis || isSpeaking) return;
    
    // Cancel any ongoing speech
    window.speechSynthesis.cancel();
    
    const textToSpeak = questions[currentQuestion].question;
    const utterance = new SpeechSynthesisUtterance(textToSpeak);
    
    utterance.onstart = () => setIsSpeaking(true);
    utterance.onend = () => setIsSpeaking(false);
    utterance.onerror = () => setIsSpeaking(false);
    
    window.speechSynthesis.speak(utterance);
  };

  // Submit all answers and exit
  const handleSubmit = async () => {
    try {
      console.log('Submitting answers:', answers);
      
      // Save final transcript for current question if it's a speaking question
      if (['behavioral', 'technical'].includes(questions[currentQuestion]?.type) && transcript) {
        handleAnswerChange(transcript);
      }
      
      // Save final code for current question if it's a coding question
      if (questions[currentQuestion]?.type === 'coding' && code) {
        handleAnswerChange({
          code,
          output: codeOutput,
          language: codeLanguage
        });
      }
      
      // Get the application ID from our ref
      const applicationId = appIdRef.current || id;
      
      // Submit final answers
      await axios.post('/api/interview/complete', {
        applicationId,
        answers,
        suspiciousEvents,
        securityViolations
      }).catch(err => {
        console.error('Error submitting answers:', err);
      });
      
      // Exit fullscreen
      exitFullScreen();
      
      // Try to use Electron API to exit
      try {
        if (window.electronAPI && window.electronAPI.exitInterview) {
          window.electronAPI.exitInterview();
          
          // Fallback if Electron API fails
          setTimeout(() => {
            router.push('/candidate/dashboard');
          }, 1000);
        } else {
          router.push('/candidate/dashboard');
        }
      } catch (e) {
        console.error('Error with Electron exit:', e);
        router.push('/candidate/dashboard');
      }
    } catch (error) {
      console.error('Error during submission:', error);
      router.push('/candidate/dashboard');
    }
  };

   // Handle navigation to next question
  const handleNext = () => {
    // Save answer for current question
    const currentQ = questions[currentQuestion];
    
    // For behavioral or technical questions, save transcript as answer
    if (['behavioral', 'technical'].includes(currentQ?.type) && transcript) {
      handleAnswerChange(transcript);
    }
    
    // For coding questions, save code as answer
    if (currentQ?.type === 'coding' && code) {
      handleAnswerChange({
        code,
        output: codeOutput,
        language: codeLanguage
      });
    }
    
    // Move to next question
    setCurrentQuestion(prev => prev + 1);
  };

  // Handle navigation to previous question
  const handlePrev = () => {
    // Save answer for current question
    const currentQ = questions[currentQuestion];
    
    // For behavioral or technical questions, save transcript as answer
    if (['behavioral', 'technical'].includes(currentQ?.type) && transcript) {
      handleAnswerChange(transcript);
    }
    
    // For coding questions, save code as answer
    if (currentQ?.type === 'coding' && code) {
      handleAnswerChange({
        code,
        output: codeOutput,
        language: codeLanguage
      });
    }
    
    // Move to previous question
    setCurrentQuestion(prev => prev - 1);
  };

  // Recognition mode toggle component
  const RecognitionModeToggle = () => (
    <div className="flex items-center mb-2">
      <span className="text-sm mr-2">Recognition Mode:</span>
      <button
        onClick={() => setUseOfflineMode(false)}
        className={`px-2 py-1 text-xs rounded mr-1 ${
          !useOfflineMode ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'
        }`}
      >
        Real-time
      </button>
      <button
        onClick={() => setUseOfflineMode(true)}
        className={`px-2 py-1 text-xs rounded ${
          useOfflineMode ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'
        }`}
      >
        Offline
      </button>
    </div>
  );

  // UPDATED: Security warning banner component with reduced size
  const SecurityWarningBanner = () => {
    if (!showWarning) return null;
    
    return (
      <div className="fixed top-0 left-0 w-full bg-red-600 text-white p-2 z-50 text-center">
        <p className="text-sm"><strong>WARNING: Security Violation Detected</strong> - {warningMessage}</p>
      </div>
    );
  };
  
  // Disqualification overlay component
  const DisqualificationOverlay = () => {
    if (!disqualified) return null;
    
    return (
      <div className="fixed inset-0 bg-red-600 text-white flex flex-col items-center justify-center z-50 p-8">
        <h2 className="text-3xl font-bold mb-4">DISQUALIFIED</h2>
        <p className="text-xl mb-8">Reason: {disqualificationReason}</p>
        <p className="mb-4">Your interview has been terminated due to security violations.</p>
        <button 
          onClick={exitInterview}
          className="bg-white text-red-600 px-6 py-3 rounded font-bold"
        >
          Return to Home
        </button>
      </div>
    );
  };

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-md text-center max-w-md">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <h2 className="text-xl font-bold mb-2">Loading Interview</h2>
          <p className="text-gray-600 mb-4">{loadingStatus}</p>
          
          {error && (
            <div className="bg-red-50 p-3 rounded border border-red-200 mb-4 text-left">
              <p className="text-red-700">{error}</p>
            </div>
          )}
          
          {showWebcam && (
            <p className="text-sm text-blue-600">Setting up camera...</p>
          )}
        </div>
      </div>
    );
  }

  // Get current question
  const currentQ = questions[currentQuestion];

  return (
    <div className="min-h-screen bg-white text-black">
      {/* Security warning banner */}
      <SecurityWarningBanner />
      
      {/* Disqualification overlay */}
      <DisqualificationOverlay />
      
      {/* Header bar */}
      <div className="flex justify-between items-center px-6 py-4 bg-blue-600 text-white">
        <div className="flex items-center">
          <FaLock className={`mr-2 ${isFullScreen ? 'text-green-500' : 'text-red-500'}`} />
          <h1 className="text-xl font-semibold">Proctored Interview</h1>
          {!isFullScreen && (
            <button 
              onClick={requestFullScreen}
              className="ml-2 bg-yellow-500 text-xs px-2 py-1 rounded"
            >
              Enter Fullscreen
            </button>
          )}
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <FaVideo className={`mr-1 ${cameraStatus.facesDetected > 0 ? 'text-green-500' : 'text-red-500'}`} />
            <span className="text-white text-sm">Camera: {cameraStatus.isDetecting ? 'Active' : 'Inactive'}</span>
          </div>
          
          <div className="flex items-center">
            <FaMicrophone className={`mr-1 ${isListening ? 'text-green-500 animate-pulse' : 'text-white'}`} />
            <span className="text-white text-sm">Audio: {isListening ? 'Recording' : 'Ready'}</span>
          </div>
          
          <div className="bg-blue-800 rounded px-3 py-1 text-sm">
            Question {currentQuestion + 1} of {questions?.length || 0}
          </div>
          
          <button
            onClick={exitInterview}
            className="bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700 flex items-center"
          >
            <FaSignOutAlt className="inline mr-1" /> Exit
          </button>
        </div>
      </div>

      {/* Error message if any */}
      {error && (
        <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4">
          <p className="font-medium">Notice</p>
          <p>{error}</p>
        </div>
      )}
      
      {/* Provider Statistics */}
      {providerStats && (
        <div className="bg-blue-50 border-l-4 border-blue-500 text-blue-700 p-4 mb-4">
          <p className="font-medium">Question Provider Statistics:</p>
          <div className="text-sm mt-1">
            <p>Enabled providers: {providerStats.enabled?.join(', ')}</p>
            <p>Questions by provider: {Object.entries(providerStats.questionCounts || {})
              .filter(([_, count]) => count > 0)
              .map(([provider, count]) => `${provider}: ${count}`)
              .join(', ')}</p>
            <p>Question types: {Object.entries(providerStats.typeDistribution || {})
              .map(([type, count]) => `${type}: ${count}`)
              .join(', ')}</p>
            <p>Difficulty levels: {Object.entries(providerStats.difficultyDistribution || {})
              .map(([level, count]) => `${level}: ${count}`)
              .join(', ')}</p>
          </div>
        </div>
      )}
      
      {/* Main content */}
      <div className="flex flex-col md:flex-row p-6 gap-8">
        {/* Left column with webcam */}
        <div className="w-full md:w-1/3">
          {webcamComponent || (
            <div className="bg-gray-100 p-6 rounded-lg shadow text-center">
              <div className="animate-pulse flex space-x-4">
                <div className="flex-1 space-y-6 py-1">
                  <div className="h-4 bg-gray-300 rounded"></div>
                  <div className="h-28 bg-gray-300 rounded"></div>
                  <div className="space-y-3">
                    <div className="h-4 bg-gray-300 rounded"></div>
                  </div>
                </div>
              </div>
              <p className="mt-4">Initializing camera...</p>
            </div>
          )}
        </div>

        {/* Right column with questions */}
        <div className="w-full md:w-2/3">
          {questions?.length ? (
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="mb-4 flex justify-between items-center">
                <h3 className="text-lg font-semibold">Question {currentQuestion + 1} of {questions.length}</h3>
                <div className="flex items-center">
                  <span className="text-sm text-gray-500 mr-3">
                    {currentQ.type === 'mcq' ? 'Multiple Choice' : 
                     currentQ.type === 'technical' ? 'Technical Question' :
                     currentQ.type === 'behavioral' ? 'Behavioral Question' :
                     currentQ.type === 'coding' ? 'Coding Question' :
                     'Interview Question'}
                  </span>
                </div>
              </div>
              
              <div className="mb-4 flex items-center justify-between">
                <p className="font-medium text-lg">{currentQ?.question || 'Loading question...'}</p>
                
                {/* Text-to-speech button */}
                <button
                  onClick={speakQuestion}
                  disabled={isSpeaking}
                  className={`ml-2 p-2 rounded-full ${
                    isSpeaking ? 'bg-gray-200 text-gray-500' : 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                  }`}
                  title="Read question aloud"
                >
                  <FaVolumeUp />
                </button>
              </div>
              
              {currentQ?.type === 'mcq' && (
                <div className="space-y-2 mb-4">
                  {currentQ.options?.map((opt, i) => (
                    <label key={i} className={`block border p-3 rounded hover:bg-gray-100 cursor-pointer transition-colors ${
                      answers[currentQuestion] === opt ? 'border-blue-500 bg-blue-50' : ''
                    }`}>
                      <input
                        type="radio"
                        name={`question-${currentQuestion}`}
                        value={opt}
                        checked={answers[currentQuestion] === opt}
                        onChange={() => handleAnswerChange(opt)}
                        className="mr-3"
                      />
                      {opt}
                    </label>
                  ))}
                </div>
              )}
              
              {/* Behavioral or Technical Questions */}
              {['behavioral', 'technical'].includes(currentQ?.type) && (
                <div className="mt-4">
                  <div className="flex justify-between items-center mb-3">
                    <h4 className="font-medium">Your Answer</h4>
                    <div className="flex flex-col">
                      <RecognitionModeToggle />
                      <div className="flex gap-2">
                        {/* Text-based response toggle */}
                        <button
                          onClick={() => {
                            stopRecording();
                          }}
                          className={`px-3 py-1 text-sm rounded ${
                            !isListening ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'
                          }`}
                        >
                          Written
                        </button>
                        
                        {/* Speech-based response toggle */}
                        <button
                          onClick={() => {
                            if (isListening) {
                              stopRecording();
                            } else {
                              startRecording();
                            }
                          }}
                          className={`px-3 py-1 text-sm rounded flex items-center ${
                            isListening ? 'bg-red-600 text-white' : 'bg-green-600 text-white'
                          }`}
                        >
                          {isListening ? 
                            <>
                              <FaStop className="mr-1" /> Stop Recording
                            </> : 
                            <>
                              <FaMicrophone className="mr-1" /> Record Answer
                            </>
                          }
                        </button>
                      </div>
                    </div>
                  </div>
                  
                  {/* Text input area (always visible) */}
                  <textarea 
                    rows={6} 
                    className="w-full border rounded p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none" 
                    placeholder="Type your answer or use speech recognition"
                    value={transcript}
                    onChange={(e) => setTranscript(e.target.value)}
                    disabled={isProcessing}
                  ></textarea>
                  
                  {/* Speech recognition status and controls */}
                  {isListening && (
                    <div className="bg-gray-50 p-3 rounded-lg border mt-3">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-red-500 animate-pulse mr-2"></div>
                        <p className="text-gray-700 text-sm flex-1">
                          {`Recording... (${formatRecordingTime(recordingTime)})`}
                        </p>
                        
                        <button 
                          onClick={stopRecording}
                          className="text-xs bg-red-50 text-red-500 px-2 py-1 rounded hover:bg-red-100"
                          disabled={isProcessing}
                        >
                          {isProcessing ? 'Processing...' : 'Stop Recording'}
                        </button>
                      </div>
                      
                      {/* Interim results */}
                      {interimResult && (
                        <div className="mt-2 text-gray-500 italic text-sm">
                          {interimResult}
                        </div>
                      )}
                      
                      {/* Processing indicator */}
                      {isProcessing && (
                        <div className="flex items-center mt-2 text-blue-600 text-xs">
                          <div className="animate-spin h-3 w-3 border-2 border-blue-600 border-t-transparent rounded-full mr-2"></div>
                          Processing your answer... This may take a few moments.
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
              
              {/* Coding Questions */}
              {currentQ?.type === 'coding' && (
                <div className="mt-4">
                  <div className="bg-gray-800 text-white p-2 rounded-t flex justify-between items-center">
                    <div className="flex items-center">
                      <FaCode className="mr-2" />
                      <span>Code Editor (JavaScript)</span>
                    </div>
                    {codeLanguage.toLowerCase() !== 'javascript' && (
                      <span className="text-xs text-yellow-300">Note: Only JavaScript execution is supported</span>
                    )}
                  </div>
                  
                  <CodeEditor
                    language="javascript" // Force JavaScript
                    value={code}
                    onChange={(newValue) => setCode(newValue)}
                    height="400px"
                  />
                  
                  <div className="flex mt-2">
                    <button
                      onClick={runCode}
                      disabled={isRunningCode}
                      className={`px-4 py-2 rounded ${
                        isRunningCode ? 'bg-gray-400' : 'bg-green-600 hover:bg-green-700'
                      } text-white`}
                    >
                      {isRunningCode ? 'Running...' : 'Run Code'}
                    </button>
                  </div>
                  
                  {/* Code output */}
                  {codeOutput && (
                    <div className="mt-4 p-4 bg-gray-800 text-white rounded overflow-auto max-h-60">
                      <div className="text-sm text-gray-400 mb-2">Output:</div>
                      <pre className="whitespace-pre-wrap">{codeOutput}</pre>
                    </div>
                  )}
                </div>
              )}
              
              {/* Navigation buttons */}
              <div className="mt-8 flex justify-between">
                <button
                  onClick={handlePrev}
                  disabled={currentQuestion === 0 || isProcessing}
                  className={`px-4 py-2 rounded ${
                    currentQuestion === 0 || isProcessing ? 'bg-gray-300 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'
                  } text-white`}
                >
                  Previous
                </button>
                
                {currentQuestion < questions.length - 1 ? (
                  <button
                    onClick={handleNext}
                    disabled={isProcessing}
                    className="px-4 py-2 rounded bg-green-600 hover:bg-green-700 text-white"
                  >
                    Next
                  </button>
                ) : (
                  <button
                    onClick={handleSubmit}
                    disabled={isProcessing}
                    className="px-4 py-2 rounded bg-green-600 hover:bg-green-700 text-white"
                  >
                    Submit Interview
                  </button>
                )}
              </div>
            </div>
          ) : (
            <div className="bg-white p-6 rounded-lg shadow text-center">
              <p className="text-gray-600">No questions available for this interview.</p>
              <button 
                onClick={exitInterview}
                className="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
              >
                Return to Dashboard
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
