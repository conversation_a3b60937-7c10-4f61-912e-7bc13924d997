// File: pages/candidate/interview-live.js - Part 1: SETUP AND IMPORTS

import { useEffect, useState, useMemo, useRef, useCallback, forwardRef, useImperativeHandle } from 'react';
import { useRouter } from 'next/router';
import { FaSignOutAlt, FaVideo, FaMicrophone, FaLock, FaVolumeUp, FaStop, FaCircle, FaCode, FaExclamationTriangle, FaEye, FaCog, FaShieldAlt, FaClock, FaCheckCircle } from 'react-icons/fa';
import dynamic from 'next/dynamic';
import axios from 'axios';

// Import our new InputPatternMonitor component
import InputPatternMonitor from '@/components/InputPatternMonitor';

// Dynamically import components
const CodeEditor = dynamic(() => import('@/components/CodeEditor'), {
  ssr: false,
  loading: () => <div className="p-4 bg-gray-100 rounded">Loading code editor...</div>
});

const WebcamFaceDetection = dynamic(() => import('@/components/WebcamFaceDetection'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-64 bg-gray-800 rounded-lg flex items-center justify-center">
      <div className="animate-pulse flex space-x-4 w-full">
        <div className="flex-1 space-y-4 py-1">
          <div className="h-40 bg-gray-400 rounded w-full"></div>
          <div className="h-4 bg-gray-400 rounded w-3/4 mx-auto"></div>
        </div>
      </div>
    </div>
  )
});

// Global escape key blocker and security setup
if (typeof window !== 'undefined') {
  // PERMISSION-AWARE SCREENSHOT DETECTION SYSTEM
  console.log('🛡️ Setting up permission-aware screenshot detection...');
  
  // Global security state
  window.INTERVIEW_SECURITY = {
    keysPressed: new Set(),
    isExiting: false,
    lastVisibilityChange: Date.now(),
    reportedScreenshots: 0,
    // NEW: Track potential screenshot attempts to detect even after permission dialog
    potentialScreenshot: {
      inProgress: false,
      method: null,
      timestamp: null,
      timeoutId: null,
      observer: null,
      permissionDialog: false
    }
  };
  
  // Create a global screenshot violation reporting function with permission awareness
  window.reportScreenshotViolation = function(method, details = {}) {
    console.log(`🚨 SCREENSHOT VIOLATION DETECTED: ${method}`, details);
    
    // Don't report during exit or if we've already reported too many times
    if (window.INTERVIEW_SECURITY.isExiting || window.INTERVIEW_SECURITY.reportedScreenshots >= 10) {
      return;
    }
    
    window.INTERVIEW_SECURITY.reportedScreenshots++;
    
    try {
      // 1. First, try handling at SecurityManager level (most reliable)
      if (window.SecurityManager && window.SecurityManager.instance) {
        window.SecurityManager.instance.handleWarningViolation('screenshot', 
          `Screenshot detected: ${method}`);
        console.log('✓ Violation reported to SecurityManager.instance');
        return;
      }
      
      // 2. Alternative: use global instance reference
      if (window.securityManagerInstance) {
        window.securityManagerInstance.handleWarningViolation('screenshot', 
          `Screenshot detected: ${method}`);
        console.log('✓ Violation reported to securityManagerInstance');
        return;
      }
      
      // 3. Fallback: dispatch custom event (will be caught by event listeners)
      const event = new CustomEvent('security-screenshot-attempt', {
        detail: {
          timestamp: new Date().toISOString(),
          method: method,
          ...details
        }
      });
      window.dispatchEvent(event);
      console.log('✓ security-screenshot-attempt event dispatched');
    } catch (error) {
      console.error('Error reporting screenshot violation:', error);
      
      // 4. Last resort: trigger a global violation event
      try {
        const fallbackEvent = new CustomEvent('security-violation', {
          detail: {
            type: 'screenshot',
            message: `Screenshot detected: ${method}`,
            count: 1,
            totalViolations: 1,
            timestamp: new Date().toISOString(),
            category: 'warning'
          }
        });
        window.dispatchEvent(fallbackEvent);
        console.log('✓ Fallback security-violation event dispatched');
      } catch (e) {
        console.error('Complete failure in violation reporting:', e);
      }
    }
  };

  // NEW: Start tracking potential screenshot with timeout and clipboard monitoring
  window.startPotentialScreenshotTracking = function(method) {
    // Clear any existing attempt first
    window.clearPotentialScreenshotAttempt();
    
    console.log(`🔍 Tracking potential screenshot attempt: ${method}`);
    
    // Start active clipboard monitoring
    const startClipboardMonitoring = () => {
      let checkCount = 0;
      const maxChecks = 6;
      
      const checkClipboard = async () => {
        if (checkCount >= maxChecks || !window.INTERVIEW_SECURITY.potentialScreenshot.inProgress) {
          return; // Stop checking after max attempts or if tracking stopped
        }
        
        checkCount++;
        
        // Try to read clipboard if supported
        if (navigator.clipboard && navigator.clipboard.read) {
          try {
            const clipboardItems = await navigator.clipboard.read();
            
            for (const item of clipboardItems) {
              // Check for image types
              if (item.types.some(type => type.startsWith('image/'))) {
                console.log('🚨 Image found in clipboard after screenshot shortcut');
                window.reportScreenshotViolation(
                  `${method} (clipboard image)`,
                  { reason: 'clipboardImage', attempt: checkCount }
                );
                window.clearPotentialScreenshotAttempt();
                return; // Stop checking after finding image
              }
            }
            
            // Schedule next check
            setTimeout(checkClipboard, 300);
          } catch (error) {
            // Permission errors are expected - just try again
            setTimeout(checkClipboard, 300);
          }
        } else {
          // No clipboard API, try next check
          setTimeout(checkClipboard, 300);
        }
      };
      
      // Start first check immediately
      checkClipboard();
    };
    
    window.INTERVIEW_SECURITY.potentialScreenshot = {
      inProgress: true,
      method: method,
      timestamp: Date.now(),
      // Auto-confirm screenshot after delay even without other evidence
      timeoutId: setTimeout(() => {
        if (window.INTERVIEW_SECURITY.potentialScreenshot.inProgress) {
          console.log('⏱️ Screenshot timeout reached - assuming screenshot was taken');
          window.reportScreenshotViolation(`${method} (timeout confirmation)`, {
            reason: 'timeout',
            elapsedMs: Date.now() - window.INTERVIEW_SECURITY.potentialScreenshot.timestamp
          });
          window.clearPotentialScreenshotAttempt();
        }
      }, 2000) // 2 second timeout
    };
    
    // Start clipboard monitoring
    startClipboardMonitoring();
    
    // Immediately check for secondary indicators
    if (document.visibilityState === 'hidden') {
      console.log('🚨 Page hidden during screenshot attempt');
      
      // Set a timer to check when it becomes visible again
      const hiddenTimestamp = Date.now();
      
      const visibilityHandler = () => {
        if (document.visibilityState === 'visible') {
          const hiddenDuration = Date.now() - hiddenTimestamp;
          
          if (hiddenDuration < 1000) {
            console.log(`🚨 Brief visibility change (${hiddenDuration}ms) confirms screenshot`);
            window.reportScreenshotViolation(`${method} (visibility confirmation)`, {
              reason: 'visibility',
              duration: hiddenDuration
            });
          }
          
          document.removeEventListener('visibilitychange', visibilityHandler);
        }
      };
      
      document.addEventListener('visibilitychange', visibilityHandler);
    }
    
    // ENHANCED: Monitor permission dialog appearance
    const permissionDialogObserver = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          for (const node of mutation.addedNodes) {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Look for dialog characteristics
              const nodeText = node.textContent?.toLowerCase() || '';
              const nodeHTML = node.outerHTML?.toLowerCase() || '';
              
              if ((nodeText.includes('permission') || nodeText.includes('allow') || 
                   nodeText.includes('access') || nodeHTML.includes('dialog') ||
                   node.tagName === 'DIALOG') && 
                  window.INTERVIEW_SECURITY.potentialScreenshot.inProgress) {
                
                console.log('🚨 Permission dialog detected during screenshot attempt');
                
                // Flag as permission dialog
                window.INTERVIEW_SECURITY.potentialScreenshot.permissionDialog = true;
                
                // Set a flag when dialog disappears
                const dialogDismissObserver = new MutationObserver(() => {
                  if (!document.body.contains(node)) {
                    console.log('🚨 Permission dialog dismissed - likely screenshot allowed');
                    
                    // Intensify clipboard checking for the next few seconds
                    let permissionCheckCount = 0;
                    const maxPermissionChecks = 10;
                    
                    const permissionCheckInterval = setInterval(async () => {
                      permissionCheckCount++;
                      
                      if (permissionCheckCount > maxPermissionChecks) {
                        clearInterval(permissionCheckInterval);
                        return;
                      }
                      
                      // Check clipboard aggressively
                      if (navigator.clipboard && navigator.clipboard.read) {
                        try {
                          const items = await navigator.clipboard.read();
                          
                          for (const item of items) {
                            if (item.types.some(type => type.startsWith('image/'))) {
                              console.log('🚨 Image in clipboard after permission dialog - screenshot confirmed');
                              window.reportScreenshotViolation(`${method} (post-permission)`, {
                                reason: 'clipboardImageAfterPermission',
                                attempt: permissionCheckCount
                              });
                              clearInterval(permissionCheckInterval);
                              return;
                            }
                          }
                        } catch (error) {
                          // Permission errors expected
                        }
                      }
                    }, 200); // Check every 200ms
                    
                    dialogDismissObserver.disconnect();
                  }
                });
                
                dialogDismissObserver.observe(document.body, { childList: true, subtree: true });
              }
            }
          }
        }
      }
    });
    
    // Start observing for dialog
    permissionDialogObserver.observe(document.body, { 
      childList: true, 
      subtree: true 
    });
    
    // Store observer in screenshot tracking object to disconnect later
    window.INTERVIEW_SECURITY.potentialScreenshot.observer = permissionDialogObserver;
    
    // Check for fullscreen exit, which often happens with screenshots
    if (!(document.fullscreenElement || 
          document.webkitFullscreenElement || 
          document.mozFullScreenElement || 
          document.msFullscreenElement)) {
      console.log('🚨 Not fullscreen during screenshot attempt');
      window.reportScreenshotViolation(`${method} (fullscreen confirmation)`, {
        reason: 'notFullscreen'
      });
    }
  };

  // ENHANCED: Clear potential screenshot tracking with added cleanup
  window.clearPotentialScreenshotAttempt = function() {
    // Clear timeout
    if (window.INTERVIEW_SECURITY.potentialScreenshot.timeoutId) {
      clearTimeout(window.INTERVIEW_SECURITY.potentialScreenshot.timeoutId);
    }
    
    // Disconnect observer if exists
    if (window.INTERVIEW_SECURITY.potentialScreenshot.observer) {
      window.INTERVIEW_SECURITY.potentialScreenshot.observer.disconnect();
    }
    
    // Reset tracking object
    window.INTERVIEW_SECURITY.potentialScreenshot = {
      inProgress: false,
      method: null,
      timestamp: null,
      timeoutId: null,
      observer: null,
      permissionDialog: false
    };
  };

  // 1. PRIMARY: PERMISSION-AWARE KEY DETECTION SYSTEM
  const setupKeystrokeMonitoring = () => {
    console.log('Setting up permission-aware keystroke monitoring...');
    
    // Define all known screenshot key combinations
    const SCREENSHOT_COMBOS = [
      // Mac screenshot combinations
      { meta: true, shift: true, key: '3' },  // Full screen capture
      { meta: true, shift: true, key: '4' },  // Area selection capture
      { meta: true, shift: true, key: '5' },  // Screenshot menu
      { meta: true, shift: true, key: 'Digit3' },
      { meta: true, shift: true, key: 'Digit4' },
      { meta: true, shift: true, key: 'Digit5' },
      // Windows screenshot combinations
      { key: 'PrintScreen' },  // Full screen to clipboard
      { alt: true, key: 'PrintScreen' },  // Active window
      { windows: true, shift: true, key: 's' },  // Windows Snipping Tool
      { windows: true, shift: true, key: 'S' },  // Windows Snipping Tool (capital S)
      { meta: true, shift: true, key: 's' },  // Alternative way Windows keys are detected
      { meta: true, shift: true, key: 'S' },
      { meta: true, alt: true, key: 'PrtScn' }, // Some Surface devices
      { meta: true, key: 'PrtScn' }  // Some Windows laptops
    ];
    
    // Low-level keydown handler with highest priority capture
    const handleKeyDown = (e) => {
      try {
        // Debug log all keys pressed (helpful for finding new screenshot combinations)
        console.log('Key pressed:', {
          key: e.key,
          code: e.code,
          which: e.which,
          keyCode: e.keyCode,
          meta: e.metaKey,
          ctrl: e.ctrlKey,
          alt: e.altKey,
          shift: e.shiftKey,
          windows: e.metaKey
        });
        
        // DIRECT KEY DETECTION - Mac screenshots (Command+Shift+3/4/5)
        if (e.metaKey && e.shiftKey && ['3', '4', '5'].includes(e.key)) {
          console.log(`🚨 Mac screenshot detected: Command+Shift+${e.key}`);
          
          // Start tracking potential screenshot attempt to detect after permission dialog
          window.startPotentialScreenshotTracking(`Mac screenshot: Command+Shift+${e.key}`);
          
          try {
            // Try to prevent default, but this likely won't work for system shortcuts
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();
          } catch (error) {
            console.log('Failed to prevent screenshot shortcut (expected):', error);
          }
          
          return false;
        }
        
        // DIRECT KEY DETECTION - Windows PrintScreen key
        if (e.key === 'PrintScreen' || e.code === 'PrintScreen' || e.key === 'PrtScn' || e.key === 'PrtSc') {
          console.log('🚨 Windows PrintScreen detected');
          
          // Start tracking potential screenshot attempt
          window.startPotentialScreenshotTracking('Windows PrintScreen key');
          
          try {
            // Try to prevent default
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();
          } catch (error) {
            console.log('Failed to prevent screenshot shortcut (expected):', error);
          }
          
          return false;
        }
        
        // DIRECT KEY DETECTION - Windows Snipping Tool (Win+Shift+S)
        if ((e.metaKey || e.key === 'Meta' || e.key === 'OS') && 
            e.shiftKey && (e.key === 's' || e.key === 'S')) {
          console.log('🚨 Windows Snipping Tool detected: Win+Shift+S');
          
          // Start tracking potential screenshot attempt
          window.startPotentialScreenshotTracking('Windows Snipping Tool: Win+Shift+S');
          
          try {
            // Try to prevent default
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();
          } catch (error) {
            console.log('Failed to prevent screenshot shortcut (expected):', error);
          }
          
          return false;
        }
        
        // COMBO TRACKING - Update pressed keys set for multi-key detection
        
        // Add the current key and any modifier keys
        window.INTERVIEW_SECURITY.keysPressed.add(e.key);
        if (e.code) window.INTERVIEW_SECURITY.keysPressed.add(e.code);
        
        if (e.metaKey) {
          window.INTERVIEW_SECURITY.keysPressed.add('Meta');
          window.INTERVIEW_SECURITY.keysPressed.add('Command');
          window.INTERVIEW_SECURITY.keysPressed.add('Windows');
        }
        
        if (e.shiftKey) window.INTERVIEW_SECURITY.keysPressed.add('Shift');
        if (e.altKey) window.INTERVIEW_SECURITY.keysPressed.add('Alt');
        if (e.ctrlKey) window.INTERVIEW_SECURITY.keysPressed.add('Control');
        
        // Debug log all currently pressed keys
        console.log('All keys currently pressed:', Array.from(window.INTERVIEW_SECURITY.keysPressed));
        
        // Check every possible screenshot combination against our tracked keys
        for (const combo of SCREENSHOT_COMBOS) {
          let match = true;
          
          // Check for required modifiers
          if (combo.meta && !(window.INTERVIEW_SECURITY.keysPressed.has('Meta') || 
                              window.INTERVIEW_SECURITY.keysPressed.has('Command'))) {
            match = false;
          }
          
          if (combo.windows && !(window.INTERVIEW_SECURITY.keysPressed.has('Meta') || 
                                window.INTERVIEW_SECURITY.keysPressed.has('Windows') || 
                                window.INTERVIEW_SECURITY.keysPressed.has('OS'))) {
            match = false;
          }
          
          if (combo.shift && !window.INTERVIEW_SECURITY.keysPressed.has('Shift')) match = false;
          if (combo.alt && !window.INTERVIEW_SECURITY.keysPressed.has('Alt')) match = false;
          if (combo.ctrl && !window.INTERVIEW_SECURITY.keysPressed.has('Control')) match = false;
          
          // Check for required key
          if (!window.INTERVIEW_SECURITY.keysPressed.has(combo.key)) match = false;
          
          if (match) {
            // Build human-readable description
            let description = '';
            if (combo.meta || combo.windows) description += 'Command+';
            if (combo.shift) description += 'Shift+';
            if (combo.alt) description += 'Alt+';
            if (combo.ctrl) description += 'Ctrl+';
            description += combo.key;
            
            console.log(`🚨 Screenshot combo detected: ${description}`);
            
            // Start tracking this as a potential screenshot
            window.startPotentialScreenshotTracking(`Key combination: ${description}`);
            
            try {
              // Try to prevent default
              e.preventDefault();
              e.stopPropagation();
              e.stopImmediatePropagation();
            } catch (error) {
              console.log('Failed to prevent screenshot shortcut (expected):', error);
            }
            
            return false;
          }
        }
      } catch (error) {
        console.error('Error in key handler:', error);
      }
    };
    
    // Keyup handler to clean up tracked keys
    const handleKeyUp = (e) => {
      try {
        // Remove the released key
        window.INTERVIEW_SECURITY.keysPressed.delete(e.key);
        if (e.code) window.INTERVIEW_SECURITY.keysPressed.delete(e.code);
        
        // Remove modifier keys if released
        if (e.key === 'Meta' || e.key === 'Command' || e.key === 'OS') {
          window.INTERVIEW_SECURITY.keysPressed.delete('Meta');
          window.INTERVIEW_SECURITY.keysPressed.delete('Command');
          window.INTERVIEW_SECURITY.keysPressed.delete('Windows');
          window.INTERVIEW_SECURITY.keysPressed.delete('OS');
        }
        if (e.key === 'Shift') window.INTERVIEW_SECURITY.keysPressed.delete('Shift');
        if (e.key === 'Alt') window.INTERVIEW_SECURITY.keysPressed.delete('Alt');
        if (e.key === 'Control') window.INTERVIEW_SECURITY.keysPressed.delete('Control');
      } catch (error) {
        console.error('Error in keyup handler:', error);
      }
    };
    
    // Apply handlers at multiple levels to ensure capture
    for (const target of [window, document, document.documentElement, document.body]) {
      if (!target) continue;
      
      // Keydown with highest priority capture and preventDefault capability
      target.addEventListener('keydown', handleKeyDown, { 
        capture: true, 
        passive: false  // Required to use preventDefault
      });
      
      // Keyup with high priority
      target.addEventListener('keyup', handleKeyUp, { 
        capture: true 
      });
    }
    
    // Reset keys when window loses focus
    window.addEventListener('blur', () => {
      window.INTERVIEW_SECURITY.keysPressed.clear();
    }, { capture: true });
    
    // Reset keys when window gains focus
    window.addEventListener('focus', () => {
      window.INTERVIEW_SECURITY.keysPressed.clear();
    }, { capture: true });
    
    // Also add handlers on DOMContentLoaded for redundancy
    document.addEventListener('DOMContentLoaded', () => {
      document.addEventListener('keydown', handleKeyDown, { 
        capture: true, 
        passive: false
      });
      document.addEventListener('keyup', handleKeyUp, { 
        capture: true
      });
    });
    
    console.log('✅ Permission-aware keystroke monitoring initialized');
  };


//================================================================================================================
//END OF PART 1
//================================================================================================================


// File: pages/candidate/interview-live.js - Part 2:PERMISSION AND DIALOG DETECTION
// 2. SECONDARY: PERMISSION AND DIALOG DETECTION
  const setupPermissionMonitoring = () => {
    console.log('Setting up permission and dialog monitoring...');
    
    try {
      // Monitor for permission changes
      if (navigator.permissions) {
        // Try to monitor permissions if available
        try {
          navigator.permissions.query({ name: 'camera' }).then(permissionStatus => {
            permissionStatus.onchange = () => {
              console.log(`📸 Camera permission status changed to: ${permissionStatus.state}`);
              
              // If we had a potential screenshot in progress, this might be the permission grant
              if (window.INTERVIEW_SECURITY.potentialScreenshot.inProgress) {
                console.log('🚨 Permission change detected during potential screenshot attempt');
                window.reportScreenshotViolation(
                  `${window.INTERVIEW_SECURITY.potentialScreenshot.method} (after permission)`,
                  { reason: 'permissionChange' }
                );
              }
            };
          }).catch(err => {
            console.log('Permission query not supported for camera');
          });
        } catch (error) {
          console.log('Camera permission monitoring failed:', error);
        }
        
        // Try for screen capture
        try {
          navigator.permissions.query({ name: 'display-capture' }).then(permissionStatus => {
            permissionStatus.onchange = () => {
              console.log(`🖥️ Screen capture permission changed to: ${permissionStatus.state}`);
              
              if (permissionStatus.state === 'granted' && 
                  window.INTERVIEW_SECURITY.potentialScreenshot.inProgress) {
                console.log('🚨 Screen capture permission granted during potential screenshot attempt');
                window.reportScreenshotViolation(
                  `${window.INTERVIEW_SECURITY.potentialScreenshot.method} (display-capture permission)`,
                  { reason: 'displayCapturePermission' }
                );
              }
            };
          }).catch(err => {
            console.log('Permission query not supported for display-capture');
          });
        } catch (error) {
          console.log('Screen capture permission monitoring failed:', error);
        }
      }
      
      // Monitor for blur events during potential screenshot
      window.addEventListener('blur', () => {
        // If there's a potential screenshot attempt and we lose focus,
        // this could be the system dialog showing
        if (window.INTERVIEW_SECURITY.potentialScreenshot.inProgress) {
          console.log('🚨 Window blur during potential screenshot - likely permission dialog');
          
          // Start a timer to check for when focus returns
          const blurTime = Date.now();
          
          // One-time focus handler
          const onFocusReturn = () => {
            const blurDuration = Date.now() - blurTime;
            
            // If the blur was brief (less than 5 seconds), it was likely a quick permission dialog
            if (blurDuration < 5000) {
              console.log(`🚨 Brief blur (${blurDuration}ms) during screenshot attempt - permission dialog confirmed`);
              window.reportScreenshotViolation(
                `${window.INTERVIEW_SECURITY.potentialScreenshot.method} (after dialog)`,
                { reason: 'blurAndFocus', duration: blurDuration }
              );
            }
            
            // Remove this one-time handler
            window.removeEventListener('focus', onFocusReturn);
          };
          
          // Add the one-time focus handler
          window.addEventListener('focus', onFocusReturn);
        }
      }, { capture: true });
      
      // MUTATION OBSERVER: Monitor for permission dialogs in the DOM
      const domObserver = new MutationObserver((mutations) => {
        // Only check if there's a potential screenshot in progress
        if (!window.INTERVIEW_SECURITY.potentialScreenshot.inProgress) return;
        
        for (const mutation of mutations) {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            for (const node of mutation.addedNodes) {
              if (node.nodeType === Node.ELEMENT_NODE) {
                // Check for permission dialog characteristics
                const isDialog = 
                  node.tagName === 'DIALOG' || 
                  node.tagName === 'DIV' && (
                    node.role === 'dialog' ||
                    node.getAttribute('role') === 'dialog' ||
                    node.classList.contains('dialog') ||
                    node.id.includes('dialog') ||
                    node.id.includes('permission') ||
                    node.classList.contains('permission') ||
                    node.style.zIndex > 9000
                  );
                
                if (isDialog) {
                  console.log('🚨 Dialog detected during screenshot attempt', node);
                  window.reportScreenshotViolation(
                    `${window.INTERVIEW_SECURITY.potentialScreenshot.method} (dialog detected)`,
                    { reason: 'dialogDetected' }
                  );
                }
              }
            }
          }
        }
      });
      
      // Start observing the entire document
      domObserver.observe(document, {
        childList: true,
        subtree: true,
        attributes: true
      });
      
      console.log('✅ Permission and dialog monitoring initialized');
    } catch (error) {
      console.error('Error setting up permission monitoring:', error);
    }
  };

  // 3. TERTIARY: CLIPBOARD AND IMAGE MONITORING
  const setupClipboardMonitoring = () => {
    console.log('Setting up clipboard and image monitoring...');
    
    try {
      // Check clipboard for images periodically during potential screenshot
      const startClipboardChecking = () => {
        if (!window.INTERVIEW_SECURITY.potentialScreenshot.inProgress) return;
        
        let checkCount = 0;
        const maxChecks = 10;
        const checkInterval = 300; // 300ms between checks
        
        const checkClipboard = async () => {
          try {
            if (checkCount >= maxChecks || !window.INTERVIEW_SECURITY.potentialScreenshot.inProgress) {
              return; // Stop checking after max attempts or if tracking stopped
            }
            
            checkCount++;
            
            // Try to read clipboard if supported
            if (navigator.clipboard && navigator.clipboard.read) {
              try {
                const clipboardItems = await navigator.clipboard.read();
                
                for (const item of clipboardItems) {
                  // Check for image types
                  if (item.types.includes('image/png') || 
                      item.types.includes('image/jpeg') || 
                      item.types.includes('image/webp')) {
                    
                    console.log('🚨 Image found in clipboard after screenshot shortcut');
                    window.reportScreenshotViolation(
                      `${window.INTERVIEW_SECURITY.potentialScreenshot.method} (clipboard image)`,
                      { reason: 'clipboardImage', attempt: checkCount }
                    );
                    return; // Stop checking after finding image
                  }
                }
                
                // Schedule next check
                setTimeout(checkClipboard, checkInterval);
              } catch (error) {
                // Permission errors are expected - just try again
                setTimeout(checkClipboard, checkInterval);
              }
            }
          } catch (error) {
            console.log('Error checking clipboard:', error);
          }
        };
        
        // Start the first check
        checkClipboard();
      };
      
      // Monitor paste events for images
      document.addEventListener('paste', (e) => {
        try {
          if (e.clipboardData && e.clipboardData.items) {
            for (let i = 0; i < e.clipboardData.items.length; i++) {
              const item = e.clipboardData.items[i];
              
              if (item.type.indexOf('image') !== -1) {
                console.log('🚨 Image pasted - possible screenshot');
                window.reportScreenshotViolation('Image paste detected', {
                  reason: 'imagePaste',
                  type: item.type
                });
                break;
              }
            }
          }
        } catch (error) {
          console.log('Error handling paste event:', error);
        }
      }, { capture: true });
      
      // Start clipboard checking when potential screenshot starts
      const originalStartTracking = window.startPotentialScreenshotTracking;
      window.startPotentialScreenshotTracking = function(method) {
        originalStartTracking.call(this, method);
        startClipboardChecking();
      };
      
      // Monitor for empty copy events (often occurs with screenshots)
      document.addEventListener('copy', (e) => {
        try {
          const selection = window.getSelection();
          if (!selection || selection.toString().trim() === '') {
            console.log('🚨 Copy event with no selection - possible screenshot');
            
            if (window.INTERVIEW_SECURITY.potentialScreenshot.inProgress) {
              window.reportScreenshotViolation(
                `${window.INTERVIEW_SECURITY.potentialScreenshot.method} (empty copy)`,
                { reason: 'emptyCopy' }
              );
            } else {
              window.reportScreenshotViolation('Empty copy event', {
                reason: 'emptyCopy'
              });
            }
          }
        } catch (error) {
          console.log('Error handling copy event:', error);
        }
      }, { capture: true });
      
      console.log('✅ Clipboard and image monitoring initialized');
    } catch (error) {
      console.error('Error setting up clipboard monitoring:', error);
    }
  };
  
  // 4. QUATERNARY: VISIBILITY AND FULLSCREEN MONITORING
  const setupVisibilityMonitoring = () => {
    console.log('Setting up visibility and fullscreen monitoring...');
    
    try {
      // Monitor for brief visibility changes (happens during screenshots)
      document.addEventListener('visibilitychange', () => {
        const now = Date.now();
        
        if (document.visibilityState === 'hidden') {
          window.INTERVIEW_SECURITY.lastVisibilityChange = now;
        } else if (document.visibilityState === 'visible') {
          const timeDiff = now - window.INTERVIEW_SECURITY.lastVisibilityChange;
          
          // Screenshot tools often cause very brief visibility changes (5-300ms)
          if (timeDiff > 5 && timeDiff < 300) {
            console.log(`🚨 Brief visibility change (${timeDiff}ms) - possible screenshot`);
            window.reportScreenshotViolation(`Brief visibility change: ${timeDiff}ms`, {
              reason: 'visibilityChange',
              duration: timeDiff
            });
          }
        }
      }, { capture: true });
      
      // Monitor for fullscreen changes during potential screenshot
      const fullscreenEvents = [
        'fullscreenchange',
        'webkitfullscreenchange',
        'mozfullscreenchange',
        'MSFullscreenChange'
      ];
      
      const handleFullscreenChange = () => {
        const isFullscreen = !!(
          document.fullscreenElement ||
          document.webkitFullscreenElement ||
          document.mozFullScreenElement ||
          document.msFullscreenElement
        );
        
        // If exiting fullscreen and potential screenshot in progress
        if (!isFullscreen && window.INTERVIEW_SECURITY.potentialScreenshot.inProgress) {
          console.log('🚨 Fullscreen exit during potential screenshot attempt');
          window.reportScreenshotViolation(
            `${window.INTERVIEW_SECURITY.potentialScreenshot.method} (fullscreen exit)`,
            { reason: 'fullscreenExit' }
          );
        }
      };
      
      // Add listeners for all fullscreen events
      for (const event of fullscreenEvents) {
        document.addEventListener(event, handleFullscreenChange, { capture: true });
      }
      
      console.log('✅ Visibility and fullscreen monitoring initialized');
    } catch (error) {
      console.error('Error setting up visibility monitoring:', error);
    }
  };
  
  // 5. QUINARY: CANVAS OPERATION MONITORING
  const setupCanvasMonitoring = () => {
    console.log('Setting up canvas operation monitoring...');
    
    try {
      // Store original canvas methods
      const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
      const originalToBlob = HTMLCanvasElement.prototype.toBlob;
      const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
      
      // Helper to check if face detection (avoid false positives)
      const isFaceDetectionCanvas = (canvas) => {
        try {
          if (!canvas) return false;
          
          // Check data attributes
          if (canvas.hasAttribute && canvas.hasAttribute('data-face-detection')) return true;
          
          // Check parent element (webcam container)
          if (canvas.parentElement && (
              canvas.parentElement.classList.contains('webcam-container') ||
              canvas.parentElement.classList.contains('face-detection') ||
              canvas.parentElement.querySelector('video')
            )) {
            return true;
          }
          
          // Check stack trace
          const stack = new Error().stack || '';
          return stack.includes('face-api') || 
                 stack.includes('faceapi') || 
                 stack.includes('WebcamFaceDetection') || 
                 stack.includes('face-detection');
        } catch (e) {
          // When in doubt, don't flag as screenshot
          return true;
        }
      };
      
      // Override toDataURL
      HTMLCanvasElement.prototype.toDataURL = function(...args) {
        try {
          if (!isFaceDetectionCanvas(this)) {
            console.log('🚨 Canvas.toDataURL called - possible screenshot');
            window.reportScreenshotViolation('Canvas method: toDataURL()', {
              reason: 'canvasToDataURL'
            });
          }
        } catch (e) {
          console.error('Error in toDataURL override:', e);
        }
        
        return originalToDataURL.apply(this, args);
      };
      
      // Override toBlob
      HTMLCanvasElement.prototype.toBlob = function(...args) {
        try {
          if (!isFaceDetectionCanvas(this)) {
            console.log('🚨 Canvas.toBlob called - possible screenshot');
            window.reportScreenshotViolation('Canvas method: toBlob()', {
              reason: 'canvasToBlob'
            });
          }
        } catch (e) {
          console.error('Error in toBlob override:', e);
        }
        
        return originalToBlob.apply(this, args);
      };
      
      // Override getImageData carefully to avoid false positives
      CanvasRenderingContext2D.prototype.getImageData = function(...args) {
        try {
          if (this.canvas && !isFaceDetectionCanvas(this.canvas)) {
            // Additional check against stack trace to avoid false positives
            const stack = new Error().stack || '';
            if (!stack.includes('face-api') && 
                !stack.includes('WebcamFaceDetection')) {
              
              console.log('🚨 Canvas.getImageData called - possible screenshot');
              window.reportScreenshotViolation('Canvas method: getImageData()', {
                reason: 'canvasGetImageData'
              });
            }
          }
        } catch (e) {
          console.error('Error in getImageData override:', e);
        }
        
        return originalGetImageData.apply(this, args);
      };
      
      console.log('✅ Canvas operation monitoring initialized');
    } catch (error) {
      console.error('Error setting up canvas monitoring:', error);
    }
  };

  // Initialize all screenshot detection methods
  try {
    setupKeystrokeMonitoring();
    setupPermissionMonitoring();
    setupClipboardMonitoring();
    setupVisibilityMonitoring();
    setupCanvasMonitoring();
    
    // Re-initialize on document ready to ensure everything is active
    document.addEventListener('DOMContentLoaded', () => {
      setupKeystrokeMonitoring();
      setupPermissionMonitoring();
      setupClipboardMonitoring();
      setupVisibilityMonitoring();
      setupCanvasMonitoring();
    });
    
    console.log('🛡️ Permission-aware screenshot detection fully active');
  } catch (error) {
    console.error('Error initializing screenshot detection:', error);
  }

  // INITIALIZE OTHER VARIABLES
  let lastVisibilityChangeTime = Date.now();
  let exitingRef = { current: false }; // For global access
  let lastBlurTime = 0;

  // Enhanced keyboard blocker - specifically targets screenshot keys
  const globalKeyboardBlocker = (e) => {
    // Block Escape key COMPLETELY - prevent all default behavior
    if (e.key === 'Escape' || e.keyCode === 27) {
      console.log('🚫 Escape key blocked - preventing fullscreen exit');
      e.preventDefault();
      e.stopPropagation();
      e.stopImmediatePropagation();
      return false;
    }
    
    // SCREENSHOT KEY BLOCKING - Special priority
    
    // Block PrintScreen key directly
    if (e.key === 'PrintScreen' || e.code === 'PrintScreen' || e.key === 'PrtScn' || e.key === 'PrtSc') {
      console.log('🚫 PrintScreen key blocked');
      e.preventDefault();
      e.stopPropagation();
      e.stopImmediatePropagation();
      
      // Report violation
      window.reportScreenshotViolation('PrintScreen key blocked', {
        reason: 'keyboardShortcut'
      });
      return false;
    }
    
    // Block Mac screenshot combinations (Command+Shift+3/4/5)
    if (e.metaKey && e.shiftKey && ['3', '4', '5'].includes(e.key)) {
      console.log(`🚫 Mac screenshot key blocked: Command+Shift+${e.key}`);
      e.preventDefault();
      e.stopPropagation();
      e.stopImmediatePropagation();
      
      // Report violation and start tracking
      window.startPotentialScreenshotTracking(`Command+Shift+${e.key} key combination`);
      return false;
    }
    
    // Block Windows snipping tool (Win+Shift+S)
    if ((e.metaKey || e.key === 'Meta' || e.key === 'OS') && e.shiftKey && (e.key === 's' || e.key === 'S')) {
      console.log('🚫 Windows+Shift+S key blocked');
      e.preventDefault();
      e.stopPropagation();
      e.stopImmediatePropagation();
      
      // Report violation and start tracking
      window.startPotentialScreenshotTracking('Windows+Shift+S key combination');
      return false;
    }
    
    // Block copy, paste, and cut shortcuts
    if (e.ctrlKey) {
      if (e.key === 'c' || e.key === 'C') {
        console.log('🚫 Copy blocked');
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
      if (e.key === 'v' || e.key === 'V') {
        console.log('🚫 Paste blocked');
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
      if (e.key === 'x' || e.key === 'X') {
        console.log('🚫 Cut blocked');
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
      // Block select all
      if (e.key === 'a' || e.key === 'A') {
        console.log('🚫 Select All blocked');
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
      // Block developer tools
      if (e.key === 'F12' || (e.shiftKey && e.key === 'I')) {
        console.log('🚫 Developer tools blocked');
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
      // Block refresh
      if (e.key === 'r' || e.key === 'R') {
        console.log('🚫 Refresh blocked');
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
      // Block new tab/window
      if (e.key === 't' || e.key === 'T' || e.key === 'n' || e.key === 'N') {
        console.log('🚫 New tab/window blocked');
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
    }
    
    // Block F12 (developer tools)
    if (e.key === 'F12') {
      console.log('🚫 F12 blocked');
      e.preventDefault();
      e.stopPropagation();
      return false;
    }
    
    // Block Alt+Tab (task switching)
    if (e.altKey && e.key === 'Tab') {
      console.log('🚫 Alt+Tab blocked');
      e.preventDefault();
      e.stopPropagation();
      return false;
    }
    
    // Block Windows key on PC
    if (e.key === 'Meta' || e.key === 'Super') {
      console.log('🚫 Windows/Super key blocked');
      e.preventDefault();
      e.stopPropagation();
      return false;
    }
  };
  
  // Add MULTIPLE listeners with HIGHEST priority to ensure blocking
  document.addEventListener('keydown', globalKeyboardBlocker, { 
    capture: true, 
    passive: false 
  });
  
  window.addEventListener('keydown', globalKeyboardBlocker, { 
    capture: true, 
    passive: false 
  });
  
  // Add event listener to body
  document.body.addEventListener('keydown', globalKeyboardBlocker, { 
    capture: true, 
    passive: false 
  });
  
  // Also block on all input elements to prevent bubbling
  document.addEventListener('DOMContentLoaded', () => {
    document.addEventListener('keydown', globalKeyboardBlocker, { 
      capture: true, 
      passive: false 
    });
  });
  
  // Block context menu (right-click)
  document.addEventListener('contextmenu', (e) => {
    console.log('🚫 Context menu blocked');
    e.preventDefault();
    e.stopPropagation();
  }, { capture: true, passive: false });
  
  // Block text selection globally
  document.addEventListener('selectstart', (e) => {
    // Allow selection only in specific elements with data-allow-select
    if (!e.target.hasAttribute('data-allow-select')) {
      console.log('🚫 Text selection blocked');
      e.preventDefault();
    }
  }, { capture: true, passive: false });
  
  // Block drag and drop
  document.addEventListener('dragstart', (e) => {
    console.log('🚫 Drag blocked');
    e.preventDefault();
  }, { capture: true, passive: false });
  
  // ADDITIONAL: Use MutationObserver to ensure handlers stay attached
  const observer = new MutationObserver(() => {
    // Re-attach handlers if they get removed
    if (!document.hasEventListener) {
      document.addEventListener('keydown', globalKeyboardBlocker, { 
        capture: true, 
        passive: false 
      });
    }
  });
  
  observer.observe(document, { 
    childList: true, 
    subtree: true 
  });
  
  // Disable developer console
  console.log('%c🛡️ Security Active: ESC key disabled during interview', 'color: red; font-size: 20px; font-weight: bold;');
  
  // Clean way to integrate with SecurityManager
  window.connectToSecurityManager = function(componentRef) {
    if (!componentRef) return false;
    
    console.log('🔄 Attempting to connect to SecurityManager...');
    
    // First try to find existing security manager instance
    if (window.SecurityManager && window.SecurityManager.instance) {
      console.log('✓ Found SecurityManager.instance - connecting');
      componentRef.current = window.SecurityManager.instance;
      window.securityManagerInstance = window.SecurityManager.instance;
      return true;
    }
    
    if (window.securityManagerInstance) {
      console.log('✓ Found securityManagerInstance - connecting');
      componentRef.current = window.securityManagerInstance;
      return true;
    }
    
    console.log('⚠️ SecurityManager instance not found yet');
    return false;
  };
  
  // Set up security-screenshot-attempt event handling
  window.addEventListener('security-screenshot-attempt', function(e) {
    console.log('📢 Screenshot attempt event received:', e.detail);
    
    try {
      // Forward to SecurityManager if not already handled
      if (window.SecurityManager && window.SecurityManager.instance) {
        window.SecurityManager.instance.handleWarningViolation('screenshot', 
          'Screenshot detected: ' + (e.detail.method || 'Unknown method'));
      } else if (window.securityManagerInstance) {
        window.securityManagerInstance.handleWarningViolation('screenshot', 
          'Screenshot detected: ' + (e.detail.method || 'Unknown method'));
      }
    } catch (error) {
      console.error('Error forwarding screenshot violation to SecurityManager:', error);
    }
  });
  
  // Track last blur event to detect Alt+Tab
  window.addEventListener('blur', () => {
    lastBlurTime = Date.now();
    if (!exitingRef.current && window.securityManagerInstance) {
      console.log('🚨 Window blur detected - possible window switch/Alt+Tab');
    }
  });
  
  // Track focus to detect tab switching
  window.addEventListener('focus', () => {
    const timeBlurred = Date.now() - lastBlurTime;
    if (timeBlurred > 100 && timeBlurred < 1000 && !exitingRef.current && window.securityManagerInstance) {
      console.log('🚨 Quick window switch detected - possible Alt+Tab');
      
      // Trigger tab switch violation event
      const event = new CustomEvent('security-tab-switch', {
        detail: { 
          timestamp: new Date().toISOString(),
          timeBlurred
        }
      });
      window.dispatchEvent(event);
    }
  });
  
  // DUAL MONITOR DETECTION
  window.addEventListener('DOMContentLoaded', () => {
    try {
      // Check for dual monitors using screen properties
      const detectDualMonitors = () => {
        console.log('📝 Checking for multiple monitors...');
        const screenWidth = window.screen.width;
        const availWidth = window.screen.availWidth;
        const screenLeft = window.screenLeft || window.screenX || 0;
        
        // Check if window position suggests multiple monitors
        if (screenLeft < 0 || screenLeft > screenWidth) {
          console.log('🚨 Window positioned on secondary monitor detected');
          
          // Trigger dual monitor violation event
          const event = new CustomEvent('security-dualmonitor-detected', {
            detail: { 
              timestamp: new Date().toISOString(),
              screenWidth,
              availWidth,
              screenLeft
            }
          });
          window.dispatchEvent(event);
          return true;
        }
        
        // Check if availWidth is significantly larger than normal
        if (availWidth > 3000) {
          console.log('🚨 Unusually large screen width detected - possible multiple monitors');
          
          // Trigger dual monitor violation event
          const event = new CustomEvent('security-dualmonitor-detected', {
            detail: { 
              timestamp: new Date().toISOString(),
              screenWidth,
              availWidth,
              screenLeft
            }
          });
          window.dispatchEvent(event);
          return true;
        }
        
        return false;
      };
      
      // Run detection immediately
      detectDualMonitors();
      
      // And periodically
      setInterval(detectDualMonitors, 10000);
    } catch (error) {
      console.error('Error detecting dual monitors:', error);
    }
  });
  
  // Detect devtools opening (basic detection)
  setInterval(() => {
    const start = new Date();
    debugger;
    const duration = new Date() - start;
    if (duration > 100) {
      console.log('🚨 Developer tools detected!');
      // Report violation if SecurityManager is available
      if (window.SecurityManager && window.securityManagerInstance) {
        window.securityManagerInstance.handleViolation('devTools', 'Developer tools detected');
      }
    }
  }, 2000);
}


//================================================================================================================
//END OF PART 2
//================================================================================================================



// File: pages/candidate/interview-live.js - Part 3: COMPONENT SETUP

export default function InterviewLive() {
  const router = useRouter();
  const { id } = router.query;

  // Security-related refs
  const appIdRef = useRef(null);
  const securityManagerRef = useRef(null);
  const questionsLoadedRef = useRef(false);
  const securityInitializedRef = useRef(false);
  const webcamRef = useRef(null);
  const exitingRef = useRef(false); // Added for exit management
  const isComponentMountedRef = useRef(true); // Add component mounted ref
  
  // Core interview states
  const [questions, setQuestions] = useState([]);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [loadingStatus, setLoadingStatus] = useState('Initializing...');
  
  // Response time tracking
  const [questionStartTime, setQuestionStartTime] = useState(null);
  const [responseTimeData, setResponseTimeData] = useState({});
  const [suspiciousResponseTimes, setSuspiciousResponseTimes] = useState([]);
  const [firstInteractionTime, setFirstInteractionTime] = useState(null);
  const [hasInteractedWithQuestion, setHasInteractedWithQuestion] = useState(false);
  
  // Response time thresholds
  const RESPONSE_TIME_THRESHOLDS = {
    mcq: 3,
    technical: 20,
    behavioral: 15,
    coding: 30
  };
  
  // Security states
  const [securityInitialized, setSecurityInitialized] = useState(false);
  const [securityReady, setSecurityReady] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [securityViolations, setSecurityViolations] = useState({
    fullscreenExit: 0,
    tabSwitch: 0,
    windowBlur: 0,
    devTools: 0,
    keyboardShortcut: 0,
    contextMenu: 0,
    copyPaste: 0,
    multipleFaces: 0,
    faceNotVisible: 0,
    lookingAway: 0,
    suspiciousResponseTime: 0,
    dualMonitor: 0,
    screenSharing: 0,
    screenshot: 0, // Added for screenshot detection
    differentPerson: 0, // Added for face identity verification
    suspiciousTyping: 0, // Added for typing pattern monitoring
    contentPasted: 0 // Added for paste detection
  });
  const [totalViolations, setTotalViolations] = useState(0);
  const [showWarning, setShowWarning] = useState(false);
  const [warningMessage, setWarningMessage] = useState('');
  const [warningCount, setWarningCount] = useState(0);
  const [disqualified, setDisqualified] = useState(false);
  const [disqualificationReason, setDisqualificationReason] = useState('');
  const [showFinalWarning, setShowFinalWarning] = useState(false);
  
  // Camera states
  const [showWebcam, setShowWebcam] = useState(false);
  const [cameraPermission, setCameraPermission] = useState('prompt');
  const [microphonePermission, setMicrophonePermission] = useState('prompt');
  const [cameraStatus, setCameraStatus] = useState({
    isDetecting: false,
    facesDetected: 0,
    lookingAway: false,
    faceConfidence: 0,
    faceQuality: 'unknown',
    differentPerson: false // Added for identity verification
  });
  
  // Audio states
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [interimResult, setInterimResult] = useState('');
  const [useOfflineMode, setUseOfflineMode] = useState(false);
  
  // Code editor states
  const [code, setCode] = useState('');
  const [codeLanguage, setCodeLanguage] = useState('javascript');
  const [codeOutput, setCodeOutput] = useState('');
  const [isRunningCode, setIsRunningCode] = useState(false);
  
  // Text-to-speech state
  const [isSpeaking, setIsSpeaking] = useState(false);
  
  // NEW: Input pattern monitoring state
  const [showInputMonitor, setShowInputMonitor] = useState(false);
  
  // Recording refs
  const audioStreamRef = useRef(null);
  const audioMediaRecorderRef = useRef(null);
  const recordedAudioChunksRef = useRef([]);
  const recordingTimerRef = useRef(null);
  const speechRecognitionTimeoutRef = useRef(null);
  
  // Permission states
  const [showPermissionModal, setShowPermissionModal] = useState(false);
  const [permissionRequested, setPermissionRequested] = useState(false);

  // Add violation tracking even before SecurityManager is active
  const [preSecurityViolations, setPreSecurityViolations] = useState([]);
  
  // FIRST: Define violation tracking function before it's used
  const trackPreSecurityViolation = useCallback((type, message) => {
    const violation = {
      type,
      message,
      timestamp: new Date().toISOString(),
      questionIndex: currentQuestion
    };
    
    setPreSecurityViolations(prev => [...prev, violation]);
    console.log('📊 Pre-security violation tracked:', violation);
  }, [currentQuestion]);

  // Turn off camera function - COMPLETELY FIXED VERSION
  const turnOffCamera = useCallback(() => {
    try {
      console.log('🔴 Turning off camera...');
      
      // Method 1: Stop webcam component if it has a stop method
      if (webcamRef.current) {
        if (typeof webcamRef.current.stopCamera === 'function') {
          webcamRef.current.stopCamera();
          console.log('✅ Webcam component stopped via stopCamera method');
        }
        
        // Try to access the video element directly from the component
        if (typeof webcamRef.current.video !== 'undefined' && webcamRef.current.video) {
          const videoElement = webcamRef.current.video;
          if (videoElement.srcObject) {
            const stream = videoElement.srcObject;
            stream.getTracks().forEach(track => {
              track.stop();
              console.log('✅ Video track stopped:', track.kind);
            });
            videoElement.srcObject = null;
            console.log('✅ Video srcObject cleared');
          }
        }
        
        // Also try querySelector as fallback
        const videoElement = webcamRef.current.querySelector?.('video');
        if (videoElement && videoElement.srcObject) {
          const stream = videoElement.srcObject;
          stream.getTracks().forEach(track => {
            track.stop();
            console.log('✅ Video track stopped (querySelector):', track.kind);
          });
          videoElement.srcObject = null;
        }
      }
      
      // Method 2: Stop all media tracks from audioStreamRef
      if (audioStreamRef.current) {
        audioStreamRef.current.getTracks().forEach(track => {
          track.stop();
          console.log('✅ Audio track stopped:', track.kind);
        });
        audioStreamRef.current = null;
      }
      
      // Method 3: Stop media recorder if active
      if (audioMediaRecorderRef.current && audioMediaRecorderRef.current.state !== 'inactive') {
        try {
          audioMediaRecorderRef.current.stop();
          console.log('✅ Media recorder stopped');
        } catch (error) {
          console.warn('Warning stopping media recorder:', error);
        }
      }
      
      // Method 4: Stop speech recognition
      if (window.currentRecognition) {
        try {
          window.currentRecognition.stop();
          window.currentRecognition = null;
          console.log('✅ Speech recognition stopped');
        } catch (error) {
          console.warn('Warning stopping speech recognition:', error);
        }
      }
      
      // Method 5: Force stop all tracks from all media streams
      if (typeof navigator !== 'undefined' && navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
        navigator.mediaDevices.enumerateDevices().then(devices => {
          console.log('📊 Available devices after cleanup:', devices.filter(d => d.kind.includes('video')).length);
        }).catch(() => {
          // Ignore errors
        });
      }
      
      // Method 6: Update camera status to reflect off state
      setCameraStatus({
        isDetecting: false,
        facesDetected: 0,
        lookingAway: false,
        faceConfidence: 0,
        faceQuality: 'camera_stopped'
      });
      
      // Method 7: Hide webcam component
      setShowWebcam(false);
      
      // Method 8: Set a timeout to ensure cleanup
      setTimeout(() => {
        console.log('🕐 Delayed camera cleanup check...');
        // Final cleanup attempt
        if (webcamRef.current && webcamRef.current.stopCamera) {
          webcamRef.current.stopCamera();
        }
      }, 1000);
      
      console.log('✅ Camera turned off successfully');
    } catch (error) {
      console.error('❌ Error turning off camera:', error);
    }
  }, []);

  // Send tracked violations to SecurityManager when it becomes active
  useEffect(() => {
    if (securityManagerRef.current && securityManagerRef.current.isInterviewActive && preSecurityViolations.length > 0) {
      console.log('📤 Sending pre-security violations to SecurityManager...');
      
      preSecurityViolations.forEach(violation => {
        try {
          if (typeof securityManagerRef.current.handleViolation === 'function') {
            securityManagerRef.current.handleViolation(violation.type, `${violation.message} (Tracked before security active)`);
          } else if (typeof securityManagerRef.current.handleSecurityViolation === 'function') {
            securityManagerRef.current.handleSecurityViolation(violation.type, `${violation.message} (Tracked before security active)`);
          } else if (typeof securityManagerRef.current.reportViolation === 'function') {
            securityManagerRef.current.reportViolation(violation.type, `${violation.message} (Tracked before security active)`);
          }
        } catch (error) {
          console.error('Error sending pre-security violation:', error);
        }
      });
      
      // Clear the pre-security violations
      setPreSecurityViolations([]);
    }
  }, [securityManagerRef.current?.isInterviewActive, preSecurityViolations]);

  // ADDED: Enhanced warning system with required acknowledgment
  const showRequiredAcknowledgmentWarning = useCallback((title, message, type = 'warning') => {
    try {
      // Create warning overlay
      const warningOverlay = document.createElement('div');
      warningOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0,0,0,0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10002;
      `;
      
      // Create warning content
      const warningContent = document.createElement('div');
      warningContent.style.cssText = `
        background: white;
        border-radius: 8px;
        width: 90%;
        max-width: 500px;
        padding: 30px;
        text-align: center;
        border-top: 5px solid ${type === 'error' ? '#ff3b30' : type === 'warning' ? '#ff9500' : '#34c759'};
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
      `;
      
      warningContent.innerHTML = `
        <div style="font-size: 32px; margin-bottom: 15px;">${
          type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️'
        }</div>
        <h2 style="font-size: 20px; margin-bottom: 15px; color: ${
          type === 'error' ? '#ff3b30' : type === 'warning' ? '#ff9500' : '#34c759'
        };">${title}</h2>
        <p style="margin-bottom: 25px; font-size: 16px; color: #444;">${message}</p>
        <button id="warning-acknowledge-btn" style="
          background: ${type === 'error' ? '#ff3b30' : type === 'warning' ? '#ff9500' : '#34c759'};
          color: white;
          border: none;
          padding: 12px 30px;
          border-radius: 6px;
          font-size: 16px;
          font-weight: bold;
          cursor: pointer;
        ">Acknowledge</button>
      `;
      
      warningOverlay.appendChild(warningContent);
      document.body.appendChild(warningOverlay);
      
      // Add click handler
      return new Promise((resolve) => {
        document.getElementById('warning-acknowledge-btn').addEventListener('click', () => {
          warningOverlay.remove();
          resolve(true);
        });
      });
    } catch (error) {
      console.error('Error showing warning:', error);
      return Promise.resolve(false);
    }
  }, []);

  // ENHANCED: Screenshot detection event handling
  useEffect(() => {
    // Custom handler for screenshot attempts with better detection and feedback
    const handleScreenshotAttempt = async (event) => {
      console.log('🚨 SCREENSHOT DETECTED:', event.detail);
      
      // First, attempt to block any default behavior
      try {
        if (event.preventDefault) {
          event.preventDefault();
        }
      } catch (e) {
        // Ignore errors - this is just a precaution
      }
      
      // Report violation to SecurityManager
      if (securityManagerRef.current && securityManagerRef.current.isInterviewActive) {
        try {
          // Try all possible function names the SecurityManager might use
          if (typeof securityManagerRef.current.handleWarningViolation === 'function') {
            securityManagerRef.current.handleWarningViolation('screenshot', 
              `Screenshot detected: ${event.detail.method || 'Unknown method'}`);
          } else if (typeof securityManagerRef.current.handleViolation === 'function') {
            securityManagerRef.current.handleViolation('screenshot', 
              `Screenshot detected: ${event.detail.method || 'Unknown method'}`);
          } else if (typeof securityManagerRef.current.handleSecurityViolation === 'function') {
            securityManagerRef.current.handleSecurityViolation('screenshot', 
              `Screenshot detected: ${event.detail.method || 'Unknown method'}`);
          } else if (typeof securityManagerRef.current.reportViolation === 'function') {
            securityManagerRef.current.reportViolation('screenshot', 
              `Screenshot detected: ${event.detail.method || 'Unknown method'}`);
          }
          
          console.log('✓ Screenshot violation reported to SecurityManager');
        } catch (error) {
          console.error('Error reporting screenshot violation to SecurityManager:', error);
        }
      } else {
        // Track violation even if SecurityManager isn't active yet
        trackPreSecurityViolation('screenshot', 
          `Screenshot detected via ${event.detail.method || 'Unknown method'} (question ${currentQuestion + 1})`);
      }
      
      // Update local state to reflect violation
      setSecurityViolations(prev => ({
        ...prev,
        screenshot: (prev.screenshot || 0) + 1
      }));
      
      // Update total violations
      setTotalViolations(prev => prev + 1);
      
      // Show warning message at top of screen
      setWarningMessage('SECURITY VIOLATION: Screenshot detected. This incident has been recorded.');
      setShowWarning(true);
      
      // Hide warning after a few seconds
      setTimeout(() => {
        setShowWarning(false);
      }, 5000);
      
      // Show prominent blocking overlay that requires acknowledgment
      await showRequiredAcknowledgmentWarning(
        'SCREENSHOT DETECTED',
        'Taking screenshots during the interview is strictly prohibited. This security violation has been recorded. ' +
        'If you reach 3 security violations, you will be automatically disqualified from the interview process.',
        'error'
      );
      
      // Log additional details for debugging
      try {
        const violationDetails = {
          timestamp: new Date().toISOString(),
          method: event.detail.method || 'Unknown',
          questionIndex: currentQuestion,
          userAgent: navigator.userAgent,
          totalViolations: totalViolations + 1
        };
        
        console.log('📝 Screenshot violation details:', violationDetails);
      } catch (error) {
        console.error('Error logging screenshot violation details:', error);
      }
    };
    
    // Register event listener for screenshot attempts
    window.addEventListener('security-screenshot-attempt', handleScreenshotAttempt, { capture: true });
    
    // Also listen for direct violation events as backup
    window.addEventListener('security-violation', (event) => {
      if (event.detail && event.detail.type === 'screenshot') {
        handleScreenshotAttempt({
          detail: {
            method: event.detail.message || 'Via security-violation event',
            timestamp: event.detail.timestamp
          }
        });
      }
    }, { capture: true });
    
    // Direct key event monitoring for screenshots (extra layer)
    const handleKeyDown = (e) => {
      // Mac screenshot combos (Command+Shift+3/4/5)
      if (e.metaKey && e.shiftKey && ['3', '4', '5'].includes(e.key)) {
        console.log(`🚨 Direct key event: Mac screenshot (Command+Shift+${e.key})`);
        
        handleScreenshotAttempt({
          detail: {
            method: `Direct key event: Command+Shift+${e.key}`,
            timestamp: new Date().toISOString()
          }
        });
        
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
      
      // Windows PrintScreen key
      if (e.key === 'PrintScreen' || e.code === 'PrintScreen' || 
          e.key === 'PrtScn' || e.key === 'PrtSc') {
        console.log('🚨 Direct key event: PrintScreen key');
        
        handleScreenshotAttempt({
          detail: {
            method: 'Direct key event: PrintScreen key',
            timestamp: new Date().toISOString()
          }
        });
        
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
      
      // Windows Snipping Tool (Win+Shift+S)
      if ((e.metaKey || e.key === 'Meta' || e.key === 'OS') && 
          e.shiftKey && (e.key === 's' || e.key === 'S')) {
        console.log('🚨 Direct key event: Windows Snipping Tool (Win+Shift+S)');
        
        handleScreenshotAttempt({
          detail: {
            method: 'Direct key event: Windows Snipping Tool (Win+Shift+S)',
            timestamp: new Date().toISOString()
          }
        });
        
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
    };
    
    // Add direct key event listener with highest priority
    document.addEventListener('keydown', handleKeyDown, { capture: true, passive: false });
    window.addEventListener('keydown', handleKeyDown, { capture: true, passive: false });
    
    // Cleanup function
    return () => {
      window.removeEventListener('security-screenshot-attempt', handleScreenshotAttempt, { capture: true });
      window.removeEventListener('security-violation', handleScreenshotAttempt, { capture: true });
      document.removeEventListener('keydown', handleKeyDown, { capture: true });
      window.removeEventListener('keydown', handleKeyDown, { capture: true });
    };
  }, [showRequiredAcknowledgmentWarning, currentQuestion, totalViolations, trackPreSecurityViolation]);

  // Handle screen sharing attempts
  useEffect(() => {
    const handleScreenShareAttempt = async (event) => {
      console.log('🚨 Screen sharing attempt detected:', event.detail);
      
      // Report violation
      if (securityManagerRef.current && securityManagerRef.current.isInterviewActive) {
        try {
          if (typeof securityManagerRef.current.handleViolation === 'function') {
            securityManagerRef.current.handleViolation('screenSharing', 'Screen sharing attempt detected');
          } else if (typeof securityManagerRef.current.handleSecurityViolation === 'function') {
            securityManagerRef.current.handleSecurityViolation('screenSharing', 'Screen sharing attempt detected');
          } else if (typeof securityManagerRef.current.reportViolation === 'function') {
            securityManagerRef.current.reportViolation('screenSharing', 'Screen sharing attempt detected');
          }
        } catch (error) {
          console.error('Error reporting screen sharing violation:', error);
        }
      } else {
        // Track violation even if SecurityManager isn't active yet
        trackPreSecurityViolation('screenSharing', 'Screen sharing attempt detected (question ' + (currentQuestion + 1) + ')');
      }
      
      // Update local state
      setSecurityViolations(prev => ({
        ...prev,
        screenSharing: (prev.screenSharing || 0) + 1
      }));
      
      // Update total violations
      setTotalViolations(prev => prev + 1);
      
      // Show warning
      setWarningMessage('SECURITY VIOLATION: Screen sharing detected. This incident has been recorded.');
      setShowWarning(true);
      
      // Hide warning after a few seconds
      setTimeout(() => {
        setShowWarning(false);
      }, 5000);
      
      // Show warning with required acknowledgment
      await showRequiredAcknowledgmentWarning(
        'Screen Sharing Blocked',
        'Screen sharing is not allowed during the interview. This violation has been recorded.',
        'error'
      );
    };
    
    // Register event listener
    window.addEventListener('security-screenshare-attempt', handleScreenShareAttempt);
    
    return () => {
      window.removeEventListener('security-screenshare-attempt', handleScreenShareAttempt);
    };
  }, [showRequiredAcknowledgmentWarning, currentQuestion, trackPreSecurityViolation]);

  // Handle dual monitor detection
  useEffect(() => {
    const handleDualMonitorDetection = async (event) => {
      console.log('🚨 Dual monitor detected:', event.detail);
      
      // Report violation
      if (securityManagerRef.current && securityManagerRef.current.isInterviewActive) {
        try {
          if (typeof securityManagerRef.current.handleViolation === 'function') {
            securityManagerRef.current.handleViolation('dualMonitor', 'Dual monitor setup detected');
          } else if (typeof securityManagerRef.current.handleSecurityViolation === 'function') {
            securityManagerRef.current.handleSecurityViolation('dualMonitor', 'Dual monitor setup detected');
          } else if (typeof securityManagerRef.current.reportViolation === 'function') {
            securityManagerRef.current.reportViolation('dualMonitor', 'Dual monitor setup detected');
          }
        } catch (error) {
          console.error('Error reporting dual monitor violation:', error);
        }
      } else {
        // Track violation even if SecurityManager isn't active yet
        trackPreSecurityViolation('dualMonitor', 'Dual monitor setup detected (question ' + (currentQuestion + 1) + ')');
      }
      
      // Update local state
      setSecurityViolations(prev => ({
        ...prev,
        dualMonitor: (prev.dualMonitor || 0) + 1
      }));
      
      // Update total violations
      setTotalViolations(prev => prev + 1);
      
      // Create blocking overlay that prevents interview continuation
      const blockingOverlay = document.createElement('div');
      blockingOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(255,0,0,0.95);
        color: white;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        font-size: 20px;
        text-align: center;
      `;
      blockingOverlay.innerHTML = `
        <div style="font-size: 32px; margin-bottom: 20px;">⚠️ MULTIPLE MONITORS DETECTED ⚠️</div>
        <div style="margin-bottom: 30px; font-size: 18px;">
          The interview requires using only a single monitor.<br/>
          Please disconnect or disable your secondary monitors to continue.
        </div>
        <button id="check-monitors-btn" style="
          font-size: 18px;
          padding: 15px 30px;
          background: white;
          color: red;
          border: none;
          border-radius: 8px;
          cursor: pointer;
          font-weight: bold;
          margin-bottom: 20px;
        ">I've Disconnected Secondary Monitors</button>
        <div style="font-size: 14px; margin-top: 5px;">
          The interview is paused until you use a single monitor.
        </div>
      `;
      
      document.body.appendChild(blockingOverlay);
      
      // Add click handler
      document.getElementById('check-monitors-btn').addEventListener('click', () => {
        // Check if still using multiple monitors
        const screenWidth = window.screen.width;
        const availWidth = window.screen.availWidth;
        const screenLeft = window.screenLeft || window.screenX || 0;
        
        if (screenLeft < 0 || screenLeft > screenWidth || availWidth > 3000) {
          // Still using multiple monitors - show warning
          showRequiredAcknowledgmentWarning(
            'Multiple Monitors Still Detected',
            'Please disconnect or disable all secondary monitors to continue the interview.',
            'error'
          );
        } else {
          // Single monitor - remove overlay
          blockingOverlay.remove();
          
          // Show confirmation
          showRequiredAcknowledgmentWarning(
            'Single Monitor Confirmed',
            'Thank you for switching to a single monitor. You may continue with the interview.',
            'success'
          );
        }
      });
    };
    
    // Handle tab switching
    const handleTabSwitch = async (event) => {
      console.log('🚨 Tab/window switching detected:', event.detail);
      
      // Report violation
      if (securityManagerRef.current && securityManagerRef.current.isInterviewActive) {
        try {
          if (typeof securityManagerRef.current.handleViolation === 'function') {
            securityManagerRef.current.handleViolation('tabSwitch', 'Tab/window switching detected');
          } else if (typeof securityManagerRef.current.handleSecurityViolation === 'function') {
            securityManagerRef.current.handleSecurityViolation('tabSwitch', 'Tab/window switching detected');
          } else if (typeof securityManagerRef.current.reportViolation === 'function') {
            securityManagerRef.current.reportViolation('tabSwitch', 'Tab/window switching detected');
          }
        } catch (error) {
          console.error('Error reporting tab switch violation:', error);
        }
      } else {
        // Track violation even if SecurityManager isn't active yet
        trackPreSecurityViolation('tabSwitch', 'Tab/window switching detected (question ' + (currentQuestion + 1) + ')');
      }
      
      // Update local state
      setSecurityViolations(prev => ({
        ...prev,
        tabSwitch: (prev.tabSwitch || 0) + 1
      }));
      
      // Update total violations
      setTotalViolations(prev => prev + 1);
      
      // Show warning
      setWarningMessage('SECURITY VIOLATION: Window/tab switching detected. This incident has been recorded.');
      setShowWarning(true);
      
      // Hide warning after a few seconds
      setTimeout(() => {
        setShowWarning(false);
      }, 5000);
      
      // Show warning with required acknowledgment
      await showRequiredAcknowledgmentWarning(
        'Window Switching Detected',
        'Switching to other windows or applications during the interview is not allowed. This violation has been recorded.',
        'error'
      );
    };
    
    // Add event listeners for all security-related events
    window.addEventListener('security-dualmonitor-detected', handleDualMonitorDetection);
    window.addEventListener('security-tab-switch', handleTabSwitch);
    
    return () => {
      window.removeEventListener('security-dualmonitor-detected', handleDualMonitorDetection);
      window.removeEventListener('security-tab-switch', handleTabSwitch);
    };
  }, [showRequiredAcknowledgmentWarning, currentQuestion, trackPreSecurityViolation]);



//================================================================================================================
//END OF PART 3
//================================================================================================================




// File: pages/candidate/interview-live.js - Part 4:Cleanup on component unmount
// Cleanup on component unmount
  useEffect(() => {
    return () => {
      console.log('🧹 Component unmounting - cleaning up camera...');
      isComponentMountedRef.current = false;
      turnOffCamera();
      
      // Flag exit to prevent violations during unmount
      if (window.INTERVIEW_SECURITY) {
        window.INTERVIEW_SECURITY.isExiting = true;
      }
    };
  }, [turnOffCamera]);

  // Handle before unload to prevent violations during exit
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (securityManagerRef.current) {
        securityManagerRef.current.setExiting(true);
      }
      exitingRef.current = true;
      
      // Flag exit in global security object
      if (window.INTERVIEW_SECURITY) {
        window.INTERVIEW_SECURITY.isExiting = true;
      }
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('unload', handleBeforeUnload);
    window.addEventListener('pagehide', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('unload', handleBeforeUnload);
      window.removeEventListener('pagehide', handleBeforeUnload);
    };
  }, []);

  // Response time analysis functions
  const checkResponseTime = useCallback((questionIndex, responseTime, questionType) => {
    const threshold = RESPONSE_TIME_THRESHOLDS[questionType] || 10;
    
    if (responseTime < threshold) {
      const suspiciousResponse = {
        questionIndex,
        questionType,
        responseTime,
        threshold,
        timestamp: new Date().toISOString()
      };
      
      setSuspiciousResponseTimes(prev => [...prev, suspiciousResponse]);
      
      if (securityManagerRef.current && securityManagerRef.current.isInterviewActive) {
        try {
          securityManagerRef.current.handleAnalyticsViolation(
            'suspiciousResponseTime',
            `Answered ${questionType} question in ${responseTime}s (expected minimum: ${threshold}s)`
          );
        } catch (error) {
          console.error('Error reporting violation to SecurityManager:', error);
        }
      }
      
      return true;
    }
    
    return false;
  }, []);

  // MOVED UP: Define trackFirstInteraction before it's used
  const trackFirstInteraction = useCallback(() => {
    if (!hasInteractedWithQuestion && questionStartTime) {
      const interactionTime = Date.now();
      setFirstInteractionTime(interactionTime);
      setHasInteractedWithQuestion(true);
      
      const timeToFirstInteraction = (interactionTime - questionStartTime) / 1000;
      
      console.log(`First interaction after ${timeToFirstInteraction} seconds`);
      
      setResponseTimeData(prev => ({
        ...prev,
        [currentQuestion]: {
          ...prev[currentQuestion],
          timeToFirstInteraction
        }
      }));
    }
  }, [hasInteractedWithQuestion, questionStartTime, currentQuestion]);

  const recordResponseTime = useCallback((questionIndex, questionType) => {
    if (!questionStartTime) return null;
    
    const endTime = Date.now();
    const responseTime = (endTime - questionStartTime) / 1000;
    
    const responseData = {
      questionIndex,
      questionType,
      startTime: questionStartTime,
      endTime,
      responseTime,
      timeToFirstInteraction: firstInteractionTime ? (firstInteractionTime - questionStartTime) / 1000 : null,
      timestamp: new Date().toISOString()
    };
    
    setResponseTimeData(prev => ({
      ...prev,
      [questionIndex]: responseData
    }));
    
    const isSuspicious = checkResponseTime(questionIndex, responseTime, questionType);
    
    return { responseData, isSuspicious };
  }, [questionStartTime, firstInteractionTime, checkResponseTime]);

  // ADDED: Handle camera update from WebcamFaceDetection component
// ENHANCED: Handle camera update from WebcamFaceDetection component with hand tracking
const handleCameraUpdate = useCallback((status) => {
  try {
    console.log('📸 Camera status update with hand data:', status);
    setCameraStatus(status);
    
    // Report face detection violations to security manager
    if (securityManagerRef.current && securityManagerRef.current.isInterviewActive) {
      const { 
        facesDetected, 
        lookingAway, 
        faceQuality, 
        differentPerson, 
        faceDescriptor, 
        faceConfidence,
        // NEW: Hand detection data
        handsDetected,
        handsRaised,
        suspiciousHandGesture,
        consecutiveHandsRaised,
        consecutiveHandsOutOfFrame
      } = status;
      
      // Pass comprehensive data to SecurityManager for analysis
      securityManagerRef.current.handleFaceDetection({
        // Face data
        facesDetected,
        lookingAway,
        faceQuality,
        differentPerson,
        faceDescriptor,
        faceConfidence,
        // NEW: Hand data
        handsDetected: handsDetected || 0,
        handsRaised: handsRaised || false,
        suspiciousHandGesture: suspiciousHandGesture || false,
        consecutiveHandsRaised: consecutiveHandsRaised || 0,
        consecutiveHandsOutOfFrame: consecutiveHandsOutOfFrame || 0
      });
    }
  } catch (error) {
    console.error('Error handling camera update:', error);
  }
}, []);



  // ADDED: Handle answer changes
  const handleAnswerChange = useCallback((answer) => {
    try {
      setAnswers(prev => ({
        ...prev,
        [currentQuestion]: answer
      }));
      
      // Track first interaction
      trackFirstInteraction();
    } catch (error) {
      console.error('Error handling answer change:', error);
    }
  }, [currentQuestion, trackFirstInteraction]);

  // ADDED: Fullscreen management functions
  const isDocumentFullScreen = () => {
    return !!(
      document.fullscreenElement ||
      document.webkitFullscreenElement ||
      document.mozFullScreenElement ||
      document.msFullscreenElement
    );
  };

  const requestFullscreen = async () => {
    try {
      console.log('🖥️ Requesting fullscreen...');
      const elem = document.documentElement;
      
      if (elem.requestFullscreen) {
        await elem.requestFullscreen();
      } else if (elem.mozRequestFullScreen) {
        await elem.mozRequestFullScreen();
      } else if (elem.webkitRequestFullscreen) {
        await elem.webkitRequestFullscreen();
      } else if (elem.msRequestFullscreen) {
        await elem.msRequestFullscreen();
      }
    } catch (error) {
      console.error('Fullscreen request failed:', error);
    }
  };

  const exitFullscreen = async () => {
    try {
      console.log('🖥️ Exiting fullscreen...');
      if (document.exitFullscreen) {
        await document.exitFullscreen();
      } else if (document.mozCancelFullScreen) {
        await document.mozCancelFullScreen();
      } else if (document.webkitExitFullscreen) {
        await document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        await document.msExitFullscreen();
      }
    } catch (error) {
      console.error('Exit fullscreen failed:', error);
    }
  };

  // ENHANCED FULLSCREEN PROTECTION: Improved detection and enforcement
  useEffect(() => {
    let blockingOverlay = null;
    
    const handleFullscreenChange = () => {
      const isFullscreen = isDocumentFullScreen();
      setIsFullScreen(isFullscreen);
      
      // If we exit fullscreen during interview (questions loaded)
      if (!isFullscreen && questions.length > 0 && !exitingRef.current) {
        console.log('🚨 Fullscreen exit detected during interview');
        
        // Show blocking overlay that REQUIRES user to click to re-enter fullscreen
        if (!blockingOverlay) {
          blockingOverlay = document.createElement('div');
          blockingOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(255,0,0,0.95);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            font-size: 20px;
            text-align: center;
          `;
          blockingOverlay.innerHTML = `
            <div style="font-size: 32px; margin-bottom: 20px;">⚠️ FULLSCREEN REQUIRED ⚠️</div>
            <div style="margin-bottom: 30px; font-size: 18px;">
              You have exited fullscreen mode.<br/>
              This is a security violation and has been recorded.
            </div>
            <button id="return-fullscreen-btn" style="
              font-size: 18px;
              padding: 15px 30px;
              background: white;
              color: red;
              border: none;
              border-radius: 8px;
              cursor: pointer;
              font-weight: bold;
            ">Click Here to Return to Fullscreen</button>
            <div style="font-size: 14px; margin-top: 20px;">
              The interview is paused until you return to fullscreen.
            </div>
          `;
          
          document.body.appendChild(blockingOverlay);
          
          // Add click handler
          document.getElementById('return-fullscreen-btn').addEventListener('click', async () => {
            try {
              const elem = document.documentElement;
              if (elem.requestFullscreen) {
                await elem.requestFullscreen();
              } else if (elem.mozRequestFullScreen) {
                await elem.mozRequestFullScreen();
              } else if (elem.webkitRequestFullscreen) {
                await elem.webkitRequestFullscreen();
              } else if (elem.msRequestFullscreen) {
                await elem.msRequestFullscreen();
              }
            } catch (error) {
              console.error('Fullscreen request failed:', error);
              alert('Please manually enter fullscreen mode to continue the interview.');
            }
          });
        }
        
        // Track violation
        if (securityManagerRef.current && securityManagerRef.current.isInterviewActive) {
          try {
            if (typeof securityManagerRef.current.handleViolation === 'function') {
              securityManagerRef.current.handleViolation('fullscreenExit', 'Exited fullscreen during interview');
            } else if (typeof securityManagerRef.current.handleSecurityViolation === 'function') {
              securityManagerRef.current.handleSecurityViolation('fullscreenExit', 'Exited fullscreen during interview');
            } else if (typeof securityManagerRef.current.reportViolation === 'function') {
              securityManagerRef.current.reportViolation('fullscreenExit', 'Exited fullscreen during interview');
            }
          } catch (error) {
            console.error('Error reporting fullscreen violation:', error);
          }
        } else {
          // Track violation even if SecurityManager isn't active yet
          trackPreSecurityViolation('fullscreenExit', 'Exited fullscreen during interview (question ' + (currentQuestion + 1) + ')');
        }
      } else if (isFullscreen && blockingOverlay) {
        // Remove overlay when fullscreen is restored
        blockingOverlay.remove();
        blockingOverlay = null;
      }
    };
    
    // Listen for fullscreen changes
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);
    
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
      if (blockingOverlay) {
        blockingOverlay.remove();
      }
    };
  }, [questions.length, trackPreSecurityViolation, currentQuestion]);

  // ADDED: Initialize security states based on fullscreen
  useEffect(() => {
    const isFullscreen = isDocumentFullScreen();
    setIsFullScreen(isFullscreen);
  }, []);

  // COMPLETELY FIXED: Don't check permissions on mount - they're already granted
  useEffect(() => {
    // REMOVED: Don't check permissions again
    console.log('🔍 Permissions already granted from interview-setup, skipping check');
  }, []);
  
  // COMPLETELY FIXED: Remove permission checking function
  const checkPermissions = () => {
    // REMOVED: Don't check permissions - they should already be granted
    console.log('🔍 Permissions already granted from interview-setup');
  };

  // COMPLETELY FIXED: Remove permission request - use existing permissions
  const requestPermissions = async () => {
    // REMOVED: Don't request permissions again
    console.error('❌ This should not be called - permissions already granted');
  };

  // NEW: Initialize webcam with already granted permissions
  const initializeWebcamWithExistingPermissions = useCallback(async () => {
    try {
      console.log('🎥 Initializing webcam with existing permissions...');
      
      // Permissions are already granted from interview-setup
      // Just start showing the webcam
      setCameraPermission('granted');
      setMicrophonePermission('granted');
      
      // Wait a moment then show webcam
      setTimeout(() => {
        console.log('✅ Starting webcam with existing permissions');
        setShowWebcam(true);
      }, 1000);
      
    } catch (error) {
      console.error('❌ Error initializing webcam with existing permissions:', error);
      setError(`Webcam initialization error: ${error.message}`);
    }
  }, []);

  // Initialize webcam when component mounts (permissions already granted)
  useEffect(() => {
    // Delay to ensure security system is ready
    const timer = setTimeout(() => {
      initializeWebcamWithExistingPermissions();
    }, 2000);
    
    return () => clearTimeout(timer);
  }, [initializeWebcamWithExistingPermissions]);

  // SIMPLE FIX: Force fetch questions after 3 seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!questions.length && !questionsLoadedRef.current && securityReady && isFullScreen) {
        console.log('🚨 FORCED: Fetching questions after timeout (fullscreen confirmed)...');
        questionsLoadedRef.current = true;
        
        // Call fetchQuestions directly here (simplified version)
        const fetchQuestionsNow = async () => {
          try {
            setLoadingStatus('Requesting questions from AI providers...');
            
            const response = await axios.post('/api/interview/generate-questions', {
              applicationId: id
             //interviewLength: 20  // optional - defaults to 20
            });
            
            if (response.data?.questions) {
              const validQuestions = response.data.questions.filter(q => 
                q && typeof q === 'object' && q.question && 
                ['mcq', 'technical', 'behavioral', 'coding'].includes(q.type)
              );
              
              console.log(`✅ Successfully loaded ${validQuestions.length} questions`);
              setQuestions(validQuestions);
              setLoading(false);
              setQuestionStartTime(Date.now());
              
              // Start security monitoring
              if (securityManagerRef.current && !securityManagerRef.current.isInterviewActive) {
                setTimeout(() => {
                  console.log('🚀 Starting SecurityManager monitoring');
                  securityManagerRef.current.startInterview();
                }, 1000);
              }
            } else {
              throw new Error('No valid questions received');
            }
          } catch (error) {
            console.error('Error fetching questions:', error);
            setError('Failed to load questions. Please refresh the page.');
            setLoading(false);
          }
        };
        
        fetchQuestionsNow();
      }
    }, 3000); // Check every 3 seconds
    
    return () => clearTimeout(timer);
  }, [id, securityReady, questions.length, isFullScreen]);

  // FULLSCREEN PROMPT: Show fullscreen prompt before starting interview
  useEffect(() => {
    const promptTimer = setTimeout(() => {
      if (securityReady && !isFullScreen && !questionsLoadedRef.current) {
        console.log('🖥️ Prompting for fullscreen...');
        
        // Create fullscreen prompt
        const createFullscreenPrompt = () => {
          const prompt = document.createElement('div');
          prompt.id = 'fullscreen-request-prompt';
          prompt.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,255,0.95);
            color: white;
            padding: 30px 40px;
            border-radius: 10px;
            font-size: 18px;
            text-align: center;
            z-index: 10000;
            box-shadow: 0 4px 20px rgba(0,0,0,0.5);
          `;
          prompt.innerHTML = `
            <div style="font-size: 24px; margin-bottom: 10px;">🎯 Interview Ready!</div>
            <div style="margin: 20px 0;">
              Click below to enter fullscreen mode and begin your interview
            </div>
            <button id="enter-fullscreen-btn" style="
              font-size: 16px;
              padding: 12px 24px;
              background: white;
              color: blue;
              border: none;
              border-radius: 6px;
              cursor: pointer;
              font-weight: bold;
            ">Enter Fullscreen & Start Interview</button>
            <div style="font-size: 14px; margin-top: 15px; color: #E0E0E0;">
              This is required for interview security
            </div>
          `;
          
          document.body.appendChild(prompt);
          
          // Add click handler
          document.getElementById('enter-fullscreen-btn').addEventListener('click', async () => {
            try {
              const elem = document.documentElement;
              if (elem.requestFullscreen) {
                await elem.requestFullscreen();
              } else if (elem.mozRequestFullScreen) {
                await elem.mozRequestFullScreen();
              } else if (elem.webkitRequestFullscreen) {
                await elem.webkitRequestFullscreen();
              } else if (elem.msRequestFullscreen) {
                await elem.msRequestFullscreen();
              }
              
              // Remove prompt after fullscreen
              prompt.remove();
            } catch (error) {
              console.error('Fullscreen request failed:', error);
              alert('Please allow fullscreen access to continue the interview.');
            }
          });
        };
        
        createFullscreenPrompt();
      }
    }, 2000);
    
    return () => clearTimeout(promptTimer);
  }, [securityReady, isFullScreen]);



//================================================================================================================
//END OF PART 4
//================================================================================================================



// File: pages/candidate/interview-live.js - Part 5: Security Manager Setup (COMPLETE)

  // Function to create a global click handler with better debugging
  const createGlobalClickHandler = (id, callback) => {
    // Remove any existing handlers with this ID
    if (window[`global_handler_${id}`]) {
      delete window[`global_handler_${id}`];
    }
    
    // Create new handler with debugging
    window[`global_handler_${id}`] = () => {
      try {
        console.log(`🔘 Global handler ${id} called`);
        const result = callback();
        console.log(`✅ Global handler ${id} completed:`, result);
        return result;
      } catch (error) {
        console.error(`❌ Error in global handler ${id}:`, error);
        return false;
      }
    };
    
    return `window.global_handler_${id}()`;
  };

  // ENHANCED: Universal modal function with better z-index and click handling
  const showUniversalModal = (options) => {
    const {
      title = 'Notice',
      message = '',
      type = 'warning',
      buttonText = 'OK',
      onConfirm = () => {},
      autoCloseDelay = 30000
    } = options;
    
    console.log('🪟 Creating universal modal:', { title, type });
    
    // Create unique ID for this modal
    const modalId = `modal_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
    
    // Remove any existing modals first
    document.querySelectorAll('.universal-modal-overlay').forEach(el => {
      console.log('🗑️ Removing existing modal');
      el.remove();
    });
    
    // Create new modal with maximum z-index
    const overlay = document.createElement('div');
    overlay.className = 'universal-modal-overlay';
    overlay.id = `overlay_${modalId}`;
    overlay.setAttribute('role', 'dialog');
    overlay.setAttribute('aria-modal', 'true');
    overlay.setAttribute('aria-labelledby', `title_${modalId}`);
    
    // ENHANCED: Use maximum possible z-index and ensure it's always on top
    overlay.style.cssText = `
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      background: rgba(0,0,0,0.8) !important;
      display: flex !important;
      justify-content: center !important;
      align-items: center !important;
      z-index: 2147483647 !important;
      pointer-events: all !important;
      user-select: none !important;
    `;
    
    // Colors based on type
    const colors = {
      warning: '#ff9500',
      error: '#ff3b30',
      info: '#007aff',
      success: '#34c759'
    };
    
    const color = colors[type] || colors.warning;
    
    // Create modal content with better styling
    const content = document.createElement('div');
    content.className = 'universal-modal-content';
    content.style.cssText = `
      background: white !important;
      border-radius: 12px !important;
      width: 90% !important;
      max-width: 500px !important;
      padding: 40px !important;
      text-align: center !important;
      box-shadow: 0 20px 40px rgba(0,0,0,0.5) !important;
      border-top: 8px solid ${color} !important;
      position: relative !important;
      pointer-events: all !important;
      user-select: text !important;
      z-index: 2147483647 !important;
    `;
    
    // Create confirm function
    const confirmAction = () => {
      try {
        console.log('🎯 Modal confirm action triggered');
        if (overlay && overlay.parentNode) {
          overlay.remove();
          console.log('🗑️ Modal removed successfully');
        }
        onConfirm();
        return true;
      } catch (error) {
        console.error('❌ Error in confirm action:', error);
        return false;
      }
    };
    
    // Create unique button ID
    const buttonId = `btn_${modalId}`;
    const closeId = `close_${modalId}`;
    
    content.innerHTML = `
      <div style="font-size: 48px; margin-bottom: 20px; user-select: none;">
        ${type === 'warning' || type === 'error' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️'}
      </div>
      <h2 id="title_${modalId}" style="font-size: 24px; margin-bottom: 20px; color: ${color}; user-select: text;">
        ${title}
      </h2>
      <div style="margin-bottom: 30px; font-size: 16px; color: #333; line-height: 1.5; user-select: text;">
        ${message}
      </div>
      <button 
        id="${buttonId}"
        style="
          background: ${color} !important;
          color: white !important;
          border: none !important;
          padding: 15px 40px !important;
          border-radius: 8px !important;
          font-size: 18px !important;
          font-weight: bold !important;
          cursor: pointer !important;
          min-width: 200px !important;
          transition: all 0.2s ease !important;
          pointer-events: all !important;
          z-index: 2147483647 !important;
          position: relative !important;
        "
      >${buttonText}</button>
      
      <!-- Enhanced close button -->
      <div 
        id="${closeId}"
        style="
          position: absolute !important;
          top: 15px !important;
          right: 15px !important;
          width: 35px !important;
          height: 35px !important;
          background: #f0f0f0 !important;
          color: #555 !important;
          border-radius: 50% !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          cursor: pointer !important;
          font-weight: bold !important;
          font-size: 18px !important;
          pointer-events: all !important;
          z-index: 2147483647 !important;
        "
      >✕</div>
    `;
    
    overlay.appendChild(content);
    document.body.appendChild(overlay);
    
    console.log('🪟 Modal created and added to DOM');
    
    // ENHANCED: Multiple event binding methods for maximum compatibility
    const bindClickEvents = () => {
      try {
        // Method 1: Direct event listeners
        const confirmButton = document.getElementById(buttonId);
        const closeButton = document.getElementById(closeId);
        
        if (confirmButton) {
          console.log('🔘 Binding events to confirm button');
          
          // Multiple event types for maximum compatibility
          ['click', 'touchend', 'mouseup'].forEach(eventType => {
            confirmButton.addEventListener(eventType, (e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log(`🎯 Confirm button ${eventType} triggered`);
              confirmAction();
            }, { passive: false, capture: true });
          });
          
          // Add hover effects
          confirmButton.addEventListener('mouseenter', () => {
            confirmButton.style.transform = 'scale(1.05)';
            confirmButton.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2)';
          });
          
          confirmButton.addEventListener('mouseleave', () => {
            confirmButton.style.transform = 'scale(1)';
            confirmButton.style.boxShadow = 'none';
          });
        }
        
        if (closeButton) {
          console.log('🔘 Binding events to close button');
          
          ['click', 'touchend', 'mouseup'].forEach(eventType => {
            closeButton.addEventListener(eventType, (e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log(`🎯 Close button ${eventType} triggered`);
              confirmAction();
            }, { passive: false, capture: true });
          });
        }
        
        // Method 2: Overlay click (background)
        overlay.addEventListener('click', (e) => {
          if (e.target === overlay) {
            console.log('🎯 Overlay background clicked');
            confirmAction();
          }
        }, { passive: false, capture: true });
        
        // Method 3: Keyboard events
        const handleKeyboard = (e) => {
          if (e.key === 'Enter' || e.key === ' ' || e.key === 'Escape') {
            e.preventDefault();
            e.stopPropagation();
            console.log(`🎯 Keyboard ${e.key} triggered`);
            confirmAction();
            document.removeEventListener('keydown', handleKeyboard);
          }
        };
        
        document.addEventListener('keydown', handleKeyboard, { passive: false, capture: true });
        
        console.log('✅ All event listeners bound successfully');
        
      } catch (error) {
        console.error('❌ Error binding click events:', error);
      }
    };
    
    // Bind events immediately and also after a small delay for safety
    bindClickEvents();
    setTimeout(bindClickEvents, 100);
    
    // Set up auto-close timer
    const autoCloseTimer = setTimeout(() => {
      console.log('⏰ Auto-close timer triggered');
      if (document.body.contains(overlay)) {
        confirmAction();
      }
    }, autoCloseDelay);
    
    // Return control object
    return {
      close: () => {
        console.log('🔧 Manual close triggered');
        clearTimeout(autoCloseTimer);
        confirmAction();
      }
    };
  };

  // Improved fullscreen request with user interaction requirement
  const requestFullscreenPermissionAware = async () => {
    try {
      console.log('🖥️ Requesting fullscreen with permission awareness...');
      const elem = document.documentElement;
      
      // Check if we're handling this from a direct user interaction
      const isUserInitiated = (Date.now() - (window.lastUserInteractionTime || 0) < 1000);
      
      if (!isUserInitiated) {
        console.log('⚠️ Fullscreen request not from user interaction - creating clickable prompt');
        
        // Create a user-clickable element to request fullscreen
        const fullscreenPrompt = document.createElement('div');
        fullscreenPrompt.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0,0,0,0.7);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 9999;
        `;
        
        const buttonContainer = document.createElement('div');
        buttonContainer.style.cssText = `
          background: white;
          padding: 20px;
          border-radius: 8px;
          text-align: center;
        `;
        
        buttonContainer.innerHTML = `
          <h3 style="margin-bottom: 15px;">Fullscreen Required</h3>
          <p style="margin-bottom: 15px;">Please click the button below to enter fullscreen mode.</p>
          <button id="user-fullscreen-btn" style="
            background: #ff3b30;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
            cursor: pointer;
          ">Enter Fullscreen</button>
        `;
        
        fullscreenPrompt.appendChild(buttonContainer);
        document.body.appendChild(fullscreenPrompt);
        
        // Wait for user to click the button
        return new Promise((resolve) => {
          document.getElementById('user-fullscreen-btn').addEventListener('click', async () => {
            try {
              // This will work because it's triggered by user interaction
              if (elem.requestFullscreen) {
                await elem.requestFullscreen();
              } else if (elem.mozRequestFullScreen) {
                await elem.mozRequestFullScreen();
              } else if (elem.webkitRequestFullscreen) {
                await elem.webkitRequestFullscreen();
              } else if (elem.msRequestFullscreen) {
                await elem.msRequestFullscreen();
              }
              
              fullscreenPrompt.remove();
              resolve(true);
            } catch (error) {
              console.error('Fullscreen request failed:', error);
              fullscreenPrompt.remove();
              resolve(false);
            }
          });
        });
      } else {
        // Direct fullscreen request from user interaction
        if (elem.requestFullscreen) {
          await elem.requestFullscreen();
        } else if (elem.mozRequestFullScreen) {
          await elem.mozRequestFullScreen();
        } else if (elem.webkitRequestFullscreen) {
          await elem.webkitRequestFullscreen();
        } else if (elem.msRequestFullscreen) {
          await elem.msRequestFullscreen();
        }
        return true;
      }
    } catch (error) {
      console.error('Fullscreen request failed:', error);
      return false;
    }
  };

  // ENHANCED: Copy/Paste warning system (shows warning but doesn't count as violation)
  const showCopyPasteWarning = useCallback((action, isExternal = false) => {
    const message = isExternal 
      ? `External ${action.toUpperCase()} BLOCKED: Cannot ${action} to/from external sources during the interview`
      : `${action.toUpperCase()} BLOCKED: Copy/paste operations are not allowed during the interview`;
    
    console.log(`🚨 Copy/Paste blocked: ${action}${isExternal ? ' (external)' : ''}`);
    
    // Show immediate warning banner
    setWarningMessage(message);
    setShowWarning(true);
    
    // Hide warning after 3 seconds
    setTimeout(() => {
      setShowWarning(false);
    }, 3000);
    
    // Show modal warning
    showUniversalModal({
      title: isExternal 
        ? `External ${action.charAt(0).toUpperCase() + action.slice(1)} Blocked`
        : `${action.charAt(0).toUpperCase() + action.slice(1)} Blocked`,
      message: isExternal
        ? `${action.charAt(0).toUpperCase() + action.slice(1)} to/from external sources is not allowed. You can still copy/paste within the code editor.`
        : `${action.charAt(0).toUpperCase() + action.slice(1)} operations are not allowed during the interview. This is a security measure to ensure fairness.`,
      type: 'warning',
      buttonText: 'I Understand',
      onConfirm: () => {
        console.log(`${action} warning acknowledged`);
      }
    });
  }, []);

  // ENHANCED: Intelligent copy/paste detection with external source blocking
  useEffect(() => {
    // Store for tracking internal clipboard operations within code editor
    let internalClipboardContent = '';
    let lastInternalCopyTime = 0;
    let isInternalOperation = false;
    
    // Helper function to safely check if element is in code editor
    const isInCodeEditor = (target) => {
      try {
        if (!target || typeof target.closest !== 'function') {
          return false;
        }
        return target.closest('.monaco-editor') !== null;
      } catch (error) {
        console.error('Error checking if in code editor:', error);
        return false;
      }
    };
    
    // Helper function to check if element is in other allowed areas (with external paste blocking)
    const isInOtherAllowedArea = (target) => {
      try {
        if (!target || typeof target.closest !== 'function') {
          return false;
        }
        
        const otherAllowedSelectors = [
          'input[data-allow-clipboard]',
          '[data-allow-clipboard]'
          // REMOVED: 'textarea[data-allow-clipboard]' - we'll handle textareas specially
        ];
        
        return otherAllowedSelectors.some(selector => {
          try {
            return target.closest(selector) !== null;
          } catch (error) {
            return false;
          }
        });
      } catch (error) {
        console.error('Error checking other allowed areas:', error);
        return false;
      }
    };

    // Helper function to check if element is a technical/behavioral answer textarea
    const isAnswerTextarea = (target) => {
      try {
        if (!target || typeof target.closest !== 'function') {
          return false;
        }
        
        // Check if this is a textarea that should allow typing but block external paste
        return target.tagName === 'TEXTAREA' && 
               !target.hasAttribute('data-allow-clipboard') &&
               !target.closest('.monaco-editor');
      } catch (error) {
        console.error('Error checking if answer textarea:', error);
        return false;
      }
    };

    // SMART COPY HANDLER - Tracks internal vs external operations
    const handleCopyOperation = async (e) => {
      try {
        const isCodeEditor = isInCodeEditor(e.target);
        const isOtherAllowed = isInOtherAllowedArea(e.target);
        const isAnswerArea = isAnswerTextarea(e.target);
        
        console.log(`🔍 Copy operation detected`, {
          isCodeEditor,
          isOtherAllowed,
          isAnswerArea,
          target: e.target.className
        });
        
        if (isCodeEditor) {
          // Allow copy within code editor, but track the content
          console.log('✅ Copy allowed within code editor');
          isInternalOperation = true;
          lastInternalCopyTime = Date.now();
          
          // Try to capture what was copied for internal tracking
          setTimeout(async () => {
            try {
              if (navigator.clipboard && navigator.clipboard.readText) {
                const text = await navigator.clipboard.readText();
                internalClipboardContent = text;
                console.log('📋 Internal clipboard content tracked (length):', text.length);
              }
            } catch (error) {
              // Expected - clipboard access limitations
              console.log('Could not track internal clipboard content');
            }
          }, 100);
          
          return; // Allow the operation
        } else if (isAnswerArea) {
          // Block copy from answer textareas to prevent sharing answers
          console.log('🚫 Copy blocked from answer textarea');
          e.preventDefault();
          e.stopPropagation();
          e.stopImmediatePropagation();
          showCopyPasteWarning('copy');
          return false;
        } else if (isOtherAllowed) {
          // Allow copy in other designated areas
          console.log('✅ Copy allowed in designated area');
          return;
        } else {
          // Block copy from non-allowed areas
          console.log('🚫 Copy blocked from non-allowed area');
          e.preventDefault();
          e.stopPropagation();
          e.stopImmediatePropagation();
          showCopyPasteWarning('copy');
          return false;
        }
      } catch (error) {
        console.error('Error handling copy operation:', error);
        e.preventDefault();
        showCopyPasteWarning('copy');
        return false;
      }
    };

    // SMART PASTE HANDLER - Blocks external content, allows internal in code editor only
    const handlePasteOperation = async (e) => {
      try {
        const isCodeEditor = isInCodeEditor(e.target);
        const isOtherAllowed = isInOtherAllowedArea(e.target);
        const isAnswerArea = isAnswerTextarea(e.target);
        
        console.log(`🔍 Paste operation detected`, {
          isCodeEditor,
          isOtherAllowed,
          isAnswerArea,
          target: e.target.className
        });
        
        if (isAnswerArea) {
          // ALWAYS block paste in answer textareas (technical/behavioral questions)
          console.log('🚫 Paste blocked in answer textarea (technical/behavioral questions)');
          e.preventDefault();
          e.stopPropagation();
          e.stopImmediatePropagation();
          showCopyPasteWarning('paste', true); // true = external
          return false;
        } else if (!isCodeEditor && !isOtherAllowed) {
          // Block paste in non-allowed areas
          console.log('🚫 Paste blocked in non-allowed area');
          e.preventDefault();
          e.stopPropagation();
          e.stopImmediatePropagation();
          showCopyPasteWarning('paste');
          return false;
        }
        
        if (isCodeEditor) {
          // For code editor, check if this is an internal or external paste
          console.log('🔍 Analyzing paste content in code editor...');
          
          let clipboardText = '';
          
          // Try to get clipboard content from the event first
          if (e.clipboardData && e.clipboardData.getData) {
            try {
              clipboardText = e.clipboardData.getData('text/plain');
              console.log('📋 Got clipboard text from event (length):', clipboardText.length);
            } catch (error) {
              console.log('Could not get clipboard text from event');
            }
          }
          
          // Fallback: try to get from navigator.clipboard
          if (!clipboardText) {
            try {
              if (navigator.clipboard && navigator.clipboard.readText) {
                clipboardText = await navigator.clipboard.readText();
                console.log('📋 Got clipboard text from navigator (length):', clipboardText.length);
              }
            } catch (error) {
              console.log('Could not access clipboard via navigator');
            }
          }
          
          // Check if this is an internal operation
          const isRecentInternalCopy = (Date.now() - lastInternalCopyTime) < 10000; // 10 seconds window
          const isInternalContent = internalClipboardContent && 
                                   clipboardText && 
                                   (clipboardText === internalClipboardContent || 
                                    internalClipboardContent.includes(clipboardText) ||
                                    clipboardText.includes(internalClipboardContent));
          
          console.log('🔍 Paste analysis:', {
            isRecentInternalCopy,
            isInternalContent,
            internalContentLength: internalClipboardContent.length,
            clipboardTextLength: clipboardText.length,
            timeSinceLastCopy: Date.now() - lastInternalCopyTime
          });
          
          if (isRecentInternalCopy && (isInternalContent || isInternalOperation)) {
            // Allow internal paste operation
            console.log('✅ Internal paste operation allowed in code editor');
            isInternalOperation = false; // Reset flag
            return; // Allow the operation
          } else {
            // This appears to be external content - block it
            console.log('🚫 External paste blocked in code editor');
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();
            showCopyPasteWarning('paste', true); // true = external
            return false;
          }
        } else if (isOtherAllowed) {
          // Allow paste in other designated areas
          console.log('✅ Paste allowed in designated area');
          return;
        }
      } catch (error) {
        console.error('Error handling paste operation:', error);
        e.preventDefault();
        showCopyPasteWarning('paste', true);
        return false;
      }
    };

    // SMART CUT HANDLER - Similar to copy but removes content
    const handleCutOperation = (e) => {
      try {
        const isCodeEditor = isInCodeEditor(e.target);
        const isOtherAllowed = isInOtherAllowedArea(e.target);
        const isAnswerArea = isAnswerTextarea(e.target);
        
        console.log(`🔍 Cut operation detected`, {
          isCodeEditor,
          isOtherAllowed,
          isAnswerArea,
          target: e.target.className
        });
        
        if (isCodeEditor) {
          // Allow cut within code editor
          console.log('✅ Cut allowed within code editor');
          isInternalOperation = true;
          lastInternalCopyTime = Date.now();
          return;
        } else if (isAnswerArea) {
          // Block cut from answer textareas
          console.log('🚫 Cut blocked from answer textarea');
          e.preventDefault();
          e.stopPropagation();
          e.stopImmediatePropagation();
          showCopyPasteWarning('cut');
          return false;
        } else if (isOtherAllowed) {
          // Allow cut in other designated areas
          console.log('✅ Cut allowed in designated area');
          return;
        } else {
          // Block cut from non-allowed areas
          console.log('🚫 Cut blocked from non-allowed area');
          e.preventDefault();
          e.stopPropagation();
          e.stopImmediatePropagation();
          showCopyPasteWarning('cut');
          return false;
        }
      } catch (error) {
        console.error('Error handling cut operation:', error);
        e.preventDefault();
        showCopyPasteWarning('cut');
        return false;
      }
    };
    
    // SMART KEYBOARD SHORTCUTS HANDLER
    const handleKeyboardShortcuts = (e) => {
      try {
        // Check for copy/paste keyboard shortcuts
        if ((e.ctrlKey || e.metaKey) && ['c', 'v', 'x', 'a'].includes(e.key.toLowerCase())) {
          const isCodeEditor = isInCodeEditor(e.target);
          const isOtherAllowed = isInOtherAllowedArea(e.target);
          const isAnswerArea = isAnswerTextarea(e.target);
          
          console.log(`🔍 Keyboard shortcut detected: ${e.ctrlKey ? 'Ctrl' : 'Cmd'}+${e.key.toUpperCase()}`, {
            isCodeEditor,
            isOtherAllowed,
            isAnswerArea
          });
          
          const actionMap = {
            'c': 'copy',
            'v': 'paste', 
            'x': 'cut',
            'a': 'select all'
          };
          
          const action = actionMap[e.key.toLowerCase()];
          
          if (isAnswerArea) {
            // Special handling for answer textareas
            if (action === 'paste') {
              // Always block paste in answer areas
              console.log('🚫 Paste shortcut blocked in answer textarea');
              e.preventDefault();
              e.stopPropagation();
              e.stopImmediatePropagation();
              showCopyPasteWarning('paste', true); // external paste blocked
              return false;
            } else if (action === 'copy' || action === 'cut') {
              // Block copy/cut from answer areas to prevent sharing
              console.log(`🚫 ${action} shortcut blocked in answer textarea`);
              e.preventDefault();
              e.stopPropagation();
              e.stopImmediatePropagation();
              showCopyPasteWarning(action);
              return false;
            } else if (action === 'select all') {
              // Allow select all in answer areas
              console.log('✅ Select all allowed in answer textarea');
              return;
            }
          } else if (isCodeEditor) {
            // For code editor, handle copy/cut/select-all normally, but special handling for paste
            if (action === 'paste') {
              // Let the paste event handler deal with this
              console.log('✅ Paste shortcut in code editor - will be handled by paste event');
              return;
            } else {
              // Allow copy, cut, select-all in code editor
              console.log(`✅ ${action} shortcut allowed in code editor`);
              if (action === 'copy' || action === 'cut') {
                isInternalOperation = true;
                lastInternalCopyTime = Date.now();
              }
              return;
            }
          } else if (isOtherAllowed) {
            // Allow in other designated areas
            console.log(`✅ ${action} shortcut allowed in designated area`);
            return;
          } else {
            // Block in non-allowed areas
            console.log(`🚫 ${action} keyboard shortcut blocked`);
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();
            showCopyPasteWarning(action);
            return false;
          }
        }
      } catch (error) {
        console.error('Error handling keyboard shortcut:', error);
        if ((e.ctrlKey || e.metaKey) && ['c', 'v', 'x', 'a'].includes(e.key.toLowerCase())) {
          e.preventDefault();
          e.stopPropagation();
          const actionMap = { 'c': 'copy', 'v': 'paste', 'x': 'cut', 'a': 'select all' };
          showCopyPasteWarning(actionMap[e.key.toLowerCase()]);
        }
        return false;
      }
    };
    
    // COMPREHENSIVE: Handle right-click context menu
    const handleContextMenu = (e) => {
      try {
        const isCodeEditor = isInCodeEditor(e.target);
        const isOtherAllowed = isInOtherAllowedArea(e.target);
        
        if (!isCodeEditor && !isOtherAllowed) {
          console.log('🚫 Context menu (right-click) blocked');
          e.preventDefault();
          e.stopPropagation();
          
          // Show a brief warning for context menu
          setWarningMessage('RIGHT-CLICK BLOCKED: Context menu is not allowed during the interview');
          setShowWarning(true);
          setTimeout(() => setShowWarning(false), 2000);
          
          return false;
        } else {
          console.log('✅ Context menu allowed in designated area');
        }
      } catch (error) {
        console.error('Error handling context menu:', error);
        e.preventDefault();
        return false;
      }
    };

    // COMPREHENSIVE: Handle text selection
    const handleSelectStart = (e) => {
      try {
        const isCodeEditor = isInCodeEditor(e.target);
        const isOtherAllowed = isInOtherAllowedArea(e.target);
        
        if (!isCodeEditor && !isOtherAllowed) {
          console.log('🚫 Text selection blocked');
          e.preventDefault();
          return false;
        }
      } catch (error) {
        console.error('Error handling text selection:', error);
        e.preventDefault();
        return false;
      }
    };

    // MONITOR: Track clipboard changes for external content detection
    const monitorClipboard = async () => {
      try {
        if (navigator.clipboard && navigator.clipboard.readText && 
            !isInternalOperation && (Date.now() - lastInternalCopyTime) > 2000) {
          // Only monitor when not doing internal operations
          const text = await navigator.clipboard.readText();
          if (text && text.length > 0 && text !== internalClipboardContent) {
            console.log('📋 External clipboard content detected during monitoring');
            // Just log for now - the paste handler will block external content
          }
        }
      } catch (error) {
        // Expected - clipboard access requires user interaction
      }
    };

    // Add event listeners with multiple layers
    
    // Primary: Copy/Cut/Paste events
    document.addEventListener('copy', handleCopyOperation, { capture: true, passive: false });
    document.addEventListener('cut', handleCutOperation, { capture: true, passive: false });
    document.addEventListener('paste', handlePasteOperation, { capture: true, passive: false });
    
    window.addEventListener('copy', handleCopyOperation, { capture: true, passive: false });
    window.addEventListener('cut', handleCutOperation, { capture: true, passive: false });
    window.addEventListener('paste', handlePasteOperation, { capture: true, passive: false });
    
    // Secondary: Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts, { capture: true, passive: false });
    window.addEventListener('keydown', handleKeyboardShortcuts, { capture: true, passive: false });
    
    // Tertiary: Context menu and text selection
    document.addEventListener('contextmenu', handleContextMenu, { capture: true, passive: false });
    window.addEventListener('contextmenu', handleContextMenu, { capture: true, passive: false });
    document.addEventListener('selectstart', handleSelectStart, { capture: true, passive: false });
    
    // Monitor clipboard periodically
    const clipboardInterval = setInterval(monitorClipboard, 3000);
    
    // Add CSS for selective text selection
    const style = document.createElement('style');
    style.textContent = `
      /* Block text selection globally */
      * {
        -webkit-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
        user-select: none !important;
      }
      
      /* Allow selection in code editor and designated areas */
      .monaco-editor,
      .monaco-editor *,
      [data-allow-clipboard],
      [data-allow-select],
      textarea[data-allow-clipboard],
      input[data-allow-clipboard] {
        -webkit-user-select: text !important;
        -moz-user-select: text !important;
        -ms-user-select: text !important;
        user-select: text !important;
      }
    `;
    document.head.appendChild(style);
    
    console.log('✅ Intelligent copy/paste system initialized - allows internal code editor operations, blocks external');
    
    // Cleanup function
    return () => {
      clearInterval(clipboardInterval);
      
      if (style.parentNode) {
        style.parentNode.removeChild(style);
      }
      
      document.removeEventListener('copy', handleCopyOperation, { capture: true });
      document.removeEventListener('cut', handleCutOperation, { capture: true });
      document.removeEventListener('paste', handlePasteOperation, { capture: true });
      
      window.removeEventListener('copy', handleCopyOperation, { capture: true });
      window.removeEventListener('cut', handleCutOperation, { capture: true });
      window.removeEventListener('paste', handlePasteOperation, { capture: true });
      
      document.removeEventListener('keydown', handleKeyboardShortcuts, { capture: true });
      window.removeEventListener('keydown', handleKeyboardShortcuts, { capture: true });
      
      document.removeEventListener('contextmenu', handleContextMenu, { capture: true });
      window.removeEventListener('contextmenu', handleContextMenu, { capture: true });
      document.removeEventListener('selectstart', handleSelectStart, { capture: true });
      
      console.log('✅ Intelligent copy/paste system cleanup completed');
    };
  }, [showCopyPasteWarning]);

  // ENHANCED: Keyboard monitoring for tab switch detection
  useEffect(() => {
    // Create direct Tab key handlers at the document level
    const detectTabSwitch = (e) => {
      if ((e.metaKey || e.ctrlKey) && (e.key === 'Tab' || e.keyCode === 9)) {
        console.log('Command+Tab or Control+Tab detected in interview-live');
        
        // If security manager is active, let it handle the violation
        if (securityManagerRef.current && securityManagerRef.current.isInterviewActive) {
          return;
        }
        
        // Otherwise handle it directly
        e.preventDefault();
        e.stopPropagation();
        
        // Show warning directly
        setWarningMessage('Security Violation: Attempted to switch tabs using keyboard shortcut');
        setShowWarning(true);
        
        return false;
      }
    };
    
    // Add direct event listeners for tab switching key combinations
    document.addEventListener('keydown', detectTabSwitch, { capture: true, passive: false });
    
    return () => {
      document.removeEventListener('keydown', detectTabSwitch, { capture: true });
    };
  }, []);

  // Load SecurityManager
  const loadSecurityManager = useCallback(async () => {
    if (typeof window !== 'undefined' && !window.SecurityManager) {
      try {
        console.log('📥 Loading SecurityManager script...');
        const script = document.createElement('script');
        script.src = '/js/security-manager.js';
        script.onload = () => {
          console.log('✅ SecurityManager script loaded successfully');
          setSecurityReady(true);
        };
        script.onerror = (error) => {
          console.error('❌ Failed to load SecurityManager:', error);
          setError('Failed to load security components. Please refresh the page.');
          setSecurityReady(false);
        };
        document.head.appendChild(script);
      } catch (error) {
        console.error('Error loading SecurityManager:', error);
        setError('Security initialization failed. Please refresh the page.');
        setSecurityReady(false);
      }
    } else if (window.SecurityManager) {
      console.log('✅ SecurityManager already available');
      setSecurityReady(true);
    }
  }, []);

  useEffect(() => {
    loadSecurityManager();
  }, [loadSecurityManager]);

  // Initialize security when ready
  useEffect(() => {
    if (!securityReady || !id || securityInitializedRef.current) return;

    try {
      console.log('🔧 Initializing SecurityManager instance...');
      
      if (!securityManagerRef.current && window.SecurityManager) {
        try {
          securityManagerRef.current = new window.SecurityManager();
          securityManagerRef.current.setApplicationId(id);
          securityManagerRef.current.interviewStartTime = Date.now();
          
          // Security event handlers
          const handleSecurityViolation = (event) => {
            try {
              const { type, message, count, totalViolations: total, category } = event.detail;
              
              // IMPORTANT: Skip window blur violations completely
              if (type === 'windowBlur') {
                console.log('Ignoring window blur violation to prevent false positives');
                return;
              }
              
              if (category === 'warning') {
                console.warn(`🚨 Security violation: ${type} - ${message} (${count})`);
                
                setSecurityViolations(prev => ({
                  ...prev,
                  [type]: count
                }));
                
                setTotalViolations(total);
                setWarningCount(prev => prev + 1);
                
                showSecurityWarning(type, message, count, total);
                reportViolation(type, message).catch(error => {
                  console.error('Failed to report violation to server:', error);
                });
              } else if (category === 'analytics') {
                console.log(`📊 Analytics violation: ${type} - ${message}`);
                // Analytics violations are recorded but don't show warnings
              }
            } catch (error) {
              console.error('Error handling security violation:', error);
            }
          };
          
          const handleDisqualification = (event) => {
            try {
              const { reason, warningViolations, analyticsViolations, totalWarningViolations, suspiciousEvents } = event.detail;
              console.error('❌ Interview disqualified:', reason);
              console.error('🚨 Violation details:', {
                warningViolations,
                analyticsViolations,
                totalWarningViolations,
                suspiciousEvents,
                interview: {
                  applicationId: id,
                  timeElapsed: securityManagerRef.current?.interviewStartTime 
                    ? Math.floor((Date.now() - securityManagerRef.current.interviewStartTime) / 1000) 
                    : 'unknown'
                }
              });
              
              // Add detailed logging for debugging
              console.log('Full disqualification event:', JSON.stringify(event.detail, null, 2));
              
              setDisqualified(true);
              setDisqualificationReason(reason);
              
              // Turn off camera when disqualified
              turnOffCamera();
              
              // Enhanced violation reporting
              const violationSummary = {
                reason,
                warningViolations: warningViolations || {},
                analyticsViolations: analyticsViolations || {},
                totalWarningViolations: totalWarningViolations || 0,
                suspiciousEvents: suspiciousEvents || [],
                timestamp: new Date().toISOString(),
                interview: {
                  applicationId: id,
                  timeElapsed: securityManagerRef.current?.interviewStartTime 
                    ? Math.floor((Date.now() - securityManagerRef.current.interviewStartTime) / 1000) 
                    : 0
                }
              };
              
              console.log('Reporting disqualification with summary:', violationSummary);
              
              reportDisqualification(reason, violationSummary).catch(error => {
                console.error('Failed to report disqualification to server:', error);
              });
            } catch (error) {
              console.error('Error handling disqualification:', error);
            }
          };
          
          const handleFinalWarning = (event) => {
            try {
              console.warn('⚠️ FINAL WARNING');
              
              setShowFinalWarning(true);
              setWarningMessage('FINAL WARNING: One more violation will result in disqualification!');
              setShowWarning(true);
              
              setTimeout(() => {
                setShowWarning(false);
                setShowFinalWarning(false);
              }, 10000);
            } catch (error) {
              console.error('Error handling final warning:', error);
            }
          };
          
          // Add event listeners
          window.addEventListener('security-violation', handleSecurityViolation);
          window.addEventListener('interview-disqualified', handleDisqualification);
          window.addEventListener('final-warning', handleFinalWarning);
          window.addEventListener('interview-started', () => {
            console.log('✅ Interview monitoring started');
            setSecurityInitialized(true);
          });
          window.addEventListener('interview-ended', () => {
            console.log('✅ Interview monitoring ended');
          });
          
          securityInitializedRef.current = true;
          console.log('✅ SecurityManager initialized successfully');
        } catch (error) {
          console.error('❌ Error creating SecurityManager instance:', error);
          setError('Failed to initialize security system. Please refresh the page.');
        }
      }
    } catch (error) {
      console.error('❌ Error initializing SecurityManager:', error);
      setError('Security system initialization failed. Please refresh the page.');
    }
  }, [securityReady, id, turnOffCamera]);

  // Store application ID
  useEffect(() => {
    if (id) {
      appIdRef.current = id;
      console.log('📝 Application ID stored:', id);
    }
  }, [id]);

  // ENHANCED: Track user interaction for permission-based operations
  useEffect(() => {
    // Initialize user interaction tracking
    if (typeof window !== 'undefined') {
      // Create global timestamp for last user interaction
      window.lastUserInteractionTime = Date.now();
      
      // Update timestamp on various user interactions
      const updateInteractionTime = () => {
        window.lastUserInteractionTime = Date.now();
      };
      
      // Track all user interaction events
      ['click', 'touchstart', 'mousedown', 'keydown', 'pointerdown'].forEach(eventType => {
        window.addEventListener(eventType, updateInteractionTime, { passive: true });
      });
      
      return () => {
        // Clean up event listeners
        ['click', 'touchstart', 'mousedown', 'keydown', 'pointerdown'].forEach(eventType => {
          window.removeEventListener(eventType, updateInteractionTime);
        });
      };
    }
  }, []);

  // ENHANCED: Show security warning with universal modal
  const showSecurityWarning = async (type, message, count, total) => {
    try {
      const isFinalWarning = total >= 4;
      const warningText = `${message} (Warning ${count} | Total: ${total}/5)`;
      
      // Show banner at top of screen
      setWarningMessage(warningText);
      setShowWarning(true);
      
      // Create title based on violation type
      const formattedType = type
        .replace(/([A-Z])/g, ' $1')
        .toLowerCase()
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
      
      // Customize message based on violation type
      let customMessage = '';
      if (type === 'fullscreenExit') {
  customMessage = 'You must stay in fullscreen mode during the interview.';
} else if (type === 'tabSwitch') {
  customMessage = 'Switching tabs during the interview is not allowed.';
} else if (type === 'windowBlur') {
  customMessage = 'Please keep this window focused during the interview.';
} else if (type === 'screenshot') {
  customMessage = 'Taking screenshots during the interview is strictly prohibited.';
} else if (type === 'handsRaised') {
  customMessage = 'Raising your hands for extended periods suggests phone usage.';
} else if (type === 'handsOutOfFrame') {
  customMessage = 'Please keep your hands visible in the camera frame.';
} else if (type === 'suspiciousHandGesture') {
  customMessage = 'Hand position suggests you may be holding a device.';
} else if (type === 'deviceDetected') {
  customMessage = 'Electronic devices detected in camera view are not allowed.';
}
      
      // Add warning count
      const warningCountText = isFinalWarning
        ? 'This is your FINAL WARNING! One more violation will end the interview.'
        : `You will be disqualified after ${5 - total} more violations.`;
      
      // Complete message
      const fullMessage = `
        ${message}<br><br>
        ${customMessage}<br><br>
        <strong style="color: ${isFinalWarning ? '#ff3b30' : '#ff9500'}">
          ${warningCountText}
        </strong>
      `;
      
      // Show centered modal with the universal function
      return new Promise((resolve) => {
        showUniversalModal({
          title: `Security Violation: ${formattedType}`,
          message: fullMessage,
          type: isFinalWarning ? 'error' : 'warning',
          buttonText: 'I Understand',
          onConfirm: async () => {
            // If this was a fullscreen exit violation, request fullscreen again
            if (type === 'fullscreenExit') {
              await requestFullscreenPermissionAware();
            }
            
            // Hide banner after acknowledgment
            setShowWarning(false);
            resolve(true);
          }
        });
      });
    } catch (error) {
      console.error('Error showing security warning:', error);
      
      // Fallback to auto-hide if modal fails
      setTimeout(() => {
        setShowWarning(false);
      }, 5000);
      
      return false;
    }
  };

  // Report violation to server
  const reportViolation = async (type, message) => {
    try {
      // IMPORTANT: Skip window blur violations completely
      if (type === 'windowBlur') {
        console.log('Skipping window blur report to server to prevent false positives');
        return;
      }
      
      const response = await axios.post('/api/interview/violation', {
        applicationId: id,
        type,
        message,
        timestamp: new Date().toISOString()
      });
      
      if (!response.data.success) {
        console.error('Server rejected violation report:', response.data);
      }
    } catch (error) {
      console.error('Network error reporting violation:', error);
    }
  };

  // Enhanced report disqualification to server
  const reportDisqualification = async (reason, violationSummary) => {
    try {
      console.log('📤 Sending disqualification report...');
      
      const disqualificationData = {
        applicationId: id,
        reason,
        violations: violationSummary,
        timestamp: new Date().toISOString()
      };
      
      console.log('Disqualification data being sent:', JSON.stringify(disqualificationData, null, 2));
      
      const response = await axios.post('/api/interview/disqualify', disqualificationData);
      
      console.log('Disqualification response:', response.data);
      
      if (!response.data.success) {
        console.error('Server rejected disqualification report:', response.data);
      } else {
        console.log('✅ Disqualification reported successfully');
      }
    } catch (error) {
      console.error('❌ Network error reporting disqualification:', error);
      console.error('Error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });
    }
  };



//================================================================================================================
//END OF PART 5
//================================================================================================================




// File: pages/candidate/interview-live.js - Part 6: Audio and Recording Functions

  // Format recording time
  const formatRecordingTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // ENHANCED: Exit confirmation that doesn't exit fullscreen mode
  const showExitConfirmation = useCallback(() => {
    try {
      // Create blocking overlay
      const overlay = document.createElement('div');
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0,0,0,0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10001;
      `;
      
      // Create content
      const content = document.createElement('div');
      content.style.cssText = `
        background: white;
        border-radius: 8px;
        width: 90%;
        max-width: 500px;
        padding: 30px;
        text-align: center;
        box-shadow: 0 10px 25px rgba(0,0,0,0.3);
      `;
      
      content.innerHTML = `
        <div style="font-size: 32px; margin-bottom: 15px;">🚪</div>
        <h2 style="font-size: 20px; margin-bottom: 15px; color: #333;">Exit Interview?</h2>
        <p style="margin-bottom: 25px; font-size: 16px; color: #444;">
          Are you sure you want to exit the interview? All progress will be lost.
        </p>
        <div style="display: flex; justify-content: center; gap: 20px;">
          <button id="exit-confirm-yes" style="
            background: #ff3b30;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
          ">Yes, Exit</button>
          <button id="exit-confirm-no" style="
            background: #007aff;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
          ">No, Continue</button>
        </div>
      `;
      
      overlay.appendChild(content);
      document.body.appendChild(overlay);
      
      // Return promise that resolves with user choice
      return new Promise((resolve) => {
        document.getElementById('exit-confirm-yes').addEventListener('click', () => {
          overlay.remove();
          resolve(true);
        });
        
        document.getElementById('exit-confirm-no').addEventListener('click', () => {
          overlay.remove();
          resolve(false);
        });
      });
    } catch (error) {
      console.error('Error showing exit confirmation:', error);
      return Promise.resolve(false);
    }
  }, []);

  // Start recording
  const startRecording = async () => {
    try {
      console.log('🎤 Starting recording...');
      setError('');
      stopRecording();
      
      trackFirstInteraction();
      
      if (!navigator.onLine) {
        setError('No internet connection. Using offline mode.');
        setUseOfflineMode(true);
      }
      
      if (!useOfflineMode && (window.SpeechRecognition || window.webkitSpeechRecognition)) {
        startBrowserTranscription();
      } else {
        startBrowserRecording();
      }
    } catch (error) {
      console.error('Error starting recording:', error);
      setError(`Failed to start recording: ${error.message}`);
      setIsListening(false);
    }
  };

  // Start browser-based speech recognition
  const startBrowserTranscription = () => {
    try {
      if ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        const recognition = new SpeechRecognition();
        
        recognition.continuous = true;
        recognition.interimResults = true;
        recognition.lang = 'en-US';
        
        recognition.onstart = () => {
          console.log('🎤 Speech recognition started');
          setIsListening(true);
          setRecordingTime(0);
          recordingTimerRef.current = setInterval(() => {
            setRecordingTime(prev => prev + 1);
          }, 1000);
        };
        
        recognition.onresult = (event) => {
          let interimTranscript = '';
          let finalTranscript = '';
          
          for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;
            if (event.results[i].isFinal) {
              finalTranscript += transcript + ' ';
            } else {
              interimTranscript += transcript;
            }
          }
          
          if (finalTranscript) {
            setTranscript(prev => (prev ? prev + ' ' + finalTranscript : finalTranscript));
          }
          
          setInterimResult(interimTranscript);
        };
        
        recognition.onerror = (event) => {
          console.error('Speech recognition error:', event.error);
          setError(`Speech recognition error: ${event.error}`);
          setIsListening(false);
        };
        
        recognition.onend = () => {
          console.log('🎤 Speech recognition ended');
          setIsListening(false);
        };
        
        window.currentRecognition = recognition;
        recognition.start();
        setInterimResult('Listening... Speak now');
      } else {
        console.log('Speech recognition not supported, falling back to recording');
        startBrowserRecording();
      }
    } catch (error) {
      console.error('Error starting browser transcription:', error);
      startBrowserRecording();
    }
  };

  // Start browser recording with manual transcription
  const startBrowserRecording = async () => {
    try {
      console.log('🎧 Starting browser recording...');
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      audioStreamRef.current = stream;
      
      const mediaRecorder = new MediaRecorder(stream);
      audioMediaRecorderRef.current = mediaRecorder;
      recordedAudioChunksRef.current = [];
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          recordedAudioChunksRef.current.push(event.data);
        }
      };
      
      mediaRecorder.onstop = async () => {
        if (recordedAudioChunksRef.current.length === 0) return;
        
        setIsProcessing(true);
        setInterimResult('Processing your recording...');
        
        try {
          const audioBlob = new Blob(recordedAudioChunksRef.current, { type: 'audio/webm' });
          const formData = new FormData();
          formData.append('file', audioBlob, 'recording.webm');
          
          if (appIdRef.current) {
            formData.append('applicationId', appIdRef.current);
          }
          
          const response = await fetch('/api/transcribe', {
            method: 'POST',
            body: formData
          });
          
          if (response.ok) {
            const result = await response.json();
            if (result.text) {
              setTranscript(prev => prev ? `${prev} ${result.text}` : result.text);
              setInterimResult('');
            }
          } else {
            throw new Error('Transcription failed');
          }
        } catch (error) {
          console.error('Error processing audio:', error);
          setError(`Failed to process recording: ${error.message}`);
        } finally {
          setIsProcessing(false);
          recordedAudioChunksRef.current = [];
        }
      };
      
      setIsListening(true);
      setRecordingTime(0);
      setInterimResult('Recording... (Will process when stopped)');
      
      mediaRecorder.start(1000);
      
      recordingTimerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    } catch (error) {
      console.error('Error starting browser recording:', error);
      setError(`Recording failed: ${error.message}`);
      setIsListening(false);
    }
  };

  // Stop recording
  const stopRecording = () => {
    try {
      console.log('⏹️ Stopping recording...');
      
      if (speechRecognitionTimeoutRef.current) {
        clearTimeout(speechRecognitionTimeoutRef.current);
        speechRecognitionTimeoutRef.current = null;
      }
      
      if (window.currentRecognition) {
        try {
          window.currentRecognition.stop();
          window.currentRecognition = null;
        } catch (error) {
          console.warn('Error stopping recognition:', error);
        }
      }
      
      if (audioMediaRecorderRef.current && audioMediaRecorderRef.current.state !== 'inactive') {
        try {
          audioMediaRecorderRef.current.stop();
        } catch (error) {
          console.warn('Error stopping recorder:', error);
        }
      }
      
      if (audioStreamRef.current) {
        audioStreamRef.current.getTracks().forEach(track => track.stop());
        audioStreamRef.current = null;
      }
      
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
        recordingTimerRef.current = null;
      }
      
      setIsListening(false);
      setInterimResult('');
    } catch (error) {
      console.error('Error stopping recording:', error);
      setIsListening(false);
    }
  };

  // Run code
  const runCode = async () => {
    if (!code.trim() || isRunningCode) return;

    try {
      console.log('▶️ Running code...');
      
      trackFirstInteraction();
      
      setIsRunningCode(true);
      setCodeOutput('Running code...');
      
      const response = await axios.post('/api/interview/runcode', {
        code,
        language: 'javascript',
        questionId: questions[currentQuestion]?.id || `question-${currentQuestion}`
      });
      
      if (response.data.success) {
        setCodeOutput(response.data.output);
      } else {
        setCodeOutput(`Error: ${response.data.output || response.data.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error running code:', error);
      setCodeOutput(`Error: ${error.message || 'Unknown error'}`);
    } finally {
      setIsRunningCode(false);
    }
  };

  // Text-to-speech
  const speakQuestion = () => {
    if (!questions[currentQuestion] || !window.speechSynthesis || isSpeaking) return;

    try {
      console.log('🔊 Speaking question...');
      window.speechSynthesis.cancel();
      
      const utterance = new SpeechSynthesisUtterance(questions[currentQuestion].question);
      utterance.onstart = () => setIsSpeaking(true);
      utterance.onend = () => setIsSpeaking(false);
      utterance.onerror = (error) => {
        console.error('Speech synthesis error:', error);
        setIsSpeaking(false);
      };
      
      window.speechSynthesis.speak(utterance);
    } catch (error) {
      console.error('Error speaking question:', error);
      setIsSpeaking(false);
    }
  };

  // Submit interview
  const handleSubmit = async () => {
    try {
      console.log('📤 Submitting interview...');
      
      const currentQ = questions[currentQuestion];
      if (['behavioral', 'technical'].includes(currentQ?.type) && transcript) {
        handleAnswerChange(transcript);
      }
      if (currentQ?.type === 'coding' && code) {
        handleAnswerChange({ code, output: codeOutput, language: codeLanguage });
      }
      
      if (questionStartTime && currentQ) {
        recordResponseTime(currentQuestion, currentQ.type);
      }
      
      const applicationId = appIdRef.current || id;
      
      let securitySummary = {};
      try {
        if (securityManagerRef.current) {
          securitySummary = securityManagerRef.current.getViolationSummary();
        }
      } catch (error) {
        console.error('Error getting security summary:', error);
      }
      
      await axios.post('/api/interview/complete', {
        applicationId,
        answers,
        securityViolations: securitySummary || {},
        responseTimeData,
        suspiciousResponseTimes
      });
      
      if (securityManagerRef.current) {
        try {
          securityManagerRef.current.endInterview();
        } catch (error) {
          console.error('Error ending security monitoring:', error);
        }
      }
      
      // Turn off camera
      turnOffCamera();
      
      exitFullscreen();
      
      console.log('✅ Interview submitted successfully');
      router.push('/candidate-dashboard');
    } catch (error) {
      console.error('Error submitting interview:', error);
      setError('Failed to submit interview. Redirecting to dashboard...');
      setTimeout(() => {
        turnOffCamera(); // Ensure camera is off even on error
        router.push('/candidate-dashboard');
      }, 3000);
    }
  };

  // Navigation handlers
  const handleNext = () => {
    try {
      console.log('⏭️ Moving to next question...');
      
      const currentQ = questions[currentQuestion];
      
      if (currentQ && questionStartTime) {
        recordResponseTime(currentQuestion, currentQ.type);
      }
      
      if (['behavioral', 'technical'].includes(currentQ?.type) && transcript) {
        handleAnswerChange(transcript);
      }
      
      if (currentQ?.type === 'coding' && code) {
        handleAnswerChange({ code, output: codeOutput, language: codeLanguage });
      }
      
      setCurrentQuestion(prev => prev + 1);
    } catch (error) {
      console.error('Error moving to next question:', error);
    }
  };

  const handlePrev = () => {
    try {
      console.log('⏮️ Moving to previous question...');
      
      const currentQ = questions[currentQuestion];
      
      if (currentQ && questionStartTime) {
        recordResponseTime(currentQuestion, currentQ.type);
      }
      
      if (['behavioral', 'technical'].includes(currentQ?.type) && transcript) {
        handleAnswerChange(transcript);
      }
      
      if (currentQ?.type === 'coding' && code) {
        handleAnswerChange({ code, output: codeOutput, language: codeLanguage });
      }
      
      setCurrentQuestion(prev => prev - 1);
    } catch (error) {
      console.error('Error moving to previous question:', error);
    }
  };

  // Alternative 1: Request fullscreen with better timing and user engagement
  const requestFullscreenWithPrompt = async () => {
    try {
      console.log('🖥️ Attempting to request fullscreen...');
      
      // First, create a subtle user interaction
      const fullscreenPrompt = document.createElement('div');
      fullscreenPrompt.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0,0,255,0.9);
        color: white;
        padding: 20px 40px;
        border-radius: 10px;
        font-size: 18px;
        text-align: center;
        z-index: 10000;
        cursor: pointer;
      `;
      fullscreenPrompt.innerHTML = `
        <div>Click here to enter fullscreen and begin interview</div>
        <div style="font-size: 14px; margin-top: 10px;">This is required for the interview</div>
      `;
      
      document.body.appendChild(fullscreenPrompt);
      
      // Wait for user click
      return new Promise((resolve) => {
        fullscreenPrompt.addEventListener('click', async () => {
          document.body.removeChild(fullscreenPrompt);
          
          const elem = document.documentElement;
          try {
            if (elem.requestFullscreen) {
              await elem.requestFullscreen();
            } else if (elem.mozRequestFullScreen) {
              await elem.mozRequestFullScreen();
            } else if (elem.webkitRequestFullscreen) {
              await elem.webkitRequestFullscreen();
            } else if (elem.msRequestFullscreen) {
              await elem.msRequestFullscreen();
            }
            console.log('✅ Fullscreen mode activated');
            resolve(true);
          } catch (error) {
            console.error('Fullscreen request failed:', error);
            alert('Fullscreen is required for this interview. Please allow fullscreen access.');
            resolve(false);
          }
        });
      });
    } catch (error) {
      console.error('Error creating fullscreen prompt:', error);
      return false;
    }
  };

  // Alternative 2: Multiple attempts with delays
  const requestFullscreenMultipleAttempts = async (attempts = 0) => {
    if (attempts >= 3) {
      console.log('Max fullscreen attempts reached');
      return false;
    }
    
    try {
      console.log(`🖥️ Fullscreen attempt ${attempts + 1}/3`);
      
      const elem = document.documentElement;
      const fullscreenMethod = elem.requestFullscreen || 
                              elem.mozRequestFullScreen || 
                              elem.webkitRequestFullscreen || 
                              elem.msRequestFullscreen;
      
      if (fullscreenMethod) {
        await fullscreenMethod.call(elem);
        console.log('✅ Fullscreen successful');
        return true;
      }
    } catch (error) {
      console.log(`Attempt ${attempts + 1} failed:`, error.message);
      
      // Wait a bit and try again
      await new Promise(resolve => setTimeout(resolve, 1000));
      return requestFullscreenMultipleAttempts(attempts + 1);
    }
    
    return false;
  };

  // Alternative 3: Mandatory fullscreen with blocking overlay
  const enforceFullscreenMode = () => {
    console.log('🔒 Enforcing fullscreen mode...');
    
    // Create blocking overlay
    if (!document.getElementById('fullscreen-blocker')) {
      const blocker = document.createElement('div');
      blocker.id = 'fullscreen-blocker';
      blocker.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(255,0,0,0.95);
        color: white;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        font-size: 24px;
        text-align: center;
      `;
      blocker.innerHTML = `
        <div>⚠️ FULLSCREEN REQUIRED ⚠️</div>
        <div style="font-size: 18px; margin: 20px 0;">
          You must be in fullscreen mode to continue the interview
        </div>
        <button id="enable-fullscreen-btn" style="
          font-size: 16px;
          padding: 10px 20px;
          background: white;
          color: red;
          border: none;
          border-radius: 5px;
          cursor: pointer;
        ">Enable Fullscreen</button>
        <div style="font-size: 14px; margin-top: 20px;">
          Exiting fullscreen will disqualify you from the interview
        </div>
      `;
      
      document.body.appendChild(blocker);
      
      // Add click handler to button
      document.getElementById('enable-fullscreen-btn').addEventListener('click', async () => {
        try {
          const elem = document.documentElement;
          const fullscreenMethod = elem.requestFullscreen || 
                                  elem.mozRequestFullScreen || 
                                  elem.webkitRequestFullscreen || 
                                  elem.msRequestFullscreen;
          
          if (fullscreenMethod) {
            await fullscreenMethod.call(elem);
          }
        } catch (error) {
          console.error('Fullscreen request failed:', error);
        }
      });
    }
    
    // Remove blocker when in fullscreen
    const checkFullscreen = () => {
      if (isDocumentFullScreen()) {
        const blocker = document.getElementById('fullscreen-blocker');
        if (blocker) {
          blocker.remove();
        }
      }
    };
    
    document.addEventListener('fullscreenchange', checkFullscreen);
    document.addEventListener('webkitfullscreenchange', checkFullscreen);
    document.addEventListener('mozfullscreenchange', checkFullscreen);
  };

  // Updated fetchQuestions with fullscreen implementation
  const fetchQuestions = async () => {
    try {
      setLoadingStatus('Requesting questions from AI providers...');
      
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const response = await axios.post('/api/interview/questions', {
        applicationId: id,
        forceRegenerate: true,
        waitForAllProviders: true
      });
      
      if (response.data && response.data.success && response.data.questions) {
        const fetchedQuestions = response.data.questions;
        console.log(`✅ Successfully loaded ${fetchedQuestions.length} questions`);
        
        const validQuestions = fetchedQuestions.filter(q => 
          q && typeof q === 'object' && q.question && 
          ['mcq', 'technical', 'behavioral', 'coding'].includes(q.type)
        );
        
        console.log('Question types:', validQuestions.map((q, i) => `${i}: ${q.type}`));
        
        setQuestions(validQuestions);
        setError('');
        
        setLoadingStatus('Questions loaded successfully');
        
        // Don't request permissions again - just show webcam
        setTimeout(() => {
          console.log('🎥 Starting webcam with existing permissions...');
          setShowWebcam(true);
          
          setTimeout(() => {
            setLoading(false);
            setQuestionStartTime(Date.now());
            
            if (securityManagerRef.current && !securityManagerRef.current.isInterviewActive) {
              setTimeout(() => {
                console.log('🚀 Starting SecurityManager monitoring');
                console.log('SecurityManager state before start:', {
                  exists: !!securityManagerRef.current,
                  isActive: securityManagerRef.current?.isInterviewActive,
                  startTime: securityManagerRef.current?.interviewStartTime
                });
                
                securityManagerRef.current.startInterview();
                
                // CHOOSE ONE OF THESE APPROACHES:
                
                // Option 1: Prompt user to click for fullscreen
                // requestFullscreenWithPrompt();
                
                // Option 2: Multiple attempts with delays
                // setTimeout(() => requestFullscreenMultipleAttempts(), 500);
                
                // Option 3: Enforce fullscreen with blocking overlay (RECOMMENDED)
                setTimeout(() => {
                  if (!isDocumentFullScreen()) {
                    enforceFullscreenMode();
                  }
                }, 1000);
                
              }, 1000);
            }
          }, 2000);
        }, 1000);
      } else {
        throw new Error('No valid questions received');
      }
    } catch (error) {
      console.error('❌ Error fetching questions:', error);
      questionsLoadedRef.current = false;
      setError('Failed to load interview questions. Please refresh and try again.');
      setLoading(false);
    }
  };



//================================================================================================================
//END OF PART 7
//================================================================================================================



// File: pages/candidate/interview-live.js - Part 7: UI Components and Render (FIXED)

  // Recognition mode toggle
  const RecognitionModeToggle = () => (
    <div className="flex items-center mb-2">
      <span className="text-sm mr-2">Mode:</span>
      <button
        onClick={() => setUseOfflineMode(false)}
        className={`px-2 py-1 text-xs rounded mr-1 ${
          !useOfflineMode ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'
        }`}
      >
        Real-time
      </button>
      <button
        onClick={() => setUseOfflineMode(true)}
        className={`px-2 py-1 text-xs rounded ${
          useOfflineMode ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'
        }`}
      >
        Offline
      </button>
    </div>
  );

  // REMOVE Permission modal component - no longer needed
  const PermissionModal = () => {
    // REMOVED - permissions already granted from interview-setup
    return null;
  };

  // SIMPLIFIED: Back to direct WebcamFaceDetection usage
  const webcamComponent = useMemo(() => {
    if (!showWebcam) return null;

    console.log('Creating webcam component with handleCameraUpdate:', typeof handleCameraUpdate);

    return (
      <div className="bg-gray-100 p-3 rounded-lg shadow">
        <h3 className="text-lg font-medium mb-2 text-gray-700">Proctoring Monitor</h3>
        
        {/* BACK TO BASICS: Direct WebcamFaceDetection component */}
        <WebcamFaceDetection 
          ref={webcamRef} 
          onStatusUpdate={handleCameraUpdate}
        />
        
        {/* Camera status indicators */}
        <div className="mt-4 grid grid-cols-2 gap-2">
          <div className={`p-2 rounded text-sm flex items-center ${
            cameraStatus.isDetecting ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            <FaVideo className="mr-1" />
            Camera: {cameraStatus.isDetecting ? 'Active' : 'Inactive'}
          </div>
          
          <div className={`p-2 rounded text-sm flex items-center ${
            cameraStatus.facesDetected === 1 ? 'bg-green-100 text-green-800' : 
            cameraStatus.facesDetected > 1 ? 'bg-red-100 text-red-800' :
            'bg-yellow-100 text-yellow-800'
          }`}>
            <FaEye className="mr-1" />
            Faces: {cameraStatus.facesDetected}
          </div>
          
          <div className={`p-2 rounded text-sm flex items-center ${
            !cameraStatus.lookingAway && cameraStatus.facesDetected === 1 ? 'bg-green-100 text-green-800' : 
            'bg-yellow-100 text-yellow-800'
          }`}>
            <FaEye className="mr-1" />
            Focus: {!cameraStatus.lookingAway && cameraStatus.facesDetected === 1 ? 'Good' : 'Check Position'}
          </div>
          
          <div className={`p-2 rounded text-sm flex items-center ${
            cameraStatus.faceQuality === 'excellent' ? 'bg-green-100 text-green-800' :
            cameraStatus.faceQuality === 'good' ? 'bg-blue-100 text-blue-800' :
            cameraStatus.faceQuality === 'fair' ? 'bg-yellow-100 text-yellow-800' : 
            'bg-red-100 text-red-800'
          }`}>
            <FaCheckCircle className="mr-1" />
            Quality: {cameraStatus.faceQuality || 'Unknown'}
          </div>
        </div>
        
        {/* Response time monitoring */}
        {questionStartTime && (
          <div className="mt-4 p-3 bg-blue-50 rounded border border-blue-200">
            <h4 className="font-medium text-blue-800 mb-2 flex items-center">
              <FaClock className="mr-1" />
              Response Time Monitor
            </h4>
            <div className="text-sm text-blue-700 space-y-1">
              <div className="flex justify-between">
                <span>Time on question:</span>
                <span className="font-mono">
                  {Math.floor((Date.now() - questionStartTime) / 1000)}s
                </span>
              </div>
              {hasInteractedWithQuestion && firstInteractionTime && (
                <div className="flex justify-between">
                  <span>Time to first interaction:</span>
                  <span className="font-mono">
                    {((firstInteractionTime - questionStartTime) / 1000).toFixed(1)}s
                  </span>
                </div>
              )}
              <div className="flex justify-between">
                <span>Expected minimum:</span>
                <span className="font-mono">
                  {RESPONSE_TIME_THRESHOLDS[questions[currentQuestion]?.type] || 10}s
                </span>
              </div>
            </div>
          </div>
        )}
        
        {/* Security status */}
        <div className="mt-4 p-3 bg-gray-50 rounded border border-gray-200">
          <h4 className="font-medium text-gray-800 mb-2 flex items-center">
            <FaShieldAlt className="mr-1" />
            Security Status
          </h4>
          <div className="text-sm text-gray-700 space-y-1">
            <div className="flex justify-between items-center">
              <span>Security System:</span>
              <span className={`font-semibold ${securityInitialized ? 'text-green-600' : 'text-yellow-600'}`}>
                {securityInitialized ? 'Active' : 'Initializing'}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span>Violation Count:</span>
              <span className={`font-semibold ${
                totalViolations === 0 ? 'text-green-600' :
                totalViolations < 3 ? 'text-yellow-600' : 
                totalViolations < 5 ? 'text-orange-600' : 'text-red-600'
              }`}>
                {totalViolations} / 5
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span>Fullscreen:</span>
              <span className={`font-semibold ${isFullScreen ? 'text-green-600' : 'text-red-600'}`}>
                {isFullScreen ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
        </div>
        
        {/* Security violations summary */}
        {totalViolations > 0 && (
          <div className="mt-4 p-3 bg-yellow-50 rounded border border-yellow-200">
            <h4 className="font-medium text-yellow-800 mb-2 flex items-center">
              <FaExclamationTriangle className="mr-1" />
              Security Violations ({totalViolations})
            </h4>
            <div className="text-sm text-yellow-700 space-y-1">
              {Object.entries(securityViolations).map(([type, count]) => 
                count > 0 ? (
                  <div key={type} className="flex justify-between">
                    <span className="capitalize">
                      {type.replace(/([A-Z])/g, ' $1').toLowerCase()}:
                    </span>
                    <span className="font-semibold">{count}</span>
                  </div>
                ) : null
              )}
            </div>
          </div>
        )}
      </div>
    );
  }, [
    showWebcam, 
    cameraStatus, 
    securityViolations, 
    totalViolations, 
    questionStartTime, 
    hasInteractedWithQuestion, 
    firstInteractionTime, 
    questions, 
    currentQuestion, 
    securityInitialized, 
    isFullScreen, 
    handleCameraUpdate
  ]);

  // Security warning banner
  const SecurityWarningBanner = () => {
    if (!showWarning) return null;

    return (
      <div className={`fixed top-0 left-0 w-full p-3 z-50 text-center ${
        showFinalWarning ? 'bg-red-600' : totalViolations >= 4 ? 'bg-orange-600' : 'bg-yellow-600'
      } text-white`}>
        <p className="text-sm font-semibold">
          <FaExclamationTriangle className="inline mr-2" />
          {warningMessage}
        </p>
        {showFinalWarning && (
          <p className="text-xs mt-1">
            This is your final warning. One more violation will end the interview.
          </p>
        )}
      </div>
    );
  };

// UPDATED: Disqualification overlay with working button and reference to 6 violations (after 5 warnings)
const DisqualificationOverlay = () => {
  if (!disqualified) return null;

  return (
    <div className="fixed inset-0 bg-red-600 text-white flex flex-col items-center justify-center z-50 p-8">
      <FaExclamationTriangle className="text-6xl mb-4" />
      <h2 className="text-3xl font-bold mb-4">INTERVIEW DISQUALIFIED</h2>
      <p className="text-xl mb-8">{disqualificationReason}</p>
      <div className="bg-red-700 p-4 rounded mb-4 text-center max-w-2xl">
        <p className="mb-2">Maximum security violations reached (6 violations)</p>
        <p className="mb-2">Common violations include:</p>
        <ul className="text-sm text-left">
          <li>• Tab switching or window switching</li>
          <li>• Exiting fullscreen mode</li>
          <li>• Using developer tools</li>
          <li>• Multiple faces in camera</li>
          <li>• Looking away from camera</li>
          <li>• Suspiciously fast responses</li>
          <li>• Using keyboard shortcuts</li>
          <li>• Taking screenshots</li>
        </ul>
      </div>
      <button 
        onClick={() => {
          console.log('🚪 Returning to dashboard from disqualification...');
          
          // Clean up first
          if (securityManagerRef.current) {
            securityManagerRef.current.endInterview();
          }
          turnOffCamera();
          
          // Set exiting flag to prevent violations during exit
          exitingRef.current = true;
          if (securityManagerRef.current) {
            securityManagerRef.current.setExiting(true);
          }
          
          // Exit fullscreen
          exitFullscreen();
          
          // Navigate to dashboard immediately
          router.push('/candidate-dashboard');
        }}
        className="bg-white text-red-600 px-6 py-3 rounded font-bold hover:bg-gray-100"
        type="button"
      >
        Return to Dashboard
      </button>
    </div>
  );
};


  // Loading state
  if (loading || showPermissionModal) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <PermissionModal />
        
        {loading && (
          <div className="bg-white p-8 rounded-lg shadow-md text-center max-w-md">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <h2 className="text-xl font-bold mb-2">Loading Interview</h2>
            <p className="text-gray-600 mb-4">{loadingStatus}</p>
            
            {error && (
              <div className="bg-red-50 p-3 rounded border border-red-200 mb-4 text-left">
                <p className="text-red-700">{error}</p>
                <button 
                  onClick={() => {
                    questionsLoadedRef.current = false;
                    window.location.reload();
                  }}
                  className="mt-2 bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700"
                >
                  Refresh Page
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    );
  }

  // Main interview component
  const currentQ = questions[currentQuestion];

  return (
    <div className="min-h-screen bg-white text-black">
      <SecurityWarningBanner />
      <DisqualificationOverlay />
      
      {/* Header bar */}
      <div className="flex justify-between items-center px-6 py-4 bg-blue-600 text-white">
        <div className="flex items-center">
          <FaLock className={`mr-2 ${isFullScreen ? 'text-green-500' : 'text-red-500'}`} />
          <h1 className="text-xl font-semibold">Proctored Interview</h1>
          {!isFullScreen && (
            <button 
              onClick={requestFullscreen}
              className="ml-2 bg-yellow-500 text-xs px-2 py-1 rounded hover:bg-yellow-600"
            >
              Enter Fullscreen
            </button>
          )}
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <FaVideo className={`mr-1 ${cameraStatus.facesDetected > 0 ? 'text-green-500' : 'text-red-500'}`} />
            <span className="text-white text-sm">Camera: {cameraStatus.isDetecting ? 'Active' : 'Inactive'}</span>
          </div>
          
          <div className="flex items-center">
            <FaMicrophone className={`mr-1 ${isListening ? 'text-green-500 animate-pulse' : 'text-white'}`} />
            <span className="text-white text-sm">Audio: {isListening ? 'Recording' : 'Ready'}</span>
          </div>
          
          <div className="flex items-center">
            <FaShieldAlt className={`mr-1 ${securityInitialized ? 'text-green-500' : 'text-red-500'}`} />
            <span className="text-white text-sm">Security: {securityInitialized ? 'Active' : 'Loading'}</span>
          </div>
          
          <div className="flex items-center">
            <FaClock className={`mr-1 text-white`} />
            <span className="text-white text-sm">
              Time: {questionStartTime ? Math.floor((Date.now() - questionStartTime) / 1000) : 0}s
            </span>
          </div>
          
          <div className="bg-blue-800 rounded px-3 py-1 text-sm">
            Question {currentQuestion + 1} of {questions?.length || 0}
          </div>
          
          {/* ENHANCED: Exit button with custom confirmation that doesn't exit fullscreen */}
          <button
            onClick={async (e) => {
              e.preventDefault();
              e.stopPropagation();
              
              console.log('🚪 Exit button clicked');
              
              // Show custom confirmation that stays in fullscreen
              const confirmed = await showExitConfirmation();
              
              if (confirmed) {
                console.log('🚪 Exiting interview confirmed...');
                
                // Set exiting flag to prevent violations
                exitingRef.current = true;
                if (securityManagerRef.current) {
                  securityManagerRef.current.setExiting(true);
                }
                
                const applicationId = appIdRef.current || id;
                
                // Record response time for current question
                if (questionStartTime && questions[currentQuestion]) {
                  recordResponseTime(currentQuestion, questions[currentQuestion].type);
                }
                
                // Notify backend about abort
                try {
                  axios.post('/api/interview/abort', {
                    applicationId,
                    responseTimeData,
                    suspiciousResponseTimes
                  }).catch(error => console.error('Error aborting interview:', error));
                } catch (error) {
                  console.error('Error aborting interview:', error);
                }
                
                // Stop recording
                stopRecording();
                
                // Cancel speech synthesis
                if (window.speechSynthesis) {
                  window.speechSynthesis.cancel();
                }
                
                // End security monitoring
                if (securityManagerRef.current) {
                  try {
                    securityManagerRef.current.endInterview();
                  } catch (error) {
                    console.error('Error ending security monitoring:', error);
                  }
                }
                
                // Turn off camera
                turnOffCamera();
                
                // Exit fullscreen
                exitFullscreen();
                
                // Navigate to dashboard
                console.log('🚪 Navigating to dashboard...');
                router.push('/candidate-dashboard');
              } else {
                console.log('🚪 Exit canceled, continuing interview');
              }
            }}
            className="bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700 flex items-center"
            type="button"
          >
            <FaSignOutAlt className="inline mr-1" /> Exit
          </button>
        </div>
      </div>

      {error && !loading && !showPermissionModal && (
        <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4">
          <p className="font-medium">Notice</p>
          <p>{error}</p>
        </div>
      )}
      
      {/* Main content */}
      <div className="flex flex-col md:flex-row p-6 gap-8">
        {/* Left column - Webcam */}
        <div className="w-full md:w-1/3">
          {cameraPermission === 'granted' && microphonePermission === 'granted' ? (
            webcamComponent || (
              <div className="bg-gray-100 p-6 rounded-lg shadow text-center">
                <div className="animate-pulse">
                  <div className="h-32 bg-gray-300 rounded mb-4"></div>
                  <div className="h-4 bg-gray-300 rounded w-3/4 mx-auto"></div>
                </div>
                <p className="mt-4">Initializing camera...</p>
              </div>
            )
          ) : (
            <div className="bg-gray-100 p-6 rounded-lg shadow text-center">
              <FaVideo className="text-4xl text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 mb-4">Camera access required</p>
              <button
                onClick={() => setShowPermissionModal(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
              >
                Grant Camera Access
              </button>
            </div>
          )}
        </div>

        {/* Right column - Questions */}
        <div className="w-full md:w-2/3">
          {questions?.length ? (
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="mb-4 flex justify-between items-center">
                <h3 className="text-lg font-semibold">Question {currentQuestion + 1} of {questions.length}</h3>
                <div className="flex items-center">
                  <span className="text-sm text-gray-500 mr-3">
                    {currentQ.type === 'mcq' ? 'Multiple Choice' : 
                     currentQ.type === 'technical' ? 'Technical Question' :
                     currentQ.type === 'behavioral' ? 'Behavioral Question' :
                     currentQ.type === 'coding' ? 'Coding Question' :
                     'Interview Question'}
                  </span>
                  <button
                    onClick={speakQuestion}
                    disabled={isSpeaking}
                    className={`ml-2 p-2 rounded-full ${
                     isSpeaking ? 'bg-gray-200 text-gray-500' : 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                    }`}
                    title="Read question aloud"
                  >
                    <FaVolumeUp />
                  </button>
                </div>
              </div>
              
              <div className="mb-4">
                <p className="font-medium text-lg mb-4">{currentQ?.question || 'Loading question...'}</p>
              </div>
              
              {/* MCQ Questions */}
              {currentQ?.type === 'mcq' && (
                <div className="space-y-2 mb-4">
                  {currentQ.options?.map((opt, i) => (
                    <label key={i} className={`block border p-3 rounded hover:bg-gray-100 cursor-pointer transition-colors ${
                      answers[currentQuestion] === opt ? 'border-blue-500 bg-blue-50' : ''
                    }`}>
                      <input
                        type="radio"
                        name={`question-${currentQuestion}`}
                        value={opt}
                        checked={answers[currentQuestion] === opt}
                        onChange={() => handleAnswerChange(opt)}
                        className="mr-3"
                        data-allow-select
                      />
                      {opt}
                    </label>
                  ))}
                </div>
              )}
              
              {/* FIXED: Behavioral/Technical Questions - Use question-specific transcript state */}
              {['behavioral', 'technical'].includes(currentQ?.type) && (
                <div className="mt-4">
                  <div className="flex justify-between items-center mb-3">
                    <h4 className="font-medium">Your Answer</h4>
                    <div className="flex flex-col">
                      <RecognitionModeToggle />
                      <div className="flex gap-2">
                        <button
                          onClick={() => stopRecording()}
                          className={`px-3 py-1 text-sm rounded ${
                            !isListening ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'
                          }`}
                        >
                          Written
                        </button>
                        
                        <button
                          onClick={() => isListening ? stopRecording() : startRecording()}
                          className={`px-3 py-1 text-sm rounded flex items-center ${
                            isListening ? 'bg-red-600 text-white' : 'bg-green-600 text-white'
                          }`}
                        >
                          {isListening ? 
                            <>
                              <FaStop className="mr-1" /> Stop Recording
                            </> : 
                            <>
                              <FaMicrophone className="mr-1" /> Record Answer
                            </>
                          }
                        </button>
                      </div>
                    </div>
                  </div>
                  
                  {/* FIXED: Use question-specific answer instead of shared transcript */}
                  <textarea 
                    rows={6} 
                    className="w-full border rounded p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none" 
                    placeholder="Type your answer or use speech recognition"
                    value={answers[currentQuestion] || ''}
                    onChange={(e) => {
                      handleAnswerChange(e.target.value);
                      trackFirstInteraction();
                    }}
                    disabled={isProcessing}
                    data-allow-select
                    // REMOVED: data-allow-clipboard - this will block external paste
                  ></textarea>
                  
                  {/* Recording status */}
                  {isListening && (
                    <div className="bg-gray-50 p-3 rounded-lg border mt-3">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-red-500 animate-pulse mr-2"></div>
                        <p className="text-gray-700 text-sm flex-1">
                          {`Recording... (${formatRecordingTime(recordingTime)})`}
                        </p>
                        
                        <button 
                          onClick={stopRecording}
                          className="text-xs bg-red-50 text-red-500 px-2 py-1 rounded hover:bg-red-100"
                          disabled={isProcessing}
                        >
                          {isProcessing ? 'Processing...' : 'Stop'}
                        </button>
                      </div>
                      
                      {interimResult && (
                        <div className="mt-2 text-gray-500 italic text-sm">
                          {interimResult}
                        </div>
                      )}
                      
                      {isProcessing && (
                        <div className="flex items-center mt-2 text-blue-600 text-xs">
                          <div className="animate-spin h-3 w-3 border-2 border-blue-600 border-t-transparent rounded-full mr-2"></div>
                          Processing your answer...
                        </div>
                      )}
                    </div>
                  )}
                  
                  {/* FIXED: Show transcribed text as current answer being built */}
                  {transcript && (
                    <div className="mt-3 p-3 bg-blue-50 rounded border border-blue-200">
                      <h5 className="text-sm font-medium text-blue-800 mb-2">Transcribed Text:</h5>
                      <p className="text-sm text-blue-700">{transcript}</p>
                      <button
                        onClick={() => {
                          // Update the current question's answer with transcript
                          handleAnswerChange(transcript);
                          setTranscript(''); // Clear transcript after using
                        }}
                        className="mt-2 text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700"
                      >
                        Use This Answer
                      </button>
                    </div>
                  )}
                </div>
              )}
              
              {/* Coding Questions */}
              {currentQ?.type === 'coding' && (
                <div className="mt-4">
                  <div className="bg-gray-800 text-white p-2 rounded-t flex justify-between items-center">
                    <div className="flex items-center">
                      <FaCode className="mr-2" />
                      <span>Code Editor (JavaScript)</span>
                    </div>
                  </div>
                  
                  <CodeEditor
                    language="javascript"
                    value={code}
                    onChange={(newValue) => {
                      setCode(newValue);
                      trackFirstInteraction();
                    }}
                    height="400px"
                  />
                  
                  <div className="flex mt-2">
                    <button
                      onClick={runCode}
                      disabled={isRunningCode}
                      className={`px-4 py-2 rounded ${
                        isRunningCode ? 'bg-gray-400' : 'bg-green-600 hover:bg-green-700'
                      } text-white`}
                    >
                      {isRunningCode ? 'Running...' : 'Run Code'}
                    </button>
                  </div>
                  
                  {codeOutput && (
                    <div className="mt-4 p-4 bg-gray-800 text-white rounded overflow-auto max-h-60">
                      <div className="text-sm text-gray-400 mb-2">Output:</div>
                      <pre className="whitespace-pre-wrap">{codeOutput}</pre>
                    </div>
                  )}
                </div>
              )}
              
              {/* Navigation buttons */}
              <div className="mt-8 flex justify-between">
                <button
                  onClick={handlePrev}
                  disabled={currentQuestion === 0 || isProcessing}
                  className={`px-4 py-2 rounded ${
                    currentQuestion === 0 || isProcessing ? 'bg-gray-300 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'
                  } text-white`}
                >
                  Previous
                </button>
                
                {currentQuestion < questions.length - 1 ? (
                  <button
                    onClick={handleNext}
                    disabled={isProcessing}
                    className="px-4 py-2 rounded bg-green-600 hover:bg-green-700 text-white"
                  >
                    Next
                  </button>
                ) : (
                  <button
                    onClick={handleSubmit}
                    disabled={isProcessing}
                    className="px-4 py-2 rounded bg-green-600 hover:bg-green-700 text-white"
                  >
                    Submit Interview
                  </button>
                )}
              </div>
            </div>
          ) : (
            <div className="bg-white p-6 rounded-lg shadow text-center">
              <p className="text-gray-600">No questions available for this interview.</p>
              <button 
                onClick={async () => {
                  // Show exit confirmation instead of immediately exiting
                  const confirmed = await showExitConfirmation();
                  
                  if (confirmed) {
                    if (securityManagerRef.current) {
                      securityManagerRef.current.setExiting(true);
                    }
                    exitingRef.current = true;
                    
                    // Turn off camera
                    turnOffCamera();
                    
                    // Exit fullscreen
                    exitFullscreen();
                    
                    router.push('/candidate-dashboard');
                  }
                }}
                className="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
                type="button"
              >
                Return to Dashboard
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
