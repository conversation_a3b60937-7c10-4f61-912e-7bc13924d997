// File: pages/candidate/interview-setup.js

import { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/router';

export default function InterviewSetup() {
  const router = useRouter();
  const { id } = router.query;

  const [acknowledged, setAcknowledged] = useState(false);
  const [mediaAccess, setMediaAccess] = useState(false);
  const [previewPassed, setPreviewPassed] = useState(false);
  const [micVerified, setMicVerified] = useState(false);
  const [micDetectedOnce, setMicDetectedOnce] = useState(false);
  const [mediaError, setMediaError] = useState(false);
  const [isElectron, setIsElectron] = useState(false);
  const [loadingStatus, setLoadingStatus] = useState('');

  const webcamRef = useRef(null);
  const canvasRef = useRef(null);
  const mediaStreamRef = useRef(null);

  useEffect(() => {
    // Set loading status
    setLoadingStatus('Checking environment...');
    
    // Check if running in Electron
    const isRunningInElectron = 
      navigator.userAgent.toLowerCase().includes('electron') || 
      (window.electronAPI && typeof window.electronAPI.isElectron === 'function');
    
    setIsElectron(isRunningInElectron);
    
    if (!isRunningInElectron) {
      setLoadingStatus('Redirecting to standalone app...');
      alert('Please use the secure ArisIQ desktop app to take the interview.');
      window.location.href = '/';
    } else {
      console.log('✅ Running in Electron environment');
      setLoadingStatus('Loading face detection models...');
    }
  }, []);

  useEffect(() => {
    if (!id || !router.isReady) {
      return;
    }
    
    if (!id || id.length !== 24) {
      console.error("❌ Invalid or missing application ID:", id);
      setLoadingStatus('Error: Invalid application ID');
      return;
    }
    
    setLoadingStatus('Loading face detection models...');
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/@vladmandic/face-api/dist/face-api.min.js';
    script.onload = async () => {
      setLoadingStatus('Setting up camera preview...');
      await setupPreview();
    };
    document.body.appendChild(script);
  }, [id, router.isReady]);

  const setupPreview = async () => {
    try {
      setLoadingStatus('Loading face detection models...');
      await Promise.all([
        window.faceapi.nets.ssdMobilenetv1.loadFromUri('/models'),
        window.faceapi.nets.faceLandmark68TinyNet.loadFromUri('/models')
      ]);
      
      setLoadingStatus('Requesting camera access...');
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { width: 640, height: 480 }, 
        audio: true 
      });
      
      mediaStreamRef.current = stream;
      if (webcamRef.current) {
        webcamRef.current.srcObject = stream;
        webcamRef.current.play();
        webcamRef.current.onloadedmetadata = () => {
          setTimeout(() => {
            if (canvasRef.current && webcamRef.current) {
              canvasRef.current.width = webcamRef.current.videoWidth;
              canvasRef.current.height = webcamRef.current.videoHeight;
              setLoadingStatus('Starting face detection...');
              detectFace();
            }
          }, 500);
        };
      }
      setMediaAccess(true);
      setMediaError(false);
      setLoadingStatus('Checking microphone...');
      monitorMic(stream);
    } catch (err) {
      console.error("❌ Media access failed:", err);
      setLoadingStatus(`Error: ${err.message}`);
      alert("Could not access camera/mic: " + err.message);
      setMediaAccess(false);
      setMediaError(true);
    }
  };

  const monitorMic = (stream) => {
    try {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const source = audioContext.createMediaStreamSource(stream);
      const analyser = audioContext.createAnalyser();
      analyser.fftSize = 256;
      const dataArray = new Uint8Array(analyser.fftSize);
      source.connect(analyser);

      const updateVolume = () => {
        analyser.getByteTimeDomainData(dataArray);
        let sum = 0;
        for (let i = 0; i < dataArray.length; i++) {
          const val = (dataArray[i] - 128) / 128;
          sum += val * val;
        }
        const rms = Math.sqrt(sum / dataArray.length);
        const volume = parseFloat((rms * 100).toFixed(2));
        
        // Add a log to debug
        if (volume > 0.2) {
          console.log(`Mic level detected: ${volume}`);
        }
        
        // Lower the threshold for detection to make it easier to verify
        if (volume > 0.2 && !micDetectedOnce) {
          console.log('Microphone verified successfully!');
          setMicDetectedOnce(true);
          setMicVerified(true);
          setLoadingStatus('All checks complete. Ready to start.');
        }
        
        if (!micVerified) {
          requestAnimationFrame(updateVolume);
        }
      };
      updateVolume();
    } catch (err) {
      console.error("Error setting up audio monitoring:", err);
      // Don't block the process if audio monitoring fails
      setMicVerified(true);
    }
  };

  const detectFace = () => {
    const detect = async () => {
      if (!webcamRef.current || !canvasRef.current || !window.faceapi) return;
      const video = webcamRef.current;
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      
      try {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        
        const detection = await window.faceapi
          .detectSingleFace(video, new window.faceapi.SsdMobilenetv1Options())
          .withFaceLandmarks(true);
          
        if (detection) {
          const landmarks = detection.landmarks;
          const nose = landmarks.getNose();
          const leftEye = landmarks.getLeftEye();
          const rightEye = landmarks.getRightEye();
          
          const noseX = nose.reduce((sum, p) => sum + p.x, 0) / nose.length;
          const leftEyeX = leftEye.reduce((sum, p) => sum + p.x, 0) / leftEye.length;
          const rightEyeX = rightEye.reduce((sum, p) => sum + p.x, 0) / rightEye.length;
          
          // Increase the buffer to make it easier to pass
          const buffer = 10;
          const direction = (noseX < leftEyeX - buffer) ? 'right' :
                          (noseX > rightEyeX + buffer) ? 'left' :
                          'straight';
                          
          // Add some debugging
          console.log(`Face detected, direction: ${direction}`);
          
          // Update preview passed status
          setPreviewPassed(direction === 'straight');
          
          // Draw visual indication on canvas
          ctx.strokeStyle = direction === 'straight' ? 'green' : 'red';
          ctx.lineWidth = 3;
          ctx.strokeRect(detection.detection.box.x, detection.detection.box.y, 
                        detection.detection.box.width, detection.detection.box.height);
        } else {
          console.log('No face detected');
          setPreviewPassed(false);
        }
      } catch (err) {
        console.error("Face detection error:", err);
      }
    };
    
    // Run detection every second
    const interval = setInterval(detect, 1000);
    
    // Store interval ID for cleanup
    return () => clearInterval(interval);
  };

  const handleStart = async () => {
    if (!acknowledged || !mediaAccess || !previewPassed || !micVerified) return;
    
    setLoadingStatus('Starting interview...');
    
    // Stop the media stream before navigating
    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(track => track.stop());
    }
    
    // Use Electron API if available to start interview
    if (isElectron && window.electronAPI && window.electronAPI.startInterview) {
      console.log("🚀 Starting interview via Electron API");
      try {
        await window.electronAPI.startInterview(id);
        console.log("Interview started successfully");
      } catch (err) {
        console.error("Error starting interview:", err);
        setLoadingStatus('Error starting interview. Please refresh and try again.');
        
        // Fallback to regular navigation
        console.log("Falling back to regular navigation");
        router.push(`/candidate/interview-live?id=${id}`);
      }
    } else {
      // Fallback to regular navigation
      console.log("🚀 Starting interview via regular navigation");
      router.push(`/candidate/interview-live?id=${id}`);
    }
  };

  return (
    <div className="p-6 text-center">
      <h2 className="text-xl font-bold mb-4">Interview Setup</h2>
      
      {loadingStatus && (
        <div className="mb-4 p-2 bg-blue-100 text-blue-800 rounded">
          <p>{loadingStatus}</p>
        </div>
      )}
      
      <div className="flex justify-center">
        <div className="relative w-full max-w-md">
          {!mediaAccess && !mediaError && (
            <div className="bg-gray-200 h-48 rounded flex items-center justify-center">
              <p>Requesting camera access...</p>
            </div>
          )}
          
          <video
            ref={webcamRef}
            autoPlay
            muted
            playsInline
            className="rounded border w-full"
            style={{ display: mediaAccess ? 'block' : 'none' }}
          />
          <canvas 
            ref={canvasRef} 
            className="absolute top-0 left-0 w-full h-full"
            style={{ display: mediaAccess ? 'block' : 'none' }}
          />
        </div>
      </div>
      
      <div className="mt-4">
        <h3 className="font-semibold mb-2">System Checks</h3>
        <ul className="mb-4 text-sm text-left max-w-md mx-auto">
          <li className={previewPassed ? "text-green-600" : "text-red-600"}>
            {previewPassed ? '✅' : '❌'} Face Position: {previewPassed ? "Correctly positioned" : "Please look directly at the camera"}
          </li>
          <li className={micVerified ? "text-green-600" : "text-red-600"}>
            {micVerified ? '✅' : '❌'} Microphone: {micVerified ? "Working properly" : "Please speak to test your microphone"}
          </li>
          <li className={acknowledged ? "text-green-600" : "text-red-600"}>
            {acknowledged ? '✅' : '❌'} Rules: {acknowledged ? "Acknowledged" : "Please read and acknowledge the rules below"}
          </li>
        </ul>
        
        {mediaError && (
          <div className="bg-red-100 text-red-800 p-3 rounded mb-4">
            <p>⚠️ Please allow camera and microphone access to continue.</p>
            <button 
              onClick={setupPreview}
              className="mt-2 px-3 py-1 bg-red-600 text-white rounded text-sm"
            >
              Try Again
            </button>
          </div>
        )}
        
        <div className="bg-gray-100 p-4 rounded mb-4 text-left max-w-md mx-auto">
          <h3 className="font-semibold mb-2">Interview Rules:</h3>
          <ol className="list-decimal pl-5 text-sm">
            <li>Ensure you're in a quiet, well-lit environment</li>
            <li>Keep your face visible to the camera at all times</li>
            <li>Do not access other applications during the interview</li>
            <li>Do not attempt to leave fullscreen mode</li>
            <li>Complete all questions within the allotted time</li>
          </ol>
        </div>
        
        <label className="inline-flex items-center cursor-pointer">
          <input 
            type="checkbox" 
            className="form-checkbox h-5 w-5 text-blue-600" 
            checked={acknowledged} 
            onChange={e => setAcknowledged(e.target.checked)} 
          />
          <span className="ml-2">I acknowledge the rules and confirm my audio/video are working properly</span>
        </label>
        
        <div className="mt-6">
          <button
            onClick={handleStart}
            disabled={!(acknowledged && previewPassed && micVerified)}
            className={`px-6 py-2 rounded font-semibold ${
              acknowledged && previewPassed && micVerified 
                ? 'bg-green-600 text-white hover:bg-green-700' 
                : 'bg-gray-400 text-gray-100 cursor-not-allowed'
            }`}
          >
            Start Interview
          </button>
        </div>
      </div>
    </div>
  );
}
