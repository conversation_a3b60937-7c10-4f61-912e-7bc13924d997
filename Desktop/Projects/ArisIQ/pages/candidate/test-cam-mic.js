// File: pages/candidate/test-cam-mic.js

import { useEffect, useRef, useState } from 'react';

export default function TestCamMic() {
  const webcamRef = useRef(null);
  const canvasRef = useRef(null);
  const [micVolume, setMicVolume] = useState(0);
  const [faceAlert, setFaceAlert] = useState('');
  const [micAlert, setMicAlert] = useState('');
  const [modalMessage, setModalMessage] = useState('');
  const lastFaceSeenTimestamp = useRef(Date.now());
  const faceOffscreenDuration = useRef(0);
  const [questions, setQuestions] = useState([]);

  const logCheat = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    console.warn(`[${timestamp}] ${message}`);
  };

  useEffect(() => {
    const loadFaceAPI = async () => {
      if (!window.faceapiLoaded) {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/@vladmandic/face-api/dist/face-api.min.js';
        script.async = true;
        script.onload = async () => {
          try {
            await window.faceapi.nets.ssdMobilenetv1.loadFromUri('/models');
            await window.faceapi.nets.faceLandmark68TinyNet.loadFromUri('/models');
            window.faceapiLoaded = true;
            console.log('✅ face-api loaded');
            startVideo();
            startMicMonitor();
          } catch (err) {
            console.error('❌ Error loading face-api model:', err);
          }
        };
        document.body.appendChild(script);
      } else {
        await window.faceapi.nets.ssdMobilenetv1.loadFromUri('/models');
        await window.faceapi.nets.faceLandmark68TinyNet.loadFromUri('/models');
        window.faceapiLoaded = true;
        console.log('✅ face-api already loaded');
        startVideo();
        startMicMonitor();
      }
    };

    const startVideo = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ video: { width: 480, height: 360 }, audio: true });
        webcamRef.current.srcObject = stream;
        webcamRef.current.onloadedmetadata = () => {
          webcamRef.current.play().then(() => {
            const { videoWidth, videoHeight } = webcamRef.current;
            canvasRef.current.width = videoWidth;
            canvasRef.current.height = videoHeight;
            detectFaces();
          });
        };
      } catch (err) {
        console.error('❌ Could not access webcam:', err);
      }
    };

    const detectFaces = () => {
      const detect = async () => {
        if (!webcamRef.current || !canvasRef.current || !window.faceapiLoaded) return;

        const video = webcamRef.current;
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');

        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        const pixelData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const avgBrightness = pixelData.data.reduce((acc, val, idx) => idx % 4 === 0 ? acc + val : acc, 0) / (canvas.width * canvas.height);

        if (avgBrightness < 40) {
          setFaceAlert('⚠️ Low lighting conditions');
          logCheat('Low light environment');
        }

        const detections = await window.faceapi
          .detectSingleFace(video, new window.faceapi.SsdMobilenetv1Options())
          .withFaceLandmarks(true);

        ctx.clearRect(0, 0, canvas.width, canvas.height);
        const dims = window.faceapi.matchDimensions(canvas, video, true);

        if (detections) {
          const resized = window.faceapi.resizeResults(detections, dims);
          window.faceapi.draw.drawDetections(canvas, [resized]);

          faceOffscreenDuration.current = 0;
          lastFaceSeenTimestamp.current = Date.now();

          const landmarks = resized.landmarks;
          const leftEye = landmarks.getLeftEye();
          const rightEye = landmarks.getRightEye();
          const nose = landmarks.getNose();
          const jaw = landmarks.getJawOutline();

          const avgX = (ptArr) => ptArr.reduce((sum, pt) => sum + pt.x, 0) / ptArr.length;
          const avgY = (ptArr) => ptArr.reduce((sum, pt) => sum + pt.y, 0) / ptArr.length;

          const eyeCenterX = (avgX(leftEye) + avgX(rightEye)) / 2;
          const noseX = avgX(nose);
          const jawY = avgY(jaw);
          const noseY = avgY(nose);

          const xDiff = noseX - eyeCenterX;
          const yDiff = jawY - noseY;

          if (Math.abs(xDiff) > 12) {
            setFaceAlert(`⚠️ Looking ${xDiff > 0 ? 'right' : 'left'}`);
            logCheat(`Looking ${xDiff > 0 ? 'right' : 'left'}`);
          } else if (yDiff > 35) {
            setFaceAlert('⚠️ Looking down');
            logCheat('Looking down');
          } else {
            setFaceAlert('✅ One face detected & looking straight');
          }
        } else {
          faceOffscreenDuration.current += 2;

          if (faceOffscreenDuration.current >= 6) {
            setFaceAlert('⚠️ No face detected');
            logCheat('No face detected');
            faceOffscreenDuration.current = 0;
          }
        }
      };
      setInterval(detect, 2000);
    };

    const startMicMonitor = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const source = audioContext.createMediaStreamSource(stream);
        const analyser = audioContext.createAnalyser();
        analyser.fftSize = 512;
        const dataArray = new Uint8Array(analyser.fftSize);
        source.connect(analyser);

        const updateVolume = () => {
          try {
            analyser.getByteTimeDomainData(dataArray);
            let sum = 0;
            for (let i = 0; i < dataArray.length; i++) {
              const val = (dataArray[i] - 128) / 128;
              sum += val * val;
            }
            const rms = Math.sqrt(sum / dataArray.length);
            const volume = (rms * 100).toFixed(2);
            setMicVolume(volume);

            const isVoice = volume > 3.0;
            setMicAlert(isVoice ? '✅ Voice detected' : '🔇 Silent / Low noise');
          } catch (e) {
            console.error('🎙️ Mic volume update error:', e);
          }
          requestAnimationFrame(updateVolume);
        };

        updateVolume();
      } catch (err) {
        console.error('❌ Could not access mic:', err);
      }
    };

    const fetchQuestions = async () => {
      try {
        const response = await fetch('/api/interview/questions/test');
        const data = await response.json();
        if (data.success && data.questions) {
          setQuestions(data.questions);
        }
      } catch (error) {
        console.error('Error fetching questions:', error);
      }
    };

    fetchQuestions();
    loadFaceAPI();
  }, []);

  return (
    <div className="min-h-screen p-4 bg-gray-100 max-w-sm mx-auto">
      <h1 className="text-2xl font-bold mb-4">🎥 AI Cam & Mic Monitor Test</h1>
      <div className="relative mb-4 w-full max-w-xs mx-auto aspect-video">
        <video
          ref={webcamRef}
          autoPlay
          muted
          playsInline
          className="absolute top-0 left-0 w-full h-full object-contain border border-gray-400 rounded shadow-sm"
        />
        <canvas
          ref={canvasRef}
          className="absolute top-0 left-0 w-full h-full z-10"
        />
      </div>
      <p className="text-gray-700">🎙️ Mic volume: <span className="font-mono text-blue-700">{micVolume}</span></p>
      <p className="text-sm font-medium text-green-700 mt-1">{micAlert}</p>
      <p className={`mt-2 text-sm font-medium ${faceAlert.includes('⚠️') ? 'text-yellow-600' : faceAlert.includes('✅') ? 'text-green-700' : 'text-red-600'}`}>{faceAlert}</p>

      {questions.length > 0 && (
        <div className="mt-6">
          <h2 className="text-lg font-semibold text-blue-800 mb-2">🧠 Sample Interview Questions:</h2>
          <ul className="list-decimal text-sm text-gray-800 pl-4 space-y-1">
            {questions.map((q, idx) => (
              <li key={idx}>{q.question}</li>
            ))}
          </ul>
        </div>
      )}

      {modalMessage && (
        <div className="bg-red-100 text-red-900 p-3 rounded mt-4 shadow-md text-sm">{modalMessage}</div>
      )}
    </div>
  );
}

