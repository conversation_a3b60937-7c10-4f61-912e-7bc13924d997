// File: pages/candidate/[id].js

import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useSession } from 'next-auth/react';

export default function JobDetail() {
  const router = useRouter();
  const { id, tab } = router.query;
  const [job, setJob] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [application, setApplication] = useState(null);
  const { data: session, status } = useSession();

  useEffect(() => {
    if (!id) return;

    setLoading(true);
    
    // Fetch job details
    fetch(`/api/jobs/${id}`)
      .then(res => {
        if (!res.ok) throw new Error("Failed to fetch job details");
        return res.json();
      })
      .then(data => {
        console.log("Fetched job data:", data);
        setJob(data.job);
        
        // If user is logged in and we're not in browse mode, check if they've applied
        if (session?.user?.email && tab !== 'browse') {
          return fetch(`/api/candidate/applications?email=${encodeURIComponent(session.user.email)}&t=${new Date().getTime()}`)
            .then(res => {
              if (!res.ok) throw new Error("Failed to fetch applications");
              return res.json();
            });
        }
      })
      .then(appData => {
        if (appData && appData.applications) {
          // Find if user has applied to this job
          const userApp = appData.applications.find(app => 
            app.jobId === id || app.jobId.toString() === id.toString()
          );
          if (userApp) {
            setApplication(userApp);
          }
        }
      })
      .catch(err => {
        console.error("Error:", err);
        setError(err.message);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [id, session, tab]);

  if (loading) {
    return <div className="max-w-3xl mx-auto p-6 text-center">Loading job details...</div>;
  }

  if (error) {
    return (
      <div className="max-w-3xl mx-auto p-6 text-center">
        <p className="text-red-600">Error: {error}</p>
        <button onClick={() => router.back()} className="mt-4 px-4 py-2 bg-blue-600 text-white rounded">
          Go Back
        </button>
      </div>
    );
  }

  if (!job) return <div className="max-w-3xl mx-auto p-6 text-center">Job not found.</div>;

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-4">
        <Link href="/candidate-dashboard">
          <span className="text-blue-600 hover:underline">← Back to Dashboard</span>
        </Link>
      </div>

      <div className="bg-white p-6 rounded shadow-md">
        <h1 className="text-3xl font-bold text-blue-700 mb-2">{job.title}</h1>
        <p className="text-gray-600 text-lg mb-4">{job.company} - {job.location}</p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="border-b pb-2">
            <span className="font-medium">Industry:</span> {job.industry || "Not specified"}
          </div>
          <div className="border-b pb-2">
            <span className="font-medium">Salary:</span> {job.salary || "Not specified"}
          </div>
          <div className="border-b pb-2">
            <span className="font-medium">Experience Required:</span> {job.experience || "Not specified"}
          </div>
          <div className="border-b pb-2">
            <span className="font-medium">Employment Type:</span> {job.employmentType || "Full-time"}
          </div>
          <div className="border-b pb-2">
            <span className="font-medium">Work Mode:</span> {job.workMode || "Not specified"}
          </div>
          <div className="border-b pb-2">
            <span className="font-medium">Visa Sponsorship:</span> <strong>{job.visaSponsorship === 'yes' ? 'Yes' : 'No'}</strong>
          </div>
          <div className="border-b pb-2">
            <span className="font-medium">Application Deadline:</span> {job.applicationDeadline ? new Date(job.applicationDeadline).toLocaleDateString() : "Not specified"}
          </div>
          <div className="border-b pb-2">
            <span className="font-medium">Company Website:</span> {job.website ? (
              <a href={job.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                {job.website}
              </a>
            ) : "Not specified"}
          </div>
        </div>

        {/* Required Skills Section - Moved above Job Description */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Required Skills</h2>
          {job.requiredSkills && job.requiredSkills.length > 0 ? (
            <ul className="list-disc list-inside">
              {Array.isArray(job.requiredSkills) 
                ? job.requiredSkills.map((skill, i) => <li key={i}>{skill}</li>)
                : job.requiredSkills.split(',').map((skill, i) => <li key={i}>{skill.trim()}</li>)
              }
            </ul>
          ) : <p>No required skills specified</p>}
        </div>
        
        {/* Preferred Skills Section - Also moved above Job Description */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Preferred Skills</h2>
          {job.preferredSkills && job.preferredSkills.length > 0 ? (
            <ul className="list-disc list-inside">
              {Array.isArray(job.preferredSkills) 
                ? job.preferredSkills.map((skill, i) => <li key={i}>{skill}</li>)
                : job.preferredSkills.split(',').map((skill, i) => <li key={i}>{skill.trim()}</li>)
              }
            </ul>
          ) : <p>No preferred skills specified</p>}
        </div>

        {/* Job Description - Now after Skills sections */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Job Description</h2>
          <p className="whitespace-pre-line">{job.description}</p>
        </div>

        {/* Application status section */}
        {application ? (
          <div className="mt-6 p-4 bg-gray-50 rounded-md border border-gray-200">
            <h2 className="text-xl font-semibold mb-2">Your Application Status</h2>
            <p><span className="font-medium">Status:</span> {application.status || "Applied"}</p>
            <p><span className="font-medium">Applied on:</span> {new Date(application.createdAt || application.appliedAt).toLocaleDateString()}</p>
            
            {application.scanResult?.canTakeInterview && (
              <button 
                onClick={() => router.push(`/candidate/interview/${application._id}`)}
                className="mt-4 px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Take Interview
              </button>
            )}
            
            {application.scanResult && !application.scanResult.canTakeInterview && (
              <p className="mt-4 text-gray-600 italic">
                Your application is being reviewed. You'll be notified if selected for an interview.
              </p>
            )}
          </div>
        ) : (
          // Show apply button if not already applied
          <div className="mt-6">
            <Link href={`/candidate/apply/${job._id}`}>
              <button className="px-6 py-3 bg-blue-600 text-white rounded hover:bg-blue-700 font-semibold">
                Apply for this Position
              </button>
            </Link>
          </div>
        )}
      </div>
    </div>
  );
}
