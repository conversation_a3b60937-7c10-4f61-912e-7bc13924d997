import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { useSession, signIn } from "next-auth/react";
import Link from "next/link";

export default function JobDetails() {
  const { data: session } = useSession();
  const router = useRouter();
  const { id } = router.query;

  const [job, setJob] = useState(null);
  const [status, setStatus] = useState(null);
  const [resume, setResume] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const [formData, setFormData] = useState({
    fullName: "",
    phone: "",
    linkedIn: "",
    portfolio: "",
    experienceYears: "",
    coverLetter: "",
    expectedSalary: "",
  });

  useEffect(() => {
    if (id) {
      fetch(`/api/jobs/${id}`)
        .then((res) => res.json())
        .then((data) => {
          console.log("Job data:", data);
          setJob(data.job || null);
        });

      if (session?.user) {
        fetch(`/api/candidate/application-status?email=${session.user.email}&jobId=${id}`)
          .then((res) => res.json())
          .then((data) => setStatus(data.status || null));
      }
    }
  }, [id, session]);

  const handleApply = async (e) => {
    e.preventDefault();
    if (!resume) return alert("Please upload a resume");
    setIsLoading(true);

    const applicationData = new FormData();
    applicationData.append("resume", resume);
    applicationData.append("email", session.user.email);
    applicationData.append("jobId", id);

    for (const key in formData) {
      applicationData.append(key, formData[key]);
    }

    const response = await fetch("/api/apply", {
      method: "POST",
      body: applicationData,
    });

    setIsLoading(false);

    if (response.ok) {
      alert("Application submitted successfully!");
      router.push("/candidate-dashboard");
    } else {
      alert("Error submitting application. Please try again.");
    }
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  if (!job) return <p className="text-center mt-10">Loading job details...</p>;

  return (
    <div className="container mx-auto p-6">
      <h2 className="text-3xl font-bold text-blue-600">{job.title}</h2>
      <p className="text-gray-700">{job.companyName} - {job.location}</p>
      <p className="mt-2"><strong>Experience Required:</strong> {job.experience} years</p>
      <p className="mt-2">
        <strong>Skills Required:</strong>{" "}
        {Array.isArray(job.requiredSkills)
          ? job.requiredSkills.join(", ")
          : "Not specified"}
      </p>
      <p className="mt-4">{job.description}</p>

      {!session ? (
        <p className="mt-4 text-red-600">
          Please{" "}
          <span
            onClick={() => signIn()}
            className="underline text-blue-600 cursor-pointer"
          >
            sign in
          </span>{" "}
          to apply.
        </p>
      ) : status ? (
        <p className="mt-4 text-blue-600">
          You have already applied for this job. Status: <strong>{status}</strong>
        </p>
      ) : (
        <form onSubmit={handleApply} className="mt-6 space-y-4">
          <input type="text" name="fullName" onChange={handleChange} placeholder="Full Name" required className="w-full p-2 border rounded" />
          <input type="tel" name="phone" onChange={handleChange} placeholder="Phone Number" required className="w-full p-2 border rounded" />
          <input type="url" name="linkedIn" onChange={handleChange} placeholder="LinkedIn Profile (optional)" className="w-full p-2 border rounded" />
          <input type="url" name="portfolio" onChange={handleChange} placeholder="Portfolio/Website (optional)" className="w-full p-2 border rounded" />
          <input type="text" name="experienceYears" onChange={handleChange} placeholder="Years of Experience" className="w-full p-2 border rounded" />
          <textarea name="coverLetter" onChange={handleChange} placeholder="Cover Letter (optional)" className="w-full p-2 border rounded"></textarea>
          <input type="text" name="expectedSalary" onChange={handleChange} placeholder="Expected Salary (optional)" className="w-full p-2 border rounded" />

          <label className="block text-lg font-medium">Upload Resume (PDF, DOCX)</label>
          <input
            type="file"
            accept=".pdf,.doc,.docx"
            className="mt-2 p-2 border rounded"
            onChange={(e) => setResume(e.target.files[0])}
            required
          />
          <button
            type="submit"
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            disabled={isLoading}
          >
            {isLoading ? "Submitting..." : "Apply Now"}
          </button>
        </form>
      )}
    </div>
  );
}
