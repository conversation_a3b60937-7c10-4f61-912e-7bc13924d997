// File: pages/candidate/interview-live.js - COMPLETE FIXED FILE WITH PROPER REF ORDER

import { useEffect, useState, useRef, useCallback } from 'react';
import { useRouter } from 'next/router';
import { FaSignOutAlt, FaVideo, FaLock, FaShieldAlt, FaExclamationTriangle } from 'react-icons/fa';
import dynamic from 'next/dynamic';
import axios from 'axios';

// Import security handlers
import { setupFullscreenSecurity, showInitialFullscreenPrompt } from '@/components/security/FullscreenSecurity';
import { DevToolsSecurity } from '@/components/security/DevToolsSecurity';
import { CopyPasteSecurity } from '@/components/security/CopyPasteSecurity';
import { EnhancedTabSwitchSecurity } from '@/components/security/EnhancedTabSwitchSecurity';

// Dynamic imports
const CodeEditor = dynamic(() => import('@/components/CodeEditor'), { ssr: false });
const WebcamFaceDetection = dynamic(() => import('@/components/WebcamFaceDetection'), { ssr: false });

export default function InterviewLive() {
  const router = useRouter();
  const { id } = router.query;

  // ===== ALL REFS DECLARED FIRST =====
  const securityManagerRef = useRef(null);
  const webcamRef = useRef(null);
  const exitingRef = useRef(false);
  const loadingRef = useRef(true); // Track loading state for security
  const securityCleanupRef = useRef(null);
  const devToolsSecurityRef = useRef(null);
  const devToolsCleanupRef = useRef(null);
  const copyPasteSecurityRef = useRef(null);
  const copyPasteCleanupRef = useRef(null);
  const enhancedTabSwitchSecurityRef = useRef(null);
  const enhancedTabSwitchCleanupRef = useRef(null);
  
  // ===== ALL STATES DECLARED SECOND =====
  const [questions, setQuestions] = useState([]);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [loadingStatus, setLoadingStatus] = useState('Initializing...');
  
  // Security states
  const [securityReady, setSecurityReady] = useState(false);
  const [securityInitialized, setSecurityInitialized] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [securityViolations, setSecurityViolations] = useState({});
  const [totalViolations, setTotalViolations] = useState(0);
  const [showWarning, setShowWarning] = useState(false);
  const [warningMessage, setWarningMessage] = useState('');
  const [disqualified, setDisqualified] = useState(false);
  const [disqualificationReason, setDisqualificationReason] = useState('');
  
  // Camera states
  const [cameraStatus, setCameraStatus] = useState({
    isDetecting: false,
    facesDetected: 0,
    lookingAway: false,
    faceConfidence: 0,
    faceQuality: 'unknown'
  });

  // Code editor states
  const [code, setCode] = useState('');
  const [codeLanguage, setCodeLanguage] = useState('javascript');
  const [codeOutput, setCodeOutput] = useState('');
  const [isRunningCode, setIsRunningCode] = useState(false);
  const [codeExecutionCount, setCodeExecutionCount] = useState({});
  const [maxExecutions] = useState(3);

  // ===== UTILITY FUNCTIONS DECLARED THIRD =====
  
  // Universal modal function
  const showUniversalModal = useCallback((options) => {
    const {
      title = 'Notice',
      message = '',
      type = 'warning',
      buttonText = 'I Understand',
      onConfirm = () => {},
      autoCloseDelay = 30000
    } = options;
    
    console.log('🪟 Creating acknowledgment modal:', { title, type });
    
    // Create unique ID for this modal
    const modalId = `modal_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
    
    // Remove any existing modals first
    document.querySelectorAll('.universal-modal-overlay').forEach(el => {
      console.log('🗑️ Removing existing modal');
      el.remove();
    });
    
    // Create new modal with maximum z-index
    const overlay = document.createElement('div');
    overlay.className = 'universal-modal-overlay';
    overlay.id = `overlay_${modalId}`;
    overlay.setAttribute('role', 'dialog');
    overlay.setAttribute('aria-modal', 'true');
    overlay.setAttribute('aria-labelledby', `title_${modalId}`);
    
    overlay.style.cssText = `
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      background: rgba(0,0,0,0.8) !important;
      display: flex !important;
      justify-content: center !important;
      align-items: center !important;
      z-index: 2147483647 !important;
      pointer-events: all !important;
      user-select: none !important;
    `;
    
    // Colors based on type
    const colors = {
      warning: '#ff9500',
      error: '#ff3b30',
      info: '#007aff',
      success: '#34c759'
    };
    
    const color = colors[type] || colors.warning;
    
    // Create modal content
    const content = document.createElement('div');
    content.className = 'universal-modal-content';
    content.style.cssText = `
      background: white !important;
      border-radius: 12px !important;
      width: 90% !important;
      max-width: 500px !important;
      padding: 40px !important;
      text-align: center !important;
      box-shadow: 0 20px 40px rgba(0,0,0,0.5) !important;
      border-top: 8px solid ${color} !important;
      position: relative !important;
      pointer-events: all !important;
      user-select: text !important;
      z-index: 2147483647 !important;
    `;
    
    // Create confirm function
    const confirmAction = () => {
      try {
        console.log('🎯 Modal confirm action triggered');
        if (overlay && overlay.parentNode) {
          overlay.remove();
          console.log('🗑️ Modal removed successfully');
        }
        onConfirm();
        return true;
      } catch (error) {
        console.error('❌ Error in confirm action:', error);
        return false;
      }
    };
    
    // Create unique button ID
    const buttonId = `btn_${modalId}`;
    
    content.innerHTML = `
      <div style="font-size: 48px; margin-bottom: 20px; user-select: none;">
        ${type === 'warning' || type === 'error' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️'}
      </div>
      <h2 id="title_${modalId}" style="font-size: 24px; margin-bottom: 20px; color: ${color}; user-select: text;">
        ${title}
      </h2>
      <div style="margin-bottom: 30px; font-size: 16px; color: #333; line-height: 1.5; user-select: text;">
        ${message}
      </div>
      <button 
        id="${buttonId}"
        style="
          background: ${color} !important;
          color: white !important;
          border: none !important;
          padding: 15px 40px !important;
          border-radius: 8px !important;
          font-size: 18px !important;
          font-weight: bold !important;
          cursor: pointer !important;
          min-width: 200px !important;
          transition: all 0.2s ease !important;
          pointer-events: all !important;
          z-index: 2147483647 !important;
          position: relative !important;
        "
      >${buttonText}</button>
    `;
    
    overlay.appendChild(content);
    document.body.appendChild(overlay);
    
    console.log('🪟 Modal created and added to DOM');
    
    // Bind click events
    const bindClickEvents = () => {
      try {
        const confirmButton = document.getElementById(buttonId);
        
        if (confirmButton) {
          console.log('🔘 Binding events to confirm button');
          
          ['click', 'touchend', 'mouseup'].forEach(eventType => {
            confirmButton.addEventListener(eventType, (e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log(`🎯 Confirm button ${eventType} triggered`);
              confirmAction();
            }, { passive: false, capture: true });
          });
          
          // Add hover effects
          confirmButton.addEventListener('mouseenter', () => {
            confirmButton.style.transform = 'scale(1.05)';
            confirmButton.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2)';
          });
          
          confirmButton.addEventListener('mouseleave', () => {
            confirmButton.style.transform = 'scale(1)';
            confirmButton.style.boxShadow = 'none';
          });
        }
        
        // Keyboard events
        const handleKeyboard = (e) => {
          if (e.key === 'Enter' || e.key === ' ' || e.key === 'Escape') {
            e.preventDefault();
            e.stopPropagation();
            console.log(`🎯 Keyboard ${e.key} triggered`);
            confirmAction();
            document.removeEventListener('keydown', handleKeyboard);
          }
        };
        
        document.addEventListener('keydown', handleKeyboard, { passive: false, capture: true });
        
        console.log('✅ All event listeners bound successfully');
        
      } catch (error) {
        console.error('❌ Error binding click events:', error);
      }
    };
    
    // Bind events immediately and after delay
    bindClickEvents();
    setTimeout(bindClickEvents, 100);
    
    // Set up auto-close timer
    const autoCloseTimer = setTimeout(() => {
      console.log('⏰ Auto-close timer triggered');
      if (document.body.contains(overlay)) {
        confirmAction();
      }
    }, autoCloseDelay);
    
    // Return control object
    return {
      close: () => {
        console.log('🔧 Manual close triggered');
        clearTimeout(autoCloseTimer);
        confirmAction();
      }
    };
  }, []);

  // Copy/Paste warning function
  const showCopyPasteWarning = useCallback((action, isExternal = false) => {
    const message = isExternal 
      ? `External ${action.toUpperCase()} BLOCKED: Cannot ${action} to/from external sources during the interview`
      : `${action.toUpperCase()} BLOCKED: Copy/paste operations are not allowed during the interview`;
    
    console.log(`🚨 Copy/Paste blocked: ${action}${isExternal ? ' (external)' : ''}`);
    
    // Show immediate warning banner
    setWarningMessage(message);
    setShowWarning(true);
    
    // Hide warning after 3 seconds
    setTimeout(() => {
      setShowWarning(false);
    }, 3000);
    
    // Show modal warning
    showUniversalModal({
      title: isExternal 
        ? `External ${action.charAt(0).toUpperCase() + action.slice(1)} Blocked`
        : `${action.charAt(0).toUpperCase() + action.slice(1)} Blocked`,
      message: isExternal
        ? `${action.charAt(0).toUpperCase() + action.slice(1)} to/from external sources is not allowed. You can still copy/paste within the code editor and type normally everywhere.`
        : `${action.charAt(0).toUpperCase() + action.slice(1)} operations are not allowed during the interview. This is a security measure to ensure fairness. Normal typing including space bar works everywhere.`,
      type: 'warning',
      buttonText: 'I Understand',
      onConfirm: () => {
        console.log(`${action} warning acknowledged`);
      }
    });
  }, [showUniversalModal]);

  // Report violation to server
  const reportViolation = async (type, message) => {
    try {
      console.log(`📤 [VIOLATION] Reporting ${type}: ${message}`);
      
      const response = await axios.post('/api/interview/violation', {
        applicationId: id,
        type,
        message,
        timestamp: new Date().toISOString()
      });
      
      console.log(`✅ [VIOLATION] ${type} reported successfully:`, response.data);
      
      if (!response.data.success) {
        console.error('❌ Server rejected violation report:', response.data);
      }
    } catch (error) {
      console.error(`❌ Network error reporting ${type} violation:`, error);
    }
  };

  // Enhanced report disqualification
  const reportDisqualification = async (reason, violationSummary) => {
    try {
      console.log('📤 [DISQUALIFICATION] Sending disqualification report...');
      
      const disqualificationData = {
        applicationId: id,
        reason,
        violations: violationSummary,
        timestamp: new Date().toISOString()
      };
      
      console.log('📋 [DISQUALIFICATION] Data being sent:', JSON.stringify(disqualificationData, null, 2));
      
      const response = await axios.post('/api/interview/disqualify', disqualificationData);
      
      console.log('✅ [DISQUALIFICATION] Response:', response.data);
      
      if (!response.data.success) {
        console.error('❌ Server rejected disqualification report:', response.data);
      } else {
        console.log('✅ [DISQUALIFICATION] Reported successfully');
      }
    } catch (error) {
      console.error('❌ [DISQUALIFICATION] Network error:', error);
      console.error('Error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });
    }
  };

  // Enhanced security warning with acknowledgment modal
  const showSecurityWarning = async (type, message, count, total) => {
    try {
      const isFinalWarning = total >= 4;
      const warningText = `${message} (Warning ${count} | Total: ${total}/5)`;
      
      console.log(`🚨 [WARNING] ${type}: ${warningText}`);
      
      // Show banner at top of screen
      setWarningMessage(warningText);
      setShowWarning(true);
      
      // Create title based on violation type
      const formattedType = type
        .replace(/([A-Z])/g, ' $1')
        .toLowerCase()
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
      
      // Customize message based on violation type
      let customMessage = '';
      if (type === 'fullscreenExit') {
        customMessage = 'You must stay in fullscreen mode during the interview.';
      } else if (type === 'tabSwitch') {
        customMessage = 'Switching tabs during the interview is not allowed.';
      } else if (type === 'windowBlur') {
        customMessage = 'Please keep this window focused during the interview.';
      } else if (type === 'screenshot') {
        customMessage = 'Taking screenshots during the interview is strictly prohibited.';
      } else if (type === 'faceNotVisible') {
        customMessage = 'Please ensure your face is clearly visible to the camera.';
      } else if (type === 'multipleFaces') {
        customMessage = 'Only one person should be visible in the camera.';
      } else if (type === 'lookingAway') {
        customMessage = 'Please look at the screen during the interview.';
      } else if (type === 'devTools') {
        customMessage = 'Developer tools are not allowed during the interview.';
      } else if (type === 'copyPaste') {
        customMessage = 'Copy/paste operations are restricted to maintain fairness. Normal typing including space bar works everywhere.';
      }
      
      // Add warning count
      const warningCountText = isFinalWarning
        ? 'This is your FINAL WARNING! One more violation will end the interview.'
        : `You will be disqualified after ${5 - total} more violations.`;
      
      // Complete message
      const fullMessage = `
        ${message}<br><br>
        ${customMessage}<br><br>
        <strong style="color: ${isFinalWarning ? '#ff3b30' : '#ff9500'}">
          ${warningCountText}
        </strong>
      `;
      
      // Show centered modal with acknowledgment requirement
      return new Promise((resolve) => {
        showUniversalModal({
          title: `Security Violation: ${formattedType}`,
          message: fullMessage,
          type: isFinalWarning ? 'error' : 'warning',
          buttonText: 'I Understand',
          onConfirm: async () => {
            console.log(`✅ [WARNING] ${type} acknowledged by user`);
            
            // Hide banner after acknowledgment
            setShowWarning(false);
            resolve(true);
          }
        });
      });
    } catch (error) {
      console.error('❌ Error showing security warning:', error);
      
      // Fallback to auto-hide if modal fails
      setTimeout(() => {
        setShowWarning(false);
      }, 5000);
      
      return false;
    }
  };

  // ===== ALL USEEFFECTS DECLARED LAST (AFTER ALL REFS AND FUNCTIONS) =====
  
  // Track loading state immediately
  useEffect(() => {
    loadingRef.current = loading;
    console.log(`🔄 Loading state updated: ${loading}`);
  }, [loading]);

  // 1. LOAD SECURITY MANAGER
  useEffect(() => {
    if (typeof window !== 'undefined' && !window.SecurityManager) {
      const script = document.createElement('script');
      script.src = '/js/security-manager.js';
      script.onload = () => {
        console.log('✅ SecurityManager loaded');
        setSecurityReady(true);
      };
      script.onerror = () => setError('Failed to load security components');
      document.head.appendChild(script);
    } else if (window.SecurityManager) {
      setSecurityReady(true);
    }
  }, []);

  // 2. SETUP RED OVERLAY SYSTEM
  useEffect(() => {
    console.log('🔴 Setting up red overlay system');
    
    const cleanup = setupFullscreenSecurity({
      exitingRef,
      setIsFullScreen
    });
    
    securityCleanupRef.current = cleanup;
    return cleanup;
  }, []);

  // 3. SETUP DEVTOOLS DETECTION IMMEDIATELY
  useEffect(() => {
    console.log('🛠️ Setting up DevTools detection system');
    
    try {
      // Create DevTools security instance immediately
      devToolsSecurityRef.current = new DevToolsSecurity({
        exitingRef,
        securityManagerRef
      });
      
      // Setup DevTools detection immediately - NO CONDITIONS
      const devToolsCleanup = devToolsSecurityRef.current.setupDevToolsDetection();
      devToolsCleanupRef.current = devToolsCleanup;
      
      console.log('✅ DevTools detection initialized immediately with space bar fix');
      
      return devToolsCleanup;
    } catch (error) {
      console.error('❌ Error setting up DevTools detection:', error);
    }
  }, []); // Empty dependency array - run once immediately

  // 4. SETUP COPY/PASTE RESTRICTIONS IMMEDIATELY WITH SPACE BAR FIX
  useEffect(() => {
    console.log('📋 Setting up Copy/Paste restrictions system with space bar fix');
    
    try {
      // Create Copy/Paste security instance immediately
      copyPasteSecurityRef.current = new CopyPasteSecurity({
        exitingRef,
        securityManagerRef,
        trackPreSecurityViolation: (type, message) => {
          console.log(`📊 Pre-security Copy/Paste violation: ${type} - ${message}`);
        },
        currentQuestion,
        showCopyPasteWarning
      });
      
      // Setup Copy/Paste monitoring immediately
      const copyPasteCleanup = copyPasteSecurityRef.current.setupCopyPasteMonitoring();
      copyPasteCleanupRef.current = copyPasteCleanup;
      
      console.log('✅ Copy/Paste restrictions initialized immediately with space bar unrestricted');
      
      return copyPasteCleanup;
    } catch (error) {
      console.error('❌ Error setting up Copy/Paste restrictions:', error);
    }
  }, []); // Empty dependency array - run once immediately

  // 5. ✅ SETUP SIMPLE TAB SWITCH SECURITY (NO RED SCREEN, JUST VIOLATIONS)
  useEffect(() => {
    console.log('🔄 Setting up Simple Tab Switch Security (no red screen)...');
    
    try {
      // Create Simple Tab Switch security instance
      enhancedTabSwitchSecurityRef.current = new EnhancedTabSwitchSecurity({
        exitingRef,
        securityManagerRef,
        isLoadingRef: loadingRef,
        trackPreSecurityViolation: (type, message) => {
          console.log(`📊 Pre-security Tab Switch violation: ${type} - ${message}`);
        },
        currentQuestion
      });
      
      // Setup Tab Switch detection
      const enhancedTabSwitchCleanup = enhancedTabSwitchSecurityRef.current.setupTabSwitchDetection();
      enhancedTabSwitchCleanupRef.current = enhancedTabSwitchCleanup;
      
      console.log('✅ Simple Tab Switch Security initialized - no red screen, uses normal violation system');
      
      return enhancedTabSwitchCleanup;
    } catch (error) {
      console.error('❌ Error setting up Simple Tab Switch Security:', error);
    }
  }, []); // Empty dependency array - run once immediately

  // 6. UPDATE SECURITY OPTIONS WHEN CURRENT QUESTION CHANGES
  useEffect(() => {
    if (copyPasteSecurityRef.current) {
      copyPasteSecurityRef.current.updateOptions({
        currentQuestion,
        showCopyPasteWarning
      });
    }
    
    if (enhancedTabSwitchSecurityRef.current) {
      enhancedTabSwitchSecurityRef.current.updateOptions({
        currentQuestion,
        isLoadingRef: loadingRef
      });
      console.log('🔄 Enhanced Tab Switch Security options updated');
    }
  }, [currentQuestion, showCopyPasteWarning, loading]);

  // Reset code states when question changes
  useEffect(() => {
    if (questions[currentQuestion]?.type === 'coding') {
      const currentAnswer = answers[currentQuestion];
      if (currentAnswer && typeof currentAnswer === 'object') {
        setCode(currentAnswer.code || '');
        setCodeLanguage(currentAnswer.language || 'javascript');
        setCodeOutput(currentAnswer.output || '');
      } else {
        setCode('');
        setCodeLanguage('javascript');
        setCodeOutput('');
      }
    }
  }, [currentQuestion, questions, answers]);

  // 7. SHOW FULLSCREEN PROMPT
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!isFullScreen) {
        showInitialFullscreenPrompt();
      }
    }, 1000);
    
    return () => clearTimeout(timer);
  }, []);

  // 8. INITIALIZE SECURITY MANAGER
  useEffect(() => {
    if (!securityReady || !id) return;

    try {
      securityManagerRef.current = new window.SecurityManager();
      securityManagerRef.current.setApplicationId(id);
      
      // Security event handlers with proper logging and reporting
      const handleSecurityViolation = async (event) => {
        try {
          const { type, message, count, totalViolations: total, category } = event.detail;
          
          console.log(`🚨 [SECURITY-EVENT] Violation received:`, {
            type,
            message,
            count,
            total,
            category,
            timestamp: new Date().toISOString()
          });
          
          // IMPORTANT: Skip window blur violations completely
          if (type === 'windowBlur') {
            console.log('⏭️ [SECURITY-EVENT] Ignoring window blur violation to prevent false positives');
            return;
          }
          
          if (category === 'warning') {
            console.warn(`🚨 [SECURITY-EVENT] Warning violation: ${type} - ${message} (${count})`);
            
            // Update local state
            setSecurityViolations(prev => ({
              ...prev,
              [type]: count
            }));
            
            setTotalViolations(total);
            
            // Show acknowledgment modal and report to server
            try {
              await showSecurityWarning(type, message, count, total);
              await reportViolation(type, message);
            } catch (error) {
              console.error('❌ [SECURITY-EVENT] Error handling warning:', error);
            }
            
          } else if (category === 'analytics') {
            console.log(`📊 [SECURITY-EVENT] Analytics violation: ${type} - ${message}`);
            // Analytics violations are recorded but don't show warnings
          }
        } catch (error) {
          console.error('❌ [SECURITY-EVENT] Error handling security violation:', error);
        }
      };
      
      const handleDisqualification = async (event) => {
        try {
          const { reason, warningViolations, analyticsViolations, totalWarningViolations, suspiciousEvents } = event.detail;
          
          console.error('❌ [DISQUALIFICATION] Interview disqualified:', reason);
          console.error('🚨 [DISQUALIFICATION] Violation details:', {
            warningViolations,
            analyticsViolations,
            totalWarningViolations,
            suspiciousEvents,
            interview: {
              applicationId: id,
              timeElapsed: securityManagerRef.current?.interviewStartTime 
                ? Math.floor((Date.now() - securityManagerRef.current.interviewStartTime) / 1000) 
                : 'unknown'
            }
          });
          
          setDisqualified(true);
          setDisqualificationReason(reason);
          
          // Enhanced violation reporting
          const violationSummary = {
            reason,
            warningViolations: warningViolations || {},
            analyticsViolations: analyticsViolations || {},
            totalWarningViolations: totalWarningViolations || 0,
            suspiciousEvents: suspiciousEvents || [],
            timestamp: new Date().toISOString(),
            interview: {
              applicationId: id,
              timeElapsed: securityManagerRef.current?.interviewStartTime 
                ? Math.floor((Date.now() - securityManagerRef.current.interviewStartTime) / 1000) 
                : 0
            }
          };
          
          console.log('📤 [DISQUALIFICATION] Reporting with summary:', violationSummary);
          
          try {
            await reportDisqualification(reason, violationSummary);
          } catch (error) {
            console.error('❌ [DISQUALIFICATION] Failed to report to server:', error);
          }
        } catch (error) {
          console.error('❌ [DISQUALIFICATION] Error handling disqualification:', error);
        }
      };
      
      const handleFinalWarning = (event) => {
        try {
          console.warn('⚠️ [FINAL-WARNING] FINAL WARNING received');
          
          showUniversalModal({
            title: 'FINAL WARNING',
            message: 'This is your final warning! One more violation will result in disqualification from the interview.',
            type: 'error',
            buttonText: 'I Understand',
            onConfirm: () => {
              console.log('✅ [FINAL-WARNING] Final warning acknowledged');
            }
          });
        } catch (error) {
          console.error('❌ [FINAL-WARNING] Error handling final warning:', error);
        }
      };
      
      // Add event listeners
      window.addEventListener('security-violation', handleSecurityViolation);
      window.addEventListener('interview-disqualified', handleDisqualification);
      window.addEventListener('final-warning', handleFinalWarning);
      window.addEventListener('interview-started', () => {
        console.log('✅ [INTERVIEW] Interview monitoring started');
        setSecurityInitialized(true);
      });
      window.addEventListener('interview-ended', () => {
        console.log('✅ [INTERVIEW] Interview monitoring ended');
      });
      
      console.log('✅ [SECURITY] SecurityManager initialized with enhanced logging and space bar fix');
    } catch (error) {
      console.error('❌ [SECURITY] Error initializing SecurityManager:', error);
      setError('Security initialization failed');
    }
  }, [securityReady, id, showUniversalModal, showSecurityWarning, reportViolation, reportDisqualification]);

  // 9. FETCH QUESTIONS AFTER FULLSCREEN
  useEffect(() => {
    if (isFullScreen && securityReady && questions.length === 0) {
      fetchQuestions();
    }
  }, [isFullScreen, securityReady]);

  // 10. START INTERVIEW MONITORING AFTER QUESTIONS LOAD
  useEffect(() => {
    if (questions.length > 0 && securityManagerRef.current && !securityManagerRef.current.isInterviewActive) {
      setTimeout(() => {
        console.log('🚀 [INTERVIEW] Starting interview monitoring...');
        securityManagerRef.current.startInterview();
      }, 1000);
    }
  }, [questions.length]);

  // ===== FUNCTIONS =====
  const fetchQuestions = async () => {
    try {
      setLoadingStatus('Loading questions...');
      loadingRef.current = true; // Ensure loading is set
      
      const response = await axios.post('/api/interview/generate-questions', {
        applicationId: id
      });
      
      if (response.data?.questions) {
        const validQuestions = response.data.questions.filter(q => 
          q && q.question && ['mcq', 'technical', 'behavioral', 'coding'].includes(q.type)
        );
        console.log(`✅ [QUESTIONS] Loaded ${validQuestions.length} questions`);
        setQuestions(validQuestions);
        setLoading(false);
        loadingRef.current = false; // Explicitly set loading to false
      }
    } catch (error) {
      console.error('❌ [QUESTIONS] Failed to load questions:', error);
      setError('Failed to load questions');
      setLoading(false);
      loadingRef.current = false; // Set loading to false on error too
    }
  };

  const handleCameraUpdate = useCallback((status) => {
    setCameraStatus(status);
    if (securityManagerRef.current?.isInterviewActive) {
      securityManagerRef.current.handleFaceDetection(status);
    }
  }, []);

  const handleAnswerChange = useCallback((answer) => {
    setAnswers(prev => ({ ...prev, [currentQuestion]: answer }));
    
    // Update code and language states for coding questions
    if (typeof answer === 'object' && answer.code !== undefined) {
      setCode(answer.code || '');
      setCodeLanguage(answer.language || 'javascript');
      setCodeOutput(answer.output || '');
    }
  }, [currentQuestion]);

  // Run code function
  const runCode = async () => {
    const currentExecutions = codeExecutionCount[currentQuestion] || 0;
    
    if (currentExecutions >= maxExecutions) {
      showUniversalModal({
        title: 'Execution Limit Reached',
        message: `You have reached the maximum number of code executions (${maxExecutions}) for this question.`,
        type: 'warning',
        buttonText: 'Understood'
      });
      return;
    }

    if (!code.trim() || isRunningCode) return;

    try {
      console.log('▶️ Running code...');
      
      setIsRunningCode(true);
      setCodeOutput('Running code...');
      
      // Update execution count
      setCodeExecutionCount(prev => ({
        ...prev,
        [currentQuestion]: currentExecutions + 1
      }));
      
      const response = await axios.post('/api/interview/runcode', {
        code,
        language: codeLanguage,
        questionId: questions[currentQuestion]?.id || `question-${currentQuestion}`
      });
      
      if (response.data.success) {
        setCodeOutput(response.data.output);
        // Update answer with execution results
        handleAnswerChange({ 
          code, 
          language: codeLanguage, 
          output: response.data.output,
          executionCount: currentExecutions + 1
        });
      } else {
        setCodeOutput(`Error: ${response.data.output || response.data.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error running code:', error);
      setCodeOutput(`Error: ${error.message || 'Unknown error'}`);
    } finally {
      setIsRunningCode(false);
    }
  };

  const handleExit = useCallback(async () => {
    console.log('🚪 [EXIT] Initiating exit...');
    
    exitingRef.current = true;
    
    // Cleanup Enhanced Tab Switch detection first
    if (enhancedTabSwitchCleanupRef.current) {
      enhancedTabSwitchCleanupRef.current();
    }
    if (enhancedTabSwitchSecurityRef.current) {
      enhancedTabSwitchSecurityRef.current.cleanup();
    }
    
    // Cleanup Copy/Paste detection
    if (copyPasteCleanupRef.current) {
      copyPasteCleanupRef.current();
    }
    if (copyPasteSecurityRef.current) {
      copyPasteSecurityRef.current.cleanup();
    }
    
    // Cleanup DevTools detection
    if (devToolsCleanupRef.current) {
      devToolsCleanupRef.current();
    }
    if (devToolsSecurityRef.current) {
      devToolsSecurityRef.current.cleanup();
    }
    
    if (securityManagerRef.current) {
      securityManagerRef.current.setExiting(true);
      securityManagerRef.current.endInterview();
    }
    if (securityCleanupRef.current?.markEnding) {
      securityCleanupRef.current.markEnding();
    }
    
    try {
      if (document.fullscreenElement) {
        await document.exitFullscreen();
      }
    } catch (error) {
      console.log('Exit fullscreen error (expected):', error);
    }
    
    console.log('🚪 [EXIT] Navigating to dashboard...');
    router.push('/candidate-dashboard');
  }, [router]);

  const handleSubmit = async () => {
    try {
      console.log('📤 [SUBMIT] Submitting interview...');
      
      // Get security summary
      let securitySummary = {};
      try {
        if (securityManagerRef.current) {
          securitySummary = securityManagerRef.current.getViolationSummary();
        }
      } catch (error) {
        console.error('❌ [SUBMIT] Error getting security summary:', error);
      }
      
      await axios.post('/api/interview/complete', {
        applicationId: id,
        answers,
        securityViolations: securitySummary || securityViolations
      });
      
      console.log('✅ [SUBMIT] Interview submitted successfully');
      handleExit();
    } catch (error) {
      console.error('❌ [SUBMIT] Failed to submit interview:', error);
      setError('Failed to submit interview');
      setTimeout(handleExit, 3000);
    }
  };

  // ===== COMPONENTS =====
  const SecurityWarningBanner = () => {
    if (!showWarning) return null;
    return (
      <div className="fixed top-0 left-0 w-full p-3 z-50 text-center bg-yellow-600 text-white">
        <p className="text-sm font-semibold">
          <FaExclamationTriangle className="inline mr-2" />
          {warningMessage}
        </p>
      </div>
    );
  };

  const DisqualificationOverlay = () => {
    if (!disqualified) return null;
    return (
      <div className="fixed inset-0 bg-red-600 text-white flex flex-col items-center justify-center z-50 p-8">
        <FaExclamationTriangle className="text-6xl mb-4" />
        <h2 className="text-3xl font-bold mb-4">INTERVIEW DISQUALIFIED</h2>
        <p className="text-xl mb-8">{disqualificationReason}</p>
        <div className="bg-red-700 p-4 rounded mb-4 text-center max-w-2xl">
          <p className="mb-2">Maximum security violations reached (6 violations)</p>
          <p className="mb-2">Common violations include:</p>
          <ul className="text-sm text-left">
            <li>• Tab switching or window switching</li>
            <li>• Exiting fullscreen mode</li>
            <li>• Face not visible to camera</li>
            <li>• Multiple faces in camera</li>
            <li>• Using developer tools (F12)</li>
            <li>• Taking screenshots</li>
            <li>• Looking away from camera</li>
            <li>• Using copy/paste operations</li>
          </ul>
        </div>
        <button onClick={handleExit} className="bg-white text-red-600 px-6 py-3 rounded font-bold">
          Return to Dashboard
        </button>
      </div>
    );
  };

  // ===== LOADING STATE =====
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-md text-center max-w-md">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <h2 className="text-xl font-bold mb-2">Loading Interview</h2>
          <p className="text-gray-600 mb-4">{loadingStatus}</p>
          
          {!isFullScreen && (
            <div className="bg-blue-50 p-3 rounded border border-blue-200 mb-4">
              <p className="text-blue-700 text-sm">
                <FaLock className="inline mr-1" />
                Please enter fullscreen mode to continue
              </p>
            </div>
          )}
          
          {error && (
            <div className="bg-red-50 p-3 rounded border border-red-200 mb-4">
              <p className="text-red-700">{error}</p>
              <button onClick={() => window.location.reload()} className="mt-2 bg-red-600 text-white px-3 py-1 rounded text-sm">
                Refresh
              </button>
            </div>
          )}
        </div>
      </div>
    );
  }

  const currentQ = questions[currentQuestion];

  // ===== MAIN RENDER =====
  return (
    <div className="min-h-screen bg-white text-black">
      <SecurityWarningBanner />
      <DisqualificationOverlay />
      
      {/* Header */}
      <div className="flex justify-between items-center px-6 py-4 bg-blue-600 text-white">
        <div className="flex items-center">
          <FaLock className={`mr-2 ${isFullScreen ? 'text-green-500' : 'text-red-500'}`} />
          <h1 className="text-xl font-semibold">Proctored Interview</h1>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <FaShieldAlt className={`mr-1 ${securityInitialized ? 'text-green-500' : 'text-red-500'}`} />
            <span className="text-white text-sm">Security: {securityInitialized ? 'Active' : 'Loading'}</span>
          </div>
          
          <div className="bg-blue-800 rounded px-3 py-1 text-sm">
            Question {currentQuestion + 1} of {questions?.length || 0}
          </div>
          
          <button onClick={handleExit} className="bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700 flex items-center">
            <FaSignOutAlt className="inline mr-1" /> Exit
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-col md:flex-row p-6 gap-8">
        {/* Webcam */}
        <div className="w-full md:w-1/3">
          <div className="bg-gray-100 p-3 rounded-lg shadow">
            <h3 className="text-lg font-medium mb-2 text-gray-700">Proctoring Monitor</h3>
            
            <WebcamFaceDetection ref={webcamRef} onStatusUpdate={handleCameraUpdate} />
            
            <div className="mt-4 grid grid-cols-2 gap-2">
              <div className={`p-2 rounded text-sm flex items-center ${
                cameraStatus.isDetecting ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                <FaVideo className="mr-1" />
                Camera: {cameraStatus.isDetecting ? 'Active' : 'Inactive'}
              </div>
              
              <div className={`p-2 rounded text-sm flex items-center ${
                cameraStatus.facesDetected === 1 ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
              }`}>
                Faces: {cameraStatus.facesDetected}
              </div>
            </div>
            
            <div className="mt-4 p-3 bg-gray-50 rounded border">
              <h4 className="font-medium text-gray-800 mb-2 flex items-center">
                <FaShieldAlt className="mr-1" />
                Security Status
              </h4>
              <div className="text-sm text-gray-700 space-y-1">
                <div className="flex justify-between">
                  <span>Violations:</span>
                  <span className={`font-semibold ${
                    totalViolations === 0 ? 'text-green-600' : 
                    totalViolations < 3 ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {totalViolations} / 5
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Fullscreen:</span>
                  <span className={`font-semibold ${isFullScreen ? 'text-green-600' : 'text-red-600'}`}>
                    {isFullScreen ? 'Active' : 'Inactive'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>DevTools:</span>
                  <span className={`font-semibold ${
                    devToolsSecurityRef.current?.getDetectionStatus()?.isOpen ? 'text-red-600' : 'text-green-600'
                  }`}>
                    {devToolsSecurityRef.current?.getDetectionStatus()?.isOpen ? 'DETECTED (Paused)' : 'Protected'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Tab/Window:</span>
                  <span className={`font-semibold ${
                    enhancedTabSwitchSecurityRef.current?.getStatus()?.detectionEnabled ? 'text-green-600' : 'text-yellow-600'
                  }`}>
                    {enhancedTabSwitchSecurityRef.current?.getStatus()?.detectionEnabled ? 'Protected' : 'Activating...'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Copy/Paste:</span>
                  <span className="font-semibold text-green-600">
                    Restricted
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Space Bar:</span>
                  <span className="font-semibold text-green-600">
                    ✅ Works Everywhere
                  </span>
                </div>
              </div>
            </div>
            
            {/* Violations Summary */}
            {totalViolations > 0 && (
              <div className="mt-4 p-3 bg-yellow-50 rounded border border-yellow-200">
                <h4 className="font-medium text-yellow-800 mb-2">Security Violations ({totalViolations})</h4>
                <div className="text-sm text-yellow-700 space-y-1">
                  {Object.entries(securityViolations).map(([type, count]) => 
                    count > 0 ? (
                      <div key={type} className="flex justify-between">
                        <span className="capitalize">{type.replace(/([A-Z])/g, ' $1').toLowerCase()}:</span>
                        <span className="font-semibold">{count}</span>
                      </div>
                    ) : null
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Questions */}
        <div className="w-full md:w-2/3">
          {questions?.length ? (
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="mb-4">
                <h3 className="text-lg font-semibold">Question {currentQuestion + 1} of {questions.length}</h3>
                <p className="font-medium text-lg mb-4 mt-4">{currentQ?.question}</p>
              </div>
              
              {/* MCQ */}
              {currentQ?.type === 'mcq' && (
                <div className="space-y-2 mb-4">
                  {currentQ.options?.map((opt, i) => (
                    <label key={i} className={`block border p-3 rounded hover:bg-gray-100 cursor-pointer ${
                      answers[currentQuestion] === opt ? 'border-blue-500 bg-blue-50' : ''
                    }`}>
                      <input
                        type="radio"
                        name={`question-${currentQuestion}`}
                        value={opt}
                        checked={answers[currentQuestion] === opt}
                        onChange={() => handleAnswerChange(opt)}
                        className="mr-3"
                      />
                      {opt}
                    </label>
                  ))}
                </div>
              )}
              
              {/* Text Questions - Space bar completely unrestricted */}
              {['behavioral', 'technical'].includes(currentQ?.type) && (
                <textarea 
                  rows={6} 
                  className="w-full border rounded p-3 focus:ring-2 focus:ring-blue-500 outline-none answer-textarea" 
                  placeholder="Type your answer here (space bar and all keys work perfectly)"
                  value={answers[currentQuestion] || ''}
                  onChange={(e) => handleAnswerChange(e.target.value)}
                  data-allow-select="true"
                  data-allow-typing="true"
                  style={{
                    userSelect: 'text',
                    pointerEvents: 'auto',
                    cursor: 'text'
                  }}
                />
              )}
              
              {/* Code Editor - Space bar completely unrestricted */}
              {currentQ?.type === 'coding' && (
                <div className="mt-4">
                  {/* Language Selection and Execution Counter */}
                  <div className="bg-gray-800 text-white p-3 rounded-t flex justify-between items-center">
                    <div className="flex items-center space-x-4">
                      <span>Code Editor</span>
                      <select 
                        value={codeLanguage}
                        onChange={(e) => {
                          setCodeLanguage(e.target.value);
                          handleAnswerChange({ 
                            code, 
                            language: e.target.value, 
                            output: codeOutput 
                          });
                        }}
                        className="bg-gray-700 text-white px-3 py-1 rounded text-sm border border-gray-600"
                      >
                        <option value="javascript">JavaScript</option>
                        <option value="python">Python</option>
                        <option value="java">Java</option>
                        <option value="cpp">C++</option>
                        <option value="c">C</option>
                        <option value="go">Go</option>
                        <option value="rust">Rust</option>
                        <option value="php">PHP</option>
                        <option value="ruby">Ruby</option>
                        <option value="kotlin">Kotlin</option>
                      </select>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-xs text-gray-300">
                        ✅ Copy/paste allowed within editor | Space bar works perfectly
                      </div>
                      <div className={`text-sm font-semibold ${
                        (codeExecutionCount[currentQuestion] || 0) >= maxExecutions 
                          ? 'text-red-400' 
                          : 'text-green-400'
                      }`}>
                        Executions: {codeExecutionCount[currentQuestion] || 0}/{maxExecutions}
                      </div>
                    </div>
                  </div>
                  
                  <CodeEditor
                    language={codeLanguage}
                    value={code}
                    onChange={(newValue) => {
                      setCode(newValue);
                      handleAnswerChange({ 
                        code: newValue, 
                        language: codeLanguage, 
                        output: codeOutput 
                      });
                    }}
                    height="400px"
                    options={{
                      selectOnLineNumbers: true,
                      automaticLayout: true,
                      fontSize: 14,
                      minimap: { enabled: false },
                      scrollBeyondLastLine: false,
                      wordWrap: 'on',
                      lineNumbers: 'on',
                      acceptSuggestionOnCommitCharacter: true,
                      acceptSuggestionOnEnter: 'on',
                      accessibilitySupport: 'auto'
                    }}
                  />
                  
                  {/* Run Button */}
                  <div className="flex mt-2 space-x-2">
                    <button
                      onClick={runCode}
                      disabled={isRunningCode || (codeExecutionCount[currentQuestion] || 0) >= maxExecutions}
                      className={`px-4 py-2 rounded flex items-center ${
                        isRunningCode || (codeExecutionCount[currentQuestion] || 0) >= maxExecutions
                          ? 'bg-gray-400 cursor-not-allowed' 
                          : 'bg-green-600 hover:bg-green-700'
                      } text-white`}
                    >
                      {isRunningCode ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                          Running...
                        </>
                      ) : (
                        <>
                          ▶ Run Code {(codeExecutionCount[currentQuestion] || 0) >= maxExecutions && '(Limit Reached)'}
                        </>
                      )}
                    </button>
                    
                    {(codeExecutionCount[currentQuestion] || 0) > 0 && (
                      <div className="flex items-center text-sm text-gray-600">
                        <span>
                          {maxExecutions - (codeExecutionCount[currentQuestion] || 0)} executions remaining
                        </span>
                      </div>
                    )}
                  </div>
                  
                  {/* Output Window */}
                  {(codeOutput || isRunningCode) && (
                    <div className="mt-4">
                      <div className="bg-gray-800 text-white p-2 rounded-t flex justify-between items-center">
                        <span className="text-sm">Output</span>
                        <button 
                          onClick={() => {
                            setCodeOutput('');
                            handleAnswerChange({ 
                              code, 
                              language: codeLanguage, 
                              output: '' 
                            });
                          }}
                          className="text-xs text-gray-400 hover:text-white"
                        >
                          Clear
                        </button>
                      </div>
                      <div className="bg-gray-900 text-green-400 p-4 rounded-b font-mono text-sm overflow-auto max-h-60 whitespace-pre-wrap">
                        {isRunningCode ? (
                          <div className="flex items-center">
                            <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-green-400 mr-2"></div>
                            Executing {codeLanguage} code...
                          </div>
                        ) : (
                          codeOutput || 'No output'
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}
              
              {/* Navigation */}
              <div className="mt-8 flex justify-between">
                <button
                  onClick={() => setCurrentQuestion(prev => prev - 1)}
                  disabled={currentQuestion === 0}
                  className={`px-4 py-2 rounded ${
                    currentQuestion === 0 ? 'bg-gray-300' : 'bg-blue-600 hover:bg-blue-700'
                  } text-white`}
                >
                  Previous
                </button>
                
                {currentQuestion < questions.length - 1 ? (
                  <button
                    onClick={() => setCurrentQuestion(prev => prev + 1)}
                    className="px-4 py-2 rounded bg-green-600 hover:bg-green-700 text-white"
                  >
                    Next
                  </button>
                ) : (
                  <button
                    onClick={handleSubmit}
                    className="px-4 py-2 rounded bg-green-600 hover:bg-green-700 text-white"
                  >
                    Submit Interview
                  </button>
                )}
              </div>
            </div>
          ) : (
            <div className="bg-white p-6 rounded-lg shadow text-center">
              <p className="text-gray-600">No questions available</p>
              <button onClick={handleExit} className="mt-4 bg-red-600 text-white px-4 py-2 rounded">
                Return to Dashboard
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
