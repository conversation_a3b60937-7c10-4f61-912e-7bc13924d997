// File: pages/candidate/interview-setup.js - COMPLETE FIXED VERSION

import { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/router';
import { FaCheck, FaTimes, FaInfoCircle, FaVideo, FaMicrophone, FaDesktop, FaExclamationTriangle, FaEye, FaVolumeUp, FaShieldAlt } from 'react-icons/fa';

export default function InterviewSetup() {
  const router = useRouter();
  const { id } = router.query;

  // Core states (exactly matching working version)
  const [acknowledged, setAcknowledged] = useState(false);
  const [mediaAccess, setMediaAccess] = useState(false);
  const [previewPassed, setPreviewPassed] = useState(false);
  const [micVerified, setMicVerified] = useState(false);
  const [micDetectedOnce, setMicDetectedOnce] = useState(false);
  const [mediaError, setMediaError] = useState(false);
  const [loadingStatus, setLoadingStatus] = useState('');

  // Additional states for enhanced UI
  const [currentStep, setCurrentStep] = useState(0);
  const [fullscreenPermission, setFullscreenPermission] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [warningMessage, setWarningMessage] = useState('');
  const [permissionRequested, setPermissionRequested] = useState(false);

  // Refs
  const webcamRef = useRef(null);
  const canvasRef = useRef(null);
  const mediaStreamRef = useRef(null);
  const faceDetectionIntervalRef = useRef(null);

  // Step definitions
  const steps = [
    { 
      id: 'camera-setup', 
      title: 'Camera Setup', 
      description: 'Setting up and testing your camera',
      icon: FaVideo 
    },
    { 
      id: 'microphone-test', 
      title: 'Microphone Test', 
      description: 'Testing your microphone',
      icon: FaMicrophone 
    },
    { 
      id: 'face-detection', 
      title: 'Face Detection', 
      description: 'Verifying face position and quality',
      icon: FaEye 
    },
    { 
      id: 'security-briefing', 
      title: 'Security Briefing', 
      description: 'Understanding interview rules and security measures',
      icon: FaShieldAlt 
    },
    { 
      id: 'final-check', 
      title: 'Final Check', 
      description: 'Ready to begin interview',
      icon: FaCheck 
    }
  ];

  // Initialize on load
  useEffect(() => {
    if (!id || !router.isReady) {
      return;
    }
    
    console.log('Initializing interview setup...');
    setLoading(true);
    
    if (!id || id.length !== 24) {
      console.error("❌ Invalid or missing application ID:", id);
      setLoadingStatus('Error: Invalid application ID');
      setLoading(false);
      return;
    }
    
    setLoadingStatus('Loading face detection models...');
    
    // Load face-api script
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/@vladmandic/face-api/dist/face-api.min.js';
    script.onload = async () => {
      console.log('Face-api script loaded');
      setLoadingStatus('Initializing face detection...');
      
      try {
        // Load face detection models
        await Promise.all([
          window.faceapi.nets.ssdMobilenetv1.loadFromUri('/models'),
          window.faceapi.nets.faceLandmark68TinyNet.loadFromUri('/models')
        ]);
        
        console.log('Face detection models loaded');
        setLoading(false);
        setLoadingStatus('');
        
        // Check if permissions are already granted
        await checkExistingPermissions();
      } catch (modelError) {
        console.error('Error loading face detection models:', modelError);
        setError('Failed to load face detection models');
        setLoading(false);
      }
    };
    
    script.onerror = () => {
      console.error('Failed to load face-api script');
      setError('Failed to load face detection library');
      setLoading(false);
    };
    
    document.body.appendChild(script);
  }, [id, router.isReady]);

  // Check if camera/mic permissions are already granted
  const checkExistingPermissions = async () => {
    try {
      console.log('Checking existing permissions...');
      
      // Try to get media without showing permission prompt
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { width: 640, height: 480 }, 
        audio: true 
      });
      
      console.log('Permissions already granted!');
      
      // If we get here, permissions are already granted
      setupWithStream(stream);
    } catch (error) {
      console.log('Permissions not granted yet, showing permission screen');
      // Permissions not granted - show permission request screen
      setCurrentStep(0);
    }
  };

  // Setup with existing stream
  const setupWithStream = (stream) => {
    console.log('Setting up with existing stream...');
    
    mediaStreamRef.current = stream;
    
    // IMPORTANT: Save the stream globally for interview-live to use
    window.savedInterviewStream = stream;
    console.log('✅ Saved camera stream globally for interview');
    
    setMediaAccess(true);
    setPermissionRequested(true);
    setCurrentStep(0);
    
    if (webcamRef.current) {
      webcamRef.current.srcObject = stream;
      
      webcamRef.current.onloadedmetadata = () => {
        console.log('Video loaded with existing stream');
        
        // Set canvas dimensions
        setTimeout(() => {
          if (canvasRef.current && webcamRef.current) {
            canvasRef.current.width = webcamRef.current.videoWidth;
            canvasRef.current.height = webcamRef.current.videoHeight;
            console.log('Canvas dimensions set:', canvasRef.current.width, 'x', canvasRef.current.height);
          }
        }, 500);
        
        // Start microphone monitoring
        setTimeout(() => {
          setCurrentStep(1);
          setLoadingStatus('Testing microphone...');
          monitorMic(stream);
        }, 1000);
      };
      
      webcamRef.current.play().catch(console.error);
    }
  };

  // Setup camera and microphone (only called when clicking Grant Access)
  const setupPreview = async () => {
    console.log('Setting up camera and microphone...');
    
    // Prevent multiple calls
    if (permissionRequested) {
      console.log('Permission already requested, skipping...');
      return;
    }
    
    setPermissionRequested(true);
    
    try {
      setLoadingStatus('Requesting camera access...');
      
      // Check if media devices are supported
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Camera access not supported by this browser');
      }
      
      // Stop any existing stream
      if (mediaStreamRef.current) {
        mediaStreamRef.current.getTracks().forEach(track => track.stop());
        mediaStreamRef.current = null;
      }
      
      // Request media permissions
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { width: 640, height: 480 }, 
        audio: true 
      });
      
      console.log('New media access granted:', stream);
      setupWithStream(stream);
      
    } catch (permissionError) {
      console.error("❌ Media permission error:", permissionError);
      setPermissionRequested(false);
      
      // Handle different permission errors
      if (permissionError.name === 'NotAllowedError') {
        setMediaError(true);
        setError('Camera and microphone access denied. Please allow access and refresh the page.');
      } else if (permissionError.name === 'NotFoundError') {
        setMediaError(true);
        setError('No camera or microphone found. Please connect your devices and refresh.');
      } else if (permissionError.name === 'NotReadableError') {
        setMediaError(true);
        setError('Camera is being used by another application. Please close other apps and refresh.');
      } else {
        setMediaError(true);
        setError('Unable to access camera or microphone. Please check your permissions.');
      }
    }
  };

  // Monitor microphone levels
  const monitorMic = (stream) => {
    console.log('Starting microphone monitoring...');
    
    try {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const source = audioContext.createMediaStreamSource(stream);
      const analyser = audioContext.createAnalyser();
      analyser.fftSize = 256;
      const dataArray = new Uint8Array(analyser.fftSize);
      source.connect(analyser);

      const updateVolume = () => {
        analyser.getByteTimeDomainData(dataArray);
        let sum = 0;
        for (let i = 0; i < dataArray.length; i++) {
          const val = (dataArray[i] - 128) / 128;
          sum += val * val;
        }
        const rms = Math.sqrt(sum / dataArray.length);
        const volume = parseFloat((rms * 100).toFixed(2));
        
        // Log detected levels
        if (volume > 0.2) {
          console.log(`Mic level detected: ${volume}`);
        }
        
        // Verify microphone
        if (volume > 0.2 && !micDetectedOnce) {
          console.log('Microphone verified successfully!');
          setMicDetectedOnce(true);
          setMicVerified(true);
          // Don't auto-advance to next step
          return;
        }
        
        if (!micVerified) {
          requestAnimationFrame(updateVolume);
        }
      };
      
      updateVolume();
      
      // Auto-verify microphone after 3 seconds
      setTimeout(() => {
        if (!micVerified) {
          console.log('Auto-verifying microphone after timeout');
          setMicVerified(true);
        }
      }, 3000);
      
    } catch (err) {
      console.error("Error setting up audio monitoring:", err);
      // Don't block progress if audio fails
      setMicVerified(true);
    }
  };

  // Face detection
  const detectFace = () => {
    console.log('Starting face detection...');
    
    const detect = async () => {
      if (!webcamRef.current || !canvasRef.current || !window.faceapi) return;
      
      const video = webcamRef.current;
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      
      try {
        // Clear canvas and draw video frame
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        
        // Detect face
        const detection = await window.faceapi
          .detectSingleFace(video, new window.faceapi.SsdMobilenetv1Options())
          .withFaceLandmarks(true);
          
        if (detection) {
          const landmarks = detection.landmarks;
          const nose = landmarks.getNose();
          const leftEye = landmarks.getLeftEye();
          const rightEye = landmarks.getRightEye();
          
          const noseX = nose.reduce((sum, p) => sum + p.x, 0) / nose.length;
          const leftEyeX = leftEye.reduce((sum, p) => sum + p.x, 0) / leftEye.length;
          const rightEyeX = rightEye.reduce((sum, p) => sum + p.x, 0) / rightEye.length;
          
          const buffer = 10;
          const direction = (noseX < leftEyeX - buffer) ? 'right' :
                          (noseX > rightEyeX + buffer) ? 'left' :
                          'straight';
                          
          console.log(`Face detected, direction: ${direction}`);
          
          // Update face detection status
          const isProperlyPositioned = direction === 'straight';
          setPreviewPassed(isProperlyPositioned);
          
          // Draw face box
          ctx.strokeStyle = isProperlyPositioned ? 'green' : 'red';
          ctx.lineWidth = 3;
          ctx.strokeRect(detection.detection.box.x, detection.detection.box.y, 
                        detection.detection.box.width, detection.detection.box.height);
          
          // Don't auto-advance to next step
          
        } else {
          console.log('No face detected');
          setPreviewPassed(false);
        }
      } catch (err) {
        console.error("Face detection error:", err);
      }
    };
    
    // Run detection every second
    faceDetectionIntervalRef.current = setInterval(detect, 1000);
  };

  // FIXED: Request fullscreen permission without enforcement
  const requestFullscreenPermission = async () => {
    try {
      console.log('Preparing fullscreen permission...');
      
      // Don't automatically enter fullscreen - just check if it's available
      if (document.documentElement.requestFullscreen) {
        console.log('Fullscreen API is available');
        setFullscreenPermission(true);
      } else {
        console.warn('Browser does not support fullscreen API');
        setFullscreenPermission(true);
      }
      
      // Always move to the next step without forcing fullscreen
      setCurrentStep(4);
      
      // Show a notice about fullscreen instead of enforcing it
      setWarningMessage('Fullscreen will be requested when you start the interview. This is required for security purposes.');
      
    } catch (error) {
      console.error('Fullscreen check failed:', error);
      // Even if there's an error, allow the user to continue
      setFullscreenPermission(true);
      setCurrentStep(4);
    }
  };

  // MODIFIED: Start interview without cleaning up the stream
  const handleStart = async () => {
    if (!acknowledged || !mediaAccess || !previewPassed || !micVerified) {
      console.log('Cannot start - missing requirements:', {
        acknowledged,
        mediaAccess,
        previewPassed,
        micVerified
      });
      return;
    }
    
    setLoadingStatus('Starting interview...');
    
    // IMPORTANT: DON'T clean up the stream - keep it for the interview
    console.log('🎥 Keeping camera stream alive for interview...');
    
    // Only clean up face detection interval
    if (faceDetectionIntervalRef.current) {
      clearInterval(faceDetectionIntervalRef.current);
    }
    
    // Navigate to interview
    console.log("🚀 Starting interview...");
    router.push(`/candidate/interview-live?id=${id}`);
  };

  // Retry setup
  const retrySetup = async () => {
    console.log('Retrying setup...');
    setError('');
    setWarningMessage('');
    setMediaError(false);
    setMediaAccess(false);
    setPreviewPassed(false);
    setMicVerified(false);
    setMicDetectedOnce(false);
    setPermissionRequested(false);
    
    // Clear any existing intervals
    if (faceDetectionIntervalRef.current) {
      clearInterval(faceDetectionIntervalRef.current);
    }
    
    // Check permissions again
    await checkExistingPermissions();
  };

  // Manual continue functions
  const continueToMicTest = () => {
    if (mediaAccess) {
      setCurrentStep(1);
      monitorMic(mediaStreamRef.current);
    }
  };

  const continueToFaceDetection = () => {
    if (micVerified || mediaAccess) {
      setCurrentStep(2);
      setLoadingStatus('Starting face detection...');
      setTimeout(() => {
        detectFace();
        setLoadingStatus('');
      }, 1000);
    }
  };

  const continueToSecurity = () => {
    if (previewPassed) {
      setCurrentStep(3);
      setLoadingStatus('');
      if (faceDetectionIntervalRef.current) {
        clearInterval(faceDetectionIntervalRef.current);
      }
    }
  };

  // MODIFIED: Clean up on unmount - only stop stream if interview not started
  useEffect(() => {
    return () => {
      console.log('Cleaning up interview setup...');
      
      // Only stop the stream if we're not navigating to the interview
      // The stream should remain alive for the interview
      if (mediaStreamRef.current && !router.pathname.includes('interview-live')) {
        console.log('🛑 Stopping stream (not going to interview)');
        mediaStreamRef.current.getTracks().forEach(track => track.stop());
        window.savedInterviewStream = null;
      } else {
        console.log('🎥 Keeping stream alive for interview');
      }
      
      if (faceDetectionIntervalRef.current) {
        clearInterval(faceDetectionIntervalRef.current);
      }
    };
  }, [router.pathname]);

  // Show loading screen
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-800 mb-2">Interview Setup</h1>
            <p className="text-gray-600 mb-8">Preparing your interview environment</p>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">Loading</h2>
            {loadingStatus && (
              <p className="text-gray-600">{loadingStatus}</p>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Show permission request screen only if needed
  if (!mediaAccess && !mediaError && currentStep === 0 && !permissionRequested) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-800 mb-2">Interview Setup</h1>
            <p className="text-gray-600">Camera and microphone access required</p>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <FaVideo className="mx-auto text-6xl text-blue-500 mb-4" />
            <h2 className="text-2xl font-semibold mb-4">Grant Camera and Microphone Access</h2>
            <p className="text-gray-600 mb-6">
              This interview requires access to your camera and microphone. 
              Please click "Allow" when prompted by your browser.
            </p>
            {loadingStatus && (
              <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3"></div>
                  <p className="text-blue-800">{loadingStatus}</p>
                </div>
              </div>
            )}
            <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200 mb-6">
              <p className="text-yellow-800 text-sm">
                <strong>Note:</strong> If the permission prompt doesn't appear, check if it's blocked in your browser's address bar.
              </p>
            </div>
            <button 
              onClick={setupPreview}
              disabled={permissionRequested}
              className={`px-6 py-3 rounded-lg font-semibold ${
                permissionRequested 
                  ? 'bg-gray-400 text-gray-300 cursor-not-allowed' 
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              {permissionRequested ? 'Requesting...' : 'Grant Access'}
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Show error screen
  if (mediaError) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-800 mb-2">Permission Required</h1>
            <p className="text-gray-600">Unable to access camera and microphone</p>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-8">
            <div className="text-center mb-6">
              <FaExclamationTriangle className="mx-auto text-6xl text-red-500 mb-4" />
              <h2 className="text-2xl font-semibold text-red-800 mb-4">Access Denied</h2>
              <p className="text-gray-600 mb-6">{error}</p>
            </div>

            <div className="bg-blue-50 p-6 rounded-lg mb-6">
              <h3 className="font-semibold text-blue-800 mb-3">How to Enable Permissions:</h3>
              <ol className="text-sm text-blue-700 space-y-2 list-decimal list-inside">
                <li>Click the camera/microphone icon in your browser's address bar</li>
                <li>Select "Allow" for both camera and microphone access</li>
                <li>If you don't see the icon, go to your browser settings and enable camera/microphone for this site</li>
                <li>Refresh the page after enabling permissions</li>
              </ol>
            </div>

            <div className="text-center">
              <button 
                onClick={retrySetup}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-semibold"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Main setup interface
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Interview Setup</h1>
          <p className="text-gray-600">Complete the following steps to begin your interview</p>
        </div>

        {/* Progress indicator */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            {steps.map((step, index) => (
              <div key={step.id} className={`flex items-center ${index < steps.length - 1 ? 'flex-1' : ''}`}>
                <div className={`rounded-full w-10 h-10 flex items-center justify-center text-sm font-medium ${
                  currentStep > index ? 'bg-green-500 text-white' : 
                  currentStep === index ? 'bg-blue-500 text-white' : 
                  'bg-gray-300 text-gray-600'
                }`}>
                  {currentStep > index ? <FaCheck /> : index + 1}
                </div>
                <div className="ml-2 flex-1">
                  <div className={`text-sm font-medium ${currentStep >= index ? 'text-gray-800' : 'text-gray-500'}`}>
                    {step.title}
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div className={`flex-1 h-1 mx-4 ${currentStep > index ? 'bg-green-500' : 'bg-gray-300'}`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Warning display */}
        {warningMessage && (
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center">
              <FaInfoCircle className="text-yellow-500 mr-2" />
              <p className="text-yellow-700">{warningMessage}</p>
            </div>
          </div>
        )}

        {/* Main content */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          {/* Camera, Mic Test, and Face Detection Steps */}
          {currentStep <= 2 && (
            <div className="text-center">
              <h2 className="text-xl font-bold mb-4">
                {currentStep === 0 ? 'Camera Setup' : 
                 currentStep === 1 ? 'Microphone Test' : 'Face Detection'}
              </h2>
              
              {loadingStatus && (
                <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
                    <p className="text-blue-800">{loadingStatus}</p>
                  </div>
                </div>
              )}
              
              <div className="flex justify-center mb-4">
                <div className="relative w-full max-w-md">
                  <video
                    ref={webcamRef}
                    autoPlay
                    muted
                    playsInline
                    className="rounded border w-full"
                    style={{ display: 'block', visibility: 'visible', opacity: 1 }}
                  />
                  <canvas 
                    ref={canvasRef} 
                    className="absolute top-0 left-0 w-full h-full"
                    style={{ display: 'block' }}
                  />
                </div>
              </div>
              
              <div className="mt-6">
                <h3 className="font-semibold mb-2">System Checks</h3>
                <ul className="mb-4 text-sm text-left max-w-md mx-auto">
                  <li className={mediaAccess ? "text-green-600" : "text-gray-600"}>
                    {mediaAccess ? '✅' : '⏳'} Camera: {mediaAccess ? "Working" : "Initializing..."}
                  </li>
                  <li className={micVerified ? "text-green-600" : "text-gray-600"}>
                    {micVerified ? '✅' : '⏳'} Microphone: {micVerified ? "Working properly" : "Testing..."}
                  </li>
                  <li className={previewPassed ? "text-green-600" : "text-gray-600"}>
                    {previewPassed ? '✅' : '⏳'} Face Position: {previewPassed ? "Correctly positioned" : "Please look directly at the camera"}
                  </li>
                </ul>
                
                {/* Manual Continue Buttons for Each Step */}
                {currentStep === 0 && mediaAccess && (
                  <div className="mt-6">
                    <p className="text-sm text-green-600 mb-3">Camera is working properly!</p>
                    <button
                      onClick={continueToMicTest}
                      className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 font-semibold"
                    >
                      Continue to Microphone Test
                    </button>
                  </div>
                )}
                
                {currentStep === 1 && (
                  <div className="mt-6">
                    <p className="text-sm text-gray-600 mb-3">
                      {micVerified ? 'Microphone verified!' : 'Speak into your microphone to test it, or continue anyway'}
                    </p>
                    <button
                      onClick={continueToFaceDetection}
                      className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 font-semibold"
                    >
                      Continue to Face Detection
                    </button>
                  </div>
                )}
                
                {currentStep === 2 && (
                  <div className="mt-6">
                    <p className="text-sm text-gray-600 mb-3">
                      {previewPassed ? 'Face detected and properly positioned!' : 'Position your face centrally and look at the camera, or continue anyway'}
                    </p>
                    <button
                      onClick={continueToSecurity}
                      className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 font-semibold"
                    >
                      Continue to Security Briefing
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Security Briefing */}
          {currentStep === 3 && (
            <div className="text-center">
              <FaShieldAlt className="mx-auto text-4xl text-blue-500 mb-4" />
              <h2 className="text-2xl font-semibold mb-4">Security Briefing</h2>
              
              <div className="bg-gray-100 p-4 rounded mb-4 text-left max-w-md mx-auto">
                <h3 className="font-semibold mb-2">Interview Rules:</h3>
                <ol className="list-decimal pl-5 text-sm">
                  <li>Ensure you're in a quiet, well-lit environment</li>
                  <li>Keep your face visible to the camera at all times</li>
                  <li>Do not access other applications during the interview</li>
                  <li>The interview will enter fullscreen mode automatically</li>
                  <li>Complete all questions within the allotted time</li>
                </ol>
              </div>
              
              <div className="bg-yellow-50 p-4 rounded-lg mb-4">
                <p className="text-yellow-800 text-sm">
                  <strong>Note:</strong> Your browser may request fullscreen permission when the interview starts. 
                  Please allow this as it's required for security purposes.
                </p>
              </div>
              
              <label className="inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="form-checkbox h-5 w-5 text-blue-600" 
                  checked={acknowledged} 
                  onChange={e => setAcknowledged(e.target.checked)} 
                />
                <span className="ml-2">I acknowledge the rules and confirm my audio/video are working properly</span>
              </label>
              
              <div className="mt-6">
                <button
                  onClick={requestFullscreenPermission}
                  disabled={!acknowledged}
                  className={`px-6 py-3 rounded-lg font-semibold ${
                    acknowledged 
                      ? 'bg-blue-600 text-white hover:bg-blue-700' 
                      : 'bg-gray-400 text-gray-300 cursor-not-allowed'
                  }`}
                >
                  Continue to Interview
                </button>
              </div>
            </div>
          )}

          {/* Final Check */}
          {currentStep === 4 && (
            <div className="text-center">
              <FaCheck className="mx-auto text-4xl text-green-500 mb-4" />
              <h2 className="text-2xl font-semibold mb-4">All Set!</h2>
              <p className="text-gray-600 mb-6">All systems are ready. You can now start your interview.</p>
              
              <div className="bg-green-50 p-4 rounded-lg mb-6">
                <h3 className="font-semibold text-green-800 mb-2">Setup Complete</h3>
                <div className="text-sm text-green-700 space-y-1">
                  <p>✓ Camera: Working</p>
                  <p>✓ Microphone: {micVerified ? 'Working' : 'Not fully verified'}</p>
                  <p>✓ Face Detection: {previewPassed ? 'Verified' : 'Not verified'}</p>
                  <p>✓ Rules: Acknowledged</p>
                  <p>✓ Fullscreen: {fullscreenPermission ? 'Ready' : 'Will be requested'}</p>
                </div>
              </div>
              
              <button
                onClick={handleStart}
                disabled={!(acknowledged && mediaAccess)}
                className={`px-8 py-3 rounded-lg font-semibold ${
                  acknowledged && mediaAccess
                    ? 'bg-green-600 text-white hover:bg-green-700' 
                    : 'bg-gray-400 text-gray-100 cursor-not-allowed'
                }`}
              >
                Start Interview
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
