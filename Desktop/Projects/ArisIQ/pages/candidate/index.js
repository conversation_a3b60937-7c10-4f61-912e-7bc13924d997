// File: pages/candidate/index.js

import { useSession } from "next-auth/react";
import { useRouter } from "next/router";
import Link from "next/link";
import { useEffect, useState } from "react";

export default function CandidateHome() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [applicationId, setApplicationId] = useState(null);

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin");
    }
  }, [status, router]);

  useEffect(() => {
    // Simulate fetching applicationId for the candidate
    async function fetchApplicationId() {
      try {
        const res = await fetch("/api/candidate/latest-application");
        const data = await res.json();
        if (data?.applicationId) {
          setApplicationId(data.applicationId);
        }
      } catch (err) {
        console.error("Failed to fetch application ID", err);
      }
    }
    fetchApplicationId();
  }, []);

  const handleTakeInterview = () => {
    if (!applicationId) {
      alert("Application ID not found.");
      return;
    }

    // Launch the desktop app via custom protocol
    const interviewUrl = `symplihire://interview?id=${applicationId}`;
    window.location.href = interviewUrl;
  };

  if (status === "loading") {
    return <p className="text-center mt-10">Loading...</p>;
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-3xl mx-auto text-center">
        <h1 className="text-3xl font-bold text-blue-600 mb-4">Welcome to ArisIQ</h1>
        <p className="text-gray-700 mb-8">As a candidate, you can browse open jobs or view the applications you've already submitted.</p>

        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Link href="/candidate-dashboard">
            <button className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 w-full sm:w-auto">
              View My Applications
            </button>
          </Link>
          <Link href="/jobs">
            <button className="bg-gray-200 text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 w-full sm:w-auto">
              Browse Jobs
            </button>
          </Link>
          <button
            onClick={handleTakeInterview}
            className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 w-full sm:w-auto"
          >
            Take Interview
          </button>
        </div>
      </div>
    </div>
  );
}
