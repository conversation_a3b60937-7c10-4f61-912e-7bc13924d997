// File: pages/candidate/apply/[id].js

import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { useSession } from "next-auth/react";
import axios from "axios";

export default function ApplyJob() {
  const router = useRouter();
  const { id } = router.query;
  const { data: session, status } = useSession();
  const [job, setJob] = useState(null);
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    linkedIn: "",
    portfolio: "",
    github: "", 
    experienceYears: "",
    coverLetter: "",
    expectedSalary: "",
    education: [{ degree: "", major: "", university: "", city: "", year: "" }],
    certifications: [""],
    availability: "",
    visaStatus: "",
    visaSponsorship: "no",
    resume: null,
    linkedinPdf: null,
  });

  // Field visibility based on industry
  const [showFields, setShowFields] = useState({
    portfolio: true,
    github: false,
    certifications: true,
    linkedIn: true
  });

  // Form state variables
  const [validationErrors, setValidationErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState(null);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/api/auth/signin");
    } else if (session?.user?.email) {
      // Auto-populate the email field when session is available
      setFormData(prev => ({ ...prev, email: session.user.email }));
    }
  }, [status, router, session]);

  // Complete industry-specific field configuration
  const determineFieldsToShow = (industry) => {
    // Default fields configuration - conservative defaults
    const fieldConfig = {
      portfolio: false,
      github: false,
      certifications: true,
      linkedIn: true
    };

    // Adjust fields based on industry
    switch(industry) {
      // Tech-related industries
      case "Information Technology":
      case "Software Development":
      case "Telecommunications":
      case "Biotechnology":
        fieldConfig.portfolio = true;
        fieldConfig.github = true;
        fieldConfig.certifications = true;
        fieldConfig.linkedIn = true;
        break;
      
      // Creative industries
      case "Marketing":
      case "Advertising":
      case "Media":
      case "Entertainment":
        fieldConfig.portfolio = true;
        fieldConfig.github = false;
        fieldConfig.certifications = false;
        fieldConfig.linkedIn = true;
        break;
      
      // Professional services
      case "Finance":
      case "Banking":
      case "Insurance":
      case "Legal":
      case "Consulting":
        fieldConfig.portfolio = false;
        fieldConfig.github = false;
        fieldConfig.certifications = true;
        fieldConfig.linkedIn = true;
        break;
      
      // Healthcare and medical
      case "Healthcare":
      case "Medical":
      case "Pharmaceutical":
        fieldConfig.portfolio = false;
        fieldConfig.github = false;
        fieldConfig.certifications = true;
        fieldConfig.linkedIn = true;
        break;
      
      // Education and research
      case "Education":
      case "Research":
        fieldConfig.portfolio = true; // For teaching materials/publications
        fieldConfig.github = false;
        fieldConfig.certifications = true;
        fieldConfig.linkedIn = true;
        break;
      
      // Industrial and manufacturing
      case "Manufacturing":
      case "Construction":
      case "Energy":
      case "Transportation":
      case "Aerospace":
        fieldConfig.portfolio = false;
        fieldConfig.github = false;
        fieldConfig.certifications = true;
        fieldConfig.linkedIn = true;
        break;
      
      // Service industries
      case "Retail":
      case "Hospitality":
      case "Tourism":
        fieldConfig.portfolio = false;
        fieldConfig.github = false;
        fieldConfig.certifications = false;
        fieldConfig.linkedIn = true;
        break;
      
      // Public and non-profit sectors
      case "Government":
      case "Non-profit":
      case "Public Service":
        fieldConfig.portfolio = false;
        fieldConfig.github = false;
        fieldConfig.certifications = true;
        fieldConfig.linkedIn = true;
        break;
      
      // Real estate and property
      case "Real Estate":
      case "Property Management":
        fieldConfig.portfolio = false;
        fieldConfig.github = false;
        fieldConfig.certifications = true;
        fieldConfig.linkedIn = true;
        break;
      
      // Other/Miscellaneous
      case "Other":
      default:
        // For "Other" category, show most fields to be safe
        fieldConfig.portfolio = true;
        fieldConfig.github = false;
        fieldConfig.certifications = true;
        fieldConfig.linkedIn = true;
        break;
    }

    return fieldConfig;
  };

  const validateForm = (formData) => {
    const errors = {};
    
    // Check required fields
    if (!formData.firstName.trim()) errors.firstName = "First name is required";
    if (!formData.lastName.trim()) errors.lastName = "Last name is required";
    if (!formData.resume) errors.resume = "Resume is required";
    
    // Validate email format if present
    if (formData.email && !/^\S+@\S+\.\S+$/.test(formData.email)) {
      errors.email = "Invalid email format";
    }
    
    // Validate phone format if provided
    if (formData.phone && !/^[0-9+\-() ]{7,}$/.test(formData.phone)) {
      errors.phone = "Invalid phone number format";
    }
    
    // Validate URLs if provided
    const urlRegex = /^(https?:\/\/)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;
    
    if (formData.linkedIn && !urlRegex.test(formData.linkedIn)) {
      errors.linkedIn = "Invalid LinkedIn URL";
    }
    
    if (formData.portfolio && !urlRegex.test(formData.portfolio)) {
      errors.portfolio = "Invalid portfolio URL";
    }
    
    if (formData.github && !urlRegex.test(formData.github)) {
      errors.github = "Invalid GitHub URL";
    }
    
    // Validate expected salary if provided
    if (formData.expectedSalary && !/^[\$]?[0-9,.]+$/.test(formData.expectedSalary)) {
      errors.expectedSalary = "Invalid salary format";
    }
    
    // Check if education entries are complete if provided
    if (formData.education && formData.education.length > 0) {
      formData.education.forEach((edu, index) => {
        if (edu.degree && !edu.university) {
          if (!errors.education) errors.education = [];
          errors.education[index] = "University is required when degree is provided";
        }
      });
    }
    
    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    
    // Clear validation error when field is edited
    if (validationErrors[name]) {
      setValidationErrors(prev => {
        const updated = { ...prev };
        delete updated[name];
        return updated;
      });
    }
  };

  const handleEducationChange = (index, e) => {
    const updated = [...formData.education];
    updated[index][e.target.name] = e.target.value;
    setFormData((prev) => ({ ...prev, education: updated }));
    
    // Clear education validation errors
    if (validationErrors.education && validationErrors.education[index]) {
      setValidationErrors(prev => {
        const updated = { ...prev };
        if (updated.education) {
          updated.education[index] = null;
          if (updated.education.every(item => !item)) {
            delete updated.education;
          }
        }
        return updated;
      });
    }
  };

  const handleCertChange = (index, e) => {
    const updated = [...formData.certifications];
    updated[index] = e.target.value;
    setFormData((prev) => ({ ...prev, certifications: updated }));
  };

  const addEducation = () => {
    setFormData((prev) => ({
      ...prev,
      education: [...prev.education, { degree: "", major: "", university: "", city: "", year: "" }],
    }));
  };

  const addCertification = () => {
    setFormData((prev) => ({
      ...prev,
      certifications: [...prev.certifications, ""],
    }));
  };

  const handleFileChange = (e) => {
    const { name, files } = e.target;
    setFormData((prev) => ({ ...prev, [name]: files[0] }));
    
    // Clear validation error
    if (validationErrors[name]) {
      setValidationErrors(prev => {
        const updated = { ...prev };
        delete updated[name];
        return updated;
      });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!session?.user?.email) {
      alert("You must be logged in to apply for jobs");
      return;
    }
    
    // Validate form data
    const { isValid, errors } = validateForm(formData);
    
    if (!isValid) {
      // Display validation errors
      setValidationErrors(errors);
      
      // Scroll to the first error
      const firstErrorField = Object.keys(errors)[0];
      const errorElement = document.querySelector(`[name="${firstErrorField}"]`);
      if (errorElement) {
        errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      
      return;
    }
    
    // Clear any previous validation errors
    setValidationErrors({});
    
    // Show loading state
    setIsSubmitting(true);
    
    const payload = new FormData();
    
    Object.entries(formData).forEach(([key, value]) => {
      if (key === "education" || key === "certifications") {
        payload.append(key, JSON.stringify(value));
      } else if ((key === "resume" || key === "linkedinPdf") && value) {
        payload.append(key, value);
      } else {
        payload.append(key, value);
      }
    });

    payload.append("jobId", id);
    
    // Add job-related info to help with resume scanning
    if (job) {
      payload.append("jobTitle", job.title);
      payload.append("companyName", job.company);
      payload.append("location", job.location);
    }

    try {
      const res = await axios.post("/api/apply", payload);
      if (res.data.success) {
        // Show success message immediately
        setSubmitSuccess(true);
        setTimeout(() => {
          router.push("/candidate-dashboard");
        }, 2000);
      } else {
        setSubmitError(res.data.message || "Failed to submit application");
      }
    } catch (err) {
      console.error("Apply error:", err);
      setSubmitError(err.response?.data?.message || err.message || "Error submitting application");
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    if (id) {
      axios.get(`/api/jobs/${id}`).then((res) => {
        setJob(res.data.job);
        
        // Set visible fields based on job industry
        if (res.data.job && res.data.job.industry) {
          setShowFields(determineFieldsToShow(res.data.job.industry));
        }
      });
    }
  }, [id]);

  // Function to render field error message
  const renderError = (field) => {
    if (validationErrors[field]) {
      return <div className="text-red-500 text-sm mt-1">{validationErrors[field]}</div>;
    }
    return null;
  };

  // Function to get input class based on validation state
  const getInputClass = (field) => {
    return `border p-2 rounded w-full ${validationErrors[field] ? 'border-red-500' : ''}`;
  };

  return (
    <div className="max-w-xl mx-auto mt-8 p-4 bg-white shadow rounded">
      <h2 className="text-2xl font-bold text-blue-700 mb-4">
        Apply for: {job?.title}
      </h2>
      
      {job && (
        <div className="mb-6 text-gray-600">
          <p><span className="font-medium">Company:</span> {job.company}</p>
          <p><span className="font-medium">Industry:</span> {job.industry}</p>
          <p><span className="font-medium">Location:</span> {job.location}</p>
        </div>
      )}
      
      {/* Success Message */}
      {submitSuccess && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4">
          <strong className="font-bold">Success!</strong>
          <span className="block sm:inline"> Your application has been submitted. Redirecting to dashboard...</span>
        </div>
      )}
      
      {/* Error Message */}
      {submitError && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> {submitError}</span>
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-3">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div>
            <input
              type="text"
              name="firstName"
              placeholder="First Name"
              value={formData.firstName}
              onChange={handleChange}
              className={getInputClass("firstName")}
              required
            />
            {renderError("firstName")}
          </div>
          
          <div>
            <input
              type="text"
              name="lastName"
              placeholder="Last Name"
              value={formData.lastName}
              onChange={handleChange}
              className={getInputClass("lastName")}
              required
            />
            {renderError("lastName")}
          </div>
        </div>
        
        {/* Email field - auto-populated and read-only */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Email (From your account)</label>
          <input
            type="email"
            name="email"
            value={formData.email}
            readOnly
            className="border p-2 rounded w-full bg-gray-100"
          />
          <p className="text-xs text-gray-500 mt-1 ml-1">
            This email is from your account and will be used for communication about your application.
          </p>
          {renderError("email")}
        </div>
        
        <div>
          <input
            type="text"
            name="phone"
            placeholder="Phone (Optional)"
            value={formData.phone}
            onChange={handleChange}
            className={getInputClass("phone")}
          />
          {renderError("phone")}
        </div>
        
        {/* LinkedIN - conditionally shown */}
        {showFields.linkedIn && (
          <div>
            <input
              type="text"
              name="linkedIn"
              placeholder="LinkedIn URL (Optional - improves Resume Score)"
              value={formData.linkedIn}
              onChange={handleChange}
              className={getInputClass("linkedIn")}
            />
            <p className="text-xs text-gray-500 mt-1 ml-1">
              Paste your public LinkedIn profile URL (optional but highly recommended for better scoring). Make sure your profile is public.
            </p>
            {renderError("linkedIn")}
            
            <div className="mt-3">
              <label className="block text-sm font-medium mb-1">
                Or Upload LinkedIn PDF Export (Optional)
              </label>
              <input
                type="file"
                name="linkedinPdf"
                accept=".pdf"
                onChange={handleFileChange}
                className="border p-2 rounded w-full"
              />
              <p className="text-xs text-gray-500 mt-1 ml-1">
                You can export your LinkedIn profile as PDF from LinkedIn and upload it here for better resume scoring.
              </p>
            </div>
          </div>
        )}
        
        {/* Portfolio - conditionally shown */}
        {showFields.portfolio && (
          <div>
            <input
              type="text"
              name="portfolio"
              placeholder="Portfolio/Website"
              value={formData.portfolio}
              onChange={handleChange}
              className={getInputClass("portfolio")}
            />
            {renderError("portfolio")}
          </div>
        )}
        
        {/* GitHub - conditionally shown for tech jobs */}
        {showFields.github && (
          <div>
            <input
              type="text"
              name="github"
              placeholder="GitHub Profile URL"
              value={formData.github}
              onChange={handleChange}
              className={getInputClass("github")}
            />
            {renderError("github")}
          </div>
        )}
        
        <div>
          <input
            type="text"
            name="experienceYears"
            placeholder="Years of Experience"
            value={formData.experienceYears}
            onChange={handleChange}
            className={getInputClass("experienceYears")}
          />
          {renderError("experienceYears")}
        </div>
        
        <div>
          <textarea
            name="coverLetter"
            placeholder="Cover Letter (Optional - improves Resume Score)"
            value={formData.coverLetter}
            onChange={handleChange}
            className={getInputClass("coverLetter")}
            rows={3}
          />
          {renderError("coverLetter")}
        </div>
        
        <div>
          <input
            type="text"
            name="expectedSalary"
            placeholder="Expected Salary"
            value={formData.expectedSalary}
            onChange={handleChange}
            className={getInputClass("expectedSalary")}
          />
          {renderError("expectedSalary")}
        </div>

        <div className="border-t pt-3 mt-3">
          <h4 className="font-semibold">Education</h4>
          {formData.education.map((edu, index) => (
            <div key={index} className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-3">
              <input
                type="text"
                name="degree"
                placeholder="Degree (e.g., BS, MS, PhD)"
                value={edu.degree}
                onChange={(e) => handleEducationChange(index, e)}
                className="border p-2 rounded w-full"
              />
              <input
                type="text"
                name="major"
                placeholder="Major/Field of Study"
                value={edu.major}
                onChange={(e) => handleEducationChange(index, e)}
                className="border p-2 rounded w-full"
              />
              <input
                type="text"
                name="university"
                placeholder="University/Institution"
                value={edu.university}
                onChange={(e) => handleEducationChange(index, e)}
                className="border p-2 rounded w-full"
              />
              <input
                type="text"
                name="city"
                placeholder="City"
                value={edu.city}
                onChange={(e) => handleEducationChange(index, e)}
                className="border p-2 rounded w-full"
              />
              <input
                type="text"
                name="year"
                placeholder="Year Passed Out"
                value={edu.year}
                onChange={(e) => handleEducationChange(index, e)}
                className="border p-2 rounded w-full"
              />
            </div>
          ))}
          <button
            type="button"
            onClick={addEducation}
            className="text-blue-600 text-sm"
          >
            + Add Education
          </button>
          {validationErrors.education && (
            <div className="text-red-500 text-sm mt-1">Please complete all education fields</div>
          )}
        </div>

        {/* Certifications - conditionally shown */}
        {showFields.certifications && (
          <div className="border-t pt-3 mt-3">
            <h4 className="font-semibold">Certifications</h4>
            {formData.certifications.map((cert, index) => (
              <input
                key={index}
                type="text"
                placeholder="Certification Name"
                value={cert}
                onChange={(e) => handleCertChange(index, e)}
                className="border p-2 rounded w-full mb-2"
              />
            ))}
            <button
              type="button"
              onClick={addCertification}
              className="text-blue-600 text-sm"
            >
              + Add Certification
            </button>
          </div>
        )}

        <div>
          <input
            type="text"
            name="availability"
            placeholder="Availability to Join"
            value={formData.availability}
            onChange={handleChange}
            className={getInputClass("availability")}
          />
          {renderError("availability")}
        </div>

        <div>
          <select
            name="visaStatus"
            value={formData.visaStatus}
            onChange={handleChange}
            className={getInputClass("visaStatus")}
          >
            <option value="">Select Visa Status</option>
            <option value="Citizen">Citizen</option>
            <option value="GreenCard">Green Card</option>
            <option value="H1B">H1B</option>
            <option value="EAD">EAD</option>
            <option value="Other">Other</option>
          </select>
          {renderError("visaStatus")}
        </div>

        <div>
          <select
            name="visaSponsorship"
            value={formData.visaSponsorship}
            onChange={handleChange}
            className={getInputClass("visaSponsorship")}
          >
            <option value="no">Visa Sponsorship Required? No</option>
            <option value="yes">Visa Sponsorship Required? Yes</option>
          </select>
          {renderError("visaSponsorship")}
        </div>

        <div className="border-t pt-3 mt-3">
          <label className="font-medium block mb-2">Upload Resume (PDF or DOCX)</label>
          <input
            type="file"
            name="resume"
            accept=".pdf,.doc,.docx"
            onChange={handleFileChange}
            className={getInputClass("resume")}
            required
          />
          {renderError("resume")}
        </div>

        <button
          type="submit"
          disabled={isSubmitting || submitSuccess}
          className={`${
            isSubmitting || submitSuccess
              ? "bg-gray-500"
              : "bg-green-600 hover:bg-green-700"
          } text-white px-4 py-2 rounded mt-4 w-full flex justify-center`}
        >
          {isSubmitting ? (
            <>
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Submitting...
            </>
          ) : submitSuccess ? (
            "Application Submitted Successfully!"
          ) : (
            "Submit Application"
          )}
        </button>
      </form>
    </div>
  );
}
