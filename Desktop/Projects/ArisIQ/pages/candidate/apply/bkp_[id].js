
// File: pages/candidate/apply/[id].js

import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

export default function ApplyJob() {
  const router = useRouter();
  const { id } = router.query;
  const [job, setJob] = useState(null);

  useEffect(() => {
    if (id) {
      fetch(`/api/jobs/${id}`)
        .then(res => res.json())
        .then(data => setJob(data.job));
    }
  }, [id]);

  return (
    <div className="max-w-3xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-4">Apply for: {job?.title || 'Job'}</h1>

      <form className="space-y-4">
        <input type="text" placeholder="First Name" className="border p-2 rounded w-full" />
        <input type="text" placeholder="Last Name" className="border p-2 rounded w-full" />
        <input type="text" placeholder="Phone Number" className="border p-2 rounded w-full" />
        <input type="text" placeholder="LinkedIn Profile (optional)" className="border p-2 rounded w-full" />
        <input type="text" placeholder="Portfolio/Website (optional)" className="border p-2 rounded w-full" />
        <input type="text" placeholder="Years of Experience" className="border p-2 rounded w-full" />
        <textarea placeholder="Cover Letter (optional)" className="border p-2 rounded w-full"></textarea>
        <input type="text" placeholder="Expected Salary (optional)" className="border p-2 rounded w-full" />
        <div>
          <label className="block font-medium mb-1">Upload Resume (PDF, DOCX)</label>
          <input type="file" className="border p-2 rounded w-full" />
        </div>
        <button type="submit" className="px-6 py-2 bg-green-600 text-white rounded">Submit Application</button>
      </form>
    </div>
  );
}
