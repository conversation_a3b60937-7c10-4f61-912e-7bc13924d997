// File: pages/candidate/interview-live.js - Complete Fixed Version
// This removes the problematic new security integration and fixes camera issues

import { useEffect, useState, useMemo, useRef, useCallback } from 'react';
import { useRouter } from 'next/router';
import { FaSignOutAlt, FaVideo, FaMicrophone, FaLock, FaVolumeUp, FaStop, FaCircle, FaCode, FaExclamationTriangle, FaEye, FaCog, FaShieldAlt, FaClock, FaCheckCircle } from 'react-icons/fa';
import dynamic from 'next/dynamic';
import axios from 'axios';

// Dynamically import components
const CodeEditor = dynamic(() => import('@/components/CodeEditor'), {
  ssr: false,
  loading: () => <div className="p-4 bg-gray-100 rounded">Loading code editor...</div>
});

const WebcamFaceDetection = dynamic(() => import('@/components/WebcamFaceDetection'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-64 bg-gray-800 rounded-lg flex items-center justify-center">
      <div className="animate-pulse flex space-x-4 w-full">
        <div className="flex-1 space-y-4 py-1">
          <div className="h-40 bg-gray-400 rounded w-full"></div>
          <div className="h-4 bg-gray-400 rounded w-3/4 mx-auto"></div>
        </div>
      </div>
    </div>
  )
});

export default function InterviewLive() {
  const router = useRouter();
  const { id } = router.query;

  // Security-related refs
  const appIdRef = useRef(null);
  const securityManagerRef = useRef(null);
  const questionsLoadedRef = useRef(false);
  const securityInitializedRef = useRef(false);
  const webcamRef = useRef(null);
  const exitingRef = useRef(false);
  const isComponentMountedRef = useRef(true);
  
  // Core interview states
  const [questions, setQuestions] = useState([]);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [loadingStatus, setLoadingStatus] = useState('Initializing...');
  
  // Response time tracking
  const [questionStartTime, setQuestionStartTime] = useState(null);
  const [responseTimeData, setResponseTimeData] = useState({});
  const [suspiciousResponseTimes, setSuspiciousResponseTimes] = useState([]);
  const [firstInteractionTime, setFirstInteractionTime] = useState(null);
  const [hasInteractedWithQuestion, setHasInteractedWithQuestion] = useState(false);
  
  // Response time thresholds
  const RESPONSE_TIME_THRESHOLDS = {
    mcq: 3,
    technical: 20,
    behavioral: 15,
    coding: 30
  };
  
  // Security states
  const [securityInitialized, setSecurityInitialized] = useState(false);
  const [securityReady, setSecurityReady] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [securityViolations, setSecurityViolations] = useState({
    fullscreenExit: 0,
    tabSwitch: 0,
    windowBlur: 0,
    devTools: 0,
    keyboardShortcut: 0,
    contextMenu: 0,
    copyPaste: 0,
    multipleFaces: 0,
    faceNotVisible: 0,
    lookingAway: 0,
    suspiciousResponseTime: 0,
    dualMonitor: 0,
    screenSharing: 0,
    screenshot: 0,
    differentPerson: 0,
    suspiciousTyping: 0,
    contentPasted: 0
  });
  const [totalViolations, setTotalViolations] = useState(0);
  const [showWarning, setShowWarning] = useState(false);
  const [warningMessage, setWarningMessage] = useState('');
  const [warningCount, setWarningCount] = useState(0);
  const [disqualified, setDisqualified] = useState(false);
  const [disqualificationReason, setDisqualificationReason] = useState('');
  const [showFinalWarning, setShowFinalWarning] = useState(false);
  
  // Camera states - FIXED: Keep camera on during interview
  const [showWebcam, setShowWebcam] = useState(false);
  const [cameraPermission, setCameraPermission] = useState('granted'); // Already granted from setup
  const [microphonePermission, setMicrophonePermission] = useState('granted'); // Already granted from setup
  const [cameraStatus, setCameraStatus] = useState({
    isDetecting: false,
    facesDetected: 0,
    lookingAway: false,
    faceConfidence: 0,
    faceQuality: 'unknown',
    differentPerson: false
  });
  
  // Audio states
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [interimResult, setInterimResult] = useState('');
  const [useOfflineMode, setUseOfflineMode] = useState(false);
  
  // Code editor states
  const [code, setCode] = useState('');
  const [codeLanguage, setCodeLanguage] = useState('javascript');
  const [codeOutput, setCodeOutput] = useState('');
  const [isRunningCode, setIsRunningCode] = useState(false);
  
  // Text-to-speech state
  const [isSpeaking, setIsSpeaking] = useState(false);
  
  // Recording refs
  const audioStreamRef = useRef(null);
  const audioMediaRecorderRef = useRef(null);
  const recordedAudioChunksRef = useRef([]);
  const recordingTimerRef = useRef(null);
  const speechRecognitionTimeoutRef = useRef(null);
  
  // Permission states
  const [showPermissionModal, setShowPermissionModal] = useState(false);
  const [permissionRequested, setPermissionRequested] = useState(false);
  const [preSecurityViolations, setPreSecurityViolations] = useState([]);

  // FIXED: Turn off camera function - only called on exit, not during interview
  const turnOffCamera = useCallback(() => {
    try {
      console.log('🔴 Turning off camera for exit...');
      
      // Only turn off camera if we're actually exiting
      if (!exitingRef.current) {
        console.log('⚠️ Not exiting - keeping camera on');
        return;
      }
      
      // Method 1: Stop webcam component if it has a stop method
      if (webcamRef.current) {
        if (typeof webcamRef.current.stopCamera === 'function') {
          webcamRef.current.stopCamera();
          console.log('✅ Webcam component stopped via stopCamera method');
        }
        
        // Try to access the video element directly from the component
        if (typeof webcamRef.current.video !== 'undefined' && webcamRef.current.video) {
          const videoElement = webcamRef.current.video;
          if (videoElement.srcObject) {
            const stream = videoElement.srcObject;
            stream.getTracks().forEach(track => {
              track.stop();
              console.log('✅ Video track stopped:', track.kind);
            });
            videoElement.srcObject = null;
            console.log('✅ Video srcObject cleared');
          }
        }
        
        // Also try querySelector as fallback
        const videoElement = webcamRef.current.querySelector?.('video');
        if (videoElement && videoElement.srcObject) {
          const stream = videoElement.srcObject;
          stream.getTracks().forEach(track => {
            track.stop();
            console.log('✅ Video track stopped (querySelector):', track.kind);
          });
          videoElement.srcObject = null;
        }
      }
      
      // Method 2: Stop all media tracks from audioStreamRef
      if (audioStreamRef.current) {
        audioStreamRef.current.getTracks().forEach(track => {
          track.stop();
          console.log('✅ Audio track stopped:', track.kind);
        });
        audioStreamRef.current = null;
      }
      
      // Method 3: Stop media recorder if active
      if (audioMediaRecorderRef.current && audioMediaRecorderRef.current.state !== 'inactive') {
        try {
          audioMediaRecorderRef.current.stop();
          console.log('✅ Media recorder stopped');
        } catch (error) {
          console.warn('Warning stopping media recorder:', error);
        }
      }
      
      // Method 4: Stop speech recognition
      if (window.currentRecognition) {
        try {
          window.currentRecognition.stop();
          window.currentRecognition = null;
          console.log('✅ Speech recognition stopped');
        } catch (error) {
          console.warn('Warning stopping speech recognition:', error);
        }
      }
      
      // Update camera status to reflect off state
      setCameraStatus({
        isDetecting: false,
        facesDetected: 0,
        lookingAway: false,
        faceConfidence: 0,
        faceQuality: 'camera_stopped'
      });
      
      // Hide webcam component only on exit
      setShowWebcam(false);
      
      console.log('✅ Camera turned off for exit');
    } catch (error) {
      console.error('❌ Error turning off camera:', error);
    }
  }, []);

  // Exit fullscreen function
  const exitFullscreen = async () => {
    try {
      console.log('🖥️ Exiting fullscreen...');
      if (document.exitFullscreen) {
        await document.exitFullscreen();
      } else if (document.mozCancelFullScreen) {
        await document.mozCancelFullScreen();
      } else if (document.webkitExitFullscreen) {
        await document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        await document.msExitFullscreen();
      }
    } catch (error) {
      console.error('Exit fullscreen failed:', error);
    }
  };

  // Request fullscreen function
  const requestFullscreen = async () => {
    try {
      console.log('🖥️ Requesting fullscreen...');
      const elem = document.documentElement;
      
      if (elem.requestFullscreen) {
        await elem.requestFullscreen();
      } else if (elem.mozRequestFullScreen) {
        await elem.mozRequestFullScreen();
      } else if (elem.webkitRequestFullscreen) {
        await elem.webkitRequestFullscreen();
      } else if (elem.msRequestFullscreen) {
        await elem.msRequestFullscreen();
      }
    } catch (error) {
      console.error('Fullscreen request failed:', error);
    }
  };

  // Track pre-security violations
  const trackPreSecurityViolation = useCallback((type, message) => {
    const violation = {
      type,
      message,
      timestamp: new Date().toISOString(),
      questionIndex: currentQuestion
    };
    
    setPreSecurityViolations(prev => [...prev, violation]);
    console.log('📊 Pre-security violation tracked:', violation);
  }, [currentQuestion]);

  // Show required acknowledgment warning
  const showRequiredAcknowledgmentWarning = useCallback((title, message, type = 'warning') => {
    try {
      // Create warning overlay
      const warningOverlay = document.createElement('div');
      warningOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0,0,0,0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10002;
      `;
      
      // Create warning content
      const warningContent = document.createElement('div');
      warningContent.style.cssText = `
        background: white;
        border-radius: 8px;
        width: 90%;
        max-width: 500px;
        padding: 30px;
        text-align: center;
        border-top: 5px solid ${type === 'error' ? '#ff3b30' : type === 'warning' ? '#ff9500' : '#34c759'};
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
      `;
      
      warningContent.innerHTML = `
        <div style="font-size: 32px; margin-bottom: 15px;">${
          type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️'
        }</div>
        <h2 style="font-size: 20px; margin-bottom: 15px; color: ${
          type === 'error' ? '#ff3b30' : type === 'warning' ? '#ff9500' : '#34c759'
        };">${title}</h2>
        <p style="margin-bottom: 25px; font-size: 16px; color: #444;">${message}</p>
        <button id="warning-acknowledge-btn" style="
          background: ${type === 'error' ? '#ff3b30' : type === 'warning' ? '#ff9500' : '#34c759'};
          color: white;
          border: none;
          padding: 12px 30px;
          border-radius: 6px;
          font-size: 16px;
          font-weight: bold;
          cursor: pointer;
        ">Acknowledge</button>
      `;
      
      warningOverlay.appendChild(warningContent);
      document.body.appendChild(warningOverlay);
      
      // Add click handler
      return new Promise((resolve) => {
        document.getElementById('warning-acknowledge-btn').addEventListener('click', () => {
          warningOverlay.remove();
          resolve(true);
        });
      });
    } catch (error) {
      console.error('Error showing warning:', error);
      return Promise.resolve(false);
    }
  }, []);

  // Show copy/paste warning
  const showCopyPasteWarning = useCallback((action, isExternal = false) => {
    const message = isExternal 
      ? `External ${action.toUpperCase()} BLOCKED: Cannot ${action} to/from external sources during the interview`
      : `${action.toUpperCase()} BLOCKED: Copy/paste operations are not allowed during the interview`;
    
    console.log(`🚨 Copy/Paste blocked: ${action}${isExternal ? ' (external)' : ''}`);
    
    // Show immediate warning banner
    setWarningMessage(message);
    setShowWarning(true);
    
    // Hide warning after 3 seconds
    setTimeout(() => {
      setShowWarning(false);
    }, 3000);
  }, []);

  // REMOVED: Problematic new security integration - using original security-manager.js instead

  // Load SecurityManager (original working version)
  const loadSecurityManager = useCallback(async () => {
    if (typeof window !== 'undefined' && !window.SecurityManager) {
      try {
        console.log('📥 Loading original SecurityManager script...');
        const script = document.createElement('script');
        script.src = '/js/security-manager.js';
        script.onload = () => {
          console.log('✅ Original SecurityManager script loaded successfully');
          setSecurityReady(true);
        };
        script.onerror = (error) => {
          console.error('❌ Failed to load SecurityManager:', error);
          setError('Failed to load security components. Please refresh the page.');
          setSecurityReady(false);
        };
        document.head.appendChild(script);
      } catch (error) {
        console.error('Error loading SecurityManager:', error);
        setError('Security initialization failed. Please refresh the page.');
        setSecurityReady(false);
      }
    } else if (window.SecurityManager) {
      console.log('✅ SecurityManager already available');
      setSecurityReady(true);
    }
  }, []);

  useEffect(() => {
    loadSecurityManager();
  }, [loadSecurityManager]);

  // Initialize security when ready (original working version)
  useEffect(() => {
    if (!securityReady || !id || securityInitializedRef.current) return;

    try {
      console.log('🔧 Initializing original SecurityManager instance...');
      
      if (!securityManagerRef.current && window.SecurityManager) {
        try {
          securityManagerRef.current = new window.SecurityManager();
          securityManagerRef.current.setApplicationId(id);
          securityManagerRef.current.interviewStartTime = Date.now();
          
          // Security event handlers
          const handleSecurityViolation = (event) => {
            try {
              const { type, message, count, totalViolations: total, category } = event.detail;
              
              // Skip window blur violations to prevent false positives
              if (type === 'windowBlur') {
                console.log('Ignoring window blur violation to prevent false positives');
                return;
              }
              
              if (category === 'warning') {
                console.warn(`🚨 Security violation: ${type} - ${message} (${count})`);
                
                setSecurityViolations(prev => ({
                  ...prev,
                  [type]: count
                }));
                
                setTotalViolations(total);
                setWarningCount(prev => prev + 1);
                
                setWarningMessage(`Security violation: ${message}`);
                setShowWarning(true);
                
                setTimeout(() => {
                  setShowWarning(false);
                }, 5000);
              } else if (category === 'analytics') {
                console.log(`📊 Analytics violation: ${type} - ${message}`);
              }
            } catch (error) {
              console.error('Error handling security violation:', error);
            }
          };
          
          const handleDisqualification = (event) => {
            try {
              const { reason, warningViolations, analyticsViolations, totalWarningViolations, suspiciousEvents } = event.detail;
              console.error('❌ Interview disqualified:', reason);
              
              setDisqualified(true);
              setDisqualificationReason(reason);
              
              // Turn off camera when disqualified
              exitingRef.current = true; // Set exit flag first
              turnOffCamera();
            } catch (error) {
              console.error('Error handling disqualification:', error);
            }
          };
          
          const handleFinalWarning = (event) => {
            try {
              console.warn('⚠️ FINAL WARNING');
              
              setShowFinalWarning(true);
              setWarningMessage('FINAL WARNING: One more violation will result in disqualification!');
              setShowWarning(true);
              
              setTimeout(() => {
                setShowWarning(false);
                setShowFinalWarning(false);
              }, 10000);
            } catch (error) {
              console.error('Error handling final warning:', error);
            }
          };
          
          // Add event listeners
          window.addEventListener('security-violation', handleSecurityViolation);
          window.addEventListener('interview-disqualified', handleDisqualification);
          window.addEventListener('final-warning', handleFinalWarning);
          window.addEventListener('interview-started', () => {
            console.log('✅ Interview monitoring started');
            setSecurityInitialized(true);
          });
          window.addEventListener('interview-ended', () => {
            console.log('✅ Interview monitoring ended');
          });
          
          securityInitializedRef.current = true;
          console.log('✅ Original SecurityManager initialized successfully');
        } catch (error) {
          console.error('❌ Error creating SecurityManager instance:', error);
          setError('Failed to initialize security system. Please refresh the page.');
        }
      }
    } catch (error) {
      console.error('❌ Error initializing SecurityManager:', error);
      setError('Security system initialization failed. Please refresh the page.');
    }
  }, [securityReady, id, turnOffCamera]);

  // Store application ID
  useEffect(() => {
    if (id) {
      appIdRef.current = id;
      console.log('📝 Application ID stored:', id);
    }
  }, [id]);

  // FIXED: Camera initialization - start camera immediately when permissions are available
  const initializeWebcamWithExistingPermissions = useCallback(async () => {
    try {
      console.log('🎥 Starting webcam immediately with existing permissions...');
      
      // Permissions are already granted from interview-setup
      setCameraPermission('granted');
      setMicrophonePermission('granted');
      
      // Start webcam immediately - don't wait
      console.log('✅ Starting webcam now');
      setShowWebcam(true);
      
    } catch (error) {
      console.error('❌ Error initializing webcam:', error);
      setError(`Webcam initialization error: ${error.message}`);
    }
  }, []);

  // FIXED: Start webcam immediately when component mounts
  useEffect(() => {
    console.log('🎥 Initializing webcam on component mount...');
    initializeWebcamWithExistingPermissions();
  }, [initializeWebcamWithExistingPermissions]);

  // Fetch questions function
  const fetchQuestions = useCallback(async () => {
    try {
      setLoadingStatus('Requesting questions from AI providers...');
      
      const response = await axios.post('/api/interview/generate-questions', {
        applicationId: id
      });
      
      if (response.data?.questions) {
        const validQuestions = response.data.questions.filter(q => 
          q && typeof q === 'object' && q.question && 
          ['mcq', 'technical', 'behavioral', 'coding'].includes(q.type)
        );
        
        console.log(`✅ Successfully loaded ${validQuestions.length} questions`);
        setQuestions(validQuestions);
        setLoading(false);
        setQuestionStartTime(Date.now());
        
        // Start security monitoring
        if (securityManagerRef.current && !securityManagerRef.current.isInterviewActive) {
          setTimeout(() => {
            console.log('🚀 Starting SecurityManager monitoring');
            securityManagerRef.current.startInterview();
          }, 1000);
        }
      } else {
        throw new Error('No valid questions received');
      }
    } catch (error) {
      console.error('Error fetching questions:', error);
      setError('Failed to load questions. Please refresh the page.');
      setLoading(false);
    }
  }, [id]);

  // FIXED: Fetch questions after security is ready and fullscreen is confirmed
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!questions.length && !questionsLoadedRef.current && securityReady && isFullScreen) {
        console.log('🚨 Fetching questions after fullscreen confirmed...');
        questionsLoadedRef.current = true;
        fetchQuestions();
      }
    }, 2000);
    
    return () => clearTimeout(timer);
  }, [securityReady, questions.length, isFullScreen, fetchQuestions]);

  // Handle camera update
  const handleCameraUpdate = useCallback((status) => {
    try {
      console.log('📸 Camera status update:', status);
      setCameraStatus(status);
      
      // Pass to SecurityManager
      if (securityManagerRef.current && securityManagerRef.current.isInterviewActive) {
        securityManagerRef.current.handleFaceDetection(status);
      }
    } catch (error) {
      console.error('Error handling camera update:', error);
    }
  }, []);

  // Handle answer changes
  const handleAnswerChange = useCallback((answer) => {
    try {
      setAnswers(prev => ({
        ...prev,
        [currentQuestion]: answer
      }));
      
      // Track first interaction
      if (!hasInteractedWithQuestion && questionStartTime) {
        const interactionTime = Date.now();
        setFirstInteractionTime(interactionTime);
        setHasInteractedWithQuestion(true);
        
        const timeToFirstInteraction = (interactionTime - questionStartTime) / 1000;
        
        setResponseTimeData(prev => ({
          ...prev,
          [currentQuestion]: {
            ...prev[currentQuestion],
            timeToFirstInteraction
          }
        }));
      }
    } catch (error) {
      console.error('Error handling answer change:', error);
    }
  }, [currentQuestion, hasInteractedWithQuestion, questionStartTime]);

  // Fullscreen management
  const isDocumentFullScreen = () => {
    return !!(
      document.fullscreenElement ||
      document.webkitFullscreenElement ||
      document.mozFullScreenElement ||
      document.msFullscreenElement
    );
  };

  // Enhanced fullscreen protection
  useEffect(() => {
    let blockingOverlay = null;
    
    const handleFullscreenChange = () => {
      const isFullscreen = isDocumentFullScreen();
      setIsFullScreen(isFullscreen);
      
      if (!isFullscreen && questions.length > 0 && !exitingRef.current) {
        console.log('🚨 Fullscreen exit detected during interview');
        
        if (!blockingOverlay) {
          blockingOverlay = document.createElement('div');
          blockingOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(255,0,0,0.95);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            font-size: 20px;
            text-align: center;
          `;
          blockingOverlay.innerHTML = `
            <div style="font-size: 32px; margin-bottom: 20px;">⚠️ FULLSCREEN REQUIRED ⚠️</div>
            <div style="margin-bottom: 30px; font-size: 18px;">
              You have exited fullscreen mode.<br/>
              This is a security violation and has been recorded.
            </div>
            <button id="return-fullscreen-btn" style="
              font-size: 18px;
              padding: 15px 30px;
              background: white;
              color: red;
              border: none;
              border-radius: 8px;
              cursor: pointer;
              font-weight: bold;
            ">Click Here to Return to Fullscreen</button>
            <div style="font-size: 14px; margin-top: 20px;">
              The interview is paused until you return to fullscreen.
            </div>
          `;
          
          document.body.appendChild(blockingOverlay);
          
          document.getElementById('return-fullscreen-btn').addEventListener('click', async () => {
            try {
              await requestFullscreen();
            } catch (error) {
              console.error('Fullscreen request failed:', error);
              alert('Please manually enter fullscreen mode to continue the interview.');
            }
          });
        }
      } else if (isFullscreen && blockingOverlay) {
        blockingOverlay.remove();
        blockingOverlay = null;
      }
    };
    
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);
    
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
      if (blockingOverlay) {
        blockingOverlay.remove();
      }
    };
  }, [questions.length]);

  // Initialize fullscreen state
  useEffect(() => {
    const isFullscreen = isDocumentFullScreen();
    setIsFullScreen(isFullscreen);
  }, []);

  // Fullscreen prompt before starting
  useEffect(() => {
    const promptTimer = setTimeout(() => {
      if (securityReady && !isFullScreen && !questionsLoadedRef.current) {
        console.log('🖥️ Prompting for fullscreen...');
        
        const createFullscreenPrompt = () => {
          const prompt = document.createElement('div');
          prompt.id = 'fullscreen-request-prompt';
          prompt.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,255,0.95);
            color: white;
            padding: 30px 40px;
            border-radius: 10px;
            font-size: 18px;
            text-align: center;
            z-index: 10000;
            box-shadow: 0 4px 20px rgba(0,0,0,0.5);
          `;
          prompt.innerHTML = `
            <div style="font-size: 24px; margin-bottom: 10px;">🎯 Interview Ready!</div>
            <div style="margin: 20px 0;">
              Click below to enter fullscreen mode and begin your interview
            </div>
            <button id="enter-fullscreen-btn" style="
              font-size: 16px;
              padding: 12px 24px;
              background: white;
              color: blue;
              border: none;
              border-radius: 6px;
              cursor: pointer;
              font-weight: bold;
            ">Enter Fullscreen & Start Interview</button>
            <div style="font-size: 14px; margin-top: 15px; color: #E0E0E0;">
              This is required for interview security
            </div>
          `;
          
          document.body.appendChild(prompt);
          
          document.getElementById('enter-fullscreen-btn').addEventListener('click', async () => {
            try {
              await requestFullscreen();
              prompt.remove();
            } catch (error) {
              console.error('Fullscreen request failed:', error);
              alert('Please allow fullscreen access to continue the interview.');
            }
          });
        };
        
        createFullscreenPrompt();
      }
    }, 2000);
    
    return () => clearTimeout(promptTimer);
  }, [securityReady, isFullScreen]);

  // Exit handling
  const handleExit = useCallback(async () => {
    console.log('🚪 Starting exit process...');
    
    // Set exiting flag to prevent violations
    exitingRef.current = true;
    if (securityManagerRef.current) {
      securityManagerRef.current.setExiting(true);
    }
    
    // Turn off camera
    turnOffCamera();
    
    // Exit fullscreen
    await exitFullscreen();
    
    // Navigate to dashboard
    router.push('/candidate-dashboard');
  }, [turnOffCamera, router]);

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      console.log('🧹 Component unmounting - cleanup...');
      isComponentMountedRef.current = false;
      
      // Set exiting flag
      exitingRef.current = true;
      if (securityManagerRef.current) {
        securityManagerRef.current.setExiting(true);
      }
      
      // Turn off camera
      turnOffCamera();
    };
  }, [turnOffCamera]);

  // Handle before unload to prevent violations during exit
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (securityManagerRef.current) {
        securityManagerRef.current.setExiting(true);
      }
      exitingRef.current = true;
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('unload', handleBeforeUnload);
    window.addEventListener('pagehide', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('unload', handleBeforeUnload);
      window.removeEventListener('pagehide', handleBeforeUnload);
    };
  }, []);

  // Basic webcam component
  const webcamComponent = useMemo(() => {
    // FIXED: Always show webcam during interview (don't check permissions again)
    if (!showWebcam) return null;

    return (
      <div className="bg-gray-100 p-3 rounded-lg shadow">
        <h3 className="text-lg font-medium mb-2 text-gray-700">Proctoring Monitor</h3>
        
        <WebcamFaceDetection 
          ref={webcamRef} 
          onStatusUpdate={handleCameraUpdate}
        />
        
        {/* Camera status indicators */}
        <div className="mt-4 grid grid-cols-2 gap-2">
          <div className={`p-2 rounded text-sm flex items-center ${
            cameraStatus.isDetecting ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            <FaVideo className="mr-1" />
            Camera: {cameraStatus.isDetecting ? 'Active' : 'Inactive'}
          </div>
          
          <div className={`p-2 rounded text-sm flex items-center ${
            cameraStatus.facesDetected === 1 ? 'bg-green-100 text-green-800' : 
            cameraStatus.facesDetected > 1 ? 'bg-red-100 text-red-800' :
            'bg-yellow-100 text-yellow-800'
          }`}>
            <FaEye className="mr-1" />
            Faces: {cameraStatus.facesDetected}
          </div>
        </div>
        
        {/* Security status */}
        <div className="mt-4 p-3 bg-gray-50 rounded border border-gray-200">
          <h4 className="font-medium text-gray-800 mb-2 flex items-center">
            <FaShieldAlt className="mr-1" />
            Security Status
          </h4>
          <div className="text-sm text-gray-700 space-y-1">
            <div className="flex justify-between items-center">
              <span>Security System:</span>
              <span className={`font-semibold ${securityInitialized ? 'text-green-600' : 'text-yellow-600'}`}>
                {securityInitialized ? 'Active' : 'Initializing'}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span>Violation Count:</span>
              <span className={`font-semibold ${
                totalViolations === 0 ? 'text-green-600' :
                totalViolations < 3 ? 'text-yellow-600' : 
                totalViolations < 5 ? 'text-orange-600' : 'text-red-600'
              }`}>
                {totalViolations} / 5
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span>Fullscreen:</span>
              <span className={`font-semibold ${isFullScreen ? 'text-green-600' : 'text-red-600'}`}>
                {isFullScreen ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  }, [showWebcam, cameraStatus, securityInitialized, totalViolations, isFullScreen, handleCameraUpdate]);

  // Security warning banner
  const SecurityWarningBanner = () => {
    if (!showWarning) return null;

    return (
      <div className={`fixed top-0 left-0 w-full p-3 z-50 text-center ${
        showFinalWarning ? 'bg-red-600' : totalViolations >= 4 ? 'bg-orange-600' : 'bg-yellow-600'
      } text-white`}>
        <p className="text-sm font-semibold">
          <FaExclamationTriangle className="inline mr-2" />
          {warningMessage}
        </p>
        {showFinalWarning && (
          <p className="text-xs mt-1">
            This is your final warning. One more violation will end the interview.
          </p>
        )}
      </div>
    );
  };

  // Disqualification overlay
  const DisqualificationOverlay = () => {
    if (!disqualified) return null;

    return (
      <div className="fixed inset-0 bg-red-600 text-white flex flex-col items-center justify-center z-50 p-8">
        <FaExclamationTriangle className="text-6xl mb-4" />
        <h2 className="text-3xl font-bold mb-4">INTERVIEW DISQUALIFIED</h2>
        <p className="text-xl mb-8">{disqualificationReason}</p>
        <div className="bg-red-700 p-4 rounded mb-4 text-center max-w-2xl">
          <p className="mb-2">Maximum security violations reached</p>
          <p className="mb-2">Common violations include:</p>
          <ul className="text-sm text-left">
            <li>• Tab switching or window switching</li>
            <li>• Exiting fullscreen mode</li>
            <li>• Using developer tools</li>
            <li>• Multiple faces in camera</li>
            <li>• Looking away from camera</li>
            <li>• Taking screenshots</li>
            <li>• Device detection (phones, etc.)</li>
          </ul>
        </div>
        <button 
          onClick={handleExit}
          className="bg-white text-red-600 px-6 py-3 rounded font-bold hover:bg-gray-100"
          type="button"
        >
          Return to Dashboard
        </button>
      </div>
    );
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-md text-center max-w-md">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <h2 className="text-xl font-bold mb-2">Loading Interview</h2>
          <p className="text-gray-600 mb-4">{loadingStatus}</p>
          
          {error && (
            <div className="bg-red-50 p-3 rounded border border-red-200 mb-4 text-left">
              <p className="text-red-700">{error}</p>
              <button 
                onClick={() => {
                  questionsLoadedRef.current = false;
                  window.location.reload();
                }}
                className="mt-2 bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700"
              >
                Refresh Page
              </button>
            </div>
          )}
        </div>
      </div>
    );
  }

  const currentQ = questions[currentQuestion];

  return (
    <div className="min-h-screen bg-white text-black">
      <SecurityWarningBanner />
      <DisqualificationOverlay />
      
      {/* Header bar */}
      <div className="flex justify-between items-center px-6 py-4 bg-blue-600 text-white">
        <div className="flex items-center">
          <FaLock className={`mr-2 ${isFullScreen ? 'text-green-500' : 'text-red-500'}`} />
          <h1 className="text-xl font-semibold">Proctored Interview</h1>
          {!isFullScreen && (
            <button 
              onClick={requestFullscreen}
              className="ml-2 bg-yellow-500 text-xs px-2 py-1 rounded hover:bg-yellow-600"
            >
              Enter Fullscreen
            </button>
          )}
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <FaVideo className={`mr-1 ${cameraStatus.facesDetected > 0 ? 'text-green-500' : 'text-red-500'}`} />
            <span className="text-white text-sm">Camera: {cameraStatus.isDetecting ? 'Active' : 'Inactive'}</span>
          </div>
          
          <div className="flex items-center">
            <FaMicrophone className={`mr-1 ${isListening ? 'text-green-500 animate-pulse' : 'text-white'}`} />
            <span className="text-white text-sm">Audio: {isListening ? 'Recording' : 'Ready'}</span>
          </div>
          
          <div className="flex items-center">
            <FaShieldAlt className={`mr-1 ${securityInitialized ? 'text-green-500' : 'text-red-500'}`} />
            <span className="text-white text-sm">Security: {securityInitialized ? 'Active' : 'Loading'}</span>
          </div>
          
          <div className="flex items-center">
            <FaClock className={`mr-1 text-white`} />
            <span className="text-white text-sm">
              Time: {questionStartTime ? Math.floor((Date.now() - questionStartTime) / 1000) : 0}s
            </span>
          </div>
          
          <div className="bg-blue-800 rounded px-3 py-1 text-sm">
            Question {currentQuestion + 1} of {questions?.length || 0}
          </div>
          
          <button
            onClick={handleExit}
            className="bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700 flex items-center"
            type="button"
          >
            <FaSignOutAlt className="inline mr-1" /> Exit
          </button>
        </div>
      </div>

      {error && !loading && (
        <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4">
          <p className="font-medium">Notice</p>
          <p>{error}</p>
        </div>
      )}
      
      {/* Main content */}
      <div className="flex flex-col md:flex-row p-6 gap-8">
        {/* Left column - Webcam */}
        <div className="w-full md:w-1/3">
          {webcamComponent}
        </div>

        {/* Right column - Questions */}
        <div className="w-full md:w-2/3">
          {questions?.length ? (
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="mb-4 flex justify-between items-center">
                <h3 className="text-lg font-semibold">Question {currentQuestion + 1} of {questions.length}</h3>
                <div className="flex items-center">
                  <span className="text-sm text-gray-500 mr-3">
                    {currentQ.type === 'mcq' ? 'Multiple Choice' : 
                     currentQ.type === 'technical' ? 'Technical Question' :
                     currentQ.type === 'behavioral' ? 'Behavioral Question' :
                     currentQ.type === 'coding' ? 'Coding Question' :
                     'Interview Question'}
                  </span>
                </div>
              </div>
              
              <div className="mb-4">
                <p className="font-medium text-lg mb-4">{currentQ?.question || 'Loading question...'}</p>
              </div>
              
              {/* MCQ Questions */}
              {currentQ?.type === 'mcq' && (
                <div className="space-y-2 mb-4">
                  {currentQ.options?.map((opt, i) => (
                    <label key={i} className={`block border p-3 rounded hover:bg-gray-100 cursor-pointer transition-colors ${
                      answers[currentQuestion] === opt ? 'border-blue-500 bg-blue-50' : ''
                    }`}>
                      <input
                        type="radio"
                        name={`question-${currentQuestion}`}
                        value={opt}
                        checked={answers[currentQuestion] === opt}
                        onChange={() => handleAnswerChange(opt)}
                        className="mr-3"
                      />
                      {opt}
                    </label>
                  ))}
                </div>
              )}
              
              {/* Behavioral/Technical Questions */}
              {['behavioral', 'technical'].includes(currentQ?.type) && (
                <div className="mt-4">
                  <div className="flex justify-between items-center mb-3">
                    <h4 className="font-medium">Your Answer</h4>
                  </div>
                  
                  <textarea 
                    rows={6} 
                    className="w-full border rounded p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none" 
                    placeholder="Type your answer here"
                    value={answers[currentQuestion] || ''}
                    onChange={(e) => handleAnswerChange(e.target.value)}
                  ></textarea>
                </div>
              )}
              
              {/* Coding Questions */}
              {currentQ?.type === 'coding' && (
                <div className="mt-4">
                  <div className="bg-gray-800 text-white p-2 rounded-t flex justify-between items-center">
                    <div className="flex items-center">
                      <FaCode className="mr-2" />
                      <span>Code Editor (JavaScript)</span>
                    </div>
                  </div>
                  
                  <CodeEditor
                    language="javascript"
                    value={code}
                    onChange={(newValue) => setCode(newValue)}
                    height="400px"
                  />
                  
                  <div className="flex mt-2">
                    <button
                      onClick={() => {
                        // Simple code execution simulation
                        setCodeOutput('Code executed successfully!');
                      }}
                      disabled={isRunningCode}
                      className={`px-4 py-2 rounded ${
                        isRunningCode ? 'bg-gray-400' : 'bg-green-600 hover:bg-green-700'
                      } text-white`}
                    >
                      {isRunningCode ? 'Running...' : 'Run Code'}
                    </button>
                  </div>
                  
                  {codeOutput && (
                    <div className="mt-4 p-4 bg-gray-800 text-white rounded overflow-auto max-h-60">
                      <div className="text-sm text-gray-400 mb-2">Output:</div>
                      <pre className="whitespace-pre-wrap">{codeOutput}</pre>
                    </div>
                  )}
                </div>
              )}
              
              {/* Navigation buttons */}
              <div className="mt-8 flex justify-between">
                <button
                  onClick={() => setCurrentQuestion(prev => prev - 1)}
                  disabled={currentQuestion === 0}
                  className={`px-4 py-2 rounded ${
                    currentQuestion === 0 ? 'bg-gray-300 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'
                  } text-white`}
                >
                  Previous
                </button>
                
                {currentQuestion < questions.length - 1 ? (
                  <button
                    onClick={() => setCurrentQuestion(prev => prev + 1)}
                    className="px-4 py-2 rounded bg-green-600 hover:bg-green-700 text-white"
                  >
                    Next
                  </button>
                ) : (
                  <button
                    onClick={() => {
                      // Simple submit
                      alert('Interview submitted successfully!');
                      handleExit();
                    }}
                    className="px-4 py-2 rounded bg-green-600 hover:bg-green-700 text-white"
                  >
                    Submit Interview
                  </button>
                )}
              </div>
            </div>
          ) : (
            <div className="bg-white p-6 rounded-lg shadow text-center">
              <p className="text-gray-600">No questions available for this interview.</p>
              <button 
                onClick={handleExit}
                className="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
                type="button"
              >
                Return to Dashboard
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
